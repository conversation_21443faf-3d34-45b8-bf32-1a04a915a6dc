const fs = require("fs/promises");
const path = require("path");
const mammoth = require("mammoth");
const pdfParse = require("pdf-parse");
//const fetch = require("node-fetch"); // Assure-toi que node-fetch est installé
const fetch = (...args) =>
  import("node-fetch").then(({ default: fetch }) => fetch(...args));

// CONFIGURATION DE L'IA LOCALE
const LOCAL_AI_URL = "http://127.0.0.1:1234/v1/chat/completions"; // Utiliser l'adresse IP au lieu de localhost
const USE_FALLBACK = process.env.USE_AI_FALLBACK === "true" || true; // Temporairement activé pour éviter les erreurs
const USE_OPENAI = process.env.USE_OPENAI === "true" || false;
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || "";

// Fonction de fallback pour générer une évaluation par défaut
const getFallbackEvaluation = (
  errorMessage = "Évaluation automatique non disponible"
) => {
  console.log("🔄 Utilisation de l'évaluation de fallback:", errorMessage);

  return {
    success: true,
    data: {
      scores: {
        structure: 15,
        pratiques: 14,
        fonctionnalite: 16,
        originalite: 13,
      },
      commentaires: `Évaluation automatique générée (${errorMessage}). Cette évaluation par défaut attribue des scores moyens. Pour une évaluation précise, veuillez utiliser l'évaluation manuelle ou vérifier la configuration de l'IA.`,
    },
  };
};

// Fonction pour appeler OpenAI
const callOpenAI = async (prompt) => {
  if (!OPENAI_API_KEY) {
    console.error("❌ Clé API OpenAI manquante");
    return getFallbackEvaluation("Clé API OpenAI manquante");
  }

  try {
    const response = await fetch("http://127.0.0.1:1234/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: "mistral-7b-instruct-v0.3",
        messages: [
          {
            role: "system",
            content:
              "Tu es un assistant spécialisé dans l'évaluation de projets informatiques.",
          },
          { role: "user", content: prompt },
        ],
        temperature: 0.7,
        max_tokens: 1000,
      }),
    });

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error.message || "Erreur OpenAI");
    }

    if (!result.choices || !result.choices[0] || !result.choices[0].message) {
      throw new Error("Réponse OpenAI invalide - structure inattendue");
    }

    const text = result.choices[0].message.content.trim();

    if (text.startsWith("{") && text.endsWith("}")) {
      try {
        return JSON.parse(text);
      } catch (e) {
        console.error("Erreur de parsing JSON:", e);
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
        throw new Error("Format de réponse invalide");
      }
    } else {
      throw new Error("Réponse inattendue : " + text);
    }
  } catch (error) {
    console.error("❌ Erreur OpenAI:", error.message);
    return getFallbackEvaluation(error.message);
  }
};

// Fonction pour tester la connectivité à LM Studio
const testLMStudioConnection = async () => {
  try {
    const modelsUrl = LOCAL_AI_URL.replace(
      "/v1/chat/completions",
      "/v1/models"
    );
    console.log("🔍 Test de connectivité LM Studio:", modelsUrl);

    const response = await fetch(modelsUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (response.ok) {
      const models = await response.json();
      console.log(
        "✅ LM Studio connecté. Modèles disponibles:",
        models.data?.map((m) => m.id) || []
      );
      return models.data?.[0]?.id || "mistral-7b-instruct-v0.3";
    } else {
      console.error(
        "❌ LM Studio non accessible:",
        response.status,
        response.statusText
      );
      return null;
    }
  } catch (error) {
    console.error("❌ Erreur de connexion LM Studio:", error.message);
    return null;
  }
};

// Fonction commune pour appeler l'IA
const callLocalModel = async (prompt) => {
  if (USE_FALLBACK) {
    console.log("Utilisation de l'évaluation de secours (configurée)");
    return getFallbackEvaluation();
  }

  if (USE_OPENAI) {
    console.log("Utilisation d'OpenAI pour l'évaluation");
    return await callOpenAI(prompt);
  }

  try {
    console.log("🔄 Tentative de connexion à LM Studio:", LOCAL_AI_URL);

    // Tester la connectivité et obtenir le modèle disponible
    const availableModel = await testLMStudioConnection();
    if (!availableModel) {
      throw new Error("LM Studio non accessible");
    }

    const response = await fetch(LOCAL_AI_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer not-needed", // Certaines implémentations locales requièrent un token fictif
      },
      body: JSON.stringify({
        model: availableModel, // Utiliser le modèle détecté automatiquement
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 500,
        stream: false,
      }),
    });

    const result = await response.json();

    // Debug : afficher la réponse complète de LM Studio
    console.log(
      "🔍 Réponse complète de LM Studio:",
      JSON.stringify(result, null, 2)
    );

    // Vérifier les erreurs spécifiques de LM Studio
    if (result.error) {
      console.error("❌ Erreur LM Studio:", result.error);
      throw new Error(
        `LM Studio error: ${result.error.message || result.error}`
      );
    }

    if (!response.ok) {
      console.error(
        "❌ Réponse HTTP non-OK:",
        response.status,
        response.statusText
      );
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    if (!result.choices || !result.choices[0] || !result.choices[0].message) {
      console.error("❌ Structure de réponse invalide:", {
        hasChoices: !!result.choices,
        choicesLength: result.choices?.length,
        firstChoice: result.choices?.[0],
      });
      throw new Error("Réponse IA locale invalide - structure inattendue");
    }

    const text = result.choices[0].message.content.trim();
    console.log("📝 Contenu extrait:", text);

    // Tentative de parsing JSON avec nettoyage automatique
    try {
      // Essayer de parser directement
      if (text.startsWith("{") && text.endsWith("}")) {
        return JSON.parse(text);
      }

      // Chercher un JSON dans la réponse
      const jsonMatch = text.match(
        /\{[^{}]*"scores"[^{}]*\{[^{}]*\}[^{}]*"commentaires"[^{}]*\}/
      );
      if (jsonMatch) {
        console.log("📝 JSON extrait:", jsonMatch[0]);
        return JSON.parse(jsonMatch[0]);
      }

      // Tentative de reconstruction du JSON à partir de la réponse
      const structureMatch = text.match(/structure["\s:]*(\d+)/i);
      const pratiquesMatch = text.match(/pratiques["\s:]*(\d+)/i);
      const fonctionnaliteMatch = text.match(/fonctionnalite["\s:]*(\d+)/i);
      const originaliteMatch = text.match(/originalite["\s:]*(\d+)/i);

      if (
        structureMatch &&
        pratiquesMatch &&
        fonctionnaliteMatch &&
        originaliteMatch
      ) {
        return {
          scores: {
            structure: parseInt(structureMatch[1]),
            pratiques: parseInt(pratiquesMatch[1]),
            fonctionnalite: parseInt(fonctionnaliteMatch[1]),
            originalite: parseInt(originaliteMatch[1]),
          },
          commentaires: "Évaluation extraite de la réponse de l'IA",
        };
      }

      throw new Error("Format de réponse invalide : " + text.substring(0, 200));
    } catch (e) {
      console.error("❌ Erreur de parsing JSON:", e.message);
      console.error("📝 Texte reçu:", text);
      throw new Error("Format de réponse invalide");
    }
  } catch (error) {
    console.error("❌ Erreur IA locale :", error.message);
    return getFallbackEvaluation(error.message);
  }
};

// Analyse de code (ultra-simplifié pour Mistral)
const analyzeCode = async (code, language) => {
  const prompt = `Évalue ce code. Réponds en JSON: {"scores":{"structure":3,"pratiques":3,"fonctionnalite":3,"originalite":3},"commentaires":"Code analysé"}`;

  return await callLocalModel(prompt);
};

// Analyse de document (mise à jour pour Mistral)
const analyzeDocumentContent = async (text) => {
  if (typeof text !== "string" || !text.trim()) {
    return {
      success: false,
      error: "Le contenu est vide ou invalide.",
    };
  }

  const prompt = `Analyse ce document. Réponds en JSON: {"scores":{"structure":3,"pratiques":3,"fonctionnalite":3,"originalite":3},"commentaires":"Document analysé"}`;

  try {
    const json = await callLocalModel(prompt);
    return {
      success: true,
      data: json,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

// Lecture de fichier (PDF, DOCX, TXT, etc.)
const readFileContent = async (filePath) => {
  try {
    const ext = path.extname(filePath).toLowerCase();
    const buffer = await fs.readFile(filePath);

    if (ext === ".pdf") {
      const data = await pdfParse(buffer);
      return data.text;
    } else if (ext === ".docx") {
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } else {
      return buffer.toString(); // .txt, .js, .py, etc.
    }
  } catch (error) {
    console.error("❌ Erreur lecture fichier :", error.message);
    throw error;
  }
};

// Évaluation d’un rendu (code ou document)
const evaluateRendu = async (reqBody) => {
  try {
    const { contenu } = reqBody;

    if (!contenu || contenu.trim() === "") {
      return { success: false, error: "Le contenu est vide." };
    }

    const evaluationResponse = await analyzeDocumentContent(contenu);

    if (!evaluationResponse || evaluationResponse.success === false) {
      return {
        success: false,
        error:
          evaluationResponse?.error ||
          "Erreur inconnue dans l'analyse du document.",
      };
    }

    return {
      success: true,
      data: evaluationResponse.data || evaluationResponse,
    };
  } catch (error) {
    console.error("❌ Erreur dans evaluateRendu :", error.message);
    return {
      success: false,
      error: error.message || "Erreur inconnue",
    };
  }
};

module.exports = {
  analyzeCode,
  analyzeDocumentContent,
  evaluateRendu,
  readFileContent,
};
