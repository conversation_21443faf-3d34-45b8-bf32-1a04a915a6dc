{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { CallType, CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION, CALL_SIGNAL_SUBSCRIPTION, SEND_CALL_SIGNAL_MUTATION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\n/**\n * Service unifié pour la gestion des appels vidéo/audio\n * Gère l'état des appels, WebRTC, et la synchronisation\n */\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // ===== ÉTAT PRINCIPAL =====\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    // ===== ÉTAT DES APPELS =====\n    this.currentCallId = null;\n    this.callState = 'idle';\n    // ===== GESTION AUDIO =====\n    this.sounds = {};\n    this.isPlaying = {};\n    // ===== WEBRTC =====\n    this.peerConnection = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.isAudioEnabled = true;\n    this.isVideoEnabled = true;\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    this.logger.info('CallService', '🚀 Initializing unified CallService...');\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n    this.logger.info('CallService', '✅ CallService initialized successfully');\n  }\n  ngOnDestroy() {\n    this.logger.info('CallService', '🔄 Destroying CallService...');\n    this.cleanup();\n  }\n  // ===== MÉTHODES PUBLIQUES PRINCIPALES =====\n  /**\n   * Initie un appel\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    this.logger.info('CallService', '📞 Initiating call:', {\n      recipientId,\n      callType\n    });\n    if (this.callState !== 'idle') {\n      return throwError(() => new Error('Another call is already in progress'));\n    }\n    this.setCallState('initiating');\n    const callId = this.generateCallId();\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables: {\n        recipientId,\n        callType,\n        callId,\n        conversationId\n      }\n    }).pipe(map(result => {\n      const call = result.data?.initiateCall;\n      if (!call) throw new Error('Failed to initiate call');\n      this.handleCallInitiated(call);\n      return call;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error initiating call:', error);\n      this.setCallState('idle');\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(call) {\n    this.logger.info('CallService', '✅ Accepting call:', call.id);\n    if (!call) {\n      return throwError(() => new Error('No call to accept'));\n    }\n    this.setCallState('connecting');\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: call.id\n      }\n    }).pipe(map(result => {\n      const acceptedCall = result.data?.acceptCall;\n      if (!acceptedCall) throw new Error('Failed to accept call');\n      this.handleCallAccepted(acceptedCall);\n      return acceptedCall;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error accepting call:', error);\n      this.setCallState('idle');\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Rejette un appel\n   */\n  rejectCall(callId, reason) {\n    this.logger.info('CallService', '❌ Rejecting call:', callId);\n    this.setCallState('ending');\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      const success = result.data?.rejectCall;\n      if (!success) throw new Error('Failed to reject call');\n      this.handleCallEnded();\n      return success;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error rejecting call:', error);\n      this.handleCallEnded(); // Nettoyer même en cas d'erreur\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Termine un appel\n   */\n  endCall(callId) {\n    this.logger.info('CallService', '🔚 Ending call:', callId);\n    this.setCallState('ending');\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId\n      }\n    }).pipe(map(result => {\n      const success = result.data?.endCall;\n      if (!success) throw new Error('Failed to end call');\n      this.handleCallEnded();\n      return success;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error ending call:', error);\n      this.handleCallEnded(); // Nettoyer même en cas d'erreur\n      return throwError(() => error);\n    }));\n  }\n  // ===== GETTERS PUBLICS =====\n  get currentCall() {\n    return this.activeCall.value;\n  }\n  get currentIncomingCall() {\n    return this.incomingCall.value;\n  }\n  get isCallActive() {\n    return this.callState === 'connected';\n  }\n  get isCallInProgress() {\n    return this.callState !== 'idle';\n  }\n  // ===== MÉTHODES PRIVÉES =====\n  generateCallId() {\n    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n  setCallState(state) {\n    this.logger.debug('CallService', `Call state: ${this.callState} → ${state}`);\n    this.callState = state;\n  }\n  handleCallInitiated(call) {\n    this.logger.info('CallService', 'Call initiated successfully:', call.id);\n    this.currentCallId = call.id;\n    this.activeCall.next(call);\n    this.setCallState('ringing');\n    this.play('ringtone', true);\n    this.startOutgoingCallMedia(call.type);\n  }\n  handleCallAccepted(call) {\n    this.logger.info('CallService', 'Call accepted successfully:', call.id);\n    this.activeCall.next(call);\n    this.incomingCall.next(null);\n    this.setCallState('connected');\n    this.stop('ringtone');\n    this.play('call-connected');\n  }\n  handleCallEnded() {\n    this.logger.info('CallService', 'Call ended, cleaning up');\n    this.setCallState('idle');\n    this.currentCallId = null;\n    this.activeCall.next(null);\n    this.incomingCall.next(null);\n    this.stopAllSounds();\n    this.play('call-end');\n    this.cleanupWebRTC();\n  }\n  handleIncomingCall(call) {\n    this.logger.info('CallService', 'Incoming call received:', call.id);\n    this.currentCallId = call.id;\n    this.incomingCall.next(call);\n    this.setCallState('ringing');\n    this.play('ringtone', true);\n    this.prepareForIncomingCall(call);\n  }\n  handleCallStatusChange(call) {\n    this.logger.info('CallService', 'Call status changed:', call.status);\n    if (call.id === this.currentCallId) {\n      this.activeCall.next(call);\n      switch (call.status) {\n        case CallStatus.CONNECTED:\n          this.setCallState('connected');\n          this.stop('ringtone');\n          this.play('call-connected');\n          break;\n        case CallStatus.ENDED:\n        case CallStatus.REJECTED:\n          this.handleCallEnded();\n          break;\n      }\n    }\n  }\n  handleCallSignal(signal) {\n    this.logger.debug('CallService', 'Call signal received:', signal.type);\n    this.callSignals.next(signal);\n    // Traitement WebRTC des signaux sera ajouté ici\n  }\n  // ===== INITIALISATION =====\n  initializeSounds() {\n    this.logger.debug('CallService', 'Initializing sounds...');\n    this.createSyntheticSounds();\n  }\n  createSyntheticSounds() {\n    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);\n    this.createSyntheticSound('call-connected', [523.25, 659.25, 783.99], 0.8, false);\n    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);\n  }\n  createSyntheticSound(name, frequencies, duration, loop) {\n    try {\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const sampleRate = audioContext.sampleRate;\n      const frameCount = sampleRate * duration;\n      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);\n      const channelData = buffer.getChannelData(0);\n      for (let i = 0; i < frameCount; i++) {\n        let sample = 0;\n        frequencies.forEach(freq => {\n          const amplitude = 0.3 / frequencies.length;\n          const phase = i / sampleRate * freq * 2 * Math.PI;\n          sample += Math.sin(phase) * amplitude;\n        });\n        const envelope = Math.sin(i / frameCount * Math.PI);\n        channelData[i] = sample * envelope;\n      }\n      const audio = new Audio();\n      audio.loop = loop;\n      audio.customPlay = () => {\n        const source = audioContext.createBufferSource();\n        source.buffer = buffer;\n        source.loop = loop;\n        source.connect(audioContext.destination);\n        source.start();\n        if (!loop) {\n          setTimeout(() => {\n            this.isPlaying[name] = false;\n          }, duration * 1000);\n        }\n        return source;\n      };\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n    } catch (error) {\n      this.logger.error('CallService', `Error creating sound '${name}':`, error);\n    }\n  }\n  initializeSubscriptions() {\n    this.logger.debug('CallService', 'Initializing subscriptions...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n  subscribeToIncomingCalls() {\n    this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.incomingCall) {\n          this.handleIncomingCall(data.incomingCall);\n        }\n        if (errors) {\n          this.logger.error('CallService', 'Incoming call subscription errors:', errors);\n        }\n      },\n      error: error => {\n        this.logger.error('CallService', 'Error in incoming call subscription:', error);\n        setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n      }\n    });\n  }\n  subscribeToCallStatusChanges() {\n    this.apollo.subscribe({\n      query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.callStatusChanged) {\n          this.handleCallStatusChange(data.callStatusChanged);\n        }\n        if (errors) {\n          this.logger.error('CallService', 'Call status subscription errors:', errors);\n        }\n      },\n      error: error => {\n        this.logger.error('CallService', 'Error in call status subscription:', error);\n        setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n      }\n    });\n  }\n  subscribeToCallSignals() {\n    this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.callSignal) {\n          this.handleCallSignal(data.callSignal);\n        }\n        if (errors) {\n          this.logger.error('CallService', 'Call signal subscription errors:', errors);\n        }\n      },\n      error: error => {\n        this.logger.error('CallService', 'Error in call signal subscription:', error);\n        setTimeout(() => this.subscribeToCallSignals(), 5000);\n      }\n    });\n  }\n  initializeWebRTC() {\n    this.logger.debug('CallService', 'Initializing WebRTC...');\n    this.createPeerConnection();\n  }\n  // ===== GESTION AUDIO =====\n  play(name, loop = false) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound || this.isPlaying[name]) return;\n      if (sound.customPlay) {\n        sound.currentSource = sound.customPlay();\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      this.logger.error('CallService', `Error playing sound '${name}':`, error);\n    }\n  }\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound || !this.isPlaying[name]) return;\n      if (sound.currentSource) {\n        sound.currentSource.stop();\n        sound.currentSource = null;\n      }\n      this.isPlaying[name] = false;\n    } catch (error) {\n      this.logger.error('CallService', `Error stopping sound '${name}':`, error);\n    }\n  }\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => this.stop(name));\n  }\n  // ===== WEBRTC =====\n  createPeerConnection() {\n    try {\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      this.logger.debug('CallService', 'PeerConnection created successfully');\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate && this.currentCallId) {\n          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      this.peerConnection.ontrack = event => {\n        this.logger.info('CallService', 'Remote track received:', event.track.kind);\n        this.remoteStream = event.streams[0];\n        this.attachRemoteStream();\n      };\n      this.peerConnection.onconnectionstatechange = () => {\n        const state = this.peerConnection?.connectionState;\n        this.logger.debug('CallService', 'Connection state changed:', state);\n        if (state === 'connected') {\n          this.logger.info('CallService', '✅ WebRTC connection established');\n          this.setCallState('connected');\n        } else if (state === 'failed') {\n          this.logger.error('CallService', '❌ WebRTC connection failed');\n          this.handleCallEnded();\n        }\n      };\n    } catch (error) {\n      this.logger.error('CallService', 'Error creating PeerConnection:', error);\n    }\n  }\n  startOutgoingCallMedia(callType) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.logger.info('CallService', '🎥 Starting outgoing call media');\n        const stream = yield _this.getUserMedia(callType);\n        _this.addLocalStreamToPeerConnection(stream);\n        _this.attachLocalStream();\n      } catch (error) {\n        _this.logger.error('CallService', 'Error starting outgoing call media:', error);\n      }\n    })();\n  }\n  prepareForIncomingCall(call) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this2.logger.debug('CallService', 'Preparing WebRTC for incoming call');\n        if (!_this2.peerConnection) {\n          _this2.createPeerConnection();\n        }\n        const stream = yield _this2.getUserMedia(call.type);\n        _this2.addLocalStreamToPeerConnection(stream);\n      } catch (error) {\n        _this2.logger.error('CallService', 'Error preparing for incoming call:', error);\n      }\n    })();\n  }\n  getUserMedia(callType) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const constraints = {\n        audio: true,\n        video: callType === CallType.VIDEO\n      };\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia(constraints);\n        _this3.localStream = stream;\n        return stream;\n      } catch (error) {\n        _this3.logger.error('CallService', 'Error getting user media:', error);\n        throw error;\n      }\n    })();\n  }\n  addLocalStreamToPeerConnection(stream) {\n    if (!this.peerConnection) return;\n    stream.getTracks().forEach(track => {\n      this.peerConnection.addTrack(track, stream);\n    });\n  }\n  attachLocalStream() {\n    if (this.localVideoElement && this.localStream) {\n      this.localVideoElement.srcObject = this.localStream;\n    }\n  }\n  attachRemoteStream() {\n    if (this.remoteVideoElement && this.remoteStream) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n    }\n  }\n  sendSignal(signalType, signalData) {\n    if (!this.currentCallId) return;\n    this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId: this.currentCallId,\n        signalType,\n        signalData\n      }\n    }).subscribe({\n      next: () => this.logger.debug('CallService', 'Signal sent:', signalType),\n      error: error => this.logger.error('CallService', 'Error sending signal:', error)\n    });\n  }\n  // ===== NETTOYAGE =====\n  cleanupWebRTC() {\n    this.logger.debug('CallService', 'Cleaning up WebRTC resources');\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n    }\n    if (this.remoteStream) {\n      this.remoteStream = null;\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n    // Recréer une nouvelle PeerConnection pour les futurs appels\n    this.createPeerConnection();\n  }\n  cleanup() {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n    this.activeCall.complete();\n    this.incomingCall.complete();\n    this.callSignals.complete();\n  }\n  // ===== MÉTHODES PUBLIQUES UTILITAIRES =====\n  /**\n   * Attache les éléments vidéo pour l'affichage\n   */\n  attachVideoElements(localVideo, remoteVideo) {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n    if (this.localStream) {\n      this.attachLocalStream();\n    }\n    if (this.remoteStream) {\n      this.attachRemoteStream();\n    }\n  }\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    if (this.localStream) {\n      this.localStream.getAudioTracks().forEach(track => {\n        track.enabled = this.isAudioEnabled;\n      });\n    }\n    return this.isAudioEnabled;\n  }\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    if (this.localStream) {\n      this.localStream.getVideoTracks().forEach(track => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n    return this.isVideoEnabled;\n  }\n  /**\n   * Méthode de compatibilité pour setVideoElements\n   */\n  setVideoElements(localVideo, remoteVideo) {\n    this.attachVideoElements(localVideo, remoteVideo);\n  }\n  /**\n   * Obtient l'état audio actuel\n   */\n  get audioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état vidéo actuel\n   */\n  get videoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient le stream local\n   */\n  get localMediaStream() {\n    return this.localStream;\n  }\n  /**\n   * Obtient le stream distant\n   */\n  get remoteMediaStream() {\n    return this.remoteStream;\n  }\n  /**\n   * Active les sons (méthode de compatibilité)\n   */\n  enableSounds() {\n    this.logger.debug('CallService', 'Sounds are always enabled in unified service');\n  }\n  /**\n   * Désactive les sons (méthode de compatibilité)\n   */\n  disableSounds() {\n    this.logger.debug('CallService', 'Disabling sounds');\n    this.stopAllSounds();\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "map", "catchError", "CallType", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CALL_SIGNAL_SUBSCRIPTION", "SEND_CALL_SIGNAL_MUTATION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "callSignals", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "currentCallId", "callState", "sounds", "isPlaying", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "isAudioEnabled", "isVideoEnabled", "rtcConfig", "iceServers", "urls", "info", "initializeSounds", "initializeSubscriptions", "initializeWebRTC", "ngOnDestroy", "cleanup", "initiateCall", "recipientId", "callType", "conversationId", "Error", "setCallState", "callId", "generateCallId", "mutate", "mutation", "variables", "pipe", "result", "call", "data", "handleCallInitiated", "error", "acceptCall", "id", "acceptedCall", "handleCallAccepted", "rejectCall", "reason", "success", "handleCallEnded", "endCall", "currentCall", "value", "currentIncomingCall", "isCallActive", "isCallInProgress", "Date", "now", "Math", "random", "toString", "substr", "state", "debug", "next", "play", "startOutgoingCallMedia", "type", "stop", "stopAllSounds", "cleanupWebRTC", "handleIncomingCall", "prepareForIncomingCall", "handleCallStatusChange", "status", "CONNECTED", "ENDED", "REJECTED", "handleCallSignal", "signal", "createSyntheticSounds", "createSyntheticSound", "name", "frequencies", "duration", "loop", "audioContext", "window", "AudioContext", "webkitAudioContext", "sampleRate", "frameCount", "buffer", "createBuffer", "channelData", "getChannelData", "i", "sample", "for<PERSON>ach", "freq", "amplitude", "length", "phase", "PI", "sin", "envelope", "audio", "Audio", "customPlay", "source", "createBufferSource", "connect", "destination", "start", "setTimeout", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "subscribeToCallSignals", "subscribe", "query", "errorPolicy", "errors", "callStatusChanged", "callSignal", "createPeerConnection", "sound", "currentSource", "Object", "keys", "RTCPeerConnection", "onicecandidate", "event", "candidate", "sendSignal", "JSON", "stringify", "ontrack", "track", "kind", "streams", "attachRemoteStream", "onconnectionstatechange", "connectionState", "_this", "_asyncToGenerator", "stream", "getUserMedia", "addLocalStreamToPeerConnection", "attachLocalStream", "_this2", "_this3", "constraints", "video", "VIDEO", "navigator", "mediaDevices", "getTracks", "addTrack", "srcObject", "signalType", "signalData", "close", "complete", "attachVideoElements", "localVideo", "remoteVideo", "toggleAudio", "getAudioTracks", "enabled", "toggleVideo", "getVideoTracks", "setVideoElements", "audioEnabled", "videoEnabled", "localMediaStream", "remoteMediaStream", "enableSounds", "disableSounds", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n  CallSignal,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  SEND_CALL_SIGNAL_MUTATION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n/**\n * Service unifié pour la gestion des appels vidéo/audio\n * Gère l'état des appels, WebRTC, et la synchronisation\n */\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnD<PERSON>roy {\n  // ===== ÉTAT PRINCIPAL =====\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n\n  // ===== ÉTAT DES APPELS =====\n  private currentCallId: string | null = null;\n  private callState:\n    | 'idle'\n    | 'initiating'\n    | 'ringing'\n    | 'connecting'\n    | 'connected'\n    | 'ending' = 'idle';\n\n  // ===== GESTION AUDIO =====\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n\n  // ===== WEBRTC =====\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n  private isAudioEnabled = true;\n  private isVideoEnabled = true;\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.logger.info('CallService', '🚀 Initializing unified CallService...');\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n    this.logger.info('CallService', '✅ CallService initialized successfully');\n  }\n\n  ngOnDestroy(): void {\n    this.logger.info('CallService', '🔄 Destroying CallService...');\n    this.cleanup();\n  }\n\n  // ===== MÉTHODES PUBLIQUES PRINCIPALES =====\n\n  /**\n   * Initie un appel\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    this.logger.info('CallService', '📞 Initiating call:', {\n      recipientId,\n      callType,\n    });\n\n    if (this.callState !== 'idle') {\n      return throwError(() => new Error('Another call is already in progress'));\n    }\n\n    this.setCallState('initiating');\n    const callId = this.generateCallId();\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: { recipientId, callType, callId, conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.initiateCall;\n          if (!call) throw new Error('Failed to initiate call');\n\n          this.handleCallInitiated(call);\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error initiating call:', error);\n          this.setCallState('idle');\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(call: IncomingCall): Observable<Call> {\n    this.logger.info('CallService', '✅ Accepting call:', call.id);\n\n    if (!call) {\n      return throwError(() => new Error('No call to accept'));\n    }\n\n    this.setCallState('connecting');\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: { callId: call.id },\n      })\n      .pipe(\n        map((result) => {\n          const acceptedCall = result.data?.acceptCall;\n          if (!acceptedCall) throw new Error('Failed to accept call');\n\n          this.handleCallAccepted(acceptedCall);\n          return acceptedCall;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error accepting call:', error);\n          this.setCallState('idle');\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    this.logger.info('CallService', '❌ Rejecting call:', callId);\n\n    this.setCallState('ending');\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: { callId, reason: reason || 'User rejected' },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.rejectCall;\n          if (!success) throw new Error('Failed to reject call');\n\n          this.handleCallEnded();\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error rejecting call:', error);\n          this.handleCallEnded(); // Nettoyer même en cas d'erreur\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Termine un appel\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    this.logger.info('CallService', '🔚 Ending call:', callId);\n\n    this.setCallState('ending');\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: { callId },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.endCall;\n          if (!success) throw new Error('Failed to end call');\n\n          this.handleCallEnded();\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error ending call:', error);\n          this.handleCallEnded(); // Nettoyer même en cas d'erreur\n          return throwError(() => error);\n        })\n      );\n  }\n\n  // ===== GETTERS PUBLICS =====\n\n  get currentCall(): Call | null {\n    return this.activeCall.value;\n  }\n\n  get currentIncomingCall(): IncomingCall | null {\n    return this.incomingCall.value;\n  }\n\n  get isCallActive(): boolean {\n    return this.callState === 'connected';\n  }\n\n  get isCallInProgress(): boolean {\n    return this.callState !== 'idle';\n  }\n\n  // ===== MÉTHODES PRIVÉES =====\n\n  private generateCallId(): string {\n    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private setCallState(state: typeof this.callState): void {\n    this.logger.debug(\n      'CallService',\n      `Call state: ${this.callState} → ${state}`\n    );\n    this.callState = state;\n  }\n\n  private handleCallInitiated(call: Call): void {\n    this.logger.info('CallService', 'Call initiated successfully:', call.id);\n    this.currentCallId = call.id;\n    this.activeCall.next(call);\n    this.setCallState('ringing');\n    this.play('ringtone', true);\n    this.startOutgoingCallMedia(call.type);\n  }\n\n  private handleCallAccepted(call: Call): void {\n    this.logger.info('CallService', 'Call accepted successfully:', call.id);\n    this.activeCall.next(call);\n    this.incomingCall.next(null);\n    this.setCallState('connected');\n    this.stop('ringtone');\n    this.play('call-connected');\n  }\n\n  private handleCallEnded(): void {\n    this.logger.info('CallService', 'Call ended, cleaning up');\n    this.setCallState('idle');\n    this.currentCallId = null;\n    this.activeCall.next(null);\n    this.incomingCall.next(null);\n    this.stopAllSounds();\n    this.play('call-end');\n    this.cleanupWebRTC();\n  }\n\n  private handleIncomingCall(call: IncomingCall): void {\n    this.logger.info('CallService', 'Incoming call received:', call.id);\n    this.currentCallId = call.id;\n    this.incomingCall.next(call);\n    this.setCallState('ringing');\n    this.play('ringtone', true);\n    this.prepareForIncomingCall(call);\n  }\n\n  private handleCallStatusChange(call: Call): void {\n    this.logger.info('CallService', 'Call status changed:', call.status);\n\n    if (call.id === this.currentCallId) {\n      this.activeCall.next(call);\n\n      switch (call.status) {\n        case CallStatus.CONNECTED:\n          this.setCallState('connected');\n          this.stop('ringtone');\n          this.play('call-connected');\n          break;\n        case CallStatus.ENDED:\n        case CallStatus.REJECTED:\n          this.handleCallEnded();\n          break;\n      }\n    }\n  }\n\n  private handleCallSignal(signal: CallSignal): void {\n    this.logger.debug('CallService', 'Call signal received:', signal.type);\n    this.callSignals.next(signal);\n    // Traitement WebRTC des signaux sera ajouté ici\n  }\n\n  // ===== INITIALISATION =====\n\n  private initializeSounds(): void {\n    this.logger.debug('CallService', 'Initializing sounds...');\n    this.createSyntheticSounds();\n  }\n\n  private createSyntheticSounds(): void {\n    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);\n    this.createSyntheticSound(\n      'call-connected',\n      [523.25, 659.25, 783.99],\n      0.8,\n      false\n    );\n    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);\n  }\n\n  private createSyntheticSound(\n    name: string,\n    frequencies: number[],\n    duration: number,\n    loop: boolean\n  ): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const sampleRate = audioContext.sampleRate;\n      const frameCount = sampleRate * duration;\n      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);\n      const channelData = buffer.getChannelData(0);\n\n      for (let i = 0; i < frameCount; i++) {\n        let sample = 0;\n        frequencies.forEach((freq) => {\n          const amplitude = 0.3 / frequencies.length;\n          const phase = (i / sampleRate) * freq * 2 * Math.PI;\n          sample += Math.sin(phase) * amplitude;\n        });\n        const envelope = Math.sin((i / frameCount) * Math.PI);\n        channelData[i] = sample * envelope;\n      }\n\n      const audio = new Audio();\n      audio.loop = loop;\n      (audio as any).customPlay = () => {\n        const source = audioContext.createBufferSource();\n        source.buffer = buffer;\n        source.loop = loop;\n        source.connect(audioContext.destination);\n        source.start();\n        if (!loop) {\n          setTimeout(() => {\n            this.isPlaying[name] = false;\n          }, duration * 1000);\n        }\n        return source;\n      };\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error creating sound '${name}':`,\n        error\n      );\n    }\n  }\n\n  private initializeSubscriptions(): void {\n    this.logger.debug('CallService', 'Initializing subscriptions...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n\n  private subscribeToIncomingCalls(): void {\n    this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.incomingCall) {\n            this.handleIncomingCall(data.incomingCall);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Incoming call subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in incoming call subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n        },\n      });\n  }\n\n  private subscribeToCallStatusChanges(): void {\n    this.apollo\n      .subscribe<{ callStatusChanged: Call }>({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callStatusChanged) {\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Call status subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in call status subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n        },\n      });\n  }\n\n  private subscribeToCallSignals(): void {\n    this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callSignal) {\n            this.handleCallSignal(data.callSignal);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Call signal subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in call signal subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToCallSignals(), 5000);\n        },\n      });\n  }\n\n  private initializeWebRTC(): void {\n    this.logger.debug('CallService', 'Initializing WebRTC...');\n    this.createPeerConnection();\n  }\n\n  // ===== GESTION AUDIO =====\n\n  private play(name: string, loop: boolean = false): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound || this.isPlaying[name]) return;\n\n      if ((sound as any).customPlay) {\n        (sound as any).currentSource = (sound as any).customPlay();\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      this.logger.error('CallService', `Error playing sound '${name}':`, error);\n    }\n  }\n\n  private stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound || !this.isPlaying[name]) return;\n\n      if ((sound as any).currentSource) {\n        (sound as any).currentSource.stop();\n        (sound as any).currentSource = null;\n      }\n      this.isPlaying[name] = false;\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error stopping sound '${name}':`,\n        error\n      );\n    }\n  }\n\n  private stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => this.stop(name));\n  }\n\n  // ===== WEBRTC =====\n\n  private createPeerConnection(): void {\n    try {\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      this.logger.debug('CallService', 'PeerConnection created successfully');\n\n      this.peerConnection.onicecandidate = (event) => {\n        if (event.candidate && this.currentCallId) {\n          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n\n      this.peerConnection.ontrack = (event) => {\n        this.logger.info(\n          'CallService',\n          'Remote track received:',\n          event.track.kind\n        );\n        this.remoteStream = event.streams[0];\n        this.attachRemoteStream();\n      };\n\n      this.peerConnection.onconnectionstatechange = () => {\n        const state = this.peerConnection?.connectionState;\n        this.logger.debug('CallService', 'Connection state changed:', state);\n\n        if (state === 'connected') {\n          this.logger.info('CallService', '✅ WebRTC connection established');\n          this.setCallState('connected');\n        } else if (state === 'failed') {\n          this.logger.error('CallService', '❌ WebRTC connection failed');\n          this.handleCallEnded();\n        }\n      };\n    } catch (error) {\n      this.logger.error('CallService', 'Error creating PeerConnection:', error);\n    }\n  }\n\n  private async startOutgoingCallMedia(callType: CallType): Promise<void> {\n    try {\n      this.logger.info('CallService', '🎥 Starting outgoing call media');\n      const stream = await this.getUserMedia(callType);\n      this.addLocalStreamToPeerConnection(stream);\n      this.attachLocalStream();\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        'Error starting outgoing call media:',\n        error\n      );\n    }\n  }\n\n  private async prepareForIncomingCall(call: IncomingCall): Promise<void> {\n    try {\n      this.logger.debug('CallService', 'Preparing WebRTC for incoming call');\n      if (!this.peerConnection) {\n        this.createPeerConnection();\n      }\n      const stream = await this.getUserMedia(call.type);\n      this.addLocalStreamToPeerConnection(stream);\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        'Error preparing for incoming call:',\n        error\n      );\n    }\n  }\n\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      this.localStream = stream;\n      return stream;\n    } catch (error) {\n      this.logger.error('CallService', 'Error getting user media:', error);\n      throw error;\n    }\n  }\n\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) return;\n\n    stream.getTracks().forEach((track) => {\n      this.peerConnection!.addTrack(track, stream);\n    });\n  }\n\n  private attachLocalStream(): void {\n    if (this.localVideoElement && this.localStream) {\n      this.localVideoElement.srcObject = this.localStream;\n    }\n  }\n\n  private attachRemoteStream(): void {\n    if (this.remoteVideoElement && this.remoteStream) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n    }\n  }\n\n  private sendSignal(signalType: string, signalData: string): void {\n    if (!this.currentCallId) return;\n\n    this.apollo\n      .mutate({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId: this.currentCallId,\n          signalType,\n          signalData,\n        },\n      })\n      .subscribe({\n        next: () =>\n          this.logger.debug('CallService', 'Signal sent:', signalType),\n        error: (error) =>\n          this.logger.error('CallService', 'Error sending signal:', error),\n      });\n  }\n\n  // ===== NETTOYAGE =====\n\n  private cleanupWebRTC(): void {\n    this.logger.debug('CallService', 'Cleaning up WebRTC resources');\n\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n    }\n\n    if (this.remoteStream) {\n      this.remoteStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n\n    // Recréer une nouvelle PeerConnection pour les futurs appels\n    this.createPeerConnection();\n  }\n\n  private cleanup(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n    this.activeCall.complete();\n    this.incomingCall.complete();\n    this.callSignals.complete();\n  }\n\n  // ===== MÉTHODES PUBLIQUES UTILITAIRES =====\n\n  /**\n   * Attache les éléments vidéo pour l'affichage\n   */\n  attachVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n\n    if (this.localStream) {\n      this.attachLocalStream();\n    }\n    if (this.remoteStream) {\n      this.attachRemoteStream();\n    }\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    if (this.localStream) {\n      this.localStream.getAudioTracks().forEach((track) => {\n        track.enabled = this.isAudioEnabled;\n      });\n    }\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    if (this.localStream) {\n      this.localStream.getVideoTracks().forEach((track) => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Méthode de compatibilité pour setVideoElements\n   */\n  setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.attachVideoElements(localVideo, remoteVideo);\n  }\n\n  /**\n   * Obtient l'état audio actuel\n   */\n  get audioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état vidéo actuel\n   */\n  get videoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient le stream local\n   */\n  get localMediaStream(): MediaStream | null {\n    return this.localStream;\n  }\n\n  /**\n   * Obtient le stream distant\n   */\n  get remoteMediaStream(): MediaStream | null {\n    return this.remoteStream;\n  }\n\n  /**\n   * Active les sons (méthode de compatibilité)\n   */\n  enableSounds(): void {\n    this.logger.debug(\n      'CallService',\n      'Sounds are always enabled in unified service'\n    );\n  }\n\n  /**\n   * Désactive les sons (méthode de compatibilité)\n   */\n  disableSounds(): void {\n    this.logger.debug('CallService', 'Disabling sounds');\n    this.stopAllSounds();\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAcC,UAAU,QAAY,MAAM;AAClE,SAASC,GAAG,EAAEC,UAAU,QAAa,gBAAgB;AACrD,SAEEC,QAAQ,EACRC,UAAU,QAIL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,gCAAgC,EAChCC,wBAAwB,EACxBC,yBAAyB,QACpB,4BAA4B;;;;AAGnC;;;;AAOA,OAAM,MAAOC,WAAW;EA0CtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAzClD;IACQ,KAAAC,UAAU,GAAG,IAAIlB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAmB,YAAY,GAAG,IAAInB,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAAoB,WAAW,GAAG,IAAIpB,eAAe,CAAoB,IAAI,CAAC;IAElE;IACO,KAAAqB,WAAW,GAAG,IAAI,CAACH,UAAU,CAACI,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACJ,YAAY,CAACG,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACJ,WAAW,CAACE,YAAY,EAAE;IAErD;IACQ,KAAAG,aAAa,GAAkB,IAAI;IACnC,KAAAC,SAAS,GAMF,MAAM;IAErB;IACQ,KAAAC,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAElD;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACiB,KAAAC,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAGC,IAAI,CAACrB,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,wCAAwC,CAAC;IACzE,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACzB,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,wCAAwC,CAAC;EAC3E;EAEAI,WAAWA,CAAA;IACT,IAAI,CAAC1B,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B,CAAC;IAC/D,IAAI,CAACK,OAAO,EAAE;EAChB;EAEA;EAEA;;;EAGAC,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClBC,cAAuB;IAEvB,IAAI,CAAC/B,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,qBAAqB,EAAE;MACrDO,WAAW;MACXC;KACD,CAAC;IAEF,IAAI,IAAI,CAACrB,SAAS,KAAK,MAAM,EAAE;MAC7B,OAAOzB,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACC,YAAY,CAAC,YAAY,CAAC;IAC/B,MAAMC,MAAM,GAAG,IAAI,CAACC,cAAc,EAAE;IAEpC,OAAO,IAAI,CAACpC,MAAM,CACfqC,MAAM,CAAyB;MAC9BC,QAAQ,EAAEhD,sBAAsB;MAChCiD,SAAS,EAAE;QAAET,WAAW;QAAEC,QAAQ;QAAEI,MAAM;QAAEH;MAAc;KAC3D,CAAC,CACDQ,IAAI,CACHtD,GAAG,CAAEuD,MAAM,IAAI;MACb,MAAMC,IAAI,GAAGD,MAAM,CAACE,IAAI,EAAEd,YAAY;MACtC,IAAI,CAACa,IAAI,EAAE,MAAM,IAAIT,KAAK,CAAC,yBAAyB,CAAC;MAErD,IAAI,CAACW,mBAAmB,CAACF,IAAI,CAAC;MAC9B,OAAOA,IAAI;IACb,CAAC,CAAC,EACFvD,UAAU,CAAE0D,KAAK,IAAI;MACnB,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,wBAAwB,EAAEA,KAAK,CAAC;MACjE,IAAI,CAACX,YAAY,CAAC,MAAM,CAAC;MACzB,OAAOjD,UAAU,CAAC,MAAM4D,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAC,UAAUA,CAACJ,IAAkB;IAC3B,IAAI,CAACzC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,mBAAmB,EAAEmB,IAAI,CAACK,EAAE,CAAC;IAE7D,IAAI,CAACL,IAAI,EAAE;MACT,OAAOzD,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzD,IAAI,CAACC,YAAY,CAAC,YAAY,CAAC;IAE/B,OAAO,IAAI,CAAClC,MAAM,CACfqC,MAAM,CAAuB;MAC5BC,QAAQ,EAAE/C,oBAAoB;MAC9BgD,SAAS,EAAE;QAAEJ,MAAM,EAAEO,IAAI,CAACK;MAAE;KAC7B,CAAC,CACDP,IAAI,CACHtD,GAAG,CAAEuD,MAAM,IAAI;MACb,MAAMO,YAAY,GAAGP,MAAM,CAACE,IAAI,EAAEG,UAAU;MAC5C,IAAI,CAACE,YAAY,EAAE,MAAM,IAAIf,KAAK,CAAC,uBAAuB,CAAC;MAE3D,IAAI,CAACgB,kBAAkB,CAACD,YAAY,CAAC;MACrC,OAAOA,YAAY;IACrB,CAAC,CAAC,EACF7D,UAAU,CAAE0D,KAAK,IAAI;MACnB,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAChE,IAAI,CAACX,YAAY,CAAC,MAAM,CAAC;MACzB,OAAOjD,UAAU,CAAC,MAAM4D,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAK,UAAUA,CAACf,MAAc,EAAEgB,MAAe;IACxC,IAAI,CAAClD,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,mBAAmB,EAAEY,MAAM,CAAC;IAE5D,IAAI,CAACD,YAAY,CAAC,QAAQ,CAAC;IAE3B,OAAO,IAAI,CAAClC,MAAM,CACfqC,MAAM,CAA8B;MACnCC,QAAQ,EAAE9C,oBAAoB;MAC9B+C,SAAS,EAAE;QAAEJ,MAAM;QAAEgB,MAAM,EAAEA,MAAM,IAAI;MAAe;KACvD,CAAC,CACDX,IAAI,CACHtD,GAAG,CAAEuD,MAAM,IAAI;MACb,MAAMW,OAAO,GAAGX,MAAM,CAACE,IAAI,EAAEO,UAAU;MACvC,IAAI,CAACE,OAAO,EAAE,MAAM,IAAInB,KAAK,CAAC,uBAAuB,CAAC;MAEtD,IAAI,CAACoB,eAAe,EAAE;MACtB,OAAOD,OAAO;IAChB,CAAC,CAAC,EACFjE,UAAU,CAAE0D,KAAK,IAAI;MACnB,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAChE,IAAI,CAACQ,eAAe,EAAE,CAAC,CAAC;MACxB,OAAOpE,UAAU,CAAC,MAAM4D,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAS,OAAOA,CAACnB,MAAc;IACpB,IAAI,CAAClC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iBAAiB,EAAEY,MAAM,CAAC;IAE1D,IAAI,CAACD,YAAY,CAAC,QAAQ,CAAC;IAE3B,OAAO,IAAI,CAAClC,MAAM,CACfqC,MAAM,CAA2B;MAChCC,QAAQ,EAAE7C,iBAAiB;MAC3B8C,SAAS,EAAE;QAAEJ;MAAM;KACpB,CAAC,CACDK,IAAI,CACHtD,GAAG,CAAEuD,MAAM,IAAI;MACb,MAAMW,OAAO,GAAGX,MAAM,CAACE,IAAI,EAAEW,OAAO;MACpC,IAAI,CAACF,OAAO,EAAE,MAAM,IAAInB,KAAK,CAAC,oBAAoB,CAAC;MAEnD,IAAI,CAACoB,eAAe,EAAE;MACtB,OAAOD,OAAO;IAChB,CAAC,CAAC,EACFjE,UAAU,CAAE0D,KAAK,IAAI;MACnB,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,oBAAoB,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACQ,eAAe,EAAE,CAAC,CAAC;MACxB,OAAOpE,UAAU,CAAC,MAAM4D,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;EAEA,IAAIU,WAAWA,CAAA;IACb,OAAO,IAAI,CAACrD,UAAU,CAACsD,KAAK;EAC9B;EAEA,IAAIC,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACtD,YAAY,CAACqD,KAAK;EAChC;EAEA,IAAIE,YAAYA,CAAA;IACd,OAAO,IAAI,CAAChD,SAAS,KAAK,WAAW;EACvC;EAEA,IAAIiD,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACjD,SAAS,KAAK,MAAM;EAClC;EAEA;EAEQ0B,cAAcA,CAAA;IACpB,OAAO,QAAQwB,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACxE;EAEQ/B,YAAYA,CAACgC,KAA4B;IAC/C,IAAI,CAACjE,MAAM,CAACkE,KAAK,CACf,aAAa,EACb,eAAe,IAAI,CAACzD,SAAS,MAAMwD,KAAK,EAAE,CAC3C;IACD,IAAI,CAACxD,SAAS,GAAGwD,KAAK;EACxB;EAEQtB,mBAAmBA,CAACF,IAAU;IACpC,IAAI,CAACzC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B,EAAEmB,IAAI,CAACK,EAAE,CAAC;IACxE,IAAI,CAACtC,aAAa,GAAGiC,IAAI,CAACK,EAAE;IAC5B,IAAI,CAAC7C,UAAU,CAACkE,IAAI,CAAC1B,IAAI,CAAC;IAC1B,IAAI,CAACR,YAAY,CAAC,SAAS,CAAC;IAC5B,IAAI,CAACmC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAC3B,IAAI,CAACC,sBAAsB,CAAC5B,IAAI,CAAC6B,IAAI,CAAC;EACxC;EAEQtB,kBAAkBA,CAACP,IAAU;IACnC,IAAI,CAACzC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,6BAA6B,EAAEmB,IAAI,CAACK,EAAE,CAAC;IACvE,IAAI,CAAC7C,UAAU,CAACkE,IAAI,CAAC1B,IAAI,CAAC;IAC1B,IAAI,CAACvC,YAAY,CAACiE,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAClC,YAAY,CAAC,WAAW,CAAC;IAC9B,IAAI,CAACsC,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACH,IAAI,CAAC,gBAAgB,CAAC;EAC7B;EAEQhB,eAAeA,CAAA;IACrB,IAAI,CAACpD,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,yBAAyB,CAAC;IAC1D,IAAI,CAACW,YAAY,CAAC,MAAM,CAAC;IACzB,IAAI,CAACzB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACP,UAAU,CAACkE,IAAI,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACjE,YAAY,CAACiE,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACK,aAAa,EAAE;IACpB,IAAI,CAACJ,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACK,aAAa,EAAE;EACtB;EAEQC,kBAAkBA,CAACjC,IAAkB;IAC3C,IAAI,CAACzC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,yBAAyB,EAAEmB,IAAI,CAACK,EAAE,CAAC;IACnE,IAAI,CAACtC,aAAa,GAAGiC,IAAI,CAACK,EAAE;IAC5B,IAAI,CAAC5C,YAAY,CAACiE,IAAI,CAAC1B,IAAI,CAAC;IAC5B,IAAI,CAACR,YAAY,CAAC,SAAS,CAAC;IAC5B,IAAI,CAACmC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAC3B,IAAI,CAACO,sBAAsB,CAAClC,IAAI,CAAC;EACnC;EAEQmC,sBAAsBA,CAACnC,IAAU;IACvC,IAAI,CAACzC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,sBAAsB,EAAEmB,IAAI,CAACoC,MAAM,CAAC;IAEpE,IAAIpC,IAAI,CAACK,EAAE,KAAK,IAAI,CAACtC,aAAa,EAAE;MAClC,IAAI,CAACP,UAAU,CAACkE,IAAI,CAAC1B,IAAI,CAAC;MAE1B,QAAQA,IAAI,CAACoC,MAAM;QACjB,KAAKzF,UAAU,CAAC0F,SAAS;UACvB,IAAI,CAAC7C,YAAY,CAAC,WAAW,CAAC;UAC9B,IAAI,CAACsC,IAAI,CAAC,UAAU,CAAC;UACrB,IAAI,CAACH,IAAI,CAAC,gBAAgB,CAAC;UAC3B;QACF,KAAKhF,UAAU,CAAC2F,KAAK;QACrB,KAAK3F,UAAU,CAAC4F,QAAQ;UACtB,IAAI,CAAC5B,eAAe,EAAE;UACtB;;;EAGR;EAEQ6B,gBAAgBA,CAACC,MAAkB;IACzC,IAAI,CAAClF,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEgB,MAAM,CAACZ,IAAI,CAAC;IACtE,IAAI,CAACnE,WAAW,CAACgE,IAAI,CAACe,MAAM,CAAC;IAC7B;EACF;EAEA;EAEQ3D,gBAAgBA,CAAA;IACtB,IAAI,CAACvB,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,wBAAwB,CAAC;IAC1D,IAAI,CAACiB,qBAAqB,EAAE;EAC9B;EAEQA,qBAAqBA,CAAA;IAC3B,IAAI,CAACC,oBAAoB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;IAC/D,IAAI,CAACA,oBAAoB,CACvB,gBAAgB,EAChB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EACxB,GAAG,EACH,KAAK,CACN;IACD,IAAI,CAACA,oBAAoB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EAC1E;EAEQA,oBAAoBA,CAC1BC,IAAY,EACZC,WAAqB,EACrBC,QAAgB,EAChBC,IAAa;IAEb,IAAI;MACF,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAMC,UAAU,GAAGJ,YAAY,CAACI,UAAU;MAC1C,MAAMC,UAAU,GAAGD,UAAU,GAAGN,QAAQ;MACxC,MAAMQ,MAAM,GAAGN,YAAY,CAACO,YAAY,CAAC,CAAC,EAAEF,UAAU,EAAED,UAAU,CAAC;MACnE,MAAMI,WAAW,GAAGF,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC;MAE5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;QACnC,IAAIC,MAAM,GAAG,CAAC;QACdd,WAAW,CAACe,OAAO,CAAEC,IAAI,IAAI;UAC3B,MAAMC,SAAS,GAAG,GAAG,GAAGjB,WAAW,CAACkB,MAAM;UAC1C,MAAMC,KAAK,GAAIN,CAAC,GAAGN,UAAU,GAAIS,IAAI,GAAG,CAAC,GAAGzC,IAAI,CAAC6C,EAAE;UACnDN,MAAM,IAAIvC,IAAI,CAAC8C,GAAG,CAACF,KAAK,CAAC,GAAGF,SAAS;QACvC,CAAC,CAAC;QACF,MAAMK,QAAQ,GAAG/C,IAAI,CAAC8C,GAAG,CAAER,CAAC,GAAGL,UAAU,GAAIjC,IAAI,CAAC6C,EAAE,CAAC;QACrDT,WAAW,CAACE,CAAC,CAAC,GAAGC,MAAM,GAAGQ,QAAQ;;MAGpC,MAAMC,KAAK,GAAG,IAAIC,KAAK,EAAE;MACzBD,KAAK,CAACrB,IAAI,GAAGA,IAAI;MAChBqB,KAAa,CAACE,UAAU,GAAG,MAAK;QAC/B,MAAMC,MAAM,GAAGvB,YAAY,CAACwB,kBAAkB,EAAE;QAChDD,MAAM,CAACjB,MAAM,GAAGA,MAAM;QACtBiB,MAAM,CAACxB,IAAI,GAAGA,IAAI;QAClBwB,MAAM,CAACE,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;QACxCH,MAAM,CAACI,KAAK,EAAE;QACd,IAAI,CAAC5B,IAAI,EAAE;UACT6B,UAAU,CAAC,MAAK;YACd,IAAI,CAAC1G,SAAS,CAAC0E,IAAI,CAAC,GAAG,KAAK;UAC9B,CAAC,EAAEE,QAAQ,GAAG,IAAI,CAAC;;QAErB,OAAOyB,MAAM;MACf,CAAC;MAED,IAAI,CAACtG,MAAM,CAAC2E,IAAI,CAAC,GAAGwB,KAAK;MACzB,IAAI,CAAClG,SAAS,CAAC0E,IAAI,CAAC,GAAG,KAAK;KAC7B,CAAC,OAAOzC,KAAK,EAAE;MACd,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,yBAAyByC,IAAI,IAAI,EACjCzC,KAAK,CACN;;EAEL;EAEQpB,uBAAuBA,CAAA;IAC7B,IAAI,CAACxB,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,+BAA+B,CAAC;IACjE,IAAI,CAACoD,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;IACnC,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQF,wBAAwBA,CAAA;IAC9B,IAAI,CAACvH,MAAM,CACR0H,SAAS,CAAiC;MACzCC,KAAK,EAAEjI,0BAA0B;MACjCkI,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTtD,IAAI,EAAEA,CAAC;QAAEzB,IAAI;QAAEkF;MAAM,CAAE,KAAI;QACzB,IAAIlF,IAAI,EAAExC,YAAY,EAAE;UACtB,IAAI,CAACwE,kBAAkB,CAAChC,IAAI,CAACxC,YAAY,CAAC;;QAE5C,IAAI0H,MAAM,EAAE;UACV,IAAI,CAAC5H,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCgF,MAAM,CACP;;MAEL,CAAC;MACDhF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,sCAAsC,EACtCA,KAAK,CACN;QACDyE,UAAU,CAAC,MAAM,IAAI,CAACC,wBAAwB,EAAE,EAAE,IAAI,CAAC;MACzD;KACD,CAAC;EACN;EAEQC,4BAA4BA,CAAA;IAClC,IAAI,CAACxH,MAAM,CACR0H,SAAS,CAA8B;MACtCC,KAAK,EAAEhI,gCAAgC;MACvCiI,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTtD,IAAI,EAAEA,CAAC;QAAEzB,IAAI;QAAEkF;MAAM,CAAE,KAAI;QACzB,IAAIlF,IAAI,EAAEmF,iBAAiB,EAAE;UAC3B,IAAI,CAACjD,sBAAsB,CAAClC,IAAI,CAACmF,iBAAiB,CAAC;;QAErD,IAAID,MAAM,EAAE;UACV,IAAI,CAAC5H,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCgF,MAAM,CACP;;MAEL,CAAC;MACDhF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;QACDyE,UAAU,CAAC,MAAM,IAAI,CAACE,4BAA4B,EAAE,EAAE,IAAI,CAAC;MAC7D;KACD,CAAC;EACN;EAEQC,sBAAsBA,CAAA;IAC5B,IAAI,CAACzH,MAAM,CACR0H,SAAS,CAA6B;MACrCC,KAAK,EAAE/H,wBAAwB;MAC/BgI,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTtD,IAAI,EAAEA,CAAC;QAAEzB,IAAI;QAAEkF;MAAM,CAAE,KAAI;QACzB,IAAIlF,IAAI,EAAEoF,UAAU,EAAE;UACpB,IAAI,CAAC7C,gBAAgB,CAACvC,IAAI,CAACoF,UAAU,CAAC;;QAExC,IAAIF,MAAM,EAAE;UACV,IAAI,CAAC5H,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCgF,MAAM,CACP;;MAEL,CAAC;MACDhF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;QACDyE,UAAU,CAAC,MAAM,IAAI,CAACG,sBAAsB,EAAE,EAAE,IAAI,CAAC;MACvD;KACD,CAAC;EACN;EAEQ/F,gBAAgBA,CAAA;IACtB,IAAI,CAACzB,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,wBAAwB,CAAC;IAC1D,IAAI,CAAC6D,oBAAoB,EAAE;EAC7B;EAEA;EAEQ3D,IAAIA,CAACiB,IAAY,EAAEG,IAAA,GAAgB,KAAK;IAC9C,IAAI;MACF,MAAMwC,KAAK,GAAG,IAAI,CAACtH,MAAM,CAAC2E,IAAI,CAAC;MAC/B,IAAI,CAAC2C,KAAK,IAAI,IAAI,CAACrH,SAAS,CAAC0E,IAAI,CAAC,EAAE;MAEpC,IAAK2C,KAAa,CAACjB,UAAU,EAAE;QAC5BiB,KAAa,CAACC,aAAa,GAAID,KAAa,CAACjB,UAAU,EAAE;QAC1D,IAAI,CAACpG,SAAS,CAAC0E,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAOzC,KAAK,EAAE;MACd,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,wBAAwByC,IAAI,IAAI,EAAEzC,KAAK,CAAC;;EAE7E;EAEQ2B,IAAIA,CAACc,IAAY;IACvB,IAAI;MACF,MAAM2C,KAAK,GAAG,IAAI,CAACtH,MAAM,CAAC2E,IAAI,CAAC;MAC/B,IAAI,CAAC2C,KAAK,IAAI,CAAC,IAAI,CAACrH,SAAS,CAAC0E,IAAI,CAAC,EAAE;MAErC,IAAK2C,KAAa,CAACC,aAAa,EAAE;QAC/BD,KAAa,CAACC,aAAa,CAAC1D,IAAI,EAAE;QAClCyD,KAAa,CAACC,aAAa,GAAG,IAAI;;MAErC,IAAI,CAACtH,SAAS,CAAC0E,IAAI,CAAC,GAAG,KAAK;KAC7B,CAAC,OAAOzC,KAAK,EAAE;MACd,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,yBAAyByC,IAAI,IAAI,EACjCzC,KAAK,CACN;;EAEL;EAEQ4B,aAAaA,CAAA;IACnB0D,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzH,MAAM,CAAC,CAAC2F,OAAO,CAAEhB,IAAI,IAAK,IAAI,CAACd,IAAI,CAACc,IAAI,CAAC,CAAC;EAC7D;EAEA;EAEQ0C,oBAAoBA,CAAA;IAC1B,IAAI;MACF,IAAI,CAACnH,cAAc,GAAG,IAAIwH,iBAAiB,CAAC,IAAI,CAACjH,SAAS,CAAC;MAC3D,IAAI,CAACnB,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,qCAAqC,CAAC;MAEvE,IAAI,CAACtD,cAAc,CAACyH,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,IAAI,IAAI,CAAC/H,aAAa,EAAE;UACzC,IAAI,CAACgI,UAAU,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACC,SAAS,CAAC,CAAC;;MAErE,CAAC;MAED,IAAI,CAAC3H,cAAc,CAAC+H,OAAO,GAAIL,KAAK,IAAI;QACtC,IAAI,CAACtI,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,wBAAwB,EACxBgH,KAAK,CAACM,KAAK,CAACC,IAAI,CACjB;QACD,IAAI,CAAC/H,YAAY,GAAGwH,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;QACpC,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MAED,IAAI,CAACnI,cAAc,CAACoI,uBAAuB,GAAG,MAAK;QACjD,MAAM/E,KAAK,GAAG,IAAI,CAACrD,cAAc,EAAEqI,eAAe;QAClD,IAAI,CAACjJ,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,2BAA2B,EAAED,KAAK,CAAC;QAEpE,IAAIA,KAAK,KAAK,WAAW,EAAE;UACzB,IAAI,CAACjE,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iCAAiC,CAAC;UAClE,IAAI,CAACW,YAAY,CAAC,WAAW,CAAC;SAC/B,MAAM,IAAIgC,KAAK,KAAK,QAAQ,EAAE;UAC7B,IAAI,CAACjE,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,4BAA4B,CAAC;UAC9D,IAAI,CAACQ,eAAe,EAAE;;MAE1B,CAAC;KACF,CAAC,OAAOR,KAAK,EAAE;MACd,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,gCAAgC,EAAEA,KAAK,CAAC;;EAE7E;EAEcyB,sBAAsBA,CAACvC,QAAkB;IAAA,IAAAoH,KAAA;IAAA,OAAAC,iBAAA;MACrD,IAAI;QACFD,KAAI,CAAClJ,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iCAAiC,CAAC;QAClE,MAAM8H,MAAM,SAASF,KAAI,CAACG,YAAY,CAACvH,QAAQ,CAAC;QAChDoH,KAAI,CAACI,8BAA8B,CAACF,MAAM,CAAC;QAC3CF,KAAI,CAACK,iBAAiB,EAAE;OACzB,CAAC,OAAO3G,KAAK,EAAE;QACdsG,KAAI,CAAClJ,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,qCAAqC,EACrCA,KAAK,CACN;;IACF;EACH;EAEc+B,sBAAsBA,CAAClC,IAAkB;IAAA,IAAA+G,MAAA;IAAA,OAAAL,iBAAA;MACrD,IAAI;QACFK,MAAI,CAACxJ,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,oCAAoC,CAAC;QACtE,IAAI,CAACsF,MAAI,CAAC5I,cAAc,EAAE;UACxB4I,MAAI,CAACzB,oBAAoB,EAAE;;QAE7B,MAAMqB,MAAM,SAASI,MAAI,CAACH,YAAY,CAAC5G,IAAI,CAAC6B,IAAI,CAAC;QACjDkF,MAAI,CAACF,8BAA8B,CAACF,MAAM,CAAC;OAC5C,CAAC,OAAOxG,KAAK,EAAE;QACd4G,MAAI,CAACxJ,MAAM,CAAC4C,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;;IACF;EACH;EAEcyG,YAAYA,CAACvH,QAAkB;IAAA,IAAA2H,MAAA;IAAA,OAAAN,iBAAA;MAC3C,MAAMO,WAAW,GAA2B;QAC1C7C,KAAK,EAAE,IAAI;QACX8C,KAAK,EAAE7H,QAAQ,KAAK3C,QAAQ,CAACyK;OAC9B;MAED,IAAI;QACF,MAAMR,MAAM,SAASS,SAAS,CAACC,YAAY,CAACT,YAAY,CAACK,WAAW,CAAC;QACrED,MAAI,CAAC5I,WAAW,GAAGuI,MAAM;QACzB,OAAOA,MAAM;OACd,CAAC,OAAOxG,KAAK,EAAE;QACd6G,MAAI,CAACzJ,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,2BAA2B,EAAEA,KAAK,CAAC;QACpE,MAAMA,KAAK;;IACZ;EACH;EAEQ0G,8BAA8BA,CAACF,MAAmB;IACxD,IAAI,CAAC,IAAI,CAACxI,cAAc,EAAE;IAE1BwI,MAAM,CAACW,SAAS,EAAE,CAAC1D,OAAO,CAAEuC,KAAK,IAAI;MACnC,IAAI,CAAChI,cAAe,CAACoJ,QAAQ,CAACpB,KAAK,EAAEQ,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEQG,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACxI,iBAAiB,IAAI,IAAI,CAACF,WAAW,EAAE;MAC9C,IAAI,CAACE,iBAAiB,CAACkJ,SAAS,GAAG,IAAI,CAACpJ,WAAW;;EAEvD;EAEQkI,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAAC/H,kBAAkB,IAAI,IAAI,CAACF,YAAY,EAAE;MAChD,IAAI,CAACE,kBAAkB,CAACiJ,SAAS,GAAG,IAAI,CAACnJ,YAAY;;EAEzD;EAEQ0H,UAAUA,CAAC0B,UAAkB,EAAEC,UAAkB;IACvD,IAAI,CAAC,IAAI,CAAC3J,aAAa,EAAE;IAEzB,IAAI,CAACT,MAAM,CACRqC,MAAM,CAAC;MACNC,QAAQ,EAAEzC,yBAAyB;MACnC0C,SAAS,EAAE;QACTJ,MAAM,EAAE,IAAI,CAAC1B,aAAa;QAC1B0J,UAAU;QACVC;;KAEH,CAAC,CACD1C,SAAS,CAAC;MACTtD,IAAI,EAAEA,CAAA,KACJ,IAAI,CAACnE,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,cAAc,EAAEgG,UAAU,CAAC;MAC9DtH,KAAK,EAAGA,KAAK,IACX,IAAI,CAAC5C,MAAM,CAAC4C,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK;KAClE,CAAC;EACN;EAEA;EAEQ6B,aAAaA,CAAA;IACnB,IAAI,CAACzE,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,8BAA8B,CAAC;IAEhE,IAAI,IAAI,CAACrD,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACkJ,SAAS,EAAE,CAAC1D,OAAO,CAAEuC,KAAK,IAAKA,KAAK,CAACrE,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC1D,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,GAAG,IAAI;;IAG1B,IAAI,IAAI,CAACF,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACwJ,KAAK,EAAE;MAC3B,IAAI,CAACxJ,cAAc,GAAG,IAAI;;IAG5B,IAAI,IAAI,CAACG,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACkJ,SAAS,GAAG,IAAI;;IAGzC,IAAI,IAAI,CAACjJ,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACiJ,SAAS,GAAG,IAAI;;IAG1C;IACA,IAAI,CAAClC,oBAAoB,EAAE;EAC7B;EAEQpG,OAAOA,CAAA;IACb,IAAI,CAAC6C,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACxE,UAAU,CAACoK,QAAQ,EAAE;IAC1B,IAAI,CAACnK,YAAY,CAACmK,QAAQ,EAAE;IAC5B,IAAI,CAAClK,WAAW,CAACkK,QAAQ,EAAE;EAC7B;EAEA;EAEA;;;EAGAC,mBAAmBA,CACjBC,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAACzJ,iBAAiB,GAAGwJ,UAAU;IACnC,IAAI,CAACvJ,kBAAkB,GAAGwJ,WAAW;IAErC,IAAI,IAAI,CAAC3J,WAAW,EAAE;MACpB,IAAI,CAAC0I,iBAAiB,EAAE;;IAE1B,IAAI,IAAI,CAACzI,YAAY,EAAE;MACrB,IAAI,CAACiI,kBAAkB,EAAE;;EAE7B;EAEA;;;EAGA0B,WAAWA,CAAA;IACT,IAAI,CAACxJ,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACJ,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC6J,cAAc,EAAE,CAACrE,OAAO,CAAEuC,KAAK,IAAI;QAClDA,KAAK,CAAC+B,OAAO,GAAG,IAAI,CAAC1J,cAAc;MACrC,CAAC,CAAC;;IAEJ,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA2J,WAAWA,CAAA;IACT,IAAI,CAAC1J,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACL,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACgK,cAAc,EAAE,CAACxE,OAAO,CAAEuC,KAAK,IAAI;QAClDA,KAAK,CAAC+B,OAAO,GAAG,IAAI,CAACzJ,cAAc;MACrC,CAAC,CAAC;;IAEJ,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGA4J,gBAAgBA,CACdP,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAACF,mBAAmB,CAACC,UAAU,EAAEC,WAAW,CAAC;EACnD;EAEA;;;EAGA,IAAIO,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC9J,cAAc;EAC5B;EAEA;;;EAGA,IAAI+J,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC9J,cAAc;EAC5B;EAEA;;;EAGA,IAAI+J,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACpK,WAAW;EACzB;EAEA;;;EAGA,IAAIqK,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACpK,YAAY;EAC1B;EAEA;;;EAGAqK,YAAYA,CAAA;IACV,IAAI,CAACnL,MAAM,CAACkE,KAAK,CACf,aAAa,EACb,8CAA8C,CAC/C;EACH;EAEA;;;EAGAkH,aAAaA,CAAA;IACX,IAAI,CAACpL,MAAM,CAACkE,KAAK,CAAC,aAAa,EAAE,kBAAkB,CAAC;IACpD,IAAI,CAACM,aAAa,EAAE;EACtB;;;uBA3vBW3E,WAAW,EAAAwL,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAX7L,WAAW;MAAA8L,OAAA,EAAX9L,WAAW,CAAA+L,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}