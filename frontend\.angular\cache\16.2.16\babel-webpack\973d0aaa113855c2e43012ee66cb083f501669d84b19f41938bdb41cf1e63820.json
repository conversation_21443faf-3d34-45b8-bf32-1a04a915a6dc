{"ast": null, "code": "import { gql } from 'apollo-angular';\n// Définir les types GraphQL\nexport const typeDefs = gql`\n  enum MessageType {\n    TEXT\n    IMAGE\n    FILE\n    AUDIO\n    VIDEO\n    SYSTEM\n    VOICE_MESSAGE\n  }\n`;\n// Message Mutations\nexport const SEND_MESSAGE_MUTATION = gql`\n  mutation SendMessage(\n    $receiverId: ID!\n    $content: String\n    $file: Upload\n    $type: MessageType\n    $metadata: JSON\n  ) {\n    sendMessage(\n      receiverId: $receiverId\n      content: $content\n      file: $file\n      type: $type\n      metadata: $metadata\n    ) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      conversation {\n        id\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n    }\n  }\n`;\nexport const MARK_AS_READ_MUTATION = gql`\n  mutation MarkMessageAsRead($messageId: ID!) {\n    markMessageAsRead(messageId: $messageId) {\n      id\n      isRead\n      readAt\n    }\n  }\n`;\nexport const EDIT_MESSAGE_MUTATION = gql`\n  mutation EditMessage($messageId: ID!, $newContent: String!) {\n    editMessage(messageId: $messageId, newContent: $newContent) {\n      id\n      content\n      isEdited\n      updatedAt\n    }\n  }\n`;\nexport const DELETE_MESSAGE_MUTATION = gql`\n  mutation DeleteMessage($messageId: ID!) {\n    deleteMessage(messageId: $messageId) {\n      id\n      isDeleted\n      deletedAt\n    }\n  }\n`;\nexport const GET_MESSAGES_QUERY = gql`\n  query GetMessages(\n    $senderId: ID!\n    $receiverId: ID!\n    $conversationId: ID!\n    $page: Int\n    $limit: Int = 25\n  ) {\n    getMessages(\n      senderId: $senderId\n      receiverId: $receiverId\n      conversationId: $conversationId\n      page: $page\n      limit: $limit\n    ) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      status\n      sender {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        name\n        size\n      }\n    }\n  }\n`;\n// Conversation Queries\nexport const GET_CONVERSATIONS_QUERY = gql`\n  query GetConversations {\n    getConversations {\n      id\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      lastMessage {\n        id\n        content\n        timestamp\n        isRead\n        sender {\n          id\n          username\n        }\n      }\n      unreadCount\n      updatedAt\n    }\n  }\n`;\nexport const GET_CONVERSATION_QUERY = gql`\n  query GetConversation(\n    $conversationId: ID!\n    $limit: Int = 10\n    $offset: Int = 0\n  ) {\n    getConversation(conversationId: $conversationId) {\n      id\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      messages(limit: $limit, offset: $offset) {\n        id\n        content\n        type\n        timestamp\n        isRead\n        sender {\n          id\n          username\n          image\n        }\n        receiver {\n          id\n          username\n          image\n        }\n        attachments {\n          url\n          type\n          duration\n        }\n        metadata\n        conversationId\n      }\n    }\n  }\n`;\n// User Queries\nexport const GET_USER_QUERY = gql`\n  query GetOneUser($id: ID!) {\n    getOneUser(id: $id) {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n  }\n`;\nexport const GET_ALL_USER_QUERY = gql`\n  query GetAllUsers(\n    $search: String\n    $page: Int\n    $limit: Int\n    $sortBy: String\n    $sortOrder: String\n    $isOnline: Boolean\n  ) {\n    getAllUsers(\n      search: $search\n      page: $page\n      limit: $limit\n      sortBy: $sortBy\n      sortOrder: $sortOrder\n      isOnline: $isOnline\n    ) {\n      users {\n        id\n        username\n        email\n        image\n        isOnline\n        lastActive\n      }\n      totalCount\n      totalPages\n      currentPage\n      hasNextPage\n      hasPreviousPage\n    }\n  }\n`;\n// Status Mutations\nexport const SET_USER_ONLINE_MUTATION = gql`\n  mutation SetUserOnline($userId: ID!) {\n    setUserOnline(userId: $userId) {\n      id\n      isOnline\n      lastActive\n    }\n  }\n`;\nexport const SET_USER_OFFLINE_MUTATION = gql`\n  mutation SetUserOffline($userId: ID!) {\n    setUserOffline(userId: $userId) {\n      id\n      isOnline\n      lastActive\n    }\n  }\n`;\n// Search Query\nexport const SEARCH_MESSAGES_QUERY = gql`\n  query SearchMessages($query: String!, $conversationId: ID) {\n    searchMessages(query: $query, conversationId: $conversationId) {\n      id\n      content\n      timestamp\n      sender {\n        id\n        username\n      }\n    }\n  }\n`;\n// Unread Messages Query\nexport const GET_UNREAD_MESSAGES_QUERY = gql`\n  query GetUnreadMessages($userId: ID!) {\n    getUnreadMessages(userId: $userId) {\n      id\n      content\n      timestamp\n      sender {\n        id\n        username\n        image\n      }\n      conversation {\n        id\n      }\n    }\n  }\n`;\n// Subscriptions\nexport const MESSAGE_SENT_SUBSCRIPTION = gql`\n  subscription MessageSent($conversationId: ID!) {\n    messageSent(conversationId: $conversationId) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      conversation {\n        id\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n    }\n  }\n`;\nexport const USER_STATUS_SUBSCRIPTION = gql`\n  subscription UserStatusChanged {\n    userStatusChanged {\n      id\n      username\n      isOnline\n      lastActive\n    }\n  }\n`;\nexport const CONVERSATION_UPDATED_SUBSCRIPTION = gql`\n  subscription ConversationUpdated($conversationId: ID!) {\n    conversationUpdated(conversationId: $conversationId) {\n      id\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      lastMessage {\n        id\n        content\n        timestamp\n        isRead\n        sender {\n          id\n          username\n        }\n      }\n      unreadCount\n      updatedAt\n    }\n  }\n`;\n// Notification Queries\nexport const GET_NOTIFICATIONS_QUERY = gql`\n  query GetUserNotifications {\n    getUserNotifications {\n      id\n      type\n      content\n      timestamp\n      isRead\n      senderId {\n        id\n        username\n        image\n      }\n      message {\n        id\n        content\n      }\n      readAt\n      relatedEntity\n      metadata\n    }\n  }\n`;\nexport const GET_NOTIFICATIONS_ATTACHAMENTS = gql`\n  query GetNotificationAttachments($id: ID!) {\n    getNotificationAttachments(notificationId: $id) {\n      url\n      type\n      name\n      size\n    }\n  }\n`;\nexport const MARK_NOTIFICATION_READ_MUTATION = gql`\n  mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n    markNotificationsAsRead(notificationIds: $notificationIds) {\n      success\n      readCount\n      remainingCount\n    }\n  }\n`;\nexport const DELETE_NOTIFICATION_MUTATION = gql`\n  mutation DeleteNotification($notificationId: ID!) {\n    deleteNotification(notificationId: $notificationId) {\n      success\n      message\n    }\n  }\n`;\nexport const DELETE_MULTIPLE_NOTIFICATIONS_MUTATION = gql`\n  mutation DeleteMultipleNotifications($notificationIds: [ID!]!) {\n    deleteMultipleNotifications(notificationIds: $notificationIds) {\n      success\n      count\n      message\n    }\n  }\n`;\nexport const DELETE_ALL_NOTIFICATIONS_MUTATION = gql`\n  mutation DeleteAllNotifications {\n    deleteAllNotifications {\n      success\n      count\n      message\n    }\n  }\n`;\nexport const NOTIFICATION_SUBSCRIPTION = gql`\n  subscription NotificationReceived {\n    notificationReceived {\n      id\n      type\n      content\n      timestamp\n      isRead\n      senderId {\n        id\n        username\n        image\n      }\n      message {\n        id\n        content\n      }\n      readAt\n      relatedEntity\n      metadata\n    }\n  }\n`;\nexport const NOTIFICATIONS_READ_SUBSCRIPTION = gql`\n  subscription NotificationsRead {\n    notificationsRead\n  }\n`;\n// group Queries\nexport const GET_GROUP_QUERY = gql`\n  query GetGroup($id: ID!) {\n    getGroup(id: $id) {\n      id\n      name\n      photo\n      description\n      participants {\n        id\n        username\n        email\n        image\n        isOnline\n      }\n      admins {\n        id\n        username\n        email\n        image\n        isOnline\n      }\n      messageCount\n      createdAt\n      updatedAt\n    }\n  }\n`;\nexport const GET_USER_GROUPS_QUERY = gql`\n  query GetUserGroups($userId: ID!) {\n    getUserGroups(userId: $userId) {\n      id\n      name\n      photo\n      description\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      admins {\n        id\n        username\n        image\n        isOnline\n      }\n      messageCount\n      createdAt\n      updatedAt\n    }\n  }\n`;\nexport const CREATE_GROUP_MUTATION = gql`\n  mutation CreateGroup(\n    $name: String!\n    $participantIds: [ID!]!\n    $photo: Upload\n    $description: String\n  ) {\n    createGroup(\n      name: $name\n      participantIds: $participantIds\n      photo: $photo\n      description: $description\n    ) {\n      id\n      name\n      photo\n      description\n      participants {\n        id\n        username\n        image\n      }\n      admins {\n        id\n        username\n        image\n      }\n    }\n  }\n`;\nexport const UPDATE_GROUP_MUTATION = gql`\n  mutation UpdateGroup($id: ID!, $input: UpdateGroupInput!) {\n    updateGroup(id: $id, input: $input) {\n      id\n      name\n      photo\n      description\n      participants {\n        id\n        username\n        image\n      }\n      admins {\n        id\n        username\n        image\n      }\n    }\n  }\n`;\nexport const DELETE_GROUP_MUTATION = gql`\n  mutation DeleteGroup($id: ID!) {\n    deleteGroup(id: $id) {\n      success\n      message\n    }\n  }\n`;\nexport const ADD_GROUP_PARTICIPANTS_MUTATION = gql`\n  mutation AddGroupParticipants($groupId: ID!, $participantIds: [ID!]!) {\n    addGroupParticipants(groupId: $groupId, participantIds: $participantIds) {\n      id\n      participants {\n        id\n        username\n        image\n      }\n    }\n  }\n`;\nexport const REMOVE_GROUP_PARTICIPANTS_MUTATION = gql`\n  mutation RemoveGroupParticipants($groupId: ID!, $participantIds: [ID!]!) {\n    removeGroupParticipants(\n      groupId: $groupId\n      participantIds: $participantIds\n    ) {\n      id\n      participants {\n        id\n        username\n        image\n      }\n    }\n  }\n`;\nexport const LEAVE_GROUP_MUTATION = gql`\n  mutation LeaveGroup($groupId: ID!) {\n    leaveGroup(groupId: $groupId) {\n      success\n      message\n    }\n  }\n`;\n// Add to exports\nexport const TYPING_INDICATOR_SUBSCRIPTION = gql`\n  subscription TypingIndicator($conversationId: ID!) {\n    typingIndicator(conversationId: $conversationId) {\n      conversationId\n      userId\n      isTyping\n    }\n  }\n`;\nexport const START_TYPING_MUTATION = gql`\n  mutation StartTyping($input: TypingInput!) {\n    startTyping(input: $input)\n  }\n`;\nexport const STOP_TYPING_MUTATION = gql`\n  mutation StopTyping($input: TypingInput!) {\n    stopTyping(input: $input)\n  }\n`;\nexport const GET_CURRENT_USER_QUERY = gql`\n  query GetCurrentUser {\n    getCurrentUser {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n      createdAt\n      updatedAt\n    }\n  }\n`;\nexport const REACT_TO_MESSAGE_MUTATION = gql`\n  mutation ReactToMessage($messageId: ID!, $emoji: String!) {\n    reactToMessage(messageId: $messageId, emoji: $emoji) {\n      id\n      reactions {\n        userId\n        emoji\n        createdAt\n      }\n    }\n  }\n`;\nexport const FORWARD_MESSAGE_MUTATION = gql`\n  mutation ForwardMessage($messageId: ID!, $conversationIds: [ID!]!) {\n    forwardMessage(messageId: $messageId, conversationIds: $conversationIds) {\n      id\n      content\n      timestamp\n      sender {\n        id\n        username\n      }\n      conversation {\n        id\n      }\n    }\n  }\n`;\nexport const PIN_MESSAGE_MUTATION = gql`\n  mutation PinMessage($messageId: ID!, $conversationId: ID!) {\n    pinMessage(messageId: $messageId, conversationId: $conversationId) {\n      id\n      pinned\n      pinnedAt\n      pinnedBy {\n        id\n        username\n      }\n    }\n  }\n`;\nexport const CREATE_CONVERSATION_MUTATION = gql`\n  mutation CreateConversation($userId: ID!) {\n    createConversation(userId: $userId) {\n      id\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      lastMessage {\n        id\n        content\n        timestamp\n      }\n      unreadCount\n      updatedAt\n    }\n  }\n`;\n// Call Queries\nexport const CALL_HISTORY_QUERY = gql`\n  query CallHistory(\n    $limit: Int\n    $offset: Int\n    $status: [CallStatus]\n    $type: [CallType]\n    $startDate: String\n    $endDate: String\n  ) {\n    callHistory(\n      limit: $limit\n      offset: $offset\n      status: $status\n      type: $type\n      startDate: $startDate\n      endDate: $endDate\n    ) {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      status\n      startTime\n      endTime\n      duration\n      conversationId\n    }\n  }\n`;\nexport const CALL_DETAILS_QUERY = gql`\n  query CallDetails($callId: ID!) {\n    callDetails(callId: $callId) {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      status\n      startTime\n      endTime\n      duration\n      conversationId\n      metadata\n    }\n  }\n`;\nexport const CALL_STATS_QUERY = gql`\n  query CallStats {\n    callStats {\n      totalCalls\n      totalDuration\n      missedCalls\n      callsByType {\n        type\n        count\n      }\n      averageCallDuration\n      mostCalledUser {\n        id\n        username\n        image\n      }\n    }\n  }\n`;\n// Call Mutations\nexport const INITIATE_CALL_MUTATION = gql`\n  mutation InitiateCall(\n    $recipientId: ID!\n    $callType: CallType!\n    $callId: String!\n    $offer: String!\n    $conversationId: ID\n    $options: CallOptions\n  ) {\n    initiateCall(\n      recipientId: $recipientId\n      callType: $callType\n      callId: $callId\n      offer: $offer\n      conversationId: $conversationId\n      options: $options\n    ) {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      status\n      startTime\n      conversationId\n    }\n  }\n`;\nexport const SEND_CALL_SIGNAL_MUTATION = gql`\n  mutation SendCallSignal(\n    $callId: ID!\n    $signalType: String!\n    $signalData: String!\n  ) {\n    sendCallSignal(\n      callId: $callId\n      signalType: $signalType\n      signalData: $signalData\n    ) {\n      success\n      message\n    }\n  }\n`;\nexport const ACCEPT_CALL_MUTATION = gql`\n  mutation AcceptCall($callId: ID!, $answer: String!) {\n    acceptCall(callId: $callId, answer: $answer) {\n      id\n      status\n    }\n  }\n`;\nexport const REJECT_CALL_MUTATION = gql`\n  mutation RejectCall($callId: ID!, $reason: String) {\n    rejectCall(callId: $callId, reason: $reason) {\n      id\n      status\n    }\n  }\n`;\nexport const END_CALL_MUTATION = gql`\n  mutation EndCall($callId: ID!, $feedback: CallFeedbackInput) {\n    endCall(callId: $callId, feedback: $feedback) {\n      id\n      status\n      endTime\n      duration\n    }\n  }\n`;\nexport const TOGGLE_CALL_MEDIA_MUTATION = gql`\n  mutation ToggleCallMedia($callId: ID!, $video: Boolean, $audio: Boolean) {\n    toggleCallMedia(callId: $callId, video: $video, audio: $audio) {\n      success\n      message\n    }\n  }\n`;\n// Call Subscriptions\nexport const CALL_SIGNAL_SUBSCRIPTION = gql`\n  subscription CallSignal($callId: ID) {\n    callSignal(callId: $callId) {\n      callId\n      senderId\n      type\n      data\n      timestamp\n    }\n  }\n`;\nexport const INCOMING_CALL_SUBSCRIPTION = gql`\n  subscription IncomingCall {\n    incomingCall {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      conversationId\n      offer\n      timestamp\n    }\n  }\n`;\nexport const CALL_STATUS_CHANGED_SUBSCRIPTION = gql`\n  subscription CallStatusChanged($callId: ID) {\n    callStatusChanged(callId: $callId) {\n      id\n      status\n      endTime\n      duration\n    }\n  }\n`;\n// Requête pour récupérer les messages vocaux\nexport const GET_VOICE_MESSAGES_QUERY = gql`\n  query GetVoiceMessages {\n    getVoiceMessages {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      status\n      startTime\n      endTime\n      duration\n      conversationId\n      metadata\n    }\n  }\n`;", "map": {"version": 3, "names": ["gql", "typeDefs", "SEND_MESSAGE_MUTATION", "MARK_AS_READ_MUTATION", "EDIT_MESSAGE_MUTATION", "DELETE_MESSAGE_MUTATION", "GET_MESSAGES_QUERY", "GET_CONVERSATIONS_QUERY", "GET_CONVERSATION_QUERY", "GET_USER_QUERY", "GET_ALL_USER_QUERY", "SET_USER_ONLINE_MUTATION", "SET_USER_OFFLINE_MUTATION", "SEARCH_MESSAGES_QUERY", "GET_UNREAD_MESSAGES_QUERY", "MESSAGE_SENT_SUBSCRIPTION", "USER_STATUS_SUBSCRIPTION", "CONVERSATION_UPDATED_SUBSCRIPTION", "GET_NOTIFICATIONS_QUERY", "GET_NOTIFICATIONS_ATTACHAMENTS", "MARK_NOTIFICATION_READ_MUTATION", "DELETE_NOTIFICATION_MUTATION", "DELETE_MULTIPLE_NOTIFICATIONS_MUTATION", "DELETE_ALL_NOTIFICATIONS_MUTATION", "NOTIFICATION_SUBSCRIPTION", "NOTIFICATIONS_READ_SUBSCRIPTION", "GET_GROUP_QUERY", "GET_USER_GROUPS_QUERY", "CREATE_GROUP_MUTATION", "UPDATE_GROUP_MUTATION", "DELETE_GROUP_MUTATION", "ADD_GROUP_PARTICIPANTS_MUTATION", "REMOVE_GROUP_PARTICIPANTS_MUTATION", "LEAVE_GROUP_MUTATION", "TYPING_INDICATOR_SUBSCRIPTION", "START_TYPING_MUTATION", "STOP_TYPING_MUTATION", "GET_CURRENT_USER_QUERY", "REACT_TO_MESSAGE_MUTATION", "FORWARD_MESSAGE_MUTATION", "PIN_MESSAGE_MUTATION", "CREATE_CONVERSATION_MUTATION", "CALL_HISTORY_QUERY", "CALL_DETAILS_QUERY", "CALL_STATS_QUERY", "INITIATE_CALL_MUTATION", "SEND_CALL_SIGNAL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "CALL_SIGNAL_SUBSCRIPTION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "GET_VOICE_MESSAGES_QUERY"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\graphql\\message.graphql.ts"], "sourcesContent": ["import { gql } from 'apollo-angular';\r\n\r\n// Définir les types GraphQL\r\nexport const typeDefs = gql`\r\n  enum MessageType {\r\n    TEXT\r\n    IMAGE\r\n    FILE\r\n    AUDIO\r\n    VIDEO\r\n    SYSTEM\r\n    VOICE_MESSAGE\r\n  }\r\n`;\r\n\r\n// Message Mutations\r\nexport const SEND_MESSAGE_MUTATION = gql`\r\n  mutation SendMessage(\r\n    $receiverId: ID!\r\n    $content: String\r\n    $file: Upload\r\n    $type: MessageType\r\n    $metadata: JSON\r\n  ) {\r\n    sendMessage(\r\n      receiverId: $receiverId\r\n      content: $content\r\n      file: $file\r\n      type: $type\r\n      metadata: $metadata\r\n    ) {\r\n      id\r\n      content\r\n      type\r\n      timestamp\r\n      isRead\r\n      sender {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      conversation {\r\n        id\r\n      }\r\n      attachments {\r\n        url\r\n        type\r\n        duration\r\n      }\r\n      metadata\r\n    }\r\n  }\r\n`;\r\nexport const MARK_AS_READ_MUTATION = gql`\r\n  mutation MarkMessageAsRead($messageId: ID!) {\r\n    markMessageAsRead(messageId: $messageId) {\r\n      id\r\n      isRead\r\n      readAt\r\n    }\r\n  }\r\n`;\r\nexport const EDIT_MESSAGE_MUTATION = gql`\r\n  mutation EditMessage($messageId: ID!, $newContent: String!) {\r\n    editMessage(messageId: $messageId, newContent: $newContent) {\r\n      id\r\n      content\r\n      isEdited\r\n      updatedAt\r\n    }\r\n  }\r\n`;\r\nexport const DELETE_MESSAGE_MUTATION = gql`\r\n  mutation DeleteMessage($messageId: ID!) {\r\n    deleteMessage(messageId: $messageId) {\r\n      id\r\n      isDeleted\r\n      deletedAt\r\n    }\r\n  }\r\n`;\r\nexport const GET_MESSAGES_QUERY = gql`\r\n  query GetMessages(\r\n    $senderId: ID!\r\n    $receiverId: ID!\r\n    $conversationId: ID!\r\n    $page: Int\r\n    $limit: Int = 25\r\n  ) {\r\n    getMessages(\r\n      senderId: $senderId\r\n      receiverId: $receiverId\r\n      conversationId: $conversationId\r\n      page: $page\r\n      limit: $limit\r\n    ) {\r\n      id\r\n      content\r\n      type\r\n      timestamp\r\n      isRead\r\n      status\r\n      sender {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      attachments {\r\n        url\r\n        type\r\n        name\r\n        size\r\n      }\r\n    }\r\n  }\r\n`;\r\n// Conversation Queries\r\nexport const GET_CONVERSATIONS_QUERY = gql`\r\n  query GetConversations {\r\n    getConversations {\r\n      id\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n        isOnline\r\n      }\r\n      lastMessage {\r\n        id\r\n        content\r\n        timestamp\r\n        isRead\r\n        sender {\r\n          id\r\n          username\r\n        }\r\n      }\r\n      unreadCount\r\n      updatedAt\r\n    }\r\n  }\r\n`;\r\nexport const GET_CONVERSATION_QUERY = gql`\r\n  query GetConversation(\r\n    $conversationId: ID!\r\n    $limit: Int = 10\r\n    $offset: Int = 0\r\n  ) {\r\n    getConversation(conversationId: $conversationId) {\r\n      id\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n        isOnline\r\n      }\r\n      messages(limit: $limit, offset: $offset) {\r\n        id\r\n        content\r\n        type\r\n        timestamp\r\n        isRead\r\n        sender {\r\n          id\r\n          username\r\n          image\r\n        }\r\n        receiver {\r\n          id\r\n          username\r\n          image\r\n        }\r\n        attachments {\r\n          url\r\n          type\r\n          duration\r\n        }\r\n        metadata\r\n        conversationId\r\n      }\r\n    }\r\n  }\r\n`;\r\n// User Queries\r\nexport const GET_USER_QUERY = gql`\r\n  query GetOneUser($id: ID!) {\r\n    getOneUser(id: $id) {\r\n      id\r\n      username\r\n      email\r\n      image\r\n      isOnline\r\n      lastActive\r\n    }\r\n  }\r\n`;\r\nexport const GET_ALL_USER_QUERY = gql`\r\n  query GetAllUsers(\r\n    $search: String\r\n    $page: Int\r\n    $limit: Int\r\n    $sortBy: String\r\n    $sortOrder: String\r\n    $isOnline: Boolean\r\n  ) {\r\n    getAllUsers(\r\n      search: $search\r\n      page: $page\r\n      limit: $limit\r\n      sortBy: $sortBy\r\n      sortOrder: $sortOrder\r\n      isOnline: $isOnline\r\n    ) {\r\n      users {\r\n        id\r\n        username\r\n        email\r\n        image\r\n        isOnline\r\n        lastActive\r\n      }\r\n      totalCount\r\n      totalPages\r\n      currentPage\r\n      hasNextPage\r\n      hasPreviousPage\r\n    }\r\n  }\r\n`;\r\n// Status Mutations\r\nexport const SET_USER_ONLINE_MUTATION = gql`\r\n  mutation SetUserOnline($userId: ID!) {\r\n    setUserOnline(userId: $userId) {\r\n      id\r\n      isOnline\r\n      lastActive\r\n    }\r\n  }\r\n`;\r\nexport const SET_USER_OFFLINE_MUTATION = gql`\r\n  mutation SetUserOffline($userId: ID!) {\r\n    setUserOffline(userId: $userId) {\r\n      id\r\n      isOnline\r\n      lastActive\r\n    }\r\n  }\r\n`;\r\n// Search Query\r\nexport const SEARCH_MESSAGES_QUERY = gql`\r\n  query SearchMessages($query: String!, $conversationId: ID) {\r\n    searchMessages(query: $query, conversationId: $conversationId) {\r\n      id\r\n      content\r\n      timestamp\r\n      sender {\r\n        id\r\n        username\r\n      }\r\n    }\r\n  }\r\n`;\r\n// Unread Messages Query\r\nexport const GET_UNREAD_MESSAGES_QUERY = gql`\r\n  query GetUnreadMessages($userId: ID!) {\r\n    getUnreadMessages(userId: $userId) {\r\n      id\r\n      content\r\n      timestamp\r\n      sender {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      conversation {\r\n        id\r\n      }\r\n    }\r\n  }\r\n`;\r\n// Subscriptions\r\nexport const MESSAGE_SENT_SUBSCRIPTION = gql`\r\n  subscription MessageSent($conversationId: ID!) {\r\n    messageSent(conversationId: $conversationId) {\r\n      id\r\n      content\r\n      type\r\n      timestamp\r\n      isRead\r\n      sender {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      conversation {\r\n        id\r\n      }\r\n      attachments {\r\n        url\r\n        type\r\n        duration\r\n      }\r\n      metadata\r\n    }\r\n  }\r\n`;\r\nexport const USER_STATUS_SUBSCRIPTION = gql`\r\n  subscription UserStatusChanged {\r\n    userStatusChanged {\r\n      id\r\n      username\r\n      isOnline\r\n      lastActive\r\n    }\r\n  }\r\n`;\r\nexport const CONVERSATION_UPDATED_SUBSCRIPTION = gql`\r\n  subscription ConversationUpdated($conversationId: ID!) {\r\n    conversationUpdated(conversationId: $conversationId) {\r\n      id\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n        isOnline\r\n      }\r\n      lastMessage {\r\n        id\r\n        content\r\n        timestamp\r\n        isRead\r\n        sender {\r\n          id\r\n          username\r\n        }\r\n      }\r\n      unreadCount\r\n      updatedAt\r\n    }\r\n  }\r\n`;\r\n// Notification Queries\r\nexport const GET_NOTIFICATIONS_QUERY = gql`\r\n  query GetUserNotifications {\r\n    getUserNotifications {\r\n      id\r\n      type\r\n      content\r\n      timestamp\r\n      isRead\r\n      senderId {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      message {\r\n        id\r\n        content\r\n      }\r\n      readAt\r\n      relatedEntity\r\n      metadata\r\n    }\r\n  }\r\n`;\r\nexport const GET_NOTIFICATIONS_ATTACHAMENTS = gql`\r\n  query GetNotificationAttachments($id: ID!) {\r\n    getNotificationAttachments(notificationId: $id) {\r\n      url\r\n      type\r\n      name\r\n      size\r\n    }\r\n  }\r\n`;\r\nexport const MARK_NOTIFICATION_READ_MUTATION = gql`\r\n  mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\r\n    markNotificationsAsRead(notificationIds: $notificationIds) {\r\n      success\r\n      readCount\r\n      remainingCount\r\n    }\r\n  }\r\n`;\r\n\r\nexport const DELETE_NOTIFICATION_MUTATION = gql`\r\n  mutation DeleteNotification($notificationId: ID!) {\r\n    deleteNotification(notificationId: $notificationId) {\r\n      success\r\n      message\r\n    }\r\n  }\r\n`;\r\n\r\nexport const DELETE_MULTIPLE_NOTIFICATIONS_MUTATION = gql`\r\n  mutation DeleteMultipleNotifications($notificationIds: [ID!]!) {\r\n    deleteMultipleNotifications(notificationIds: $notificationIds) {\r\n      success\r\n      count\r\n      message\r\n    }\r\n  }\r\n`;\r\n\r\nexport const DELETE_ALL_NOTIFICATIONS_MUTATION = gql`\r\n  mutation DeleteAllNotifications {\r\n    deleteAllNotifications {\r\n      success\r\n      count\r\n      message\r\n    }\r\n  }\r\n`;\r\nexport const NOTIFICATION_SUBSCRIPTION = gql`\r\n  subscription NotificationReceived {\r\n    notificationReceived {\r\n      id\r\n      type\r\n      content\r\n      timestamp\r\n      isRead\r\n      senderId {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      message {\r\n        id\r\n        content\r\n      }\r\n      readAt\r\n      relatedEntity\r\n      metadata\r\n    }\r\n  }\r\n`;\r\nexport const NOTIFICATIONS_READ_SUBSCRIPTION = gql`\r\n  subscription NotificationsRead {\r\n    notificationsRead\r\n  }\r\n`;\r\n// group Queries\r\nexport const GET_GROUP_QUERY = gql`\r\n  query GetGroup($id: ID!) {\r\n    getGroup(id: $id) {\r\n      id\r\n      name\r\n      photo\r\n      description\r\n      participants {\r\n        id\r\n        username\r\n        email\r\n        image\r\n        isOnline\r\n      }\r\n      admins {\r\n        id\r\n        username\r\n        email\r\n        image\r\n        isOnline\r\n      }\r\n      messageCount\r\n      createdAt\r\n      updatedAt\r\n    }\r\n  }\r\n`;\r\nexport const GET_USER_GROUPS_QUERY = gql`\r\n  query GetUserGroups($userId: ID!) {\r\n    getUserGroups(userId: $userId) {\r\n      id\r\n      name\r\n      photo\r\n      description\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n        isOnline\r\n      }\r\n      admins {\r\n        id\r\n        username\r\n        image\r\n        isOnline\r\n      }\r\n      messageCount\r\n      createdAt\r\n      updatedAt\r\n    }\r\n  }\r\n`;\r\nexport const CREATE_GROUP_MUTATION = gql`\r\n  mutation CreateGroup(\r\n    $name: String!\r\n    $participantIds: [ID!]!\r\n    $photo: Upload\r\n    $description: String\r\n  ) {\r\n    createGroup(\r\n      name: $name\r\n      participantIds: $participantIds\r\n      photo: $photo\r\n      description: $description\r\n    ) {\r\n      id\r\n      name\r\n      photo\r\n      description\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      admins {\r\n        id\r\n        username\r\n        image\r\n      }\r\n    }\r\n  }\r\n`;\r\nexport const UPDATE_GROUP_MUTATION = gql`\r\n  mutation UpdateGroup($id: ID!, $input: UpdateGroupInput!) {\r\n    updateGroup(id: $id, input: $input) {\r\n      id\r\n      name\r\n      photo\r\n      description\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      admins {\r\n        id\r\n        username\r\n        image\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport const DELETE_GROUP_MUTATION = gql`\r\n  mutation DeleteGroup($id: ID!) {\r\n    deleteGroup(id: $id) {\r\n      success\r\n      message\r\n    }\r\n  }\r\n`;\r\n\r\nexport const ADD_GROUP_PARTICIPANTS_MUTATION = gql`\r\n  mutation AddGroupParticipants($groupId: ID!, $participantIds: [ID!]!) {\r\n    addGroupParticipants(groupId: $groupId, participantIds: $participantIds) {\r\n      id\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport const REMOVE_GROUP_PARTICIPANTS_MUTATION = gql`\r\n  mutation RemoveGroupParticipants($groupId: ID!, $participantIds: [ID!]!) {\r\n    removeGroupParticipants(\r\n      groupId: $groupId\r\n      participantIds: $participantIds\r\n    ) {\r\n      id\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport const LEAVE_GROUP_MUTATION = gql`\r\n  mutation LeaveGroup($groupId: ID!) {\r\n    leaveGroup(groupId: $groupId) {\r\n      success\r\n      message\r\n    }\r\n  }\r\n`;\r\n\r\n// Add to exports\r\nexport const TYPING_INDICATOR_SUBSCRIPTION = gql`\r\n  subscription TypingIndicator($conversationId: ID!) {\r\n    typingIndicator(conversationId: $conversationId) {\r\n      conversationId\r\n      userId\r\n      isTyping\r\n    }\r\n  }\r\n`;\r\nexport const START_TYPING_MUTATION = gql`\r\n  mutation StartTyping($input: TypingInput!) {\r\n    startTyping(input: $input)\r\n  }\r\n`;\r\nexport const STOP_TYPING_MUTATION = gql`\r\n  mutation StopTyping($input: TypingInput!) {\r\n    stopTyping(input: $input)\r\n  }\r\n`;\r\nexport const GET_CURRENT_USER_QUERY = gql`\r\n  query GetCurrentUser {\r\n    getCurrentUser {\r\n      id\r\n      username\r\n      email\r\n      image\r\n      isOnline\r\n      lastActive\r\n      createdAt\r\n      updatedAt\r\n    }\r\n  }\r\n`;\r\nexport const REACT_TO_MESSAGE_MUTATION = gql`\r\n  mutation ReactToMessage($messageId: ID!, $emoji: String!) {\r\n    reactToMessage(messageId: $messageId, emoji: $emoji) {\r\n      id\r\n      reactions {\r\n        userId\r\n        emoji\r\n        createdAt\r\n      }\r\n    }\r\n  }\r\n`;\r\nexport const FORWARD_MESSAGE_MUTATION = gql`\r\n  mutation ForwardMessage($messageId: ID!, $conversationIds: [ID!]!) {\r\n    forwardMessage(messageId: $messageId, conversationIds: $conversationIds) {\r\n      id\r\n      content\r\n      timestamp\r\n      sender {\r\n        id\r\n        username\r\n      }\r\n      conversation {\r\n        id\r\n      }\r\n    }\r\n  }\r\n`;\r\nexport const PIN_MESSAGE_MUTATION = gql`\r\n  mutation PinMessage($messageId: ID!, $conversationId: ID!) {\r\n    pinMessage(messageId: $messageId, conversationId: $conversationId) {\r\n      id\r\n      pinned\r\n      pinnedAt\r\n      pinnedBy {\r\n        id\r\n        username\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport const CREATE_CONVERSATION_MUTATION = gql`\r\n  mutation CreateConversation($userId: ID!) {\r\n    createConversation(userId: $userId) {\r\n      id\r\n      participants {\r\n        id\r\n        username\r\n        image\r\n        isOnline\r\n      }\r\n      lastMessage {\r\n        id\r\n        content\r\n        timestamp\r\n      }\r\n      unreadCount\r\n      updatedAt\r\n    }\r\n  }\r\n`;\r\n\r\n// Call Queries\r\nexport const CALL_HISTORY_QUERY = gql`\r\n  query CallHistory(\r\n    $limit: Int\r\n    $offset: Int\r\n    $status: [CallStatus]\r\n    $type: [CallType]\r\n    $startDate: String\r\n    $endDate: String\r\n  ) {\r\n    callHistory(\r\n      limit: $limit\r\n      offset: $offset\r\n      status: $status\r\n      type: $type\r\n      startDate: $startDate\r\n      endDate: $endDate\r\n    ) {\r\n      id\r\n      caller {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      recipient {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      type\r\n      status\r\n      startTime\r\n      endTime\r\n      duration\r\n      conversationId\r\n    }\r\n  }\r\n`;\r\n\r\nexport const CALL_DETAILS_QUERY = gql`\r\n  query CallDetails($callId: ID!) {\r\n    callDetails(callId: $callId) {\r\n      id\r\n      caller {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      recipient {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      type\r\n      status\r\n      startTime\r\n      endTime\r\n      duration\r\n      conversationId\r\n      metadata\r\n    }\r\n  }\r\n`;\r\n\r\nexport const CALL_STATS_QUERY = gql`\r\n  query CallStats {\r\n    callStats {\r\n      totalCalls\r\n      totalDuration\r\n      missedCalls\r\n      callsByType {\r\n        type\r\n        count\r\n      }\r\n      averageCallDuration\r\n      mostCalledUser {\r\n        id\r\n        username\r\n        image\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\n// Call Mutations\r\nexport const INITIATE_CALL_MUTATION = gql`\r\n  mutation InitiateCall(\r\n    $recipientId: ID!\r\n    $callType: CallType!\r\n    $callId: String!\r\n    $offer: String!\r\n    $conversationId: ID\r\n    $options: CallOptions\r\n  ) {\r\n    initiateCall(\r\n      recipientId: $recipientId\r\n      callType: $callType\r\n      callId: $callId\r\n      offer: $offer\r\n      conversationId: $conversationId\r\n      options: $options\r\n    ) {\r\n      id\r\n      caller {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      recipient {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      type\r\n      status\r\n      startTime\r\n      conversationId\r\n    }\r\n  }\r\n`;\r\n\r\nexport const SEND_CALL_SIGNAL_MUTATION = gql`\r\n  mutation SendCallSignal(\r\n    $callId: ID!\r\n    $signalType: String!\r\n    $signalData: String!\r\n  ) {\r\n    sendCallSignal(\r\n      callId: $callId\r\n      signalType: $signalType\r\n      signalData: $signalData\r\n    ) {\r\n      success\r\n      message\r\n    }\r\n  }\r\n`;\r\n\r\nexport const ACCEPT_CALL_MUTATION = gql`\r\n  mutation AcceptCall($callId: ID!, $answer: String!) {\r\n    acceptCall(callId: $callId, answer: $answer) {\r\n      id\r\n      status\r\n    }\r\n  }\r\n`;\r\n\r\nexport const REJECT_CALL_MUTATION = gql`\r\n  mutation RejectCall($callId: ID!, $reason: String) {\r\n    rejectCall(callId: $callId, reason: $reason) {\r\n      id\r\n      status\r\n    }\r\n  }\r\n`;\r\n\r\nexport const END_CALL_MUTATION = gql`\r\n  mutation EndCall($callId: ID!, $feedback: CallFeedbackInput) {\r\n    endCall(callId: $callId, feedback: $feedback) {\r\n      id\r\n      status\r\n      endTime\r\n      duration\r\n    }\r\n  }\r\n`;\r\n\r\nexport const TOGGLE_CALL_MEDIA_MUTATION = gql`\r\n  mutation ToggleCallMedia($callId: ID!, $video: Boolean, $audio: Boolean) {\r\n    toggleCallMedia(callId: $callId, video: $video, audio: $audio) {\r\n      success\r\n      message\r\n    }\r\n  }\r\n`;\r\n\r\n// Call Subscriptions\r\nexport const CALL_SIGNAL_SUBSCRIPTION = gql`\r\n  subscription CallSignal($callId: ID) {\r\n    callSignal(callId: $callId) {\r\n      callId\r\n      senderId\r\n      type\r\n      data\r\n      timestamp\r\n    }\r\n  }\r\n`;\r\n\r\nexport const INCOMING_CALL_SUBSCRIPTION = gql`\r\n  subscription IncomingCall {\r\n    incomingCall {\r\n      id\r\n      caller {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      recipient {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      type\r\n      conversationId\r\n      offer\r\n      timestamp\r\n    }\r\n  }\r\n`;\r\n\r\nexport const CALL_STATUS_CHANGED_SUBSCRIPTION = gql`\r\n  subscription CallStatusChanged($callId: ID) {\r\n    callStatusChanged(callId: $callId) {\r\n      id\r\n      status\r\n      endTime\r\n      duration\r\n    }\r\n  }\r\n`;\r\n\r\n// Requête pour récupérer les messages vocaux\r\nexport const GET_VOICE_MESSAGES_QUERY = gql`\r\n  query GetVoiceMessages {\r\n    getVoiceMessages {\r\n      id\r\n      caller {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      recipient {\r\n        id\r\n        username\r\n        image\r\n      }\r\n      type\r\n      status\r\n      startTime\r\n      endTime\r\n      duration\r\n      conversationId\r\n      metadata\r\n    }\r\n  }\r\n`;\r\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC;AACA,OAAO,MAAMC,QAAQ,GAAGD,GAAG;;;;;;;;;;CAU1B;AAED;AACA,OAAO,MAAME,qBAAqB,GAAGF,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCvC;AACD,OAAO,MAAMG,qBAAqB,GAAGH,GAAG;;;;;;;;CAQvC;AACD,OAAO,MAAMI,qBAAqB,GAAGJ,GAAG;;;;;;;;;CASvC;AACD,OAAO,MAAMK,uBAAuB,GAAGL,GAAG;;;;;;;;CAQzC;AACD,OAAO,MAAMM,kBAAkB,GAAGN,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCpC;AACD;AACA,OAAO,MAAMO,uBAAuB,GAAGP,GAAG;;;;;;;;;;;;;;;;;;;;;;;;CAwBzC;AACD,OAAO,MAAMQ,sBAAsB,GAAGR,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwCxC;AACD;AACA,OAAO,MAAMS,cAAc,GAAGT,GAAG;;;;;;;;;;;CAWhC;AACD,OAAO,MAAMU,kBAAkB,GAAGV,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgCpC;AACD;AACA,OAAO,MAAMW,wBAAwB,GAAGX,GAAG;;;;;;;;CAQ1C;AACD,OAAO,MAAMY,yBAAyB,GAAGZ,GAAG;;;;;;;;CAQ3C;AACD;AACA,OAAO,MAAMa,qBAAqB,GAAGb,GAAG;;;;;;;;;;;;CAYvC;AACD;AACA,OAAO,MAAMc,yBAAyB,GAAGd,GAAG;;;;;;;;;;;;;;;;CAgB3C;AACD;AACA,OAAO,MAAMe,yBAAyB,GAAGf,GAAG;;;;;;;;;;;;;;;;;;;;;;;;CAwB3C;AACD,OAAO,MAAMgB,wBAAwB,GAAGhB,GAAG;;;;;;;;;CAS1C;AACD,OAAO,MAAMiB,iCAAiC,GAAGjB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;CAwBnD;AACD;AACA,OAAO,MAAMkB,uBAAuB,GAAGlB,GAAG;;;;;;;;;;;;;;;;;;;;;;CAsBzC;AACD,OAAO,MAAMmB,8BAA8B,GAAGnB,GAAG;;;;;;;;;CAShD;AACD,OAAO,MAAMoB,+BAA+B,GAAGpB,GAAG;;;;;;;;CAQjD;AAED,OAAO,MAAMqB,4BAA4B,GAAGrB,GAAG;;;;;;;CAO9C;AAED,OAAO,MAAMsB,sCAAsC,GAAGtB,GAAG;;;;;;;;CAQxD;AAED,OAAO,MAAMuB,iCAAiC,GAAGvB,GAAG;;;;;;;;CAQnD;AACD,OAAO,MAAMwB,yBAAyB,GAAGxB,GAAG;;;;;;;;;;;;;;;;;;;;;;CAsB3C;AACD,OAAO,MAAMyB,+BAA+B,GAAGzB,GAAG;;;;CAIjD;AACD;AACA,OAAO,MAAM0B,eAAe,GAAG1B,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BjC;AACD,OAAO,MAAM2B,qBAAqB,GAAG3B,GAAG;;;;;;;;;;;;;;;;;;;;;;;;CAwBvC;AACD,OAAO,MAAM4B,qBAAqB,GAAG5B,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6BvC;AACD,OAAO,MAAM6B,qBAAqB,GAAG7B,GAAG;;;;;;;;;;;;;;;;;;;CAmBvC;AAED,OAAO,MAAM8B,qBAAqB,GAAG9B,GAAG;;;;;;;CAOvC;AAED,OAAO,MAAM+B,+BAA+B,GAAG/B,GAAG;;;;;;;;;;;CAWjD;AAED,OAAO,MAAMgC,kCAAkC,GAAGhC,GAAG;;;;;;;;;;;;;;CAcpD;AAED,OAAO,MAAMiC,oBAAoB,GAAGjC,GAAG;;;;;;;CAOtC;AAED;AACA,OAAO,MAAMkC,6BAA6B,GAAGlC,GAAG;;;;;;;;CAQ/C;AACD,OAAO,MAAMmC,qBAAqB,GAAGnC,GAAG;;;;CAIvC;AACD,OAAO,MAAMoC,oBAAoB,GAAGpC,GAAG;;;;CAItC;AACD,OAAO,MAAMqC,sBAAsB,GAAGrC,GAAG;;;;;;;;;;;;;CAaxC;AACD,OAAO,MAAMsC,yBAAyB,GAAGtC,GAAG;;;;;;;;;;;CAW3C;AACD,OAAO,MAAMuC,wBAAwB,GAAGvC,GAAG;;;;;;;;;;;;;;;CAe1C;AACD,OAAO,MAAMwC,oBAAoB,GAAGxC,GAAG;;;;;;;;;;;;CAYtC;AAED,OAAO,MAAMyC,4BAA4B,GAAGzC,GAAG;;;;;;;;;;;;;;;;;;;CAmB9C;AAED;AACA,OAAO,MAAM0C,kBAAkB,GAAG1C,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCpC;AAED,OAAO,MAAM2C,kBAAkB,GAAG3C,GAAG;;;;;;;;;;;;;;;;;;;;;;;CAuBpC;AAED,OAAO,MAAM4C,gBAAgB,GAAG5C,GAAG;;;;;;;;;;;;;;;;;;CAkBlC;AAED;AACA,OAAO,MAAM6C,sBAAsB,GAAG7C,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCxC;AAED,OAAO,MAAM8C,yBAAyB,GAAG9C,GAAG;;;;;;;;;;;;;;;CAe3C;AAED,OAAO,MAAM+C,oBAAoB,GAAG/C,GAAG;;;;;;;CAOtC;AAED,OAAO,MAAMgD,oBAAoB,GAAGhD,GAAG;;;;;;;CAOtC;AAED,OAAO,MAAMiD,iBAAiB,GAAGjD,GAAG;;;;;;;;;CASnC;AAED,OAAO,MAAMkD,0BAA0B,GAAGlD,GAAG;;;;;;;CAO5C;AAED;AACA,OAAO,MAAMmD,wBAAwB,GAAGnD,GAAG;;;;;;;;;;CAU1C;AAED,OAAO,MAAMoD,0BAA0B,GAAGpD,GAAG;;;;;;;;;;;;;;;;;;;;CAoB5C;AAED,OAAO,MAAMqD,gCAAgC,GAAGrD,GAAG;;;;;;;;;CASlD;AAED;AACA,OAAO,MAAMsD,wBAAwB,GAAGtD,GAAG;;;;;;;;;;;;;;;;;;;;;;;CAuB1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}