{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMPTY } from 'rxjs';\nimport { map, catchError, tap, filter } from 'rxjs/operators';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, DELETE_GROUP_MUTATION, LEAVE_GROUP_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION,\n// Requêtes et mutations pour les appels\nCALL_HISTORY_QUERY, CALL_DETAILS_QUERY, CALL_STATS_QUERY, SEND_CALL_SIGNAL_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class MessageService {\n  constructor(apollo, logger, zone) {\n    this.apollo = apollo;\n    this.logger = logger;\n    this.zone = zone;\n    // État partagé\n    this.activeConversation = new BehaviorSubject(null);\n    this.notifications = new BehaviorSubject([]);\n    this.notificationCache = new Map();\n    this.notificationCount = new BehaviorSubject(0);\n    this.onlineUsers = new Map();\n    this.subscriptions = [];\n    this.CACHE_DURATION = 300000;\n    this.lastFetchTime = 0;\n    // Propriétés pour les appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    // Observables publics pour les appels\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    this.localStream$ = new BehaviorSubject(null);\n    this.remoteStream$ = new BehaviorSubject(null);\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    this.usersCache = [];\n    // Pagination metadata for user list\n    this.currentUserPagination = {\n      totalCount: 0,\n      totalPages: 0,\n      currentPage: 1,\n      hasNextPage: false,\n      hasPreviousPage: false\n    };\n    // Observables publics\n    this.activeConversation$ = this.activeConversation.asObservable();\n    this.notifications$ = this.notifications.asObservable();\n    this.notificationCount$ = this.notificationCount.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // --------------------------------------------------------------------------\n    // Section 2: Méthodes pour les Notifications\n    // --------------------------------------------------------------------------\n    // Propriétés pour la pagination des notifications\n    this.notificationPagination = {\n      currentPage: 1,\n      limit: 10,\n      hasMoreNotifications: true\n    };\n    // --------------------------------------------------------------------------\n    // Section 4: Subscriptions et Gestion Temps Réel\n    // --------------------------------------------------------------------------\n    // ✅ Optimized subscription with connection pooling and caching\n    this.subscriptionCache = new Map();\n    this.subscriptionRefCount = new Map();\n    this.toSafeISOString = date => {\n      if (!date) return undefined;\n      return typeof date === 'string' ? date : date.toISOString();\n    };\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  loadNotificationsFromLocalStorage() {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications);\n        this.notificationCache.clear();\n        notifications.forEach(notification => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  initSubscriptions() {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n      // 🔥 AJOUT: Subscription générale pour l'utilisateur\n    });\n\n    this.subscribeToUserStatus();\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    return this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.incomingCall) {\n        return null;\n      }\n      // Gérer l'appel entrant\n      this.handleIncomingCall(data.incomingCall);\n      return data.incomingCall;\n    }), catchError(error => {\n      this.logger.error('Error in incoming call subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      return;\n    }\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(error => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted) {\n    this.muted = muted;\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted() {\n    return this.muted;\n  }\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound() {\n    console.log('MessageService: Tentative de lecture du son de notification');\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n    // Créer une mélodie agréable avec l'API Web Audio\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n      this.playNotificationMelody1(audioContext);\n      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n      // this.playNotificationMelody2(audioContext);\n      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n      // this.playNotificationMelody3(audioContext);\n      // SON 4: Triple note (Discord style) - Décommentez pour tester\n      // this.playNotificationMelody4(audioContext);\n      // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n      // this.playNotificationMelody5(audioContext);\n      console.log('MessageService: Son de notification mélodieux généré avec succès');\n    } catch (error) {\n      console.error('MessageService: Erreur lors de la génération du son:', error);\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 0.7; // Volume plus doux\n        audio.play().catch(err => {\n          console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n        });\n      } catch (audioError) {\n        console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n      }\n    }\n  }\n  // 🎵 SON 1: Mélodie douce (WhatsApp style)\n  playNotificationMelody1(audioContext) {\n    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n  }\n  // 🎵 SON 2: Mélodie montante (iPhone style)\n  playNotificationMelody2(audioContext) {\n    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n  }\n  // 🎵 SON 3: Mélodie descendante (Messenger style)\n  playNotificationMelody3(audioContext) {\n    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n  }\n  // 🎵 SON 4: Triple note (Discord style)\n  playNotificationMelody4(audioContext) {\n    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n  }\n  // 🎵 SON 5: Cloche douce (Slack style)\n  playNotificationMelody5(audioContext) {\n    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n  }\n  /**\n   * Joue une note individuelle pour la mélodie de notification\n   */\n  playNotificationTone(audioContext, startTime, frequency, duration) {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n    // Configurer l'oscillateur pour un son plus doux\n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n    // Configurer le volume avec une enveloppe douce\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + startTime + 0.02);\n    gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + startTime + duration * 0.7);\n    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + startTime + duration);\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  /**\n   * Joue un son de cloche pour les notifications\n   */\n  playBellTone(audioContext, startTime, frequency, duration) {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n    // Configurer l'oscillateur pour un son de cloche\n    oscillator.type = 'triangle'; // Son plus doux que sine\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n    // Enveloppe de cloche (attaque rapide, déclin lent)\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(0.4, audioContext.currentTime + startTime + 0.01);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + startTime + duration);\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl) {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n      audio.onended = () => {\n        resolve();\n      };\n      audio.onerror = error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n      audio.play().catch(error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages() {\n    this.logger.debug('[MessageService] Getting voice messages');\n    return this.apollo.watchQuery({\n      query: GET_VOICE_MESSAGES_QUERY,\n      fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n    }).valueChanges.pipe(map(result => {\n      const voiceMessages = result.data?.getVoiceMessages || [];\n      this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n      return voiceMessages;\n    }), catchError(error => {\n      this.logger.error('[MessageService] Error fetching voice messages:', error);\n      return throwError(() => new Error('Failed to fetch voice messages'));\n    }));\n  }\n  // Message methods\n  getMessages(senderId, receiverId, conversationId, page = 1, limit = 25 // ✅ Increased batch size for better performance\n  ) {\n    return this.apollo.watchQuery({\n      query: GET_MESSAGES_QUERY,\n      variables: {\n        senderId,\n        receiverId,\n        conversationId,\n        limit,\n        page\n      },\n      fetchPolicy: 'cache-first',\n      errorPolicy: 'all' // ✅ Handle partial errors gracefully\n    }).valueChanges.pipe(map(result => {\n      const messages = result.data?.getMessages || [];\n      // ✅ Batch normalize messages for better performance\n      return this.batchNormalizeMessages(messages);\n    }), catchError(error => {\n      console.error('Error fetching messages:', error);\n      return throwError(() => new Error('Failed to fetch messages'));\n    }));\n  }\n  editMessage(messageId, newContent) {\n    return this.apollo.mutate({\n      mutation: EDIT_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        newContent\n      }\n    }).pipe(map(result => {\n      if (!result.data?.editMessage) {\n        throw new Error('Failed to edit message');\n      }\n      return this.normalizeMessage(result.data.editMessage);\n    }), catchError(error => {\n      this.logger.error('Error editing message:', error);\n      return throwError(() => new Error('Failed to edit message'));\n    }));\n  }\n  deleteMessage(messageId) {\n    return this.apollo.mutate({\n      mutation: DELETE_MESSAGE_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.deleteMessage) {\n        throw new Error('Failed to delete message');\n      }\n      return this.normalizeMessage(result.data.deleteMessage);\n    }), catchError(error => {\n      this.logger.error('Error deleting message:', error);\n      return throwError(() => new Error('Failed to delete message'));\n    }));\n  }\n  markMessageAsRead(messageId) {\n    return this.apollo.mutate({\n      mutation: MARK_AS_READ_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n      return {\n        ...result.data.markMessageAsRead,\n        readAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error marking message as read:', error);\n      return throwError(() => new Error('Failed to mark message as read'));\n    }));\n  }\n  reactToMessage(messageId, emoji) {\n    return this.apollo.mutate({\n      mutation: REACT_TO_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        emoji\n      }\n    }).pipe(map(result => {\n      if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n      return result.data.reactToMessage;\n    }), catchError(error => {\n      console.error('Error reacting to message:', error);\n      return throwError(() => new Error('Failed to react to message'));\n    }));\n  }\n  forwardMessage(messageId, conversationIds) {\n    return this.apollo.mutate({\n      mutation: FORWARD_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationIds\n      }\n    }).pipe(map(result => {\n      if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n      return result.data.forwardMessage.map(msg => ({\n        ...msg,\n        timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n      }));\n    }), catchError(error => {\n      console.error('Error forwarding message:', error);\n      return throwError(() => new Error('Failed to forward message'));\n    }));\n  }\n  pinMessage(messageId, conversationId) {\n    return this.apollo.mutate({\n      mutation: PIN_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n      return {\n        ...result.data.pinMessage,\n        pinnedAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error pinning message:', error);\n      return throwError(() => new Error('Failed to pin message'));\n    }));\n  }\n  searchMessages(query, conversationId, filters = {}) {\n    return this.apollo.watchQuery({\n      query: SEARCH_MESSAGES_QUERY,\n      variables: {\n        query,\n        conversationId,\n        ...filters,\n        dateFrom: this.toSafeISOString(filters.dateFrom),\n        dateTo: this.toSafeISOString(filters.dateTo)\n      },\n      fetchPolicy: 'cache-first',\n      errorPolicy: 'all'\n    }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error searching messages:', error);\n      return throwError(() => new Error('Failed to search messages'));\n    }));\n  }\n  // ✅ Batch normalization for better performance\n  batchNormalizeMessages(messages) {\n    if (!messages || messages.length === 0) return [];\n    return messages.map(msg => {\n      try {\n        return this.normalizeMessage(msg);\n      } catch (error) {\n        console.error('Error normalizing message:', error);\n        // Return minimal valid message on error\n        return {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n      }\n    });\n  }\n  getUnreadMessages(userId) {\n    return this.apollo.watchQuery({\n      query: GET_UNREAD_MESSAGES_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error fetching unread messages:', error);\n      return throwError(() => new Error('Failed to fetch unread messages'));\n    }));\n  }\n  setActiveConversation(conversationId) {\n    this.activeConversation.next(conversationId);\n  }\n  getConversations() {\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATIONS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const conversations = result.data?.getConversations || [];\n      return conversations.map(conv => this.normalizeConversation(conv));\n    }), catchError(error => {\n      console.error('Error fetching conversations:', error);\n      return throwError(() => new Error('Failed to load conversations'));\n    }));\n  }\n  getConversation(conversationId, limit, page) {\n    this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n    const variables = {\n      conversationId\n    };\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATION_QUERY,\n      variables: variables,\n      fetchPolicy: 'network-only',\n      errorPolicy: 'all'\n    }).valueChanges.pipe(retry(2),\n    // Réessayer 2 fois en cas d'erreur\n    map(result => {\n      this.logger.debug(`[MessageService] Conversation response received:`, result);\n      const conv = result.data?.getConversation;\n      if (!conv) {\n        this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n        throw new Error('Conversation not found');\n      }\n      this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n      const normalizedConversation = this.normalizeConversation(conv);\n      this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n      return normalizedConversation;\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error fetching conversation:`, error);\n      return throwError(() => new Error('Failed to load conversation'));\n    }));\n  }\n  createConversation(userId) {\n    this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to create a conversation'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_CONVERSATION_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation creation response:`, result);\n      const conversation = result.data?.createConversation;\n      if (!conversation) {\n        this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n        throw new Error('Failed to create conversation');\n      }\n      try {\n        const normalizedConversation = this.normalizeConversation(conversation);\n        this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n        throw new Error('Error processing created conversation');\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n      return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n    }));\n  }\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId) {\n    this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to get/create a conversation'));\n    }\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(map(conversations => {\n      // Récupérer l'ID de l'utilisateur actuel\n      const currentUserId = this.getCurrentUserId();\n      // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n      const existingConversation = conversations.find(conv => {\n        if (conv.isGroup) return false;\n        // Vérifier si la conversation contient les deux utilisateurs\n        const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n        return participantIds.includes(userId) && participantIds.includes(currentUserId);\n      });\n      if (existingConversation) {\n        this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n        return existingConversation;\n      }\n      // Si aucune conversation n'est trouvée, en créer une nouvelle\n      throw new Error('No existing conversation found');\n    }), catchError(error => {\n      this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n      return this.createConversation(userId);\n    }));\n  }\n  getNotifications(refresh = false, page = 1, limit = 10) {\n    this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY\n    });\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n    return this.apollo.watchQuery({\n      query: GET_NOTIFICATIONS_QUERY,\n      variables: {\n        page: page,\n        limit: limit\n      },\n      fetchPolicy: refresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Notifications response received');\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      const notifications = result.data?.getUserNotifications || [];\n      this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n      // Vérifier s'il y a plus de notifications à charger\n      this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n      if (notifications.length === 0) {\n        this.logger.info('MessageService', 'No notifications received from server');\n        this.notificationPagination.hasMoreNotifications = false;\n      }\n      // Filtrer les notifications supprimées\n      const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n      this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n      // Afficher les notifications reçues pour le débogage\n      filteredNotifications.forEach((notif, index) => {\n        console.log(`Notification ${index + 1} (page ${page}):`, {\n          id: notif.id || notif._id,\n          type: notif.type,\n          content: notif.content,\n          isRead: notif.isRead\n        });\n      });\n      // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n      // Mettre à jour le cache avec les nouvelles notifications\n      this.updateCache(filteredNotifications);\n      // Récupérer toutes les notifications du cache et les TRIER\n      const cachedNotifications = Array.from(this.notificationCache.values());\n      // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n      const sortedNotifications = this.sortNotificationsByDate(cachedNotifications);\n      console.log(`📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`);\n      // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n      this.notifications.next(sortedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage\n      this.saveNotificationsToLocalStorage();\n      return cachedNotifications;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error loading notifications:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      return throwError(() => new Error('Failed to load notifications'));\n    }));\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIds = new Set();\n      const savedNotifications = localStorage.getItem('notifications');\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications = this.apollo.client.readQuery({\n        query: GET_NOTIFICATIONS_QUERY\n      })?.getUserNotifications || [];\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach(notification => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n      return deletedIds;\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications() {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications() {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n  }\n  getNotificationById(id) {\n    return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n      this.logger.error('Error finding notification:', error);\n      return throwError(() => new Error('Failed to find notification'));\n    }));\n  }\n  getNotificationCount() {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId) {\n    return this.apollo.query({\n      query: GET_NOTIFICATIONS_ATTACHAMENTS,\n      variables: {\n        id: notificationId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n      this.logger.error('Error fetching notification attachments:', error);\n      return throwError(() => new Error('Failed to fetch attachments'));\n    }));\n  }\n  getUnreadNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n  }\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(notificationId) {\n    this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n    // Appeler le backend pour supprimer la notification\n    return this.apollo.mutate({\n      mutation: DELETE_NOTIFICATION_MUTATION,\n      variables: {\n        notificationId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteNotification;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de la notification', {\n      success: true,\n      message: 'Notification supprimée localement (erreur serveur)'\n    })));\n  }\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  saveNotificationsToLocalStorage() {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n    }\n  }\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications() {\n    this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n    }).pipe(map(result => {\n      const response = result.data?.deleteAllNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de toutes les notifications', {\n      success: true,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(notificationIds) {\n    this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n      variables: {\n        notificationIds\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteMultipleNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression multiple de notifications', {\n      success: count > 0,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  groupNotificationsByType() {\n    return this.notifications$.pipe(map(notifications => {\n      const groups = new Map();\n      notifications.forEach(notif => {\n        if (!groups.has(notif.type)) {\n          groups.set(notif.type, []);\n        }\n        groups.get(notif.type)?.push(notif);\n      });\n      return groups;\n    }));\n  }\n  markAsRead(notificationIds) {\n    this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value\n      });\n    }\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n    this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      }\n    };\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n    return this.apollo.mutate({\n      mutation: MARK_NOTIFICATION_READ_MUTATION,\n      variables: {\n        notificationIds: validIds\n      },\n      optimisticResponse: optimisticResponse,\n      errorPolicy: 'all',\n      fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n    }).pipe(map(result => {\n      this.logger.debug('MessageService', 'Mutation result', result);\n      console.log('Mutation result:', result);\n      // Si nous avons des erreurs GraphQL, les logger mais continuer\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        console.error('GraphQL errors:', result.errors);\n      }\n      // Utiliser la réponse du serveur ou notre réponse optimiste\n      const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error marking notifications as read:', error);\n      console.error('Error in markAsRead:', error);\n      // En cas d'erreur, retourner quand même un succès simulé\n      // puisque nous avons déjà mis à jour l'interface utilisateur\n      return of({\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      });\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels (SUPPRIMÉES - VOIR SECTION À LA FIN)\n  // --------------------------------------------------------------------------\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId) {\n    return this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      variables: {\n        callId\n      }\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.callSignal) {\n        throw new Error('No call signal received');\n      }\n      return data.callSignal;\n    }), tap(signal => {\n      this.callSignals.next(signal);\n      this.handleCallSignal(signal);\n    }), catchError(error => {\n      this.logger.error('Error in call signal subscription', error);\n      return throwError(() => new Error('Call signal subscription failed'));\n    }));\n  }\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(callId, signalType, signalData) {\n    return this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId,\n        signalType,\n        signalData\n      }\n    }).pipe(map(result => {\n      const success = result.data?.sendCallSignal;\n      if (!success) {\n        throw new Error('Failed to send call signal');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error sending call signal', error);\n      return throwError(() => new Error('Failed to send call signal'));\n    }));\n  }\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(limit = 20, offset = 0, status, type, startDate, endDate) {\n    return this.apollo.watchQuery({\n      query: CALL_HISTORY_QUERY,\n      variables: {\n        limit,\n        offset,\n        status,\n        type,\n        startDate,\n        endDate\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const history = result.data?.callHistory || [];\n      this.logger.debug(`Retrieved ${history.length} call history items`);\n      return history;\n    }), catchError(error => {\n      this.logger.error('Error fetching call history:', error);\n      return throwError(() => new Error('Failed to fetch call history'));\n    }));\n  }\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId) {\n    return this.apollo.watchQuery({\n      query: CALL_DETAILS_QUERY,\n      variables: {\n        callId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const details = result.data?.callDetails;\n      if (!details) {\n        throw new Error('Call details not found');\n      }\n      this.logger.debug(`Retrieved call details for: ${callId}`);\n      return details;\n    }), catchError(error => {\n      this.logger.error('Error fetching call details:', error);\n      return throwError(() => new Error('Failed to fetch call details'));\n    }));\n  }\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats() {\n    return this.apollo.watchQuery({\n      query: CALL_STATS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const stats = result.data?.callStats;\n      if (!stats) {\n        throw new Error('Call stats not found');\n      }\n      this.logger.debug('Retrieved call stats:', stats);\n      return stats;\n    }), catchError(error => {\n      this.logger.error('Error fetching call stats:', error);\n      return throwError(() => new Error('Failed to fetch call stats'));\n    }));\n  }\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  handleCallSignal(signal) {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  handleIceCandidate(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n        this.logger.error('Error adding ICE candidate', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error);\n    }\n  }\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  handleAnswer(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n        this.logger.error('Error setting remote description', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error);\n    }\n  }\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  handleEndCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  handleRejectCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Nettoie les ressources d'appel\n   */\n  cleanupCall() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  setupMediaDevices(callType) {\n    const constraints = {\n      audio: true,\n      video: callType !== CallType.AUDIO ? {\n        width: {\n          ideal: 1280\n        },\n        height: {\n          ideal: 720\n        }\n      } : false\n    };\n    return new Observable(observer => {\n      navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n        observer.next(stream);\n        observer.complete();\n      }).catch(error => {\n        this.logger.error('Error accessing media devices', error);\n        observer.error(new Error('Failed to access media devices'));\n      });\n    });\n  }\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  generateCallId() {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n    this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n    const now = Date.now();\n    const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n      return of([...this.usersCache]);\n    }\n    this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n    return this.apollo.watchQuery({\n      query: GET_ALL_USER_QUERY,\n      variables: {\n        search,\n        page,\n        limit,\n        sortBy,\n        sortOrder,\n        isOnline: isOnline !== undefined ? isOnline : null\n      },\n      fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Users response received', result);\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      if (!result.data?.getAllUsers) {\n        this.logger.warn('MessageService', 'No users data received from server');\n        return [];\n      }\n      const paginatedResponse = result.data.getAllUsers;\n      // Log pagination metadata\n      this.logger.debug('MessageService', 'Pagination metadata:', {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      });\n      // Normalize users with error handling\n      const users = [];\n      for (const user of paginatedResponse.users) {\n        try {\n          if (user) {\n            users.push(this.normalizeUser(user));\n          }\n        } catch (error) {\n          this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n        }\n      }\n      this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n      // Update cache only for first page with no filters\n      if (!search && page === 1 && !isOnline) {\n        this.usersCache = [...users];\n        this.lastFetchTime = Date.now();\n        this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n      }\n      // Store pagination metadata in a property for component access\n      this.currentUserPagination = {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      };\n      return users;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching users:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      // Return cache if available (only for first page)\n      if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n        this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n        return of([...this.usersCache]);\n      }\n      return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n    }));\n  }\n  getOneUser(userId) {\n    return this.apollo.watchQuery({\n      query: GET_USER_QUERY,\n      variables: {\n        id: userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching user:', error);\n      return throwError(() => new Error('Failed to fetch user'));\n    }));\n  }\n  getCurrentUser() {\n    return this.apollo.watchQuery({\n      query: GET_CURRENT_USER_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching current user:', error);\n      return throwError(() => new Error('Failed to fetch current user'));\n    }));\n  }\n  setUserOnline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_ONLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n      return this.normalizeUser(result.data.setUserOnline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user online:', error);\n      return throwError(() => new Error('Failed to set user online'));\n    }));\n  }\n  setUserOffline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_OFFLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n      return this.normalizeUser(result.data.setUserOffline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user offline:', error);\n      return throwError(() => new Error('Failed to set user offline'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(name, participantIds, photo, description) {\n    this.logger.debug('MessageService', `Creating group: ${name} with ${participantIds.length} participants`);\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(() => new Error('Nom du groupe et participants requis'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_GROUP_MUTATION,\n      variables: {\n        name,\n        participantIds,\n        photo,\n        description\n      }\n    }).pipe(map(result => {\n      const group = result.data?.createGroup;\n      if (!group) {\n        throw new Error('Échec de la création du groupe');\n      }\n      this.logger.info('MessageService', `Group created successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error creating group:', error);\n      return throwError(() => new Error('Échec de la création du groupe'));\n    }));\n  }\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId, input) {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: UPDATE_GROUP_MUTATION,\n      variables: {\n        id: groupId,\n        input\n      }\n    }).pipe(map(result => {\n      const group = result.data?.updateGroup;\n      if (!group) {\n        throw new Error('Échec de la mise à jour du groupe');\n      }\n      this.logger.info('MessageService', `Group updated successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error updating group:', error);\n      return throwError(() => new Error('Échec de la mise à jour du groupe'));\n    }));\n  }\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(groupId) {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: DELETE_GROUP_MUTATION,\n      variables: {\n        id: groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteGroup;\n      if (!response) {\n        throw new Error('Échec de la suppression du groupe');\n      }\n      this.logger.info('MessageService', `Group deleted successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error deleting group:', error);\n      return throwError(() => new Error('Échec de la suppression du groupe'));\n    }));\n  }\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(groupId) {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: LEAVE_GROUP_MUTATION,\n      variables: {\n        groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.leaveGroup;\n      if (!response) {\n        throw new Error('Échec de la sortie du groupe');\n      }\n      this.logger.info('MessageService', `Left group successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error leaving group:', error);\n      return throwError(() => new Error('Échec de la sortie du groupe'));\n    }));\n  }\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId) {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.query({\n      query: GET_GROUP_QUERY,\n      variables: {\n        id: groupId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const group = result.data?.getGroup;\n      if (!group) {\n        throw new Error('Groupe non trouvé');\n      }\n      this.logger.info('MessageService', `Group retrieved successfully: ${groupId}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting group:', error);\n      return throwError(() => new Error('Échec de la récupération du groupe'));\n    }));\n  }\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId) {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n    return this.apollo.query({\n      query: GET_USER_GROUPS_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const groups = result.data?.getUserGroups || [];\n      this.logger.info('MessageService', `Retrieved ${groups.length} groups for user: ${userId}`);\n      return groups;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting user groups:', error);\n      return throwError(() => new Error('Échec de la récupération des groupes'));\n    }));\n  }\n  subscribeToNewMessages(conversationId) {\n    if (!conversationId) {\n      return throwError(() => new Error('Conversation ID is required'));\n    }\n    // ✅ Use cached subscription if available\n    const cacheKey = `messages_${conversationId}`;\n    if (this.subscriptionCache.has(cacheKey)) {\n      const refCount = this.subscriptionRefCount.get(cacheKey) || 0;\n      this.subscriptionRefCount.set(cacheKey, refCount + 1);\n      return this.subscriptionCache.get(cacheKey);\n    }\n    // ✅ Quick token validation without verbose logging\n    if (!this.isTokenValid()) {\n      return EMPTY;\n    }\n    // ✅ Reduced logging for better performance\n    if (!environment.production) {\n      console.log(`🚀 Setting up subscription: ${conversationId}`);\n    }\n    console.log(`🔍 DEBUG: MESSAGE_SENT_SUBSCRIPTION query:`, MESSAGE_SENT_SUBSCRIPTION);\n    console.log(`🔍 DEBUG: Subscription variables:`, {\n      conversationId\n    });\n    const sub$ = this.apollo.subscribe({\n      query: MESSAGE_SENT_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(tap(result => {\n      console.log(`🔍 DEBUG: Raw subscription result received:`, result);\n      console.log(`🔍 DEBUG: result.data:`, result.data);\n      console.log(`🔍 DEBUG: result.data?.messageSent:`, result.data?.messageSent);\n    }), map(result => {\n      const msg = result.data?.messageSent;\n      if (!msg) {\n        console.log(`❌ DEBUG: No message payload received in result:`, result);\n        this.logger.warn('⚠️ No message payload received');\n        throw new Error('No message payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New message received via WebSocket', msg);\n      // Vérifier que l'ID est présent\n      if (!msg.id && !msg._id) {\n        this.logger.warn('⚠️ Message without ID received, generating temp ID');\n        msg.id = `temp-${Date.now()}`;\n      }\n      try {\n        // NORMALISATION RAPIDE du message\n        const normalizedMessage = this.normalizeMessage(msg);\n        this.logger.debug('✅ INSTANT: Message normalized successfully', normalizedMessage);\n        // TRAITEMENT INSTANTANÉ selon le type\n        if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.type === MessageType.VOICE_MESSAGE || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'AUDIO')) {\n          this.logger.debug('🎤 INSTANT: Voice message received in real-time');\n        }\n        // MISE À JOUR IMMÉDIATE de l'UI\n        this.zone.run(() => {\n          this.logger.debug('📡 INSTANT: Updating conversation UI immediately');\n          this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n        });\n        return normalizedMessage;\n      } catch (err) {\n        this.logger.error('❌ Error normalizing message:', err);\n        // Créer un message minimal mais valide pour éviter les erreurs\n        const minimalMessage = {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n        this.logger.debug('🔧 FALLBACK: Created minimal message', minimalMessage);\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Message subscription error:', error);\n      // Retourner un observable vide au lieu de null\n      return EMPTY;\n    }),\n    // Filtrer les valeurs null\n    filter(message => !!message),\n    // Réessayer après un délai en cas d'erreur\n    retry(3));\n    console.log(`🔍 DEBUG: Setting up subscription observer...`);\n    const sub = sub$.subscribe({\n      next: message => {\n        console.log(`✅ DEBUG: Message received via subscription:`, message);\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: err => {\n        console.error(`❌ DEBUG: Subscription error:`, err);\n        this.logger.error('Error in message subscription:', err);\n      },\n      complete: () => {\n        console.log(`🔚 DEBUG: Subscription completed`);\n      }\n    });\n    // Log pour confirmer que la subscription est créée\n    console.log(`🔗 DEBUG: Subscription object created:`, sub);\n    console.log(`🔗 DEBUG: Apollo client state:`, this.apollo);\n    this.subscriptions.push(sub);\n    console.log(`✅ DEBUG: Subscription established and added to subscriptions list. Total subscriptions: ${this.subscriptions.length}`);\n    return sub$;\n  }\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  updateConversationWithNewMessage(conversationId, message) {\n    this.logger.debug(`⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`);\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: conversation => {\n          this.logger.debug(`✅ BACKGROUND: Conversation ${conversationId} refreshed with ${conversation?.messages?.length || 0} messages`);\n        },\n        error: error => {\n          this.logger.error(`⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`, error);\n        }\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n  /**\n   * Rafraîchit les notifications du sender après envoi d'un message\n   */\n  refreshSenderNotifications() {\n    console.log('🔄 SENDER: Refreshing notifications after message sent');\n    // Recharger les notifications en arrière-plan\n    this.getNotifications(true).subscribe({\n      next: notifications => {\n        console.log('🔄 SENDER: Notifications refreshed successfully', notifications.length);\n      },\n      error: error => {\n        console.error('🔄 SENDER: Error refreshing notifications:', error);\n      }\n    });\n  }\n  subscribeToUserStatus() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n    const sub$ = this.apollo.subscribe({\n      query: USER_STATUS_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n      const user = result.data?.userStatusChanged;\n      if (!user) {\n        this.logger.error('No status payload received');\n        throw new Error('No status payload received');\n      }\n      return this.normalizeUser(user);\n    }), catchError(error => {\n      this.logger.error('Status subscription error:', error);\n      return throwError(() => new Error('Status subscription failed'));\n    }), retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: CONVERSATION_UPDATED_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const conv = result.data?.conversationUpdated;\n      if (!conv) throw new Error('No conversation payload received');\n      const normalizedConversation = {\n        ...conv,\n        participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n        lastMessage: conv.lastMessage ? {\n          ...conv.lastMessage,\n          sender: this.normalizeUser(conv.lastMessage.sender),\n          timestamp: this.safeDate(conv.lastMessage.timestamp),\n          readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n          // Conservez toutes les autres propriétés du message\n          id: conv.lastMessage.id,\n          content: conv.lastMessage.content,\n          type: conv.lastMessage.type,\n          isRead: conv.lastMessage.isRead\n          // ... autres propriétés nécessaires\n        } : null // On conserve null comme dans votre version originale\n      };\n\n      return normalizedConversation; // Assertion de type si nécessaire\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Conversation subscription error:', error);\n      return throwError(() => new Error('Conversation subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: TYPING_INDICATOR_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n      this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n      return throwError(() => new Error('Typing indicator subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  isTokenValid() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString()\n        });\n        return false;\n      }\n      return true;\n    } catch (error) {\n      this.logger.error('Erreur lors de la vérification du token:', error);\n      return false;\n    }\n  }\n  subscribeToNotificationsRead() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n      return of([]);\n    }\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n    const sub$ = this.apollo.subscribe({\n      query: NOTIFICATIONS_READ_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n      const notificationIds = result.data?.notificationsRead || [];\n      this.logger.debug('Notifications marquées comme lues:', notificationIds);\n      this.updateNotificationStatus(notificationIds, true);\n      return notificationIds;\n    }), catchError(err => {\n      this.logger.error('Notifications read subscription error:', err);\n      // Retourner un tableau vide au lieu de propager l'erreur\n      return of([]);\n    }),\n    // Réessayer après un délai en cas d'erreur\n    retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications() {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n      return EMPTY;\n    }\n    this.logger.debug('🚀 INSTANT NOTIFICATION: Setting up real-time subscription');\n    const source$ = this.apollo.subscribe({\n      query: NOTIFICATION_SUBSCRIPTION\n    });\n    const processed$ = source$.pipe(map(result => {\n      const notification = result.data?.notificationReceived;\n      if (!notification) {\n        throw new Error('No notification payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New notification received', notification);\n      const normalized = this.normalizeNotification(notification);\n      // Vérification rapide du cache\n      if (this.notificationCache.has(normalized.id)) {\n        this.logger.debug(`🔄 Notification ${normalized.id} already in cache, skipping`);\n        throw new Error('Notification already exists in cache');\n      }\n      // TRAITEMENT INSTANTANÉ\n      this.logger.debug('📡 INSTANT: Processing notification immediately');\n      // Vérifier si la notification existe déjà pour éviter les doublons\n      const currentNotifications = this.notifications.value;\n      const existingNotification = currentNotifications.find(n => n.id === normalized.id);\n      if (existingNotification) {\n        this.logger.debug('🔄 DUPLICATE: Notification already exists, skipping:', normalized.id);\n        return normalized;\n      }\n      // Son de notification IMMÉDIAT\n      this.playNotificationSound();\n      // Mise à jour INSTANTANÉE du cache\n      this.updateNotificationCache(normalized);\n      // Émettre IMMÉDIATEMENT la notification EN PREMIER\n      this.zone.run(() => {\n        // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n        const updatedNotifications = [normalized, ...currentNotifications];\n        this.logger.debug(`⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`);\n        this.notifications.next(updatedNotifications);\n        this.notificationCount.next(this.notificationCount.value + 1);\n      });\n      this.logger.debug('✅ INSTANT: Notification processed and emitted', normalized);\n      return normalized;\n    }),\n    // Gestion d'erreurs optimisée\n    catchError(err => {\n      if (err instanceof Error && err.message === 'Notification already exists in cache') {\n        return EMPTY;\n      }\n      this.logger.error('❌ Notification subscription error:', err);\n      return EMPTY;\n    }),\n    // Optimisation: traitement en temps réel\n    tap(notification => {\n      this.logger.debug('⚡ INSTANT: Notification ready for UI update', notification);\n    }));\n    const sub = processed$.subscribe({\n      next: notification => {\n        this.logger.debug('✅ INSTANT: Notification delivered to UI', notification);\n      },\n      error: error => {\n        this.logger.error('❌ CRITICAL: Notification subscription error', error);\n      }\n    });\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n  startCleanupInterval() {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  cleanupExpiredNotifications() {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    let expiredCount = 0;\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(this.notificationCache.values());\n      const sortedNotifications = this.sortNotificationsByDate(remainingNotifications);\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  sortNotificationsByDate(notifications) {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  getCurrentUserId() {\n    return localStorage.getItem('userId') || '';\n  }\n  normalizeMessage(message) {\n    if (!message) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined message');\n      throw new Error('Message object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error('[MessageService] Message ID is missing', undefined, message);\n        throw new Error('Message ID is required');\n      }\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n      } catch (error) {\n        this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true\n        };\n      }\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n      }\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments = message.attachments?.map(att => ({\n        id: att.id || att._id || `attachment-${Date.now()}`,\n        url: att.url || '',\n        type: att.type || 'unknown',\n        name: att.name || 'attachment',\n        size: att.size || 0,\n        duration: att.duration || 0\n      })) || [];\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null\n      };\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id\n      });\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n      throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeUser(user) {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n    const role = user.role || 'user';\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount\n    };\n  }\n  normalizeConversation(conv) {\n    if (!conv) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n      throw new Error('Conversation object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n        throw new Error('Conversation ID is required');\n      }\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n          }\n        }\n      } else {\n        this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n      }\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length\n        });\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug('[MessageService] Successfully normalized message', {\n                messageId: normalizedMessage.id,\n                content: normalizedMessage.content?.substring(0, 20),\n                sender: normalizedMessage.sender?.username\n              });\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n          }\n        }\n      } else {\n        this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n      }\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n        }\n      }\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt)\n      };\n      this.logger.debug('[MessageService] Conversation normalized successfully', {\n        conversationId: normalizedConversation.id,\n        participantCount: normalizedParticipants.length,\n        messageCount: normalizedMessages.length\n      });\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n      throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  safeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  normalizeNotification(notification) {\n    this.logger.debug('MessageService', 'Normalizing notification', notification);\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || notification._id;\n    if (!notificationId) {\n      this.logger.error('MessageService', 'Notification ID is missing', notification);\n      throw new Error('Notification ID is required');\n    }\n    if (!notification.timestamp) {\n      this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n      notification.timestamp = new Date();\n    }\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId,\n        id: notificationId,\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId)\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message)\n        })\n      };\n      this.logger.debug('MessageService', 'Normalized notification result', normalized);\n      return normalized;\n    } catch (error) {\n      this.logger.error('MessageService', 'Error in normalizeNotification', error);\n      throw error;\n    }\n  }\n  normalizeSender(sender) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && {\n        image: sender.image\n      })\n    };\n  }\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  normalizeNotMessage(message) {\n    if (!message) return null;\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && {\n        sender: this.normalizeSender(message.sender)\n      })\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  updateCache(notifications, skipDuplicates = true) {\n    const notificationArray = Array.isArray(notifications) ? notifications : [notifications];\n    this.logger.debug('MessageService', `Updating notification cache with ${notificationArray.length} notifications`);\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(notif => notif && (notif.id || notif._id));\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn('MessageService', `Found ${notificationArray.length - validNotifications.length} notifications without valid IDs`);\n    }\n    let addedCount = 0;\n    let skippedCount = 0;\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || notif._id;\n        if (!notifId) {\n          this.logger.error('MessageService', 'Notification without ID:', notif);\n          return;\n        }\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n          skippedCount++;\n          return;\n        }\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n        this.logger.debug('MessageService', `Added notification ${normalized.id} to cache`);\n      } catch (error) {\n        this.logger.error('MessageService', `Error processing notification ${index + 1}:`, error);\n      }\n    });\n    this.logger.debug('MessageService', `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`);\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  refreshNotificationObservables() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n    this.logger.debug(`📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`);\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  updateUnreadCount() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    const unreadNotifications = allNotifications.filter(n => !n.isRead);\n    const count = unreadNotifications.length;\n    // Forcer la mise à jour dans la zone Angular\n    this.zone.run(() => {\n      this.notificationCount.next(count);\n      // Émettre un événement global pour forcer la mise à jour du layout\n      window.dispatchEvent(new CustomEvent('notificationCountChanged', {\n        detail: {\n          count\n        }\n      }));\n    });\n  }\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  updateNotificationCache(notification) {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  updateNotificationStatus(ids, isRead) {\n    ids.forEach(id => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  removeNotificationsFromCache(notificationIds) {\n    console.log('🗑️ REMOVE FROM CACHE: Starting removal of', notificationIds.length, 'notifications');\n    console.log('🗑️ REMOVE FROM CACHE: Cache size before:', this.notificationCache.size);\n    let removedCount = 0;\n    notificationIds.forEach(id => {\n      if (this.notificationCache.has(id)) {\n        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n        this.notificationCache.delete(id);\n        removedCount++;\n      } else {\n        console.log('🗑️ REMOVE FROM CACHE: Notification not found in cache:', id);\n      }\n    });\n    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n    console.log('🗑️ REMOVE FROM CACHE: Cache size after:', this.notificationCache.size);\n    if (removedCount > 0) {\n      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n      this.refreshNotificationObservables();\n    }\n    return removedCount;\n  }\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  handleDeletionError(error, operation, fallbackResponse) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: START_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error starting typing indicator', error);\n      return throwError(() => new Error('Failed to start typing indicator'));\n    }));\n  }\n  stopTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: STOP_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error stopping typing indicator', error);\n      return throwError(() => new Error('Failed to stop typing indicator'));\n    }));\n  }\n  // ========================================\n  // MÉTHODE SENDMESSAGE MANQUANTE\n  // ========================================\n  /**\n   * Envoie un message (texte, fichier, audio, etc.)\n   * @param receiverId ID du destinataire\n   * @param content Contenu du message (texte)\n   * @param file Fichier à envoyer (optionnel)\n   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n   * @param conversationId ID de la conversation\n   * @returns Observable avec le message envoyé\n   */\n  sendMessage(receiverId, content, file, messageType = 'TEXT', conversationId) {\n    console.log('🚀 [MessageService] sendMessage called with:', {\n      receiverId,\n      content: content?.substring(0, 50),\n      hasFile: !!file,\n      fileName: file?.name,\n      fileType: file?.type,\n      fileSize: file?.size,\n      messageType,\n      conversationId\n    });\n    if (!receiverId) {\n      const error = new Error('Receiver ID is required');\n      console.error('❌ [MessageService] sendMessage error:', error);\n      return throwError(() => error);\n    }\n    // Préparer les variables pour la mutation\n    const variables = {\n      receiverId,\n      content: content || '',\n      type: messageType\n    };\n    // Ajouter l'ID de conversation si fourni\n    if (conversationId) {\n      variables.conversationId = conversationId;\n    }\n    // Si un fichier est fourni, l'ajouter aux variables\n    if (file) {\n      variables.file = file;\n      console.log('📁 [MessageService] Adding file to mutation:', {\n        name: file.name,\n        type: file.type,\n        size: file.size\n      });\n    }\n    console.log('📤 [MessageService] Sending mutation with variables:', variables);\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables,\n      context: {\n        useMultipart: !!file // Utiliser multipart si un fichier est présent\n      }\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] sendMessage mutation result:', result);\n      if (!result.data?.sendMessage) {\n        throw new Error('No message data received from server');\n      }\n      const message = result.data.sendMessage;\n      console.log('📨 [MessageService] Message sent successfully:', {\n        id: message.id,\n        type: message.type,\n        content: message.content?.substring(0, 50),\n        hasAttachments: !!message.attachments?.length\n      });\n      // Normaliser le message reçu\n      const normalizedMessage = this.normalizeMessage(message);\n      console.log('🔧 [MessageService] Message normalized:', normalizedMessage);\n      return normalizedMessage;\n    }), catchError(error => {\n      console.error('❌ [MessageService] sendMessage error:', error);\n      this.logger.error('Error sending message:', error);\n      // Fournir un message d'erreur plus spécifique\n      let errorMessage = \"Erreur lors de l'envoi du message\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp) {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp) {\n    if (!timestamp) return 'Unknown date';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      }\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      const day = date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      }).toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages, index) {\n    if (index === 0) return true;\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n  getDateFromTimestamp(timestamp) {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType) {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n    return 'fa-file';\n  }\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType) {\n    if (!mimeType) return 'File';\n    const typeMap = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive'\n    };\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message) {\n    if (!message) return false;\n    // Vérifier le type du message\n    if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE) {\n      return true;\n    }\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n      });\n    }\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const voiceAttachment = message.attachments.find(att => {\n      const type = att.type?.toString();\n      return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n    });\n    return voiceAttachment?.url || '';\n  }\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message) {\n    if (!message) return 0;\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n    return 0;\n  }\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index) {\n    const pattern = [8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18];\n    return pattern[index % pattern.length];\n  }\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds) {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message) {\n    if (!message) return MessageType.TEXT;\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n            return MessageType.FILE;\n          } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n            return MessageType.VIDEO;\n          }\n        }\n        return MessageType.FILE;\n      }\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis() {\n    return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n  }\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message, currentUserId) {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n    try {\n      const isCurrentUser = message.sender?.id === currentUserId || message.sender?._id === currentUserId || message.senderId === currentUserId;\n      const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n      const messageType = this.getMessageType(message);\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n      // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n  // ========================================\n  // APPELS WEBRTC - DÉLÉGUÉS AU CALLSERVICE\n  // ========================================\n  // Note: Les méthodes d'appel ont été déplacées vers CallService\n  // pour éviter la duplication de code et centraliser la logique\n  // destroy\n  cleanupSubscriptions() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "of", "throwError", "retry", "EMPTY", "map", "catchError", "tap", "filter", "MessageType", "CallType", "CallStatus", "GET_CONVERSATIONS_QUERY", "GET_NOTIFICATIONS_QUERY", "NOTIFICATION_SUBSCRIPTION", "GET_CONVERSATION_QUERY", "SEND_MESSAGE_MUTATION", "MARK_AS_READ_MUTATION", "MESSAGE_SENT_SUBSCRIPTION", "USER_STATUS_SUBSCRIPTION", "GET_USER_QUERY", "GET_ALL_USER_QUERY", "CONVERSATION_UPDATED_SUBSCRIPTION", "SEARCH_MESSAGES_QUERY", "GET_UNREAD_MESSAGES_QUERY", "SET_USER_ONLINE_MUTATION", "SET_USER_OFFLINE_MUTATION", "START_TYPING_MUTATION", "STOP_TYPING_MUTATION", "TYPING_INDICATOR_SUBSCRIPTION", "GET_CURRENT_USER_QUERY", "REACT_TO_MESSAGE_MUTATION", "FORWARD_MESSAGE_MUTATION", "PIN_MESSAGE_MUTATION", "CREATE_GROUP_MUTATION", "UPDATE_GROUP_MUTATION", "DELETE_GROUP_MUTATION", "LEAVE_GROUP_MUTATION", "GET_GROUP_QUERY", "GET_USER_GROUPS_QUERY", "EDIT_MESSAGE_MUTATION", "DELETE_MESSAGE_MUTATION", "GET_MESSAGES_QUERY", "GET_NOTIFICATIONS_ATTACHAMENTS", "MARK_NOTIFICATION_READ_MUTATION", "NOTIFICATIONS_READ_SUBSCRIPTION", "CREATE_CONVERSATION_MUTATION", "DELETE_NOTIFICATION_MUTATION", "DELETE_MULTIPLE_NOTIFICATIONS_MUTATION", "DELETE_ALL_NOTIFICATIONS_MUTATION", "CALL_HISTORY_QUERY", "CALL_DETAILS_QUERY", "CALL_STATS_QUERY", "SEND_CALL_SIGNAL_MUTATION", "CALL_SIGNAL_SUBSCRIPTION", "INCOMING_CALL_SUBSCRIPTION", "GET_VOICE_MESSAGES_QUERY", "MessageService", "constructor", "apollo", "logger", "zone", "activeConversation", "notifications", "notificationCache", "Map", "notificationCount", "onlineUsers", "subscriptions", "CACHE_DURATION", "lastFetchTime", "activeCall", "incomingCall", "callSignals", "localStream", "remoteStream", "peerConnection", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "localStream$", "remoteStream$", "rtcConfig", "iceServers", "urls", "usersCache", "currentUserPagination", "totalCount", "totalPages", "currentPage", "hasNextPage", "hasPreviousPage", "activeConversation$", "notifications$", "notificationCount$", "sounds", "isPlaying", "muted", "notificationPagination", "limit", "hasMoreNotifications", "subscriptionCache", "subscriptionRefCount", "toSafeISOString", "date", "undefined", "toISOString", "loadNotificationsFromLocalStorage", "initSubscriptions", "startCleanupInterval", "preloadSounds", "savedNotifications", "localStorage", "getItem", "JSON", "parse", "clear", "for<PERSON>ach", "notification", "id", "set", "next", "Array", "from", "values", "updateUnreadCount", "error", "runOutsideAngular", "subscribeToNewNotifications", "subscribe", "subscribeToNotificationsRead", "subscribeToIncomingCalls", "subscribeToUserStatus", "query", "pipe", "data", "handleIncomingCall", "call", "play", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "loop", "sound", "currentTime", "catch", "stop", "pause", "stopAllSounds", "Object", "keys", "setMuted", "isMuted", "playNotificationSound", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "playNotificationMelody1", "volume", "err", "audioError", "playNotificationTone", "playNotificationMelody2", "playNotificationMelody3", "playNotificationMelody4", "playNotificationMelody5", "playBellTone", "startTime", "frequency", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "type", "setValueAtTime", "gain", "linearRampToValueAtTime", "connect", "destination", "start", "exponentialRampToValueAtTime", "playAudio", "audioUrl", "Promise", "resolve", "reject", "onended", "onerror", "getVoiceMessages", "debug", "watch<PERSON><PERSON>y", "fetchPolicy", "valueChanges", "result", "voiceMessages", "length", "Error", "getMessages", "senderId", "receiverId", "conversationId", "page", "variables", "errorPolicy", "messages", "batchNormalizeMessages", "editMessage", "messageId", "newContent", "mutate", "mutation", "normalizeMessage", "deleteMessage", "markMessageAsRead", "readAt", "Date", "reactToMessage", "emoji", "forwardMessage", "conversationIds", "msg", "timestamp", "normalizeDate", "pinMessage", "pinnedAt", "searchMessages", "filters", "dateFrom", "dateTo", "safeDate", "sender", "normalizeUser", "_id", "now", "content", "TEXT", "isRead", "getCurrentUserId", "username", "getUnreadMessages", "userId", "setActiveConversation", "getConversations", "conversations", "conv", "normalizeConversation", "getConversation", "info", "offset", "normalizedConversation", "participants", "createConversation", "conversation", "message", "getOrCreateConversation", "currentUserId", "existingConversation", "find", "isGroup", "participantIds", "p", "includes", "getNotifications", "refresh", "deletedNotificationIds", "getDeletedNotificationIds", "size", "errors", "e", "join", "getUserNotifications", "filteredNotifications", "notif", "has", "index", "updateCache", "cachedNotifications", "sortedNotifications", "sortNotificationsByDate", "saveNotificationsToLocalStorage", "graphQLErrors", "networkError", "deletedIds", "Set", "savedNotificationIds", "n", "serverNotifications", "client", "readQuery", "add", "loadMoreNotifications", "nextPage", "getNotificationById", "getNotificationCount", "value", "getNotificationAttachments", "notificationId", "getUnreadNotifications", "deleteNotification", "warn", "removedCount", "removeNotificationsFromCache", "response", "handleDeletionError", "success", "setItem", "stringify", "deleteAllNotifications", "count", "allNotificationIds", "deleteMultipleNotifications", "notificationIds", "groupNotificationsByType", "groups", "get", "push", "mark<PERSON><PERSON><PERSON>", "readCount", "remainingCount", "validIds", "trim", "provided", "valid", "updateNotificationStatus", "optimisticResponse", "markNotificationsAsRead", "Math", "max", "subscribeToCallSignals", "callId", "callSignal", "signal", "handleCallSignal", "sendCallSignal", "signalType", "signalData", "getCallHistory", "status", "startDate", "endDate", "history", "callHistory", "getCallDetails", "details", "callDetails", "getCallStats", "stats", "callStats", "handleIceCandidate", "handleAnswer", "handleEndCall", "handleRejectCall", "candidate", "addIceCandidate", "RTCIceCandidate", "answer", "setRemoteDescription", "RTCSessionDescription", "cleanupCall", "currentCall", "ENDED", "endTime", "REJECTED", "getTracks", "track", "close", "setupMediaDevices", "callType", "constraints", "video", "AUDIO", "width", "ideal", "height", "observer", "navigator", "mediaDevices", "getUserMedia", "then", "stream", "complete", "generateCallId", "toString", "random", "substring", "getAllUsers", "forceRefresh", "search", "sortBy", "sortOrder", "isOnline", "cacheValid", "paginatedResponse", "users", "user", "getOneUser", "getCurrentUser", "setUserOnline", "setUserOffline", "createGroup", "photo", "description", "group", "updateGroup", "groupId", "input", "deleteGroup", "leaveGroup", "getGroup", "getUserGroups", "subscribeToNewMessages", "cache<PERSON>ey", "refCount", "isTokenValid", "environment", "production", "sub$", "messageSent", "normalizedMessage", "VOICE_MESSAGE", "attachments", "some", "att", "run", "updateConversationWithNewMessage", "minimalMessage", "sub", "setTimeout", "refreshSenderNotifications", "userStatusChanged", "subscribeToConversationUpdates", "conversationUpdated", "lastMessage", "subscribeToTypingIndicator", "typingIndicator", "Boolean", "token", "parts", "split", "payload", "atob", "exp", "expirationDate", "expiration", "notificationsRead", "source$", "processed$", "notificationReceived", "normalized", "normalizeNotification", "currentNotifications", "existingNotification", "updateNotificationCache", "updatedNotifications", "cleanupInterval", "setInterval", "cleanupExpiredNotifications", "thirtyDaysAgo", "getTime", "expiredCount", "notificationDate", "delete", "remainingNotifications", "sort", "a", "b", "dateA", "dateB", "normalizedSender", "email", "role", "isActive", "normalizedReceiver", "receiver", "normalizedAttachments", "url", "metadata", "String", "image", "bio", "lastActive", "createdAt", "updatedAt", "followingCount", "followersCount", "postCount", "normalizedParticipants", "isArray", "participant", "normalizedMessages", "normalizedLastMessage", "unreadCount", "participantCount", "messageCount", "normalizeSender", "normalizeNotMessage", "skipDuplicates", "notificationArray", "validNotifications", "addedCount", "skippedCount", "notifId", "refreshNotificationObservables", "allNotifications", "unreadNotifications", "dispatchEvent", "CustomEvent", "detail", "ids", "operation", "fallbackResponse", "startTyping", "stopTyping", "sendMessage", "file", "messageType", "hasFile", "fileName", "fileType", "fileSize", "context", "useMultipart", "hasAttachments", "errorMessage", "formatMessageTime", "toLocaleTimeString", "hour", "minute", "hour12", "formatLastActive", "lastActiveDate", "diffHours", "abs", "toLocaleDateString", "formatMessageDate", "today", "toDateString", "yesterday", "setDate", "getDate", "day", "weekday", "toUpperCase", "shouldShowDateHeader", "currentMsg", "prevMsg", "currentDate", "getDateFromTimestamp", "prevDate", "getFileIcon", "mimeType", "startsWith", "getFileType", "typeMap", "key", "entries", "hasImage", "attachment", "isVoiceMessage", "getVoiceMessageUrl", "voiceAttachment", "getVoiceMessageDuration", "getVoiceBarHeight", "pattern", "formatVoiceDuration", "seconds", "minutes", "floor", "remainingSeconds", "padStart", "getImageUrl", "getMessageType", "msgType", "IMAGE", "FILE", "VIDEO", "SYSTEM", "attachmentTypeStr", "getCommonEmojis", "getMessageTypeClass", "isCurrentUser", "baseClass", "cleanupSubscriptions", "unsubscribe", "clearInterval", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\message.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';\nimport { <PERSON> } from 'apollo-angular';\nimport {\n  BehaviorSubject,\n  Observable,\n  of,\n  Subscription,\n  throwError,\n  retry,\n  EMPTY,\n} from 'rxjs';\nimport {\n  map,\n  catchError,\n  tap,\n  filter,\n  switchMap,\n  concatMap,\n  toArray,\n} from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport {\n  MessageType,\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSignal,\n  CallOptions,\n  CallFeedback,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  GET_CONVERSATIONS_QUERY,\n  GET_NOTIFICATIONS_QUERY,\n  NOTIFICATION_SUBSCRIPTION,\n  GET_CONVERSATION_QUERY,\n  SEND_MESSAGE_MUTATION,\n  MARK_AS_READ_MUTATION,\n  MESSAGE_SENT_SUBSCRIPTION,\n  USER_STATUS_SUBSCRIPTION,\n  GET_USER_QUERY,\n  GET_ALL_USER_QUERY,\n  CONVERSATION_UPDATED_SUBSCRIPTION,\n  SEARCH_MESSAGES_QUERY,\n  GET_UNREAD_MESSAGES_QUERY,\n  SET_USER_ONLINE_MUTATION,\n  SET_USER_OFFLINE_MUTATION,\n  START_TYPING_MUTATION,\n  STOP_TYPING_MUTATION,\n  TYPING_INDICATOR_SUBSCRIPTION,\n  GET_CURRENT_USER_QUERY,\n  REACT_TO_MESSAGE_MUTATION,\n  FORWARD_MESSAGE_MUTATION,\n  PIN_MESSAGE_MUTATION,\n  CREATE_GROUP_MUTATION,\n  UPDATE_GROUP_MUTATION,\n  DELETE_GROUP_MUTATION,\n  ADD_GROUP_PARTICIPANTS_MUTATION,\n  REMOVE_GROUP_PARTICIPANTS_MUTATION,\n  LEAVE_GROUP_MUTATION,\n  GET_GROUP_QUERY,\n  GET_USER_GROUPS_QUERY,\n  EDIT_MESSAGE_MUTATION,\n  DELETE_MESSAGE_MUTATION,\n  GET_MESSAGES_QUERY,\n  GET_NOTIFICATIONS_ATTACHAMENTS,\n  MARK_NOTIFICATION_READ_MUTATION,\n  NOTIFICATIONS_READ_SUBSCRIPTION,\n  CREATE_CONVERSATION_MUTATION,\n  DELETE_NOTIFICATION_MUTATION,\n  DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n  DELETE_ALL_NOTIFICATIONS_MUTATION,\n  // Requêtes et mutations pour les appels\n  CALL_HISTORY_QUERY,\n  CALL_DETAILS_QUERY,\n  CALL_STATS_QUERY,\n  INITIATE_CALL_MUTATION,\n  SEND_CALL_SIGNAL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  GET_VOICE_MESSAGES_QUERY,\n} from '../graphql/message.graphql';\nimport {\n  Conversation,\n  Message,\n  Notification,\n  User,\n  Attachment,\n  getNotificationAttachmentsEvent,\n  Group,\n  MessageFilter,\n  TypingIndicatorEvent,\n  GetConversationsResponse,\n  GetConversationResponse,\n  MarkAsReadResponse,\n  ReactToMessageResponse,\n  ForwardMessageResponse,\n  PinMessageResponse,\n  SearchMessagesResponse,\n  SendMessageResponse,\n  GetUnreadMessagesResponse,\n  GetAllUsersResponse,\n  GetOneUserResponse,\n  getCurrentUserResponse,\n  SetUserOnlineResponse,\n  SetUserOfflineResponse,\n  GetGroupResponse,\n  GetUserGroupsResponse,\n  CreateGroupResponse,\n  UpdateGroupResponse,\n  StartTupingResponse,\n  StopTypingResponse,\n  TypingIndicatorEvents,\n  getUserNotificationsResponse,\n  NotificationType,\n  MarkNotificationsAsReadResponse,\n  NotificationReceivedEvent,\n  NotificationsReadEvent,\n} from '../models/message.model';\nimport { LoggerService } from './logger.service';\n@Injectable({\n  providedIn: 'root',\n})\nexport class MessageService implements OnDestroy {\n  // État partagé\n  private activeConversation = new BehaviorSubject<string | null>(null);\n  private notifications = new BehaviorSubject<Notification[]>([]);\n  private notificationCache = new Map<string, Notification>();\n  private cleanupInterval: any;\n  private notificationCount = new BehaviorSubject<number>(0);\n  private onlineUsers = new Map<string, User>();\n  private subscriptions: Subscription[] = [];\n  private readonly CACHE_DURATION = 300000;\n  private lastFetchTime = 0;\n\n  // Propriétés pour les appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private peerConnection: RTCPeerConnection | null = null;\n\n  // Observables publics pour les appels\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n  public localStream$ = new BehaviorSubject<MediaStream | null>(null);\n  public remoteStream$ = new BehaviorSubject<MediaStream | null>(null);\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n  private usersCache: User[] = [];\n\n  // Pagination metadata for user list\n  public currentUserPagination: {\n    totalCount: number;\n    totalPages: number;\n    currentPage: number;\n    hasNextPage: boolean;\n    hasPreviousPage: boolean;\n  } = {\n    totalCount: 0,\n    totalPages: 0,\n    currentPage: 1,\n    hasNextPage: false,\n    hasPreviousPage: false,\n  };\n\n  // Observables publics\n  public activeConversation$ = this.activeConversation.asObservable();\n  public notifications$ = this.notifications.asObservable();\n  public notificationCount$ = this.notificationCount.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  constructor(\n    private apollo: Apollo,\n    private logger: LoggerService,\n    private zone: NgZone\n  ) {\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  private loadNotificationsFromLocalStorage(): void {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications) as Notification[];\n\n        this.notificationCache.clear();\n\n        notifications.forEach((notification) => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  private initSubscriptions(): void {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n      // 🔥 AJOUT: Subscription générale pour l'utilisateur\n    });\n    this.subscribeToUserStatus();\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): Observable<IncomingCall | null> {\n    return this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.incomingCall) {\n            return null;\n          }\n\n          // Gérer l'appel entrant\n          this.handleIncomingCall(data.incomingCall);\n          return data.incomingCall;\n        }),\n        catchError((error) => {\n          this.logger.error('Error in incoming call subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      return;\n    }\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      sound.loop = loop;\n\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch((error) => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted: boolean): void {\n    this.muted = muted;\n\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted(): boolean {\n    return this.muted;\n  }\n\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound(): void {\n    console.log('MessageService: Tentative de lecture du son de notification');\n\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n\n    // Créer une mélodie agréable avec l'API Web Audio\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n\n      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n      this.playNotificationMelody1(audioContext);\n\n      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n      // this.playNotificationMelody2(audioContext);\n\n      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n      // this.playNotificationMelody3(audioContext);\n\n      // SON 4: Triple note (Discord style) - Décommentez pour tester\n      // this.playNotificationMelody4(audioContext);\n\n      // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n      // this.playNotificationMelody5(audioContext);\n\n      console.log(\n        'MessageService: Son de notification mélodieux généré avec succès'\n      );\n    } catch (error) {\n      console.error(\n        'MessageService: Erreur lors de la génération du son:',\n        error\n      );\n\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 0.7; // Volume plus doux\n        audio.play().catch((err) => {\n          console.error(\n            'MessageService: Erreur lors de la lecture du fichier son:',\n            err\n          );\n        });\n      } catch (audioError) {\n        console.error(\n          'MessageService: Exception lors de la lecture du fichier son:',\n          audioError\n        );\n      }\n    }\n  }\n\n  // 🎵 SON 1: Mélodie douce (WhatsApp style)\n  private playNotificationMelody1(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n  }\n\n  // 🎵 SON 2: Mélodie montante (iPhone style)\n  private playNotificationMelody2(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n  }\n\n  // 🎵 SON 3: Mélodie descendante (Messenger style)\n  private playNotificationMelody3(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n  }\n\n  // 🎵 SON 4: Triple note (Discord style)\n  private playNotificationMelody4(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n  }\n\n  // 🎵 SON 5: Cloche douce (Slack style)\n  private playNotificationMelody5(audioContext: AudioContext): void {\n    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n  }\n\n  /**\n   * Joue une note individuelle pour la mélodie de notification\n   */\n  private playNotificationTone(\n    audioContext: AudioContext,\n    startTime: number,\n    frequency: number,\n    duration: number\n  ): void {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    // Configurer l'oscillateur pour un son plus doux\n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(\n      frequency,\n      audioContext.currentTime + startTime\n    );\n\n    // Configurer le volume avec une enveloppe douce\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(\n      0.3,\n      audioContext.currentTime + startTime + 0.02\n    );\n    gainNode.gain.linearRampToValueAtTime(\n      0.2,\n      audioContext.currentTime + startTime + duration * 0.7\n    );\n    gainNode.gain.linearRampToValueAtTime(\n      0,\n      audioContext.currentTime + startTime + duration\n    );\n\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n\n  /**\n   * Joue un son de cloche pour les notifications\n   */\n  private playBellTone(\n    audioContext: AudioContext,\n    startTime: number,\n    frequency: number,\n    duration: number\n  ): void {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    // Configurer l'oscillateur pour un son de cloche\n    oscillator.type = 'triangle'; // Son plus doux que sine\n    oscillator.frequency.setValueAtTime(\n      frequency,\n      audioContext.currentTime + startTime\n    );\n\n    // Enveloppe de cloche (attaque rapide, déclin lent)\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(\n      0.4,\n      audioContext.currentTime + startTime + 0.01\n    );\n    gainNode.gain.exponentialRampToValueAtTime(\n      0.01,\n      audioContext.currentTime + startTime + duration\n    );\n\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n\n      audio.onended = () => {\n        resolve();\n      };\n\n      audio.onerror = (error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n\n      audio.play().catch((error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages(): Observable<Call[]> {\n    this.logger.debug('[MessageService] Getting voice messages');\n\n    return this.apollo\n      .watchQuery<{ getVoiceMessages: Call[] }>({\n        query: GET_VOICE_MESSAGES_QUERY,\n        fetchPolicy: 'network-only', // Ne pas utiliser le cache pour cette requête\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const voiceMessages = result.data?.getVoiceMessages || [];\n          this.logger.debug(\n            `[MessageService] Retrieved ${voiceMessages.length} voice messages`\n          );\n          return voiceMessages;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            '[MessageService] Error fetching voice messages:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch voice messages'));\n        })\n      );\n  }\n  // Message methods\n  getMessages(\n    senderId: string,\n    receiverId: string,\n    conversationId: string,\n    page: number = 1,\n    limit: number = 25 // ✅ Increased batch size for better performance\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<{ getMessages: Message[] }>({\n        query: GET_MESSAGES_QUERY,\n        variables: { senderId, receiverId, conversationId, limit, page },\n        fetchPolicy: 'cache-first', // ✅ Use cache for better performance\n        errorPolicy: 'all', // ✅ Handle partial errors gracefully\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const messages = result.data?.getMessages || [];\n          // ✅ Batch normalize messages for better performance\n          return this.batchNormalizeMessages(messages);\n        }),\n        catchError((error) => {\n          console.error('Error fetching messages:', error);\n          return throwError(() => new Error('Failed to fetch messages'));\n        })\n      );\n  }\n  editMessage(messageId: string, newContent: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ editMessage: Message }>({\n        mutation: EDIT_MESSAGE_MUTATION,\n        variables: { messageId, newContent },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.editMessage) {\n            throw new Error('Failed to edit message');\n          }\n          return this.normalizeMessage(result.data.editMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error editing message:', error);\n          return throwError(() => new Error('Failed to edit message'));\n        })\n      );\n  }\n\n  deleteMessage(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ deleteMessage: Message }>({\n        mutation: DELETE_MESSAGE_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.deleteMessage) {\n            throw new Error('Failed to delete message');\n          }\n          return this.normalizeMessage(result.data.deleteMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error deleting message:', error);\n          return throwError(() => new Error('Failed to delete message'));\n        })\n      );\n  }\n\n  markMessageAsRead(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<MarkAsReadResponse>({\n        mutation: MARK_AS_READ_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.markMessageAsRead)\n            throw new Error('Failed to mark message as read');\n          return {\n            ...result.data.markMessageAsRead,\n            readAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error marking message as read:', error);\n          return throwError(() => new Error('Failed to mark message as read'));\n        })\n      );\n  }\n\n  reactToMessage(messageId: string, emoji: string): Observable<Message> {\n    return this.apollo\n      .mutate<ReactToMessageResponse>({\n        mutation: REACT_TO_MESSAGE_MUTATION,\n        variables: { messageId, emoji },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.reactToMessage)\n            throw new Error('Failed to react to message');\n          return result.data.reactToMessage;\n        }),\n        catchError((error) => {\n          console.error('Error reacting to message:', error);\n          return throwError(() => new Error('Failed to react to message'));\n        })\n      );\n  }\n\n  forwardMessage(\n    messageId: string,\n    conversationIds: string[]\n  ): Observable<Message[]> {\n    return this.apollo\n      .mutate<ForwardMessageResponse>({\n        mutation: FORWARD_MESSAGE_MUTATION,\n        variables: { messageId, conversationIds },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.forwardMessage)\n            throw new Error('Failed to forward message');\n          return result.data.forwardMessage.map((msg) => ({\n            ...msg,\n            timestamp: msg.timestamp\n              ? this.normalizeDate(msg.timestamp)\n              : new Date(),\n          }));\n        }),\n        catchError((error) => {\n          console.error('Error forwarding message:', error);\n          return throwError(() => new Error('Failed to forward message'));\n        })\n      );\n  }\n\n  pinMessage(messageId: string, conversationId: string): Observable<Message> {\n    return this.apollo\n      .mutate<PinMessageResponse>({\n        mutation: PIN_MESSAGE_MUTATION,\n        variables: { messageId, conversationId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.pinMessage)\n            throw new Error('Failed to pin message');\n          return {\n            ...result.data.pinMessage,\n            pinnedAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error pinning message:', error);\n          return throwError(() => new Error('Failed to pin message'));\n        })\n      );\n  }\n\n  searchMessages(\n    query: string,\n    conversationId?: string,\n    filters: MessageFilter = {}\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<SearchMessagesResponse>({\n        query: SEARCH_MESSAGES_QUERY,\n        variables: {\n          query,\n          conversationId,\n          ...filters,\n          dateFrom: this.toSafeISOString(filters.dateFrom),\n          dateTo: this.toSafeISOString(filters.dateTo),\n        },\n        fetchPolicy: 'cache-first', // ✅ Use cache for better performance\n        errorPolicy: 'all',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.searchMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error searching messages:', error);\n          return throwError(() => new Error('Failed to search messages'));\n        })\n      );\n  }\n\n  // ✅ Batch normalization for better performance\n  private batchNormalizeMessages(messages: any[]): Message[] {\n    if (!messages || messages.length === 0) return [];\n\n    return messages.map((msg) => {\n      try {\n        return this.normalizeMessage(msg);\n      } catch (error) {\n        console.error('Error normalizing message:', error);\n        // Return minimal valid message on error\n        return {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender\n            ? this.normalizeUser(msg.sender)\n            : {\n                id: this.getCurrentUserId(),\n                username: 'Unknown',\n              },\n        } as Message;\n      }\n    });\n  }\n\n  getUnreadMessages(userId: string): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<GetUnreadMessagesResponse>({\n        query: GET_UNREAD_MESSAGES_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.getUnreadMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error fetching unread messages:', error);\n          return throwError(() => new Error('Failed to fetch unread messages'));\n        })\n      );\n  }\n\n  setActiveConversation(conversationId: string): void {\n    this.activeConversation.next(conversationId);\n  }\n\n  getConversations(): Observable<Conversation[]> {\n    return this.apollo\n      .watchQuery<GetConversationsResponse>({\n        query: GET_CONVERSATIONS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const conversations = result.data?.getConversations || [];\n          return conversations.map((conv) => this.normalizeConversation(conv));\n        }),\n        catchError((error) => {\n          console.error('Error fetching conversations:', error);\n          return throwError(() => new Error('Failed to load conversations'));\n        })\n      );\n  }\n\n  getConversation(\n    conversationId: string,\n    limit?: number,\n    page?: number\n  ): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`\n    );\n\n    const variables: any = { conversationId };\n\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(\n        `[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`\n      );\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(\n      `[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`\n    );\n\n    return this.apollo\n      .watchQuery<GetConversationResponse>({\n        query: GET_CONVERSATION_QUERY,\n        variables: variables,\n        fetchPolicy: 'network-only',\n        errorPolicy: 'all',\n      })\n      .valueChanges.pipe(\n        retry(2), // Réessayer 2 fois en cas d'erreur\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation response received:`,\n            result\n          );\n\n          const conv = result.data?.getConversation;\n          if (!conv) {\n            this.logger.error(\n              `[MessageService] Conversation not found: ${conversationId}`\n            );\n            throw new Error('Conversation not found');\n          }\n\n          this.logger.debug(\n            `[MessageService] Normalizing conversation: ${conversationId}`\n          );\n          const normalizedConversation = this.normalizeConversation(conv);\n\n          this.logger.info(\n            `[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${\n              normalizedConversation.participants?.length || 0\n            }, messages: ${normalizedConversation.messages?.length || 0}`\n          );\n          return normalizedConversation;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error fetching conversation:`,\n            error\n          );\n          return throwError(() => new Error('Failed to load conversation'));\n        })\n      );\n  }\n\n  createConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to create a conversation')\n      );\n    }\n\n    return this.apollo\n      .mutate<{ createConversation: Conversation }>({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation creation response:`,\n            result\n          );\n\n          const conversation = result.data?.createConversation;\n          if (!conversation) {\n            this.logger.error(\n              `[MessageService] Failed to create conversation with user: ${userId}`\n            );\n            throw new Error('Failed to create conversation');\n          }\n\n          try {\n            const normalizedConversation =\n              this.normalizeConversation(conversation);\n            this.logger.info(\n              `[MessageService] Conversation created successfully: ${normalizedConversation.id}`\n            );\n            return normalizedConversation;\n          } catch (error) {\n            this.logger.error(\n              `[MessageService] Error normalizing created conversation:`,\n              error\n            );\n            throw new Error('Error processing created conversation');\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error creating conversation with user ${userId}:`,\n            error\n          );\n          return throwError(\n            () => new Error(`Failed to create conversation: ${error.message}`)\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting or creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot get/create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to get/create a conversation')\n      );\n    }\n\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(\n      map((conversations) => {\n        // Récupérer l'ID de l'utilisateur actuel\n        const currentUserId = this.getCurrentUserId();\n\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n        const existingConversation = conversations.find((conv) => {\n          if (conv.isGroup) return false;\n\n          // Vérifier si la conversation contient les deux utilisateurs\n          const participantIds =\n            conv.participants?.map((p) => p.id || p._id) || [];\n          return (\n            participantIds.includes(userId) &&\n            participantIds.includes(currentUserId)\n          );\n        });\n\n        if (existingConversation) {\n          this.logger.info(\n            `[MessageService] Found existing conversation: ${existingConversation.id}`\n          );\n          return existingConversation;\n        }\n\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\n        throw new Error('No existing conversation found');\n      }),\n      catchError((error) => {\n        this.logger.info(\n          `[MessageService] No existing conversation found, creating new one: ${error.message}`\n        );\n        return this.createConversation(userId);\n      })\n    );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 2: Méthodes pour les Notifications\n  // --------------------------------------------------------------------------\n  // Propriétés pour la pagination des notifications\n  private notificationPagination = {\n    currentPage: 1,\n    limit: 10,\n    hasMoreNotifications: true,\n  };\n\n  getNotifications(\n    refresh = false,\n    page = 1,\n    limit = 10\n  ): Observable<Notification[]> {\n    this.logger.info(\n      'MessageService',\n      `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`\n    );\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY,\n    });\n\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug(\n        'MessageService',\n        'Resetting pagination due to refresh'\n      );\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug(\n      'MessageService',\n      `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`\n    );\n\n    return this.apollo\n      .watchQuery<getUserNotificationsResponse>({\n        query: GET_NOTIFICATIONS_QUERY,\n        variables: {\n          page: page,\n          limit: limit,\n        },\n        fetchPolicy: refresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Notifications response received'\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          const notifications = result.data?.getUserNotifications || [];\n          this.logger.debug(\n            'MessageService',\n            `Received ${notifications.length} notifications from server for page ${page}`\n          );\n\n          // Vérifier s'il y a plus de notifications à charger\n          this.notificationPagination.hasMoreNotifications =\n            notifications.length >= limit;\n\n          if (notifications.length === 0) {\n            this.logger.info(\n              'MessageService',\n              'No notifications received from server'\n            );\n            this.notificationPagination.hasMoreNotifications = false;\n          }\n\n          // Filtrer les notifications supprimées\n          const filteredNotifications = notifications.filter(\n            (notif) => !deletedNotificationIds.has(notif.id)\n          );\n\n          this.logger.debug(\n            'MessageService',\n            `Filtered out ${\n              notifications.length - filteredNotifications.length\n            } deleted notifications`\n          );\n\n          // Afficher les notifications reçues pour le débogage\n          filteredNotifications.forEach((notif, index) => {\n            console.log(`Notification ${index + 1} (page ${page}):`, {\n              id: notif.id || (notif as any)._id,\n              type: notif.type,\n              content: notif.content,\n              isRead: notif.isRead,\n            });\n          });\n\n          // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n          // Mettre à jour le cache avec les nouvelles notifications\n          this.updateCache(filteredNotifications);\n\n          // Récupérer toutes les notifications du cache et les TRIER\n          const cachedNotifications = Array.from(\n            this.notificationCache.values()\n          );\n\n          // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n          const sortedNotifications =\n            this.sortNotificationsByDate(cachedNotifications);\n\n          console.log(\n            `📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`\n          );\n\n          // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n          this.notifications.next(sortedNotifications);\n\n          // Mettre à jour le compteur de notifications non lues\n          this.updateUnreadCount();\n\n          // Sauvegarder les notifications dans le localStorage\n          this.saveNotificationsToLocalStorage();\n\n          return cachedNotifications;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error loading notifications:',\n            error\n          );\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          return throwError(() => new Error('Failed to load notifications'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  private getDeletedNotificationIds(): Set<string> {\n    try {\n      const deletedIds = new Set<string>();\n      const savedNotifications = localStorage.getItem('notifications');\n\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(\n        JSON.parse(savedNotifications).map((n: Notification) => n.id)\n      );\n\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications =\n        this.apollo.client.readQuery<getUserNotificationsResponse>({\n          query: GET_NOTIFICATIONS_QUERY,\n        })?.getUserNotifications || [];\n\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach((notification) => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n\n      return deletedIds;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\n        error\n      );\n      return new Set<string>();\n    }\n  }\n\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications(): boolean {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications(): Observable<Notification[]> {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(\n      false,\n      nextPage,\n      this.notificationPagination.limit\n    );\n  }\n  getNotificationById(id: string): Observable<Notification | undefined> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.find((n) => n.id === id)),\n      catchError((error) => {\n        this.logger.error('Error finding notification:', error);\n        return throwError(() => new Error('Failed to find notification'));\n      })\n    );\n  }\n  getNotificationCount(): number {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId: string): Observable<Attachment[]> {\n    return this.apollo\n      .query<getNotificationAttachmentsEvent>({\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\n        variables: { id: notificationId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result) => result.data?.getNotificationAttachments || []),\n        catchError((error) => {\n          this.logger.error('Error fetching notification attachments:', error);\n          return throwError(() => new Error('Failed to fetch attachments'));\n        })\n      );\n  }\n  getUnreadNotifications(): Observable<Notification[]> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.filter((n) => !n.isRead))\n    );\n  }\n\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(\n    notificationId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de la notification ${notificationId}`\n    );\n\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n\n    // Appeler le backend pour supprimer la notification\n    return this.apollo\n      .mutate<{ deleteNotification: { success: boolean; message: string } }>({\n        mutation: DELETE_NOTIFICATION_MUTATION,\n        variables: { notificationId },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteNotification;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(error, 'la suppression de la notification', {\n            success: true,\n            message: 'Notification supprimée localement (erreur serveur)',\n          })\n        )\n      );\n  }\n\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  private saveNotificationsToLocalStorage(): void {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug(\n        'MessageService',\n        'Notifications sauvegardées localement'\n      );\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la sauvegarde des notifications:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications(): Observable<{\n    success: boolean;\n    count: number;\n    message: string;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      'Suppression de toutes les notifications'\n    );\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo\n      .mutate<{\n        deleteAllNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION,\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteAllNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression de toutes les notifications:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression de toutes les notifications',\n            {\n              success: true,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(\n    notificationIds: string[]\n  ): Observable<{ success: boolean; count: number; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de ${notificationIds.length} notifications`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo\n      .mutate<{\n        deleteMultipleNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n        variables: { notificationIds },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteMultipleNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression multiple:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression multiple de notifications',\n            {\n              success: count > 0,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n  groupNotificationsByType(): Observable<\n    Map<NotificationType, Notification[]>\n  > {\n    return this.notifications$.pipe(\n      map((notifications) => {\n        const groups = new Map<NotificationType, Notification[]>();\n        notifications.forEach((notif) => {\n          if (!groups.has(notif.type)) {\n            groups.set(notif.type, []);\n          }\n          groups.get(notif.type)?.push(notif);\n        });\n        return groups;\n      })\n    );\n  }\n  markAsRead(notificationIds: string[]): Observable<{\n    success: boolean;\n    readCount: number;\n    remainingCount: number;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value,\n      });\n    }\n\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(\n      (id) => id && typeof id === 'string' && id.trim() !== ''\n    );\n\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds,\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n\n    this.logger.debug(\n      'MessageService',\n      'Sending mutation to mark notifications as read',\n      validIds\n    );\n\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(\n          0,\n          this.notificationCount.value - validIds.length\n        ),\n      },\n    };\n\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds,\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n\n    return this.apollo\n      .mutate<MarkNotificationsAsReadResponse>({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: { notificationIds: validIds },\n        optimisticResponse: optimisticResponse,\n        errorPolicy: 'all', // Continuer même en cas d'erreur\n        fetchPolicy: 'no-cache', // Ne pas utiliser le cache pour cette mutation\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug('MessageService', 'Mutation result', result);\n          console.log('Mutation result:', result);\n\n          // Si nous avons des erreurs GraphQL, les logger mais continuer\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            console.error('GraphQL errors:', result.errors);\n          }\n\n          // Utiliser la réponse du serveur ou notre réponse optimiste\n          const response =\n            result.data?.markNotificationsAsRead ??\n            optimisticResponse.markNotificationsAsRead;\n\n          return response;\n        }),\n        catchError((error: Error) => {\n          this.logger.error(\n            'MessageService',\n            'Error marking notifications as read:',\n            error\n          );\n          console.error('Error in markAsRead:', error);\n\n          // En cas d'erreur, retourner quand même un succès simulé\n          // puisque nous avons déjà mis à jour l'interface utilisateur\n          return of({\n            success: true,\n            readCount: validIds.length,\n            remainingCount: Math.max(\n              0,\n              this.notificationCount.value - validIds.length\n            ),\n          });\n        })\n      );\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels (SUPPRIMÉES - VOIR SECTION À LA FIN)\n  // --------------------------------------------------------------------------\n\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId: string): Observable<CallSignal> {\n    return this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: { callId },\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.callSignal) {\n            throw new Error('No call signal received');\n          }\n          return data.callSignal;\n        }),\n        tap((signal) => {\n          this.callSignals.next(signal);\n          this.handleCallSignal(signal);\n        }),\n        catchError((error) => {\n          this.logger.error('Error in call signal subscription', error);\n          return throwError(() => new Error('Call signal subscription failed'));\n        })\n      );\n  }\n\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(\n    callId: string,\n    signalType: string,\n    signalData: string\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ sendCallSignal: CallSuccess }>({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId,\n          signalType,\n          signalData,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.sendCallSignal;\n          if (!success) {\n            throw new Error('Failed to send call signal');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error sending call signal', error);\n          return throwError(() => new Error('Failed to send call signal'));\n        })\n      );\n  }\n\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(\n    limit: number = 20,\n    offset: number = 0,\n    status?: string[],\n    type?: string[],\n    startDate?: string | null,\n    endDate?: string | null\n  ): Observable<Call[]> {\n    return this.apollo\n      .watchQuery<{ callHistory: Call[] }>({\n        query: CALL_HISTORY_QUERY,\n        variables: {\n          limit,\n          offset,\n          status,\n          type,\n          startDate,\n          endDate,\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const history = result.data?.callHistory || [];\n          this.logger.debug(`Retrieved ${history.length} call history items`);\n          return history;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call history:', error);\n          return throwError(() => new Error('Failed to fetch call history'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId: string): Observable<Call> {\n    return this.apollo\n      .watchQuery<{ callDetails: Call }>({\n        query: CALL_DETAILS_QUERY,\n        variables: { callId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const details = result.data?.callDetails;\n          if (!details) {\n            throw new Error('Call details not found');\n          }\n          this.logger.debug(`Retrieved call details for: ${callId}`);\n          return details;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call details:', error);\n          return throwError(() => new Error('Failed to fetch call details'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats(): Observable<any> {\n    return this.apollo\n      .watchQuery<{ callStats: any }>({\n        query: CALL_STATS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const stats = result.data?.callStats;\n          if (!stats) {\n            throw new Error('Call stats not found');\n          }\n          this.logger.debug('Retrieved call stats:', stats);\n          return stats;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call stats:', error);\n          return throwError(() => new Error('Failed to fetch call stats'));\n        })\n      );\n  }\n\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  private handleCallSignal(signal: CallSignal): void {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  private handleIceCandidate(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection\n        .addIceCandidate(new RTCIceCandidate(candidate))\n        .catch((error) => {\n          this.logger.error('Error adding ICE candidate', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error as Error);\n    }\n  }\n\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  private handleAnswer(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection\n        .setRemoteDescription(new RTCSessionDescription(answer))\n        .catch((error) => {\n          this.logger.error('Error setting remote description', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error as Error);\n    }\n  }\n\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  private handleEndCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  private handleRejectCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Nettoie les ressources d'appel\n   */\n  private cleanupCall(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  private setupMediaDevices(callType: CallType): Observable<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video:\n        callType !== CallType.AUDIO\n          ? {\n              width: { ideal: 1280 },\n              height: { ideal: 720 },\n            }\n          : false,\n    };\n\n    return new Observable<MediaStream>((observer) => {\n      navigator.mediaDevices\n        .getUserMedia(constraints)\n        .then((stream) => {\n          observer.next(stream);\n          observer.complete();\n        })\n        .catch((error) => {\n          this.logger.error('Error accessing media devices', error);\n          observer.error(new Error('Failed to access media devices'));\n        });\n    });\n  }\n\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  private generateCallId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(\n    forceRefresh = false,\n    search?: string,\n    page: number = 1,\n    limit: number = 10,\n    sortBy: string = 'username',\n    sortOrder: string = 'asc',\n    isOnline?: boolean\n  ): Observable<User[]> {\n    this.logger.info(\n      'MessageService',\n      `Getting users with params: forceRefresh=${forceRefresh}, search=${\n        search || '(empty)'\n      }, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`\n    );\n\n    const now = Date.now();\n    const cacheValid =\n      !forceRefresh &&\n      this.usersCache.length > 0 &&\n      now - this.lastFetchTime <= this.CACHE_DURATION &&\n      !search &&\n      page === 1 &&\n      limit >= this.usersCache.length;\n\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug(\n        'MessageService',\n        `Using cached users (${this.usersCache.length} users)`\n      );\n      return of([...this.usersCache]);\n    }\n\n    this.logger.debug(\n      'MessageService',\n      `Fetching users from server with pagination, fetchPolicy=${\n        forceRefresh ? 'network-only' : 'cache-first'\n      }`\n    );\n\n    return this.apollo\n      .watchQuery<any>({\n        query: GET_ALL_USER_QUERY,\n        variables: {\n          search,\n          page,\n          limit,\n          sortBy,\n          sortOrder,\n          isOnline: isOnline !== undefined ? isOnline : null,\n        },\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Users response received',\n            result\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors in getAllUsers:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          if (!result.data?.getAllUsers) {\n            this.logger.warn(\n              'MessageService',\n              'No users data received from server'\n            );\n            return [];\n          }\n\n          const paginatedResponse = result.data.getAllUsers;\n\n          // Log pagination metadata\n          this.logger.debug('MessageService', 'Pagination metadata:', {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          });\n\n          // Normalize users with error handling\n          const users: User[] = [];\n          for (const user of paginatedResponse.users) {\n            try {\n              if (user) {\n                users.push(this.normalizeUser(user));\n              }\n            } catch (error) {\n              this.logger.warn(\n                'MessageService',\n                `Error normalizing user, skipping:`,\n                error\n              );\n            }\n          }\n\n          this.logger.info(\n            'MessageService',\n            `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`\n          );\n\n          // Update cache only for first page with no filters\n          if (!search && page === 1 && !isOnline) {\n            this.usersCache = [...users];\n            this.lastFetchTime = Date.now();\n            this.logger.debug(\n              'MessageService',\n              `User cache updated with ${users.length} users`\n            );\n          }\n\n          // Store pagination metadata in a property for component access\n          this.currentUserPagination = {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          };\n\n          return users;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching users:', error);\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          // Return cache if available (only for first page)\n          if (\n            this.usersCache.length > 0 &&\n            page === 1 &&\n            !search &&\n            !isOnline\n          ) {\n            this.logger.warn(\n              'MessageService',\n              `Returning ${this.usersCache.length} cached users due to fetch error`\n            );\n            return of([...this.usersCache]);\n          }\n\n          return throwError(\n            () =>\n              new Error(\n                `Failed to fetch users: ${error.message || 'Unknown error'}`\n              )\n          );\n        })\n      );\n  }\n  getOneUser(userId: string): Observable<User> {\n    return this.apollo\n      .watchQuery<GetOneUserResponse>({\n        query: GET_USER_QUERY,\n        variables: { id: userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getOneUser)),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching user:', error);\n          return throwError(() => new Error('Failed to fetch user'));\n        })\n      );\n  }\n  getCurrentUser(): Observable<User> {\n    return this.apollo\n      .watchQuery<getCurrentUserResponse>({\n        query: GET_CURRENT_USER_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getCurrentUser)),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error fetching current user:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch current user'));\n        })\n      );\n  }\n  setUserOnline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOnlineResponse>({\n        mutation: SET_USER_ONLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOnline)\n            throw new Error('Failed to set user online');\n          return this.normalizeUser(result.data.setUserOnline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user online:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user online'));\n        })\n      );\n  }\n  setUserOffline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOfflineResponse>({\n        mutation: SET_USER_OFFLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOffline)\n            throw new Error('Failed to set user offline');\n          return this.normalizeUser(result.data.setUserOffline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user offline:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user offline'));\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(\n    name: string,\n    participantIds: string[],\n    photo?: File,\n    description?: string\n  ): Observable<any> {\n    this.logger.debug(\n      'MessageService',\n      `Creating group: ${name} with ${participantIds.length} participants`\n    );\n\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(\n        () => new Error('Nom du groupe et participants requis')\n      );\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: CREATE_GROUP_MUTATION,\n        variables: { name, participantIds, photo, description },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.createGroup;\n          if (!group) {\n            throw new Error('Échec de la création du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group created successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error creating group:', error);\n          return throwError(() => new Error('Échec de la création du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId: string, input: any): Observable<any> {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: UPDATE_GROUP_MUTATION,\n        variables: { id: groupId, input },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.updateGroup;\n          if (!group) {\n            throw new Error('Échec de la mise à jour du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group updated successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error updating group:', error);\n          return throwError(\n            () => new Error('Échec de la mise à jour du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: DELETE_GROUP_MUTATION,\n        variables: { id: groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.deleteGroup;\n          if (!response) {\n            throw new Error('Échec de la suppression du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group deleted successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error deleting group:', error);\n          return throwError(\n            () => new Error('Échec de la suppression du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: LEAVE_GROUP_MUTATION,\n        variables: { groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.leaveGroup;\n          if (!response) {\n            throw new Error('Échec de la sortie du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Left group successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error leaving group:', error);\n          return throwError(() => new Error('Échec de la sortie du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId: string): Observable<any> {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_GROUP_QUERY,\n        variables: { id: groupId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.getGroup;\n          if (!group) {\n            throw new Error('Groupe non trouvé');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group retrieved successfully: ${groupId}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error getting group:', error);\n          return throwError(\n            () => new Error('Échec de la récupération du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId: string): Observable<any[]> {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_USER_GROUPS_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const groups = result.data?.getUserGroups || [];\n          this.logger.info(\n            'MessageService',\n            `Retrieved ${groups.length} groups for user: ${userId}`\n          );\n          return groups;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error getting user groups:',\n            error\n          );\n          return throwError(\n            () => new Error('Échec de la récupération des groupes')\n          );\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  // ✅ Optimized subscription with connection pooling and caching\n  private subscriptionCache = new Map<string, Observable<Message>>();\n  private subscriptionRefCount = new Map<string, number>();\n\n  subscribeToNewMessages(conversationId: string): Observable<Message> {\n    if (!conversationId) {\n      return throwError(() => new Error('Conversation ID is required'));\n    }\n\n    // ✅ Use cached subscription if available\n    const cacheKey = `messages_${conversationId}`;\n    if (this.subscriptionCache.has(cacheKey)) {\n      const refCount = this.subscriptionRefCount.get(cacheKey) || 0;\n      this.subscriptionRefCount.set(cacheKey, refCount + 1);\n      return this.subscriptionCache.get(cacheKey)!;\n    }\n\n    // ✅ Quick token validation without verbose logging\n    if (!this.isTokenValid()) {\n      return EMPTY;\n    }\n\n    // ✅ Reduced logging for better performance\n    if (!environment.production) {\n      console.log(`🚀 Setting up subscription: ${conversationId}`);\n    }\n    console.log(\n      `🔍 DEBUG: MESSAGE_SENT_SUBSCRIPTION query:`,\n      MESSAGE_SENT_SUBSCRIPTION\n    );\n    console.log(`🔍 DEBUG: Subscription variables:`, { conversationId });\n\n    const sub$ = this.apollo\n      .subscribe<{ messageSent: Message }>({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        tap((result) => {\n          console.log(`🔍 DEBUG: Raw subscription result received:`, result);\n          console.log(`🔍 DEBUG: result.data:`, result.data);\n          console.log(\n            `🔍 DEBUG: result.data?.messageSent:`,\n            result.data?.messageSent\n          );\n        }),\n        map((result) => {\n          const msg = result.data?.messageSent;\n          if (!msg) {\n            console.log(\n              `❌ DEBUG: No message payload received in result:`,\n              result\n            );\n            this.logger.warn('⚠️ No message payload received');\n            throw new Error('No message payload received');\n          }\n\n          this.logger.debug(\n            '⚡ INSTANT: New message received via WebSocket',\n            msg\n          );\n\n          // Vérifier que l'ID est présent\n          if (!msg.id && !msg._id) {\n            this.logger.warn(\n              '⚠️ Message without ID received, generating temp ID'\n            );\n            msg.id = `temp-${Date.now()}`;\n          }\n\n          try {\n            // NORMALISATION RAPIDE du message\n            const normalizedMessage = this.normalizeMessage(msg);\n\n            this.logger.debug(\n              '✅ INSTANT: Message normalized successfully',\n              normalizedMessage\n            );\n\n            // TRAITEMENT INSTANTANÉ selon le type\n            if (\n              normalizedMessage.type === MessageType.AUDIO ||\n              normalizedMessage.type === MessageType.VOICE_MESSAGE ||\n              (normalizedMessage.attachments &&\n                normalizedMessage.attachments.some(\n                  (att) => att.type === 'AUDIO'\n                ))\n            ) {\n              this.logger.debug(\n                '🎤 INSTANT: Voice message received in real-time'\n              );\n            }\n\n            // MISE À JOUR IMMÉDIATE de l'UI\n            this.zone.run(() => {\n              this.logger.debug(\n                '📡 INSTANT: Updating conversation UI immediately'\n              );\n              this.updateConversationWithNewMessage(\n                conversationId,\n                normalizedMessage\n              );\n            });\n\n            return normalizedMessage;\n          } catch (err) {\n            this.logger.error('❌ Error normalizing message:', err);\n\n            // Créer un message minimal mais valide pour éviter les erreurs\n            const minimalMessage: Message = {\n              id: msg.id || msg._id || `temp-${Date.now()}`,\n              content: msg.content || '',\n              type: msg.type || MessageType.TEXT,\n              timestamp: this.safeDate(msg.timestamp),\n              isRead: false,\n              sender: msg.sender\n                ? this.normalizeUser(msg.sender)\n                : {\n                    id: this.getCurrentUserId(),\n                    username: 'Unknown',\n                  },\n            };\n\n            this.logger.debug(\n              '🔧 FALLBACK: Created minimal message',\n              minimalMessage\n            );\n            return minimalMessage;\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Message subscription error:',\n            error\n          );\n          // Retourner un observable vide au lieu de null\n          return EMPTY;\n        }),\n        // Filtrer les valeurs null\n        filter((message) => !!message),\n        // Réessayer après un délai en cas d'erreur\n        retry(3)\n      );\n\n    console.log(`🔍 DEBUG: Setting up subscription observer...`);\n\n    const sub = sub$.subscribe({\n      next: (message) => {\n        console.log(`✅ DEBUG: Message received via subscription:`, message);\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: (err) => {\n        console.error(`❌ DEBUG: Subscription error:`, err);\n        this.logger.error('Error in message subscription:', err);\n      },\n      complete: () => {\n        console.log(`🔚 DEBUG: Subscription completed`);\n      },\n    });\n\n    // Log pour confirmer que la subscription est créée\n    console.log(`🔗 DEBUG: Subscription object created:`, sub);\n    console.log(`🔗 DEBUG: Apollo client state:`, this.apollo);\n\n    this.subscriptions.push(sub);\n    console.log(\n      `✅ DEBUG: Subscription established and added to subscriptions list. Total subscriptions: ${this.subscriptions.length}`\n    );\n    return sub$;\n  }\n\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  private updateConversationWithNewMessage(\n    conversationId: string,\n    message: Message\n  ): void {\n    this.logger.debug(\n      `⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`\n    );\n\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: (conversation) => {\n          this.logger.debug(\n            `✅ BACKGROUND: Conversation ${conversationId} refreshed with ${\n              conversation?.messages?.length || 0\n            } messages`\n          );\n        },\n        error: (error) => {\n          this.logger.error(\n            `⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`,\n            error\n          );\n        },\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n\n  /**\n   * Rafraîchit les notifications du sender après envoi d'un message\n   */\n  private refreshSenderNotifications(): void {\n    console.log('🔄 SENDER: Refreshing notifications after message sent');\n\n    // Recharger les notifications en arrière-plan\n    this.getNotifications(true).subscribe({\n      next: (notifications) => {\n        console.log(\n          '🔄 SENDER: Notifications refreshed successfully',\n          notifications.length\n        );\n      },\n      error: (error) => {\n        console.error('🔄 SENDER: Error refreshing notifications:', error);\n      },\n    });\n  }\n\n  subscribeToUserStatus(): Observable<User> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\"\n      );\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n\n    const sub$ = this.apollo\n      .subscribe<{ userStatusChanged: User }>({\n        query: USER_STATUS_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement au statut utilisateur:\",\n            result\n          )\n        ),\n        map((result) => {\n          const user = result.data?.userStatusChanged;\n          if (!user) {\n            this.logger.error('No status payload received');\n            throw new Error('No status payload received');\n          }\n          return this.normalizeUser(user);\n        }),\n        catchError((error) => {\n          this.logger.error('Status subscription error:', error as Error);\n          return throwError(() => new Error('Status subscription failed'));\n        }),\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(\n    conversationId: string\n  ): Observable<Conversation> {\n    const sub$ = this.apollo\n      .subscribe<{ conversationUpdated: Conversation }>({\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const conv = result.data?.conversationUpdated;\n          if (!conv) throw new Error('No conversation payload received');\n\n          const normalizedConversation: Conversation = {\n            ...conv,\n            participants:\n              conv.participants?.map((p) => this.normalizeUser(p)) || [],\n            lastMessage: conv.lastMessage\n              ? {\n                  ...conv.lastMessage,\n                  sender: this.normalizeUser(conv.lastMessage.sender),\n                  timestamp: this.safeDate(conv.lastMessage.timestamp),\n                  readAt: conv.lastMessage.readAt\n                    ? this.safeDate(conv.lastMessage.readAt)\n                    : undefined,\n                  // Conservez toutes les autres propriétés du message\n                  id: conv.lastMessage.id,\n                  content: conv.lastMessage.content,\n                  type: conv.lastMessage.type,\n                  isRead: conv.lastMessage.isRead,\n                  // ... autres propriétés nécessaires\n                }\n              : null, // On conserve null comme dans votre version originale\n          };\n\n          return normalizedConversation as Conversation; // Assertion de type si nécessaire\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Conversation subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Conversation subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(\n    conversationId: string\n  ): Observable<TypingIndicatorEvent> {\n    const sub$ = this.apollo\n      .subscribe<TypingIndicatorEvents>({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => result.data?.typingIndicator),\n        filter(Boolean),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Typing indicator subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Typing indicator subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  private isTokenValid(): boolean {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString(),\n        });\n        return false;\n      }\n\n      return true;\n    } catch (error) {\n      this.logger.error(\n        'Erreur lors de la vérification du token:',\n        error as Error\n      );\n      return false;\n    }\n  }\n\n  subscribeToNotificationsRead(): Observable<string[]> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications avec un token invalide ou expiré\"\n      );\n      return of([]);\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n\n    const sub$ = this.apollo\n      .subscribe<NotificationsReadEvent>({\n        query: NOTIFICATIONS_READ_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement aux notifications lues:\",\n            result\n          )\n        ),\n        map((result) => {\n          const notificationIds = result.data?.notificationsRead || [];\n          this.logger.debug(\n            'Notifications marquées comme lues:',\n            notificationIds\n          );\n          this.updateNotificationStatus(notificationIds, true);\n          return notificationIds;\n        }),\n        catchError((err) => {\n          this.logger.error(\n            'Notifications read subscription error:',\n            err as Error\n          );\n          // Retourner un tableau vide au lieu de propager l'erreur\n          return of([]);\n        }),\n        // Réessayer après un délai en cas d'erreur\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications(): Observable<Notification> {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications sans être connecté\"\n      );\n      return EMPTY;\n    }\n\n    this.logger.debug(\n      '🚀 INSTANT NOTIFICATION: Setting up real-time subscription'\n    );\n\n    const source$ = this.apollo.subscribe<NotificationReceivedEvent>({\n      query: NOTIFICATION_SUBSCRIPTION,\n    });\n\n    const processed$ = source$.pipe(\n      map((result) => {\n        const notification = result.data?.notificationReceived;\n        if (!notification) {\n          throw new Error('No notification payload received');\n        }\n\n        this.logger.debug(\n          '⚡ INSTANT: New notification received',\n          notification\n        );\n\n        const normalized = this.normalizeNotification(notification);\n\n        // Vérification rapide du cache\n        if (this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            `🔄 Notification ${normalized.id} already in cache, skipping`\n          );\n          throw new Error('Notification already exists in cache');\n        }\n\n        // TRAITEMENT INSTANTANÉ\n        this.logger.debug('📡 INSTANT: Processing notification immediately');\n\n        // Vérifier si la notification existe déjà pour éviter les doublons\n        const currentNotifications = this.notifications.value;\n        const existingNotification = currentNotifications.find(\n          (n) => n.id === normalized.id\n        );\n\n        if (existingNotification) {\n          this.logger.debug(\n            '🔄 DUPLICATE: Notification already exists, skipping:',\n            normalized.id\n          );\n          return normalized;\n        }\n\n        // Son de notification IMMÉDIAT\n        this.playNotificationSound();\n\n        // Mise à jour INSTANTANÉE du cache\n        this.updateNotificationCache(normalized);\n\n        // Émettre IMMÉDIATEMENT la notification EN PREMIER\n        this.zone.run(() => {\n          // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n          const updatedNotifications = [normalized, ...currentNotifications];\n\n          this.logger.debug(\n            `⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`\n          );\n\n          this.notifications.next(updatedNotifications);\n          this.notificationCount.next(this.notificationCount.value + 1);\n        });\n\n        this.logger.debug(\n          '✅ INSTANT: Notification processed and emitted',\n          normalized\n        );\n\n        return normalized;\n      }),\n      // Gestion d'erreurs optimisée\n      catchError((err) => {\n        if (\n          err instanceof Error &&\n          err.message === 'Notification already exists in cache'\n        ) {\n          return EMPTY;\n        }\n\n        this.logger.error('❌ Notification subscription error:', err as Error);\n        return EMPTY;\n      }),\n      // Optimisation: traitement en temps réel\n      tap((notification) => {\n        this.logger.debug(\n          '⚡ INSTANT: Notification ready for UI update',\n          notification\n        );\n      })\n    );\n\n    const sub = processed$.subscribe({\n      next: (notification) => {\n        this.logger.debug(\n          '✅ INSTANT: Notification delivered to UI',\n          notification\n        );\n      },\n      error: (error) => {\n        this.logger.error(\n          '❌ CRITICAL: Notification subscription error',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n\n  private startCleanupInterval(): void {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  private cleanupExpiredNotifications(): void {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n    let expiredCount = 0;\n\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(\n        this.notificationCache.values()\n      );\n      const sortedNotifications = this.sortNotificationsByDate(\n        remainingNotifications\n      );\n\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  private sortNotificationsByDate(\n    notifications: Notification[]\n  ): Notification[] {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  private getCurrentUserId(): string {\n    return localStorage.getItem('userId') || '';\n  }\n  private normalizeMessage(message: Message): Message {\n    if (!message) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined message'\n      );\n      throw new Error('Message object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error(\n          '[MessageService] Message ID is missing',\n          undefined,\n          message\n        );\n        throw new Error('Message ID is required');\n      }\n\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender\n          ? this.normalizeUser(message.sender)\n          : undefined;\n      } catch (error) {\n        this.logger.warn(\n          '[MessageService] Error normalizing message sender, using default values',\n          error\n        );\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true,\n        };\n      }\n\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing message receiver, using default values',\n            error\n          );\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true,\n          };\n        }\n      }\n\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments =\n        message.attachments?.map((att) => ({\n          id: att.id || att._id || `attachment-${Date.now()}`,\n          url: att.url || '',\n          type: att.type || 'unknown',\n          name: att.name || 'attachment',\n          size: att.size || 0,\n          duration: att.duration || 0,\n        })) || [];\n\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null,\n      };\n\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id,\n      });\n\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing message:',\n        error instanceof Error ? error : new Error(String(error)),\n        message\n      );\n      throw new Error(\n        `Failed to normalize message: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n\n  public normalizeUser(user: any): User {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive =\n      user.isActive !== undefined && user.isActive !== null\n        ? user.isActive\n        : true;\n    const role = user.role || 'user';\n\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount,\n    };\n  }\n  private normalizeConversation(conv: Conversation): Conversation {\n    if (!conv) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined conversation'\n      );\n      throw new Error('Conversation object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error(\n          '[MessageService] Conversation ID is missing',\n          undefined,\n          conv\n        );\n        throw new Error('Conversation ID is required');\n      }\n\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing participant, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.warn(\n          '[MessageService] Conversation has no participants or invalid participants array',\n          conv\n        );\n      }\n\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length,\n        });\n\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug(\n                '[MessageService] Successfully normalized message',\n                {\n                  messageId: normalizedMessage.id,\n                  content: normalizedMessage.content?.substring(0, 20),\n                  sender: normalizedMessage.sender?.username,\n                }\n              );\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing message in conversation, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.debug(\n          '[MessageService] No messages found in conversation or invalid messages array'\n        );\n      }\n\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing last message, using null',\n            error\n          );\n        }\n      }\n\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt),\n      };\n\n      this.logger.debug(\n        '[MessageService] Conversation normalized successfully',\n        {\n          conversationId: normalizedConversation.id,\n          participantCount: normalizedParticipants.length,\n          messageCount: normalizedMessages.length,\n        }\n      );\n\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing conversation:',\n        error instanceof Error ? error : new Error(String(error)),\n        conv\n      );\n      throw new Error(\n        `Failed to normalize conversation: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n  private normalizeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  private safeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  private toSafeISOString = (\n    date: Date | string | undefined\n  ): string | undefined => {\n    if (!date) return undefined;\n    return typeof date === 'string' ? date : date.toISOString();\n  };\n  private normalizeNotification(notification: Notification): Notification {\n    this.logger.debug(\n      'MessageService',\n      'Normalizing notification',\n      notification\n    );\n\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || (notification as any)._id;\n    if (!notificationId) {\n      this.logger.error(\n        'MessageService',\n        'Notification ID is missing',\n        notification\n      );\n      throw new Error('Notification ID is required');\n    }\n\n    if (!notification.timestamp) {\n      this.logger.warn(\n        'MessageService',\n        'Notification timestamp is missing, using current time',\n        notification\n      );\n      notification.timestamp = new Date();\n    }\n\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId, // Conserver l'ID MongoDB original\n        id: notificationId, // Utiliser le même ID pour les deux propriétés\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId),\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message),\n        }),\n      };\n\n      this.logger.debug(\n        'MessageService',\n        'Normalized notification result',\n        normalized\n      );\n      return normalized;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Error in normalizeNotification',\n        error\n      );\n      throw error;\n    }\n  }\n  private normalizeSender(sender: any) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && { image: sender.image }),\n    };\n  }\n\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  private normalizeNotMessage(message: any) {\n    if (!message) return null;\n\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && { sender: this.normalizeSender(message.sender) }),\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  private updateCache(\n    notifications: Notification | Notification[],\n    skipDuplicates: boolean = true\n  ) {\n    const notificationArray = Array.isArray(notifications)\n      ? notifications\n      : [notifications];\n\n    this.logger.debug(\n      'MessageService',\n      `Updating notification cache with ${notificationArray.length} notifications`\n    );\n\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(\n      (notif) => notif && (notif.id || (notif as any)._id)\n    );\n\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn(\n        'MessageService',\n        `Found ${\n          notificationArray.length - validNotifications.length\n        } notifications without valid IDs`\n      );\n    }\n\n    let addedCount = 0;\n    let skippedCount = 0;\n\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || (notif as any)._id;\n        if (!notifId) {\n          this.logger.error(\n            'MessageService',\n            'Notification without ID:',\n            notif\n          );\n          return;\n        }\n\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            'MessageService',\n            `Notification ${normalized.id} already exists in cache, skipping`\n          );\n          skippedCount++;\n          return;\n        }\n\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n\n        this.logger.debug(\n          'MessageService',\n          `Added notification ${normalized.id} to cache`\n        );\n      } catch (error) {\n        this.logger.error(\n          'MessageService',\n          `Error processing notification ${index + 1}:`,\n          error\n        );\n      }\n    });\n\n    this.logger.debug(\n      'MessageService',\n      `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`\n    );\n\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  private refreshNotificationObservables(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n\n    this.logger.debug(\n      `📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`\n    );\n\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  private updateUnreadCount(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n    const unreadNotifications = allNotifications.filter((n) => !n.isRead);\n    const count = unreadNotifications.length;\n\n    // Forcer la mise à jour dans la zone Angular\n    this.zone.run(() => {\n      this.notificationCount.next(count);\n\n      // Émettre un événement global pour forcer la mise à jour du layout\n      window.dispatchEvent(\n        new CustomEvent('notificationCountChanged', {\n          detail: { count },\n        })\n      );\n    });\n  }\n\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  private updateNotificationCache(notification: Notification): void {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  private updateNotificationStatus(ids: string[], isRead: boolean): void {\n    ids.forEach((id) => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined,\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  private removeNotificationsFromCache(notificationIds: string[]): number {\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Starting removal of',\n      notificationIds.length,\n      'notifications'\n    );\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Cache size before:',\n      this.notificationCache.size\n    );\n\n    let removedCount = 0;\n    notificationIds.forEach((id) => {\n      if (this.notificationCache.has(id)) {\n        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n        this.notificationCache.delete(id);\n        removedCount++;\n      } else {\n        console.log(\n          '🗑️ REMOVE FROM CACHE: Notification not found in cache:',\n          id\n        );\n      }\n    });\n\n    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Cache size after:',\n      this.notificationCache.size\n    );\n\n    if (removedCount > 0) {\n      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n      this.refreshNotificationObservables();\n    }\n\n    return removedCount;\n  }\n\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  private handleDeletionError(\n    error: any,\n    operation: string,\n    fallbackResponse: any\n  ) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StartTupingResponse>({\n        mutation: START_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.startTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error starting typing indicator',\n            error\n          );\n          return throwError(\n            () => new Error('Failed to start typing indicator')\n          );\n        })\n      );\n  }\n\n  stopTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StopTypingResponse>({\n        mutation: STOP_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.stopTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error stopping typing indicator',\n            error\n          );\n          return throwError(() => new Error('Failed to stop typing indicator'));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODE SENDMESSAGE MANQUANTE\n  // ========================================\n\n  /**\n   * Envoie un message (texte, fichier, audio, etc.)\n   * @param receiverId ID du destinataire\n   * @param content Contenu du message (texte)\n   * @param file Fichier à envoyer (optionnel)\n   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n   * @param conversationId ID de la conversation\n   * @returns Observable avec le message envoyé\n   */\n  sendMessage(\n    receiverId: string,\n    content: string,\n    file?: File,\n    messageType: any = 'TEXT',\n    conversationId?: string\n  ): Observable<Message> {\n    console.log('🚀 [MessageService] sendMessage called with:', {\n      receiverId,\n      content: content?.substring(0, 50),\n      hasFile: !!file,\n      fileName: file?.name,\n      fileType: file?.type,\n      fileSize: file?.size,\n      messageType,\n      conversationId,\n    });\n\n    if (!receiverId) {\n      const error = new Error('Receiver ID is required');\n      console.error('❌ [MessageService] sendMessage error:', error);\n      return throwError(() => error);\n    }\n\n    // Préparer les variables pour la mutation\n    const variables: any = {\n      receiverId,\n      content: content || '',\n      type: messageType,\n    };\n\n    // Ajouter l'ID de conversation si fourni\n    if (conversationId) {\n      variables.conversationId = conversationId;\n    }\n\n    // Si un fichier est fourni, l'ajouter aux variables\n    if (file) {\n      variables.file = file;\n      console.log('📁 [MessageService] Adding file to mutation:', {\n        name: file.name,\n        type: file.type,\n        size: file.size,\n      });\n    }\n\n    console.log(\n      '📤 [MessageService] Sending mutation with variables:',\n      variables\n    );\n\n    return this.apollo\n      .mutate<SendMessageResponse>({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables,\n        context: {\n          useMultipart: !!file, // Utiliser multipart si un fichier est présent\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log(\n            '✅ [MessageService] sendMessage mutation result:',\n            result\n          );\n\n          if (!result.data?.sendMessage) {\n            throw new Error('No message data received from server');\n          }\n\n          const message = result.data.sendMessage;\n          console.log('📨 [MessageService] Message sent successfully:', {\n            id: message.id,\n            type: message.type,\n            content: message.content?.substring(0, 50),\n            hasAttachments: !!message.attachments?.length,\n          });\n\n          // Normaliser le message reçu\n          const normalizedMessage = this.normalizeMessage(message);\n          console.log(\n            '🔧 [MessageService] Message normalized:',\n            normalizedMessage\n          );\n\n          return normalizedMessage;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] sendMessage error:', error);\n          this.logger.error('Error sending message:', error);\n\n          // Fournir un message d'erreur plus spécifique\n          let errorMessage = \"Erreur lors de l'envoi du message\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false,\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive: string | Date | undefined): string {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate =\n      lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours =\n      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown date';\n\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        });\n      }\n\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        })}`;\n      }\n\n      const day = date\n        .toLocaleDateString('fr-FR', { weekday: 'short' })\n        .toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages: any[], index: number): boolean {\n    if (index === 0) return true;\n\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  private getDateFromTimestamp(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (\n        timestamp instanceof Date ? timestamp : new Date(timestamp)\n      ).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType?: string): string {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword'))\n      return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed'))\n      return 'fa-file-archive';\n    return 'fa-file';\n  }\n\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType?: string): string {\n    if (!mimeType) return 'File';\n\n    const typeMap: Record<string, string> = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\n        'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':\n        'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation':\n        'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive',\n    };\n\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message: any): boolean {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message: any): boolean {\n    if (!message) return false;\n\n    // Vérifier le type du message\n    if (\n      message.type === MessageType.VOICE_MESSAGE ||\n      message.type === MessageType.VOICE_MESSAGE\n    ) {\n      return true;\n    }\n\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          (message.metadata?.isVoiceMessage &&\n            (type === 'AUDIO' || type === 'audio'))\n        );\n      });\n    }\n\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const voiceAttachment = message.attachments.find((att: any) => {\n      const type = att.type?.toString();\n      return (\n        type === 'VOICE_MESSAGE' ||\n        type === 'voice_message' ||\n        type === 'AUDIO' ||\n        type === 'audio'\n      );\n    });\n\n    return voiceAttachment?.url || '';\n  }\n\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message: any): number {\n    if (!message) return 0;\n\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          type === 'AUDIO' ||\n          type === 'audio'\n        );\n      });\n\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n\n    return 0;\n  }\n\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index: number): number {\n    const pattern = [\n      8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18,\n    ];\n    return pattern[index % pattern.length];\n  }\n\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds: number): string {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message: any): MessageType {\n    if (!message) return MessageType.TEXT;\n\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (\n            attachmentTypeStr === 'file' ||\n            attachmentTypeStr === 'FILE'\n          ) {\n            return MessageType.FILE;\n          } else if (\n            attachmentTypeStr === 'audio' ||\n            attachmentTypeStr === 'AUDIO'\n          ) {\n            return MessageType.AUDIO;\n          } else if (\n            attachmentTypeStr === 'video' ||\n            attachmentTypeStr === 'VIDEO'\n          ) {\n            return MessageType.VIDEO;\n          }\n        }\n\n        return MessageType.FILE;\n      }\n\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis(): string[] {\n    return [\n      '😀',\n      '😃',\n      '😄',\n      '😁',\n      '😆',\n      '😅',\n      '😂',\n      '🤣',\n      '😊',\n      '😇',\n      '🙂',\n      '🙃',\n      '😉',\n      '😌',\n      '😍',\n      '🥰',\n      '😘',\n      '😗',\n      '😙',\n      '😚',\n      '😋',\n      '😛',\n      '😝',\n      '😜',\n      '🤪',\n      '🤨',\n      '🧐',\n      '🤓',\n      '😎',\n      '🤩',\n      '😏',\n      '😒',\n      '😞',\n      '😔',\n      '😟',\n      '😕',\n      '🙁',\n      '☹️',\n      '😣',\n      '😖',\n      '😫',\n      '😩',\n      '🥺',\n      '😢',\n      '😭',\n      '😤',\n      '😠',\n      '😡',\n      '🤬',\n      '🤯',\n      '😳',\n      '🥵',\n      '🥶',\n      '😱',\n      '😨',\n      '😰',\n      '😥',\n      '😓',\n      '🤗',\n      '🤔',\n      '👍',\n      '👎',\n      '👏',\n      '🙌',\n      '👐',\n      '🤲',\n      '🤝',\n      '🙏',\n      '✌️',\n      '🤞',\n      '❤️',\n      '🧡',\n      '💛',\n      '💚',\n      '💙',\n      '💜',\n      '🖤',\n      '💔',\n      '💯',\n      '💢',\n    ];\n  }\n\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message: any, currentUserId: string | null): string {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n\n    try {\n      const isCurrentUser =\n        message.sender?.id === currentUserId ||\n        message.sender?._id === currentUserId ||\n        message.senderId === currentUserId;\n\n      const baseClass = isCurrentUser\n        ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm'\n        : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n\n      const messageType = this.getMessageType(message);\n\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (\n            attachmentTypeStr === 'FILE' ||\n            attachmentTypeStr === 'file'\n          ) {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n\n      // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n\n  // ========================================\n  // APPELS WEBRTC - DÉLÉGUÉS AU CALLSERVICE\n  // ========================================\n  // Note: Les méthodes d'appel ont été déplacées vers CallService\n  // pour éviter la duplication de code et centraliser la logique\n\n  // destroy\n  cleanupSubscriptions(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n}\n"], "mappings": "AAEA,SACEA,eAAe,EACfC,UAAU,EACVC,EAAE,EAEFC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,MAAM,QAID,gBAAgB;AAEvB,SACEC,WAAW,EAEXC,QAAQ,EACRC,UAAU,QAML,yBAAyB;AAChC,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,yBAAyB,EACzBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,iCAAiC,EACjCC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,yBAAyB,EACzBC,qBAAqB,EACrBC,oBAAoB,EACpBC,6BAA6B,EAC7BC,sBAAsB,EACtBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,EACpBC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EAGrBC,oBAAoB,EACpBC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,+BAA+B,EAC/BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,sCAAsC,EACtCC,iCAAiC;AACjC;AACAC,kBAAkB,EAClBC,kBAAkB,EAClBC,gBAAgB,EAEhBC,yBAAyB,EAKzBC,wBAAwB,EACxBC,0BAA0B,EAE1BC,wBAAwB,QACnB,4BAA4B;;;;AA0CnC,OAAM,MAAOC,cAAc;EA6DzBC,YACUC,MAAc,EACdC,MAAqB,EACrBC,IAAY;IAFZ,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IA/Dd;IACQ,KAAAC,kBAAkB,GAAG,IAAI/D,eAAe,CAAgB,IAAI,CAAC;IAC7D,KAAAgE,aAAa,GAAG,IAAIhE,eAAe,CAAiB,EAAE,CAAC;IACvD,KAAAiE,iBAAiB,GAAG,IAAIC,GAAG,EAAwB;IAEnD,KAAAC,iBAAiB,GAAG,IAAInE,eAAe,CAAS,CAAC,CAAC;IAClD,KAAAoE,WAAW,GAAG,IAAIF,GAAG,EAAgB;IACrC,KAAAG,aAAa,GAAmB,EAAE;IACzB,KAAAC,cAAc,GAAG,MAAM;IAChC,KAAAC,aAAa,GAAG,CAAC;IAEzB;IACQ,KAAAC,UAAU,GAAG,IAAIxE,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAyE,YAAY,GAAG,IAAIzE,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAA0E,WAAW,GAAG,IAAI1E,eAAe,CAAoB,IAAI,CAAC;IAC1D,KAAA2E,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,cAAc,GAA6B,IAAI;IAEvD;IACO,KAAAC,WAAW,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACP,YAAY,CAACM,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACK,YAAY,EAAE;IAC9C,KAAAG,YAAY,GAAG,IAAIlF,eAAe,CAAqB,IAAI,CAAC;IAC5D,KAAAmF,aAAa,GAAG,IAAInF,eAAe,CAAqB,IAAI,CAAC;IAEpE;IACiB,KAAAoF,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IACO,KAAAC,UAAU,GAAW,EAAE;IAE/B;IACO,KAAAC,qBAAqB,GAMxB;MACFC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE;KAClB;IAED;IACO,KAAAC,mBAAmB,GAAG,IAAI,CAAC/B,kBAAkB,CAACgB,YAAY,EAAE;IAC5D,KAAAgB,cAAc,GAAG,IAAI,CAAC/B,aAAa,CAACe,YAAY,EAAE;IAClD,KAAAiB,kBAAkB,GAAG,IAAI,CAAC7B,iBAAiB,CAACY,YAAY,EAAE;IAEjE;IACQ,KAAAkB,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAw3BrB;IACA;IACA;IACA;IACQ,KAAAC,sBAAsB,GAAG;MAC/BT,WAAW,EAAE,CAAC;MACdU,KAAK,EAAE,EAAE;MACTC,oBAAoB,EAAE;KACvB;IA02CD;IACA;IACA;IACA;IACQ,KAAAC,iBAAiB,GAAG,IAAIrC,GAAG,EAA+B;IAC1D,KAAAsC,oBAAoB,GAAG,IAAItC,GAAG,EAAkB;IA25BhD,KAAAuC,eAAe,GACrBC,IAA+B,IACT;MACtB,IAAI,CAACA,IAAI,EAAE,OAAOC,SAAS;MAC3B,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACE,WAAW,EAAE;IAC7D,CAAC;IAxoGC,IAAI,CAACC,iCAAiC,EAAE;IACxC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;;EAIQH,iCAAiCA,CAAA;IACvC,IAAI;MACF,MAAMI,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAChE,IAAIF,kBAAkB,EAAE;QACtB,MAAMjD,aAAa,GAAGoD,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAmB;QAEtE,IAAI,CAAChD,iBAAiB,CAACqD,KAAK,EAAE;QAE9BtD,aAAa,CAACuD,OAAO,CAAEC,YAAY,IAAI;UACrC,IAAIA,YAAY,IAAIA,YAAY,CAACC,EAAE,EAAE;YACnC,IAAI,CAACxD,iBAAiB,CAACyD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;;QAE7D,CAAC,CAAC;QAEF,IAAI,CAACxD,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAAC,CAAC;QACpE,IAAI,CAACC,iBAAiB,EAAE;;KAE3B,CAAC,OAAOC,KAAK,EAAE;MACd;IAAA;EAEJ;EACQlB,iBAAiBA,CAAA;IACvB,IAAI,CAAChD,IAAI,CAACmE,iBAAiB,CAAC,MAAK;MAC/B,IAAI,CAACC,2BAA2B,EAAE,CAACC,SAAS,EAAE;MAC9C,IAAI,CAACC,4BAA4B,EAAE,CAACD,SAAS,EAAE;MAC/C,IAAI,CAACE,wBAAwB,EAAE,CAACF,SAAS,EAAE;MAC3C;IACF,CAAC,CAAC;;IACF,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQD,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACzE,MAAM,CACfuE,SAAS,CAAiC;MACzCI,KAAK,EAAE/E;KACR,CAAC,CACDgF,IAAI,CACHlI,GAAG,CAAC,CAAC;MAAEmI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAEhE,YAAY,EAAE;QACvB,OAAO,IAAI;;MAGb;MACA,IAAI,CAACiE,kBAAkB,CAACD,IAAI,CAAChE,YAAY,CAAC;MAC1C,OAAOgE,IAAI,CAAChE,YAAY;IAC1B,CAAC,CAAC,EACFlE,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC/D,OAAO9H,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQwI,kBAAkBA,CAACC,IAAkB;IAC3C,IAAI,CAAClE,YAAY,CAACkD,IAAI,CAACgB,IAAI,CAAC;IAC5B,IAAI,CAACC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;EACA;EACA;EAEA;;;EAGQ5B,aAAaA,CAAA;IACnB,IAAI,CAAC6B,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;IACpE,IAAI,CAACA,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;EAClE;EAEA;;;;;EAKQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAACjD,MAAM,CAAC6C,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAAC9C,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;MAE5BE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACjD,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;;EAKAY,IAAIA,CAACE,IAAY,EAAEM,IAAA,GAAgB,KAAK;IACtC,IAAI,IAAI,CAACjD,KAAK,EAAE;MACd;;IAGF,IAAI;MACF,MAAMkD,KAAK,GAAG,IAAI,CAACpD,MAAM,CAAC6C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGFA,KAAK,CAACD,IAAI,GAAGA,IAAI;MAEjB,IAAI,CAAC,IAAI,CAAClD,SAAS,CAAC4C,IAAI,CAAC,EAAE;QACzBO,KAAK,CAACC,WAAW,GAAG,CAAC;QACrBD,KAAK,CAACT,IAAI,EAAE,CAACW,KAAK,CAAEvB,KAAK,IAAI;UAC3B;QAAA,CACD,CAAC;QACF,IAAI,CAAC9B,SAAS,CAAC4C,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;EAIAwB,IAAIA,CAACV,IAAY;IACf,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAACpD,MAAM,CAAC6C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGF,IAAI,IAAI,CAACnD,SAAS,CAAC4C,IAAI,CAAC,EAAE;QACxBO,KAAK,CAACI,KAAK,EAAE;QACbJ,KAAK,CAACC,WAAW,GAAG,CAAC;QACrB,IAAI,CAACpD,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;;KAE/B,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;EAGA0B,aAAaA,CAAA;IACXC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3D,MAAM,CAAC,CAACsB,OAAO,CAAEuB,IAAI,IAAI;MACxC,IAAI,CAACU,IAAI,CAACV,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;;EAIAe,QAAQA,CAAC1D,KAAc;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACuD,aAAa,EAAE;;EAExB;EAEA;;;;EAIAI,OAAOA,CAAA;IACL,OAAO,IAAI,CAAC3D,KAAK;EACnB;EAEA;;;EAGA4D,qBAAqBA,CAAA;IACnBC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAE1E,IAAI,IAAI,CAAC9D,KAAK,EAAE;MACd6D,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF;IACA,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MAEA;MACA,IAAI,CAACC,uBAAuB,CAACJ,YAAY,CAAC;MAE1C;MACA;MAEA;MACA;MAEA;MACA;MAEA;MACA;MAEAF,OAAO,CAACC,GAAG,CACT,kEAAkE,CACnE;KACF,CAAC,OAAOjC,KAAK,EAAE;MACdgC,OAAO,CAAChC,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;MAED;MACA,IAAI;QACF,MAAMgB,KAAK,GAAG,IAAIC,KAAK,CAAC,gCAAgC,CAAC;QACzDD,KAAK,CAACuB,MAAM,GAAG,GAAG,CAAC,CAAC;QACpBvB,KAAK,CAACJ,IAAI,EAAE,CAACW,KAAK,CAAEiB,GAAG,IAAI;UACzBR,OAAO,CAAChC,KAAK,CACX,2DAA2D,EAC3DwC,GAAG,CACJ;QACH,CAAC,CAAC;OACH,CAAC,OAAOC,UAAU,EAAE;QACnBT,OAAO,CAAChC,KAAK,CACX,8DAA8D,EAC9DyC,UAAU,CACX;;;EAGP;EAEA;EACQH,uBAAuBA,CAACJ,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D;EAEA;EACQS,uBAAuBA,CAACT,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D;EAEA;EACQU,uBAAuBA,CAACV,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACtD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA;EACQW,uBAAuBA,CAACX,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D;EAEA;EACQY,uBAAuBA,CAACZ,YAA0B;IACxD,IAAI,CAACa,YAAY,CAACb,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;EACnD;EAEA;;;EAGQQ,oBAAoBA,CAC1BR,YAA0B,EAC1Bc,SAAiB,EACjBC,SAAiB,EACjBC,QAAgB;IAEhB,MAAMC,UAAU,GAAGjB,YAAY,CAACkB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAGnB,YAAY,CAACoB,UAAU,EAAE;IAE1C;IACAH,UAAU,CAACI,IAAI,GAAG,MAAM;IACxBJ,UAAU,CAACF,SAAS,CAACO,cAAc,CACjCP,SAAS,EACTf,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CACrC;IAED;IACAK,QAAQ,CAACI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEtB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACrEK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAG,IAAI,CAC5C;IACDK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,GAAG,GAAG,CACtD;IACDG,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,CAAC,EACDxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAChD;IAED;IACAC,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC;IAC5BA,QAAQ,CAACM,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;IAE1C;IACAT,UAAU,CAACU,KAAK,CAAC3B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACtDG,UAAU,CAAC3B,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAAC;EAClE;EAEA;;;EAGQH,YAAYA,CAClBb,YAA0B,EAC1Bc,SAAiB,EACjBC,SAAiB,EACjBC,QAAgB;IAEhB,MAAMC,UAAU,GAAGjB,YAAY,CAACkB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAGnB,YAAY,CAACoB,UAAU,EAAE;IAE1C;IACAH,UAAU,CAACI,IAAI,GAAG,UAAU,CAAC,CAAC;IAC9BJ,UAAU,CAACF,SAAS,CAACO,cAAc,CACjCP,SAAS,EACTf,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CACrC;IAED;IACAK,QAAQ,CAACI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEtB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACrEK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAG,IAAI,CAC5C;IACDK,QAAQ,CAACI,IAAI,CAACK,4BAA4B,CACxC,IAAI,EACJ5B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAChD;IAED;IACAC,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC;IAC5BA,QAAQ,CAACM,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;IAE1C;IACAT,UAAU,CAACU,KAAK,CAAC3B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACtDG,UAAU,CAAC3B,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAAC;EAClE;EACA;EACA;EACA;EAEA;;;;;EAKAa,SAASA,CAACC,QAAgB;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMnD,KAAK,GAAG,IAAIC,KAAK,CAAC+C,QAAQ,CAAC;MAEjChD,KAAK,CAACoD,OAAO,GAAG,MAAK;QACnBF,OAAO,EAAE;MACX,CAAC;MAEDlD,KAAK,CAACqD,OAAO,GAAIrE,KAAK,IAAI;QACxB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjEmE,MAAM,CAACnE,KAAK,CAAC;MACf,CAAC;MAEDgB,KAAK,CAACJ,IAAI,EAAE,CAACW,KAAK,CAAEvB,KAAK,IAAI;QAC3B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjEmE,MAAM,CAACnE,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;EAIAsE,gBAAgBA,CAAA;IACd,IAAI,CAACzI,MAAM,CAAC0I,KAAK,CAAC,yCAAyC,CAAC;IAE5D,OAAO,IAAI,CAAC3I,MAAM,CACf4I,UAAU,CAA+B;MACxCjE,KAAK,EAAE9E,wBAAwB;MAC/BgJ,WAAW,EAAE,cAAc,CAAE;KAC9B,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMC,aAAa,GAAGD,MAAM,CAAClE,IAAI,EAAE6D,gBAAgB,IAAI,EAAE;MACzD,IAAI,CAACzI,MAAM,CAAC0I,KAAK,CACf,8BAA8BK,aAAa,CAACC,MAAM,iBAAiB,CACpE;MACD,OAAOD,aAAa;IACtB,CAAC,CAAC,EACFrM,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,iDAAiD,EACjDA,KAAK,CACN;MACD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EACA;EACAC,WAAWA,CACTC,QAAgB,EAChBC,UAAkB,EAClBC,cAAsB,EACtBC,IAAA,GAAe,CAAC,EAChB9G,KAAA,GAAgB,EAAE,CAAC;EAAA,E;IAEnB,OAAO,IAAI,CAACzC,MAAM,CACf4I,UAAU,CAA6B;MACtCjE,KAAK,EAAE5F,kBAAkB;MACzByK,SAAS,EAAE;QAAEJ,QAAQ;QAAEC,UAAU;QAAEC,cAAc;QAAE7G,KAAK;QAAE8G;MAAI,CAAE;MAChEV,WAAW,EAAE,aAAa;MAC1BY,WAAW,EAAE,KAAK,CAAE;KACrB,CAAC,CACDX,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMW,QAAQ,GAAGX,MAAM,CAAClE,IAAI,EAAEsE,WAAW,IAAI,EAAE;MAC/C;MACA,OAAO,IAAI,CAACQ,sBAAsB,CAACD,QAAQ,CAAC;IAC9C,CAAC,CAAC,EACF/M,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EACAU,WAAWA,CAACC,SAAiB,EAAEC,UAAkB;IAC/C,OAAO,IAAI,CAAC9J,MAAM,CACf+J,MAAM,CAA2B;MAChCC,QAAQ,EAAEnL,qBAAqB;MAC/B2K,SAAS,EAAE;QAAEK,SAAS;QAAEC;MAAU;KACnC,CAAC,CACDlF,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE+E,WAAW,EAAE;QAC7B,MAAM,IAAIV,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAACe,gBAAgB,CAAClB,MAAM,CAAClE,IAAI,CAAC+E,WAAW,CAAC;IACvD,CAAC,CAAC,EACFjN,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAgB,aAAaA,CAACL,SAAiB;IAC7B,OAAO,IAAI,CAAC7J,MAAM,CACf+J,MAAM,CAA6B;MAClCC,QAAQ,EAAElL,uBAAuB;MACjC0K,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDjF,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEqF,aAAa,EAAE;QAC/B,MAAM,IAAIhB,KAAK,CAAC,0BAA0B,CAAC;;MAE7C,OAAO,IAAI,CAACe,gBAAgB,CAAClB,MAAM,CAAClE,IAAI,CAACqF,aAAa,CAAC;IACzD,CAAC,CAAC,EACFvN,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACnD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EAEAiB,iBAAiBA,CAACN,SAAiB;IACjC,OAAO,IAAI,CAAC7J,MAAM,CACf+J,MAAM,CAAqB;MAC1BC,QAAQ,EAAE1M,qBAAqB;MAC/BkM,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDjF,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEsF,iBAAiB,EACjC,MAAM,IAAIjB,KAAK,CAAC,gCAAgC,CAAC;MACnD,OAAO;QACL,GAAGH,MAAM,CAAClE,IAAI,CAACsF,iBAAiB;QAChCC,MAAM,EAAE,IAAIC,IAAI;OACjB;IACH,CAAC,CAAC,EACF1N,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEAoB,cAAcA,CAACT,SAAiB,EAAEU,KAAa;IAC7C,OAAO,IAAI,CAACvK,MAAM,CACf+J,MAAM,CAAyB;MAC9BC,QAAQ,EAAE5L,yBAAyB;MACnCoL,SAAS,EAAE;QAAEK,SAAS;QAAEU;MAAK;KAC9B,CAAC,CACD3F,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEyF,cAAc,EAC9B,MAAM,IAAIpB,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAOH,MAAM,CAAClE,IAAI,CAACyF,cAAc;IACnC,CAAC,CAAC,EACF3N,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEAsB,cAAcA,CACZX,SAAiB,EACjBY,eAAyB;IAEzB,OAAO,IAAI,CAACzK,MAAM,CACf+J,MAAM,CAAyB;MAC9BC,QAAQ,EAAE3L,wBAAwB;MAClCmL,SAAS,EAAE;QAAEK,SAAS;QAAEY;MAAe;KACxC,CAAC,CACD7F,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE2F,cAAc,EAC9B,MAAM,IAAItB,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAOH,MAAM,CAAClE,IAAI,CAAC2F,cAAc,CAAC9N,GAAG,CAAEgO,GAAG,KAAM;QAC9C,GAAGA,GAAG;QACNC,SAAS,EAAED,GAAG,CAACC,SAAS,GACpB,IAAI,CAACC,aAAa,CAACF,GAAG,CAACC,SAAS,CAAC,GACjC,IAAIN,IAAI;OACb,CAAC,CAAC;IACL,CAAC,CAAC,EACF1N,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEA2B,UAAUA,CAAChB,SAAiB,EAAEP,cAAsB;IAClD,OAAO,IAAI,CAACtJ,MAAM,CACf+J,MAAM,CAAqB;MAC1BC,QAAQ,EAAE1L,oBAAoB;MAC9BkL,SAAS,EAAE;QAAEK,SAAS;QAAEP;MAAc;KACvC,CAAC,CACD1E,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEgG,UAAU,EAC1B,MAAM,IAAI3B,KAAK,CAAC,uBAAuB,CAAC;MAC1C,OAAO;QACL,GAAGH,MAAM,CAAClE,IAAI,CAACgG,UAAU;QACzBC,QAAQ,EAAE,IAAIT,IAAI;OACnB;IACH,CAAC,CAAC,EACF1N,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA6B,cAAcA,CACZpG,KAAa,EACb2E,cAAuB,EACvB0B,OAAA,GAAyB,EAAE;IAE3B,OAAO,IAAI,CAAChL,MAAM,CACf4I,UAAU,CAAyB;MAClCjE,KAAK,EAAE/G,qBAAqB;MAC5B4L,SAAS,EAAE;QACT7E,KAAK;QACL2E,cAAc;QACd,GAAG0B,OAAO;QACVC,QAAQ,EAAE,IAAI,CAACpI,eAAe,CAACmI,OAAO,CAACC,QAAQ,CAAC;QAChDC,MAAM,EAAE,IAAI,CAACrI,eAAe,CAACmI,OAAO,CAACE,MAAM;OAC5C;MACDrC,WAAW,EAAE,aAAa;MAC1BY,WAAW,EAAE;KACd,CAAC,CACDX,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CACAqM,MAAM,IACLA,MAAM,CAAClE,IAAI,EAAEkG,cAAc,EAAErO,GAAG,CAAEgO,GAAG,KAAM;MACzC,GAAGA,GAAG;MACNC,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACT,GAAG,CAACC,SAAS,CAAC;MACvCS,MAAM,EAAE,IAAI,CAACC,aAAa,CAACX,GAAG,CAACU,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDzO,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEA;EACQS,sBAAsBA,CAACD,QAAe;IAC5C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEjD,OAAOS,QAAQ,CAAChN,GAAG,CAAEgO,GAAG,IAAI;MAC1B,IAAI;QACF,OAAO,IAAI,CAACT,gBAAgB,CAACS,GAAG,CAAC;OAClC,CAAC,OAAOtG,KAAK,EAAE;QACdgC,OAAO,CAAChC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD;QACA,OAAO;UACLP,EAAE,EAAE6G,GAAG,CAAC7G,EAAE,IAAI6G,GAAG,CAACY,GAAG,IAAI,QAAQjB,IAAI,CAACkB,GAAG,EAAE,EAAE;UAC7CC,OAAO,EAAEd,GAAG,CAACc,OAAO,IAAI,EAAE;UAC1B7D,IAAI,EAAE+C,GAAG,CAAC/C,IAAI,IAAI7K,WAAW,CAAC2O,IAAI;UAClCd,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACT,GAAG,CAACC,SAAS,CAAC;UACvCe,MAAM,EAAE,KAAK;UACbN,MAAM,EAAEV,GAAG,CAACU,MAAM,GACd,IAAI,CAACC,aAAa,CAACX,GAAG,CAACU,MAAM,CAAC,GAC9B;YACEvH,EAAE,EAAE,IAAI,CAAC8H,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEN;;IAEhB,CAAC,CAAC;EACJ;EAEAC,iBAAiBA,CAACC,MAAc;IAC9B,OAAO,IAAI,CAAC9L,MAAM,CACf4I,UAAU,CAA4B;MACrCjE,KAAK,EAAE9G,yBAAyB;MAChC2L,SAAS,EAAE;QAAEsC;MAAM,CAAE;MACrBjD,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CACAqM,MAAM,IACLA,MAAM,CAAClE,IAAI,EAAEgH,iBAAiB,EAAEnP,GAAG,CAAEgO,GAAG,KAAM;MAC5C,GAAGA,GAAG;MACNC,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACT,GAAG,CAACC,SAAS,CAAC;MACvCS,MAAM,EAAE,IAAI,CAACC,aAAa,CAACX,GAAG,CAACU,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDzO,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA6C,qBAAqBA,CAACzC,cAAsB;IAC1C,IAAI,CAACnJ,kBAAkB,CAAC4D,IAAI,CAACuF,cAAc,CAAC;EAC9C;EAEA0C,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAChM,MAAM,CACf4I,UAAU,CAA2B;MACpCjE,KAAK,EAAE1H,uBAAuB;MAC9B4L,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMkD,aAAa,GAAGlD,MAAM,CAAClE,IAAI,EAAEmH,gBAAgB,IAAI,EAAE;MACzD,OAAOC,aAAa,CAACvP,GAAG,CAAEwP,IAAI,IAAK,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC,EACFvP,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEAkD,eAAeA,CACb9C,cAAsB,EACtB7G,KAAc,EACd8G,IAAa;IAEb,IAAI,CAACtJ,MAAM,CAACoM,IAAI,CACd,0CAA0C/C,cAAc,YAAY7G,KAAK,WAAW8G,IAAI,EAAE,CAC3F;IAED,MAAMC,SAAS,GAAQ;MAAEF;IAAc,CAAE;IAEzC;IACA,IAAI7G,KAAK,KAAKM,SAAS,EAAE;MACvByG,SAAS,CAAC/G,KAAK,GAAGA,KAAK;KACxB,MAAM;MACL+G,SAAS,CAAC/G,KAAK,GAAG,EAAE,CAAC,CAAC;;IAGxB;IACA,IAAI8G,IAAI,KAAKxG,SAAS,EAAE;MACtB;MACA,MAAMuJ,MAAM,GAAG,CAAC/C,IAAI,GAAG,CAAC,IAAIC,SAAS,CAAC/G,KAAK;MAC3C+G,SAAS,CAAC8C,MAAM,GAAGA,MAAM;MACzB,IAAI,CAACrM,MAAM,CAAC0I,KAAK,CACf,uCAAuC2D,MAAM,eAAe/C,IAAI,eAAeC,SAAS,CAAC/G,KAAK,EAAE,CACjG;KACF,MAAM;MACL+G,SAAS,CAAC8C,MAAM,GAAG,CAAC,CAAC,CAAC;;;IAGxB,IAAI,CAACrM,MAAM,CAAC0I,KAAK,CACf,uDAAuDa,SAAS,CAAC/G,KAAK,YAAY+G,SAAS,CAAC8C,MAAM,EAAE,CACrG;IAED,OAAO,IAAI,CAACtM,MAAM,CACf4I,UAAU,CAA0B;MACnCjE,KAAK,EAAEvH,sBAAsB;MAC7BoM,SAAS,EAAEA,SAAS;MACpBX,WAAW,EAAE,cAAc;MAC3BY,WAAW,EAAE;KACd,CAAC,CACDX,YAAY,CAAClE,IAAI,CAChBpI,KAAK,CAAC,CAAC,CAAC;IAAE;IACVE,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAAC9I,MAAM,CAAC0I,KAAK,CACf,kDAAkD,EAClDI,MAAM,CACP;MAED,MAAMmD,IAAI,GAAGnD,MAAM,CAAClE,IAAI,EAAEuH,eAAe;MACzC,IAAI,CAACF,IAAI,EAAE;QACT,IAAI,CAACjM,MAAM,CAACmE,KAAK,CACf,4CAA4CkF,cAAc,EAAE,CAC7D;QACD,MAAM,IAAIJ,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CACf,8CAA8CW,cAAc,EAAE,CAC/D;MACD,MAAMiD,sBAAsB,GAAG,IAAI,CAACJ,qBAAqB,CAACD,IAAI,CAAC;MAE/D,IAAI,CAACjM,MAAM,CAACoM,IAAI,CACd,sDAAsD/C,cAAc,mBAClEiD,sBAAsB,CAACC,YAAY,EAAEvD,MAAM,IAAI,CACjD,eAAesD,sBAAsB,CAAC7C,QAAQ,EAAET,MAAM,IAAI,CAAC,EAAE,CAC9D;MACD,OAAOsD,sBAAsB;IAC/B,CAAC,CAAC,EACF5P,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,+CAA+C,EAC/CA,KAAK,CACN;MACD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EAEAuD,kBAAkBA,CAACX,MAAc;IAC/B,IAAI,CAAC7L,MAAM,CAACoM,IAAI,CACd,qDAAqDP,MAAM,EAAE,CAC9D;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC7L,MAAM,CAACmE,KAAK,CACf,kEAAkE,CACnE;MACD,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,8CAA8C,CAAC,CAChE;;IAGH,OAAO,IAAI,CAAClJ,MAAM,CACf+J,MAAM,CAAuC;MAC5CC,QAAQ,EAAE7K,4BAA4B;MACtCqK,SAAS,EAAE;QAAEsC;MAAM;KACpB,CAAC,CACDlH,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAAC9I,MAAM,CAAC0I,KAAK,CACf,kDAAkD,EAClDI,MAAM,CACP;MAED,MAAM2D,YAAY,GAAG3D,MAAM,CAAClE,IAAI,EAAE4H,kBAAkB;MACpD,IAAI,CAACC,YAAY,EAAE;QACjB,IAAI,CAACzM,MAAM,CAACmE,KAAK,CACf,6DAA6D0H,MAAM,EAAE,CACtE;QACD,MAAM,IAAI5C,KAAK,CAAC,+BAA+B,CAAC;;MAGlD,IAAI;QACF,MAAMqD,sBAAsB,GAC1B,IAAI,CAACJ,qBAAqB,CAACO,YAAY,CAAC;QAC1C,IAAI,CAACzM,MAAM,CAACoM,IAAI,CACd,uDAAuDE,sBAAsB,CAAC1I,EAAE,EAAE,CACnF;QACD,OAAO0I,sBAAsB;OAC9B,CAAC,OAAOnI,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0DAA0D,EAC1DA,KAAK,CACN;QACD,MAAM,IAAI8E,KAAK,CAAC,uCAAuC,CAAC;;IAE5D,CAAC,CAAC,EACFvM,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0DAA0D0H,MAAM,GAAG,EACnE1H,KAAK,CACN;MACD,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,kCAAkC9E,KAAK,CAACuI,OAAO,EAAE,CAAC,CACnE;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,uBAAuBA,CAACd,MAAc;IACpC,IAAI,CAAC7L,MAAM,CAACoM,IAAI,CACd,gEAAgEP,MAAM,EAAE,CACzE;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC7L,MAAM,CAACmE,KAAK,CACf,sEAAsE,CACvE;MACD,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,kDAAkD,CAAC,CACpE;;IAGH;IACA,OAAO,IAAI,CAAC8C,gBAAgB,EAAE,CAACpH,IAAI,CACjClI,GAAG,CAAEuP,aAAa,IAAI;MACpB;MACA,MAAMY,aAAa,GAAG,IAAI,CAAClB,gBAAgB,EAAE;MAE7C;MACA,MAAMmB,oBAAoB,GAAGb,aAAa,CAACc,IAAI,CAAEb,IAAI,IAAI;QACvD,IAAIA,IAAI,CAACc,OAAO,EAAE,OAAO,KAAK;QAE9B;QACA,MAAMC,cAAc,GAClBf,IAAI,CAACM,YAAY,EAAE9P,GAAG,CAAEwQ,CAAC,IAAKA,CAAC,CAACrJ,EAAE,IAAIqJ,CAAC,CAAC5B,GAAG,CAAC,IAAI,EAAE;QACpD,OACE2B,cAAc,CAACE,QAAQ,CAACrB,MAAM,CAAC,IAC/BmB,cAAc,CAACE,QAAQ,CAACN,aAAa,CAAC;MAE1C,CAAC,CAAC;MAEF,IAAIC,oBAAoB,EAAE;QACxB,IAAI,CAAC7M,MAAM,CAACoM,IAAI,CACd,iDAAiDS,oBAAoB,CAACjJ,EAAE,EAAE,CAC3E;QACD,OAAOiJ,oBAAoB;;MAG7B;MACA,MAAM,IAAI5D,KAAK,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,EACFvM,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACoM,IAAI,CACd,sEAAsEjI,KAAK,CAACuI,OAAO,EAAE,CACtF;MACD,OAAO,IAAI,CAACF,kBAAkB,CAACX,MAAM,CAAC;IACxC,CAAC,CAAC,CACH;EACH;EAYAsB,gBAAgBA,CACdC,OAAO,GAAG,KAAK,EACf9D,IAAI,GAAG,CAAC,EACR9G,KAAK,GAAG,EAAE;IAEV,IAAI,CAACxC,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,oCAAoCgB,OAAO,WAAW9D,IAAI,YAAY9G,KAAK,EAAE,CAC9E;IACD,IAAI,CAACxC,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,aAAa,EAAE;MACjDhE,KAAK,EAAEzH;KACR,CAAC;IAEF;IACA;IACA,IAAImQ,OAAO,EAAE;MACX,IAAI,CAACpN,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,qCAAqC,CACtC;MACD,IAAI,CAACnG,sBAAsB,CAACT,WAAW,GAAG,CAAC;MAC3C,IAAI,CAACS,sBAAsB,CAACE,oBAAoB,GAAG,IAAI;;IAGzD;IACA,IAAI,CAACF,sBAAsB,CAACT,WAAW,GAAGwH,IAAI;IAC9C,IAAI,CAAC/G,sBAAsB,CAACC,KAAK,GAAGA,KAAK;IAEzC;IACA,MAAM6K,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D,IAAI,CAACtN,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,SAAS2E,sBAAsB,CAACE,IAAI,2CAA2C,CAChF;IAED,OAAO,IAAI,CAACxN,MAAM,CACf4I,UAAU,CAA+B;MACxCjE,KAAK,EAAEzH,uBAAuB;MAC9BsM,SAAS,EAAE;QACTD,IAAI,EAAEA,IAAI;QACV9G,KAAK,EAAEA;OACR;MACDoG,WAAW,EAAEwE,OAAO,GAAG,cAAc,GAAG;KACzC,CAAC,CACDvE,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAAC9I,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,iCAAiC,CAClC;MAED,IAAII,MAAM,CAAC0E,MAAM,EAAE;QACjB,IAAI,CAACxN,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjB2E,MAAM,CAAC0E,MAAM,CACd;QACD,MAAM,IAAIvE,KAAK,CAACH,MAAM,CAAC0E,MAAM,CAAC/Q,GAAG,CAAEgR,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,MAAMvN,aAAa,GAAG2I,MAAM,CAAClE,IAAI,EAAE+I,oBAAoB,IAAI,EAAE;MAC7D,IAAI,CAAC3N,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,YAAYvI,aAAa,CAAC6I,MAAM,uCAAuCM,IAAI,EAAE,CAC9E;MAED;MACA,IAAI,CAAC/G,sBAAsB,CAACE,oBAAoB,GAC9CtC,aAAa,CAAC6I,MAAM,IAAIxG,KAAK;MAE/B,IAAIrC,aAAa,CAAC6I,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAChJ,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,uCAAuC,CACxC;QACD,IAAI,CAAC7J,sBAAsB,CAACE,oBAAoB,GAAG,KAAK;;MAG1D;MACA,MAAMmL,qBAAqB,GAAGzN,aAAa,CAACvD,MAAM,CAC/CiR,KAAK,IAAK,CAACR,sBAAsB,CAACS,GAAG,CAACD,KAAK,CAACjK,EAAE,CAAC,CACjD;MAED,IAAI,CAAC5D,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,gBACEvI,aAAa,CAAC6I,MAAM,GAAG4E,qBAAqB,CAAC5E,MAC/C,wBAAwB,CACzB;MAED;MACA4E,qBAAqB,CAAClK,OAAO,CAAC,CAACmK,KAAK,EAAEE,KAAK,KAAI;QAC7C5H,OAAO,CAACC,GAAG,CAAC,gBAAgB2H,KAAK,GAAG,CAAC,UAAUzE,IAAI,IAAI,EAAE;UACvD1F,EAAE,EAAEiK,KAAK,CAACjK,EAAE,IAAKiK,KAAa,CAACxC,GAAG;UAClC3D,IAAI,EAAEmG,KAAK,CAACnG,IAAI;UAChB6D,OAAO,EAAEsC,KAAK,CAACtC,OAAO;UACtBE,MAAM,EAAEoC,KAAK,CAACpC;SACf,CAAC;MACJ,CAAC,CAAC;MAEF;MACA;MACA,IAAI,CAACuC,WAAW,CAACJ,qBAAqB,CAAC;MAEvC;MACA,MAAMK,mBAAmB,GAAGlK,KAAK,CAACC,IAAI,CACpC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAChC;MAED;MACA,MAAMiK,mBAAmB,GACvB,IAAI,CAACC,uBAAuB,CAACF,mBAAmB,CAAC;MAEnD9H,OAAO,CAACC,GAAG,CACT,cAAc8H,mBAAmB,CAAClF,MAAM,kDAAkD,CAC3F;MAED;MACA,IAAI,CAAC7I,aAAa,CAAC2D,IAAI,CAACoK,mBAAmB,CAAC;MAE5C;MACA,IAAI,CAAChK,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACkK,+BAA+B,EAAE;MAEtC,OAAOH,mBAAmB;IAC5B,CAAC,CAAC,EACFvR,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MAED,IAAIA,KAAK,CAACkK,aAAa,EAAE;QACvB,IAAI,CAACrO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACkK,aAAa,CACpB;;MAGH,IAAIlK,KAAK,CAACmK,YAAY,EAAE;QACtB,IAAI,CAACtO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACmK,YAAY,CACnB;;MAGH,OAAOhS,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKQqE,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMiB,UAAU,GAAG,IAAIC,GAAG,EAAU;MACpC,MAAMpL,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAEhE;MACA,IAAI,CAACF,kBAAkB,EAAE;QACvB,OAAOmL,UAAU;;MAGnB;MACA,MAAME,oBAAoB,GAAG,IAAID,GAAG,CAClCjL,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAC,CAAC3G,GAAG,CAAEiS,CAAe,IAAKA,CAAC,CAAC9K,EAAE,CAAC,CAC9D;MAED;MACA,MAAM+K,mBAAmB,GACvB,IAAI,CAAC5O,MAAM,CAAC6O,MAAM,CAACC,SAAS,CAA+B;QACzDnK,KAAK,EAAEzH;OACR,CAAC,EAAE0Q,oBAAoB,IAAI,EAAE;MAEhC;MACAgB,mBAAmB,CAACjL,OAAO,CAAEC,YAAY,IAAI;QAC3C,IAAI,CAAC8K,oBAAoB,CAACX,GAAG,CAACnK,YAAY,CAACC,EAAE,CAAC,EAAE;UAC9C2K,UAAU,CAACO,GAAG,CAACnL,YAAY,CAACC,EAAE,CAAC;;MAEnC,CAAC,CAAC;MAEF,OAAO2K,UAAU;KAClB,CAAC,OAAOpK,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAIqK,GAAG,EAAU;;EAE5B;EAEA;EACA/L,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACF,sBAAsB,CAACE,oBAAoB;EACzD;EAEA;EACAsM,qBAAqBA,CAAA;IACnB,MAAMC,QAAQ,GAAG,IAAI,CAACzM,sBAAsB,CAACT,WAAW,GAAG,CAAC;IAC5D,OAAO,IAAI,CAACqL,gBAAgB,CAC1B,KAAK,EACL6B,QAAQ,EACR,IAAI,CAACzM,sBAAsB,CAACC,KAAK,CAClC;EACH;EACAyM,mBAAmBA,CAACrL,EAAU;IAC5B,OAAO,IAAI,CAAC1B,cAAc,CAACyC,IAAI,CAC7BlI,GAAG,CAAE0D,aAAa,IAAKA,aAAa,CAAC2M,IAAI,CAAE4B,CAAC,IAAKA,CAAC,CAAC9K,EAAE,KAAKA,EAAE,CAAC,CAAC,EAC9DlH,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACvD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACH;EACAiG,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC/O,aAAa,CAACgP,KAAK,EAAEnG,MAAM,IAAI,CAAC;EAC9C;EACAoG,0BAA0BA,CAACC,cAAsB;IAC/C,OAAO,IAAI,CAACtP,MAAM,CACf2E,KAAK,CAAkC;MACtCA,KAAK,EAAE3F,8BAA8B;MACrCwK,SAAS,EAAE;QAAE3F,EAAE,EAAEyL;MAAc,CAAE;MACjCzG,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEwK,0BAA0B,IAAI,EAAE,CAAC,EAC9D1S,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EACAqG,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACpN,cAAc,CAACyC,IAAI,CAC7BlI,GAAG,CAAE0D,aAAa,IAAKA,aAAa,CAACvD,MAAM,CAAE8R,CAAC,IAAK,CAACA,CAAC,CAACjD,MAAM,CAAC,CAAC,CAC/D;EACH;EAEA;;;;;EAKA8D,kBAAkBA,CAChBF,cAAsB;IAEtB,IAAI,CAACrP,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,kCAAkC2G,cAAc,EAAE,CACnD;IAED,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACrP,MAAM,CAACwP,IAAI,CAAC,gBAAgB,EAAE,6BAA6B,CAAC;MACjE,OAAOlT,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,MAAMwG,YAAY,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAACL,cAAc,CAAC,CAAC;IAExE;IACA,OAAO,IAAI,CAACtP,MAAM,CACf+J,MAAM,CAAgE;MACrEC,QAAQ,EAAE5K,4BAA4B;MACtCoK,SAAS,EAAE;QAAE8F;MAAc;KAC5B,CAAC,CACD1K,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAM6G,QAAQ,GAAG7G,MAAM,CAAClE,IAAI,EAAE2K,kBAAkB;MAChD,IAAI,CAACI,QAAQ,EAAE;QACb,MAAM,IAAI1G,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BiH,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFjT,UAAU,CAAEyH,KAAK,IACf,IAAI,CAACyL,mBAAmB,CAACzL,KAAK,EAAE,mCAAmC,EAAE;MACnE0L,OAAO,EAAE,IAAI;MACbnD,OAAO,EAAE;KACV,CAAC,CACH,CACF;EACL;EAEA;;;;EAIQ0B,+BAA+BA,CAAA;IACrC,IAAI;MACF,MAAMjO,aAAa,GAAG4D,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAAC;MACjEZ,YAAY,CAACyM,OAAO,CAAC,eAAe,EAAEvM,IAAI,CAACwM,SAAS,CAAC5P,aAAa,CAAC,CAAC;MACpE,IAAI,CAACH,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,uCAAuC,CACxC;KACF,CAAC,OAAOvE,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iDAAiD,EACjDA,KAAK,CACN;;EAEL;EAEA;;;;EAIA6L,sBAAsBA,CAAA;IAKpB,IAAI,CAAChQ,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,yCAAyC,CAC1C;IAED;IACA,MAAMuH,KAAK,GAAG,IAAI,CAAC7P,iBAAiB,CAACmN,IAAI;IACzC,MAAM2C,kBAAkB,GAAGnM,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC2F,IAAI,EAAE,CAAC;IACpE,IAAI,CAAC2J,4BAA4B,CAACQ,kBAAkB,CAAC;IAErD;IACA,OAAO,IAAI,CAACnQ,MAAM,CACf+J,MAAM,CAMJ;MACDC,QAAQ,EAAE1K;KACX,CAAC,CACDsF,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAM6G,QAAQ,GAAG7G,MAAM,CAAClE,IAAI,EAAEoL,sBAAsB;MACpD,IAAI,CAACL,QAAQ,EAAE;QACb,MAAM,IAAI1G,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,yDAAyD,EACzDiH,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFjT,UAAU,CAAEyH,KAAK,IACf,IAAI,CAACyL,mBAAmB,CACtBzL,KAAK,EACL,4CAA4C,EAC5C;MACE0L,OAAO,EAAE,IAAI;MACbI,KAAK;MACLvD,OAAO,EAAE,GAAGuD,KAAK;KAClB,CACF,CACF,CACF;EACL;EAEA;;;;;EAKAE,2BAA2BA,CACzBC,eAAyB;IAEzB,IAAI,CAACpQ,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,kBAAkB0H,eAAe,CAACpH,MAAM,gBAAgB,CACzD;IAED,IAAI,CAACoH,eAAe,IAAIA,eAAe,CAACpH,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAAChJ,MAAM,CAACwP,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOlT,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,iCAAiC,CAAC,CAAC;;IAGvE;IACA,MAAMgH,KAAK,GAAG,IAAI,CAACP,4BAA4B,CAACU,eAAe,CAAC;IAEhE;IACA,OAAO,IAAI,CAACrQ,MAAM,CACf+J,MAAM,CAMJ;MACDC,QAAQ,EAAE3K,sCAAsC;MAChDmK,SAAS,EAAE;QAAE6G;MAAe;KAC7B,CAAC,CACDzL,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAM6G,QAAQ,GAAG7G,MAAM,CAAClE,IAAI,EAAEuL,2BAA2B;MACzD,IAAI,CAACR,QAAQ,EAAE;QACb,MAAM,IAAI1G,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCiH,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFjT,UAAU,CAAEyH,KAAK,IACf,IAAI,CAACyL,mBAAmB,CACtBzL,KAAK,EACL,0CAA0C,EAC1C;MACE0L,OAAO,EAAEI,KAAK,GAAG,CAAC;MAClBA,KAAK;MACLvD,OAAO,EAAE,GAAGuD,KAAK;KAClB,CACF,CACF,CACF;EACL;EACAI,wBAAwBA,CAAA;IAGtB,OAAO,IAAI,CAACnO,cAAc,CAACyC,IAAI,CAC7BlI,GAAG,CAAE0D,aAAa,IAAI;MACpB,MAAMmQ,MAAM,GAAG,IAAIjQ,GAAG,EAAoC;MAC1DF,aAAa,CAACuD,OAAO,CAAEmK,KAAK,IAAI;QAC9B,IAAI,CAACyC,MAAM,CAACxC,GAAG,CAACD,KAAK,CAACnG,IAAI,CAAC,EAAE;UAC3B4I,MAAM,CAACzM,GAAG,CAACgK,KAAK,CAACnG,IAAI,EAAE,EAAE,CAAC;;QAE5B4I,MAAM,CAACC,GAAG,CAAC1C,KAAK,CAACnG,IAAI,CAAC,EAAE8I,IAAI,CAAC3C,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,OAAOyC,MAAM;IACf,CAAC,CAAC,CACH;EACH;EACAG,UAAUA,CAACL,eAAyB;IAKlC,IAAI,CAACpQ,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,kCAAkC0H,eAAe,EAAE1C,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAC1E;IAED,IAAI,CAAC0C,eAAe,IAAIA,eAAe,CAACpH,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAAChJ,MAAM,CAACwP,IAAI,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;MAClE,OAAOnT,EAAE,CAAC;QACRwT,OAAO,EAAE,KAAK;QACda,SAAS,EAAE,CAAC;QACZC,cAAc,EAAE,IAAI,CAACrQ,iBAAiB,CAAC6O;OACxC,CAAC;;IAGJ;IACA,MAAMyB,QAAQ,GAAGR,eAAe,CAACxT,MAAM,CACpCgH,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACiN,IAAI,EAAE,KAAK,EAAE,CACzD;IAED,IAAID,QAAQ,CAAC5H,MAAM,KAAKoH,eAAe,CAACpH,MAAM,EAAE;MAC9C,IAAI,CAAChJ,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,EAAE;QACvE2M,QAAQ,EAAEV,eAAe;QACzBW,KAAK,EAAEH;OACR,CAAC;MACF,OAAOtU,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,mCAAmC,CAAC,CAAC;;IAGzE,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,gDAAgD,EAChDkI,QAAQ,CACT;IAED;IACA,IAAI,CAACI,wBAAwB,CAACJ,QAAQ,EAAE,IAAI,CAAC;IAE7C;IACA,MAAMK,kBAAkB,GAAG;MACzBC,uBAAuB,EAAE;QACvBrB,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAAC5H,MAAM;QAC1B2H,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAAC9Q,iBAAiB,CAAC6O,KAAK,GAAGyB,QAAQ,CAAC5H,MAAM;;KAGnD;IAED;IACA7C,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE;MACtEgK,eAAe,EAAEQ;KAClB,CAAC;IACFzK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEpH,+BAA+B,CAAC;IAE/D,OAAO,IAAI,CAACe,MAAM,CACf+J,MAAM,CAAkC;MACvCC,QAAQ,EAAE/K,+BAA+B;MACzCuK,SAAS,EAAE;QAAE6G,eAAe,EAAEQ;MAAQ,CAAE;MACxCK,kBAAkB,EAAEA,kBAAkB;MACtCzH,WAAW,EAAE,KAAK;MAClBZ,WAAW,EAAE,UAAU,CAAE;KAC1B,CAAC,CACDjE,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAAC9I,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,EAAEI,MAAM,CAAC;MAC9D3C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0C,MAAM,CAAC;MAEvC;MACA,IAAIA,MAAM,CAAC0E,MAAM,EAAE;QACjB,IAAI,CAACxN,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjB2E,MAAM,CAAC0E,MAAM,CACd;QACDrH,OAAO,CAAChC,KAAK,CAAC,iBAAiB,EAAE2E,MAAM,CAAC0E,MAAM,CAAC;;MAGjD;MACA,MAAMmC,QAAQ,GACZ7G,MAAM,CAAClE,IAAI,EAAEsM,uBAAuB,IACpCD,kBAAkB,CAACC,uBAAuB;MAE5C,OAAOvB,QAAQ;IACjB,CAAC,CAAC,EACFjT,UAAU,CAAEyH,KAAY,IAAI;MAC1B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACDgC,OAAO,CAAChC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C;MACA;MACA,OAAO9H,EAAE,CAAC;QACRwT,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAAC5H,MAAM;QAC1B2H,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAAC9Q,iBAAiB,CAAC6O,KAAK,GAAGyB,QAAQ,CAAC5H,MAAM;OAEjD,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACA;EACA;EACA;EAEA;;;;;EAKAqI,sBAAsBA,CAACC,MAAc;IACnC,OAAO,IAAI,CAACvR,MAAM,CACfuE,SAAS,CAA6B;MACrCI,KAAK,EAAEhF,wBAAwB;MAC/B6J,SAAS,EAAE;QAAE+H;MAAM;KACpB,CAAC,CACD3M,IAAI,CACHlI,GAAG,CAAC,CAAC;MAAEmI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE2M,UAAU,EAAE;QACrB,MAAM,IAAItI,KAAK,CAAC,yBAAyB,CAAC;;MAE5C,OAAOrE,IAAI,CAAC2M,UAAU;IACxB,CAAC,CAAC,EACF5U,GAAG,CAAE6U,MAAM,IAAI;MACb,IAAI,CAAC3Q,WAAW,CAACiD,IAAI,CAAC0N,MAAM,CAAC;MAC7B,IAAI,CAACC,gBAAgB,CAACD,MAAM,CAAC;IAC/B,CAAC,CAAC,EACF9U,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC7D,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOAyI,cAAcA,CACZJ,MAAc,EACdK,UAAkB,EAClBC,UAAkB;IAElB,OAAO,IAAI,CAAC7R,MAAM,CACf+J,MAAM,CAAkC;MACvCC,QAAQ,EAAEtK,yBAAyB;MACnC8J,SAAS,EAAE;QACT+H,MAAM;QACNK,UAAU;QACVC;;KAEH,CAAC,CACDjN,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAM+G,OAAO,GAAG/G,MAAM,CAAClE,IAAI,EAAE8M,cAAc;MAC3C,IAAI,CAAC7B,OAAO,EAAE;QACZ,MAAM,IAAI5G,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO4G,OAAO;IAChB,CAAC,CAAC,EACFnT,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;;;;EAUA4I,cAAcA,CACZrP,KAAA,GAAgB,EAAE,EAClB6J,MAAA,GAAiB,CAAC,EAClByF,MAAiB,EACjBpK,IAAe,EACfqK,SAAyB,EACzBC,OAAuB;IAEvB,OAAO,IAAI,CAACjS,MAAM,CACf4I,UAAU,CAA0B;MACnCjE,KAAK,EAAEpF,kBAAkB;MACzBiK,SAAS,EAAE;QACT/G,KAAK;QACL6J,MAAM;QACNyF,MAAM;QACNpK,IAAI;QACJqK,SAAS;QACTC;OACD;MACDpJ,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMmJ,OAAO,GAAGnJ,MAAM,CAAClE,IAAI,EAAEsN,WAAW,IAAI,EAAE;MAC9C,IAAI,CAAClS,MAAM,CAAC0I,KAAK,CAAC,aAAauJ,OAAO,CAACjJ,MAAM,qBAAqB,CAAC;MACnE,OAAOiJ,OAAO;IAChB,CAAC,CAAC,EACFvV,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAkJ,cAAcA,CAACb,MAAc;IAC3B,OAAO,IAAI,CAACvR,MAAM,CACf4I,UAAU,CAAwB;MACjCjE,KAAK,EAAEnF,kBAAkB;MACzBgK,SAAS,EAAE;QAAE+H;MAAM,CAAE;MACrB1I,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMsJ,OAAO,GAAGtJ,MAAM,CAAClE,IAAI,EAAEyN,WAAW;MACxC,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAInJ,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CAAC,+BAA+B4I,MAAM,EAAE,CAAC;MAC1D,OAAOc,OAAO;IAChB,CAAC,CAAC,EACF1V,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAqJ,YAAYA,CAAA;IACV,OAAO,IAAI,CAACvS,MAAM,CACf4I,UAAU,CAAqB;MAC9BjE,KAAK,EAAElF,gBAAgB;MACvBoJ,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMyJ,KAAK,GAAGzJ,MAAM,CAAClE,IAAI,EAAE4N,SAAS;MACpC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAItJ,KAAK,CAAC,sBAAsB,CAAC;;MAEzC,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CAAC,uBAAuB,EAAE6J,KAAK,CAAC;MACjD,OAAOA,KAAK;IACd,CAAC,CAAC,EACF7V,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACtD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQwI,gBAAgBA,CAACD,MAAkB;IACzC,QAAQA,MAAM,CAAC9J,IAAI;MACjB,KAAK,eAAe;QAClB,IAAI,CAAC+K,kBAAkB,CAACjB,MAAM,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,IAAI,CAACkB,YAAY,CAAClB,MAAM,CAAC;QACzB;MACF,KAAK,UAAU;QACb,IAAI,CAACmB,aAAa,CAACnB,MAAM,CAAC;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACoB,gBAAgB,CAACpB,MAAM,CAAC;QAC7B;MACF;QACE,IAAI,CAACxR,MAAM,CAAC0I,KAAK,CAAC,0BAA0B8I,MAAM,CAAC9J,IAAI,EAAE,EAAE8J,MAAM,CAAC;;EAExE;EAEA;;;;EAIQiB,kBAAkBA,CAACjB,MAAkB;IAC3C,IAAI,CAAC,IAAI,CAACxQ,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACmE,KAAK,CAAC,gDAAgD,CAAC;MACnE;;IAGF,IAAI;MACF,MAAM0O,SAAS,GAAGtP,IAAI,CAACC,KAAK,CAACgO,MAAM,CAAC5M,IAAI,CAAC;MACzC,IAAI,CAAC5D,cAAc,CAChB8R,eAAe,CAAC,IAAIC,eAAe,CAACF,SAAS,CAAC,CAAC,CAC/CnN,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MACjE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,6BAA6B,EAAEA,KAAc,CAAC;;EAEpE;EAEA;;;;EAIQuO,YAAYA,CAAClB,MAAkB;IACrC,IAAI,CAAC,IAAI,CAACxQ,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACmE,KAAK,CAAC,yCAAyC,CAAC;MAC5D;;IAGF,IAAI;MACF,MAAM6O,MAAM,GAAGzP,IAAI,CAACC,KAAK,CAACgO,MAAM,CAAC5M,IAAI,CAAC;MACtC,IAAI,CAAC5D,cAAc,CAChBiS,oBAAoB,CAAC,IAAIC,qBAAqB,CAACF,MAAM,CAAC,CAAC,CACvDtN,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,kCAAkC,EAAEA,KAAc,CAAC;MACvE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAc,CAAC;;EAE7D;EAEA;;;;EAIQwO,aAAaA,CAACnB,MAAkB;IACtC,IAAI,CAAC7L,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACwN,WAAW,EAAE;IAElB;IACA,MAAMC,WAAW,GAAG,IAAI,CAACzS,UAAU,CAACwO,KAAK;IACzC,IAAIiE,WAAW,IAAIA,WAAW,CAACxP,EAAE,KAAK4N,MAAM,CAACF,MAAM,EAAE;MACnD,IAAI,CAAC3Q,UAAU,CAACmD,IAAI,CAAC;QACnB,GAAGsP,WAAW;QACdtB,MAAM,EAAE/U,UAAU,CAACsW,KAAK;QACxBC,OAAO,EAAE,IAAIlJ,IAAI,EAAE,CAACrH,WAAW;OAChC,CAAC;;EAEN;EAEA;;;;EAIQ6P,gBAAgBA,CAACpB,MAAkB;IACzC,IAAI,CAAC7L,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACwN,WAAW,EAAE;IAElB;IACA,MAAMC,WAAW,GAAG,IAAI,CAACzS,UAAU,CAACwO,KAAK;IACzC,IAAIiE,WAAW,IAAIA,WAAW,CAACxP,EAAE,KAAK4N,MAAM,CAACF,MAAM,EAAE;MACnD,IAAI,CAAC3Q,UAAU,CAACmD,IAAI,CAAC;QACnB,GAAGsP,WAAW;QACdtB,MAAM,EAAE/U,UAAU,CAACwW,QAAQ;QAC3BD,OAAO,EAAE,IAAIlJ,IAAI,EAAE,CAACrH,WAAW;OAChC,CAAC;;EAEN;EAEA;;;EAGQoQ,WAAWA,CAAA;IACjB,IAAI,IAAI,CAACrS,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC0S,SAAS,EAAE,CAAC9P,OAAO,CAAE+P,KAAK,IAAKA,KAAK,CAAC9N,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC7E,WAAW,GAAG,IAAI;MACvB,IAAI,CAACO,YAAY,CAACyC,IAAI,CAAC,IAAI,CAAC;;IAG9B,IAAI,IAAI,CAAC9C,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAC0S,KAAK,EAAE;MAC3B,IAAI,CAAC1S,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACO,aAAa,CAACwC,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;EAKQ6P,iBAAiBA,CAACC,QAAkB;IAC1C,MAAMC,WAAW,GAA2B;MAC1C1O,KAAK,EAAE,IAAI;MACX2O,KAAK,EACHF,QAAQ,KAAK9W,QAAQ,CAACiX,KAAK,GACvB;QACEC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QACtBC,MAAM,EAAE;UAAED,KAAK,EAAE;QAAG;OACrB,GACD;KACP;IAED,OAAO,IAAI7X,UAAU,CAAe+X,QAAQ,IAAI;MAC9CC,SAAS,CAACC,YAAY,CACnBC,YAAY,CAACT,WAAW,CAAC,CACzBU,IAAI,CAAEC,MAAM,IAAI;QACfL,QAAQ,CAACrQ,IAAI,CAAC0Q,MAAM,CAAC;QACrBL,QAAQ,CAACM,QAAQ,EAAE;MACrB,CAAC,CAAC,CACD/O,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACzDgQ,QAAQ,CAAChQ,KAAK,CAAC,IAAI8E,KAAK,CAAC,gCAAgC,CAAC,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA;;;;EAIQyL,cAAcA,CAAA;IACpB,OAAOtK,IAAI,CAACkB,GAAG,EAAE,CAACqJ,QAAQ,EAAE,GAAGxD,IAAI,CAACyD,MAAM,EAAE,CAACD,QAAQ,CAAC,EAAE,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA;EACA;EACA;EACA;EACAC,WAAWA,CACTC,YAAY,GAAG,KAAK,EACpBC,MAAe,EACf1L,IAAA,GAAe,CAAC,EAChB9G,KAAA,GAAgB,EAAE,EAClByS,MAAA,GAAiB,UAAU,EAC3BC,SAAA,GAAoB,KAAK,EACzBC,QAAkB;IAElB,IAAI,CAACnV,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,2CAA2C2I,YAAY,YACrDC,MAAM,IAAI,SACZ,UAAU1L,IAAI,WAAW9G,KAAK,YAAYyS,MAAM,eAAeC,SAAS,cAAcC,QAAQ,EAAE,CACjG;IAED,MAAM7J,GAAG,GAAGlB,IAAI,CAACkB,GAAG,EAAE;IACtB,MAAM8J,UAAU,GACd,CAACL,YAAY,IACb,IAAI,CAACrT,UAAU,CAACsH,MAAM,GAAG,CAAC,IAC1BsC,GAAG,GAAG,IAAI,CAAC5K,aAAa,IAAI,IAAI,CAACD,cAAc,IAC/C,CAACuU,MAAM,IACP1L,IAAI,KAAK,CAAC,IACV9G,KAAK,IAAI,IAAI,CAACd,UAAU,CAACsH,MAAM;IAEjC;IACA,IAAIoM,UAAU,EAAE;MACd,IAAI,CAACpV,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,uBAAuB,IAAI,CAAChH,UAAU,CAACsH,MAAM,SAAS,CACvD;MACD,OAAO3M,EAAE,CAAC,CAAC,GAAG,IAAI,CAACqF,UAAU,CAAC,CAAC;;IAGjC,IAAI,CAAC1B,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,2DACEqM,YAAY,GAAG,cAAc,GAAG,aAClC,EAAE,CACH;IAED,OAAO,IAAI,CAAChV,MAAM,CACf4I,UAAU,CAAM;MACfjE,KAAK,EAAEjH,kBAAkB;MACzB8L,SAAS,EAAE;QACTyL,MAAM;QACN1L,IAAI;QACJ9G,KAAK;QACLyS,MAAM;QACNC,SAAS;QACTC,QAAQ,EAAEA,QAAQ,KAAKrS,SAAS,GAAGqS,QAAQ,GAAG;OAC/C;MACDvM,WAAW,EAAEmM,YAAY,GAAG,cAAc,GAAG;KAC9C,CAAC,CACDlM,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAAC9I,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,yBAAyB,EACzBI,MAAM,CACP;MAED,IAAIA,MAAM,CAAC0E,MAAM,EAAE;QACjB,IAAI,CAACxN,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChC2E,MAAM,CAAC0E,MAAM,CACd;QACD,MAAM,IAAIvE,KAAK,CAACH,MAAM,CAAC0E,MAAM,CAAC/Q,GAAG,CAAEgR,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,IAAI,CAAC5E,MAAM,CAAClE,IAAI,EAAEkQ,WAAW,EAAE;QAC7B,IAAI,CAAC9U,MAAM,CAACwP,IAAI,CACd,gBAAgB,EAChB,oCAAoC,CACrC;QACD,OAAO,EAAE;;MAGX,MAAM6F,iBAAiB,GAAGvM,MAAM,CAAClE,IAAI,CAACkQ,WAAW;MAEjD;MACA,IAAI,CAAC9U,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;QAC1D9G,UAAU,EAAEyT,iBAAiB,CAACzT,UAAU;QACxCC,UAAU,EAAEwT,iBAAiB,CAACxT,UAAU;QACxCC,WAAW,EAAEuT,iBAAiB,CAACvT,WAAW;QAC1CC,WAAW,EAAEsT,iBAAiB,CAACtT,WAAW;QAC1CC,eAAe,EAAEqT,iBAAiB,CAACrT;OACpC,CAAC;MAEF;MACA,MAAMsT,KAAK,GAAW,EAAE;MACxB,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,CAACC,KAAK,EAAE;QAC1C,IAAI;UACF,IAAIC,IAAI,EAAE;YACRD,KAAK,CAAC9E,IAAI,CAAC,IAAI,CAACpF,aAAa,CAACmK,IAAI,CAAC,CAAC;;SAEvC,CAAC,OAAOpR,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACwP,IAAI,CACd,gBAAgB,EAChB,mCAAmC,EACnCrL,KAAK,CACN;;;MAIL,IAAI,CAACnE,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,YAAYkJ,KAAK,CAACtM,MAAM,4BAA4BqM,iBAAiB,CAACvT,WAAW,OAAOuT,iBAAiB,CAACxT,UAAU,GAAG,CACxH;MAED;MACA,IAAI,CAACmT,MAAM,IAAI1L,IAAI,KAAK,CAAC,IAAI,CAAC6L,QAAQ,EAAE;QACtC,IAAI,CAACzT,UAAU,GAAG,CAAC,GAAG4T,KAAK,CAAC;QAC5B,IAAI,CAAC5U,aAAa,GAAG0J,IAAI,CAACkB,GAAG,EAAE;QAC/B,IAAI,CAACtL,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,2BAA2B4M,KAAK,CAACtM,MAAM,QAAQ,CAChD;;MAGH;MACA,IAAI,CAACrH,qBAAqB,GAAG;QAC3BC,UAAU,EAAEyT,iBAAiB,CAACzT,UAAU;QACxCC,UAAU,EAAEwT,iBAAiB,CAACxT,UAAU;QACxCC,WAAW,EAAEuT,iBAAiB,CAACvT,WAAW;QAC1CC,WAAW,EAAEsT,iBAAiB,CAACtT,WAAW;QAC1CC,eAAe,EAAEqT,iBAAiB,CAACrT;OACpC;MAED,OAAOsT,KAAK;IACd,CAAC,CAAC,EACF5Y,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAEnE,IAAIA,KAAK,CAACkK,aAAa,EAAE;QACvB,IAAI,CAACrO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACkK,aAAa,CACpB;;MAGH,IAAIlK,KAAK,CAACmK,YAAY,EAAE;QACtB,IAAI,CAACtO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACmK,YAAY,CACnB;;MAGH;MACA,IACE,IAAI,CAAC5M,UAAU,CAACsH,MAAM,GAAG,CAAC,IAC1BM,IAAI,KAAK,CAAC,IACV,CAAC0L,MAAM,IACP,CAACG,QAAQ,EACT;QACA,IAAI,CAACnV,MAAM,CAACwP,IAAI,CACd,gBAAgB,EAChB,aAAa,IAAI,CAAC9N,UAAU,CAACsH,MAAM,kCAAkC,CACtE;QACD,OAAO3M,EAAE,CAAC,CAAC,GAAG,IAAI,CAACqF,UAAU,CAAC,CAAC;;MAGjC,OAAOpF,UAAU,CACf,MACE,IAAI2M,KAAK,CACP,0BAA0B9E,KAAK,CAACuI,OAAO,IAAI,eAAe,EAAE,CAC7D,CACJ;IACH,CAAC,CAAC,CACH;EACL;EACA8I,UAAUA,CAAC3J,MAAc;IACvB,OAAO,IAAI,CAAC9L,MAAM,CACf4I,UAAU,CAAqB;MAC9BjE,KAAK,EAAElH,cAAc;MACrB+L,SAAS,EAAE;QAAE3F,EAAE,EAAEiI;MAAM,CAAE;MACzBjD,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAK,IAAI,CAACsC,aAAa,CAACtC,MAAM,CAAClE,IAAI,EAAE4Q,UAAU,CAAC,CAAC,EAC5D9Y,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC5D,CAAC,CAAC,CACH;EACL;EACAwM,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC1V,MAAM,CACf4I,UAAU,CAAyB;MAClCjE,KAAK,EAAExG,sBAAsB;MAC7B0K,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBlI,GAAG,CAAEqM,MAAM,IAAK,IAAI,CAACsC,aAAa,CAACtC,MAAM,CAAClE,IAAI,EAAE6Q,cAAc,CAAC,CAAC,EAChE/Y,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MACD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EACAyM,aAAaA,CAAC7J,MAAc;IAC1B,OAAO,IAAI,CAAC9L,MAAM,CACf+J,MAAM,CAAwB;MAC7BC,QAAQ,EAAElM,wBAAwB;MAClC0L,SAAS,EAAE;QAAEsC;MAAM;KACpB,CAAC,CACDlH,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE8Q,aAAa,EAC7B,MAAM,IAAIzM,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAO,IAAI,CAACmC,aAAa,CAACtC,MAAM,CAAClE,IAAI,CAAC8Q,aAAa,CAAC;IACtD,CAAC,CAAC,EACFhZ,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EACA0M,cAAcA,CAAC9J,MAAc;IAC3B,OAAO,IAAI,CAAC9L,MAAM,CACf+J,MAAM,CAAyB;MAC9BC,QAAQ,EAAEjM,yBAAyB;MACnCyL,SAAS,EAAE;QAAEsC;MAAM;KACpB,CAAC,CACDlH,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE+Q,cAAc,EAC9B,MAAM,IAAI1M,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAO,IAAI,CAACmC,aAAa,CAACtC,MAAM,CAAClE,IAAI,CAAC+Q,cAAc,CAAC;IACvD,CAAC,CAAC,EACFjZ,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGA2M,WAAWA,CACT3Q,IAAY,EACZ+H,cAAwB,EACxB6I,KAAY,EACZC,WAAoB;IAEpB,IAAI,CAAC9V,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,mBAAmBzD,IAAI,SAAS+H,cAAc,CAAChE,MAAM,eAAe,CACrE;IAED,IAAI,CAAC/D,IAAI,IAAI,CAAC+H,cAAc,IAAIA,cAAc,CAAChE,MAAM,KAAK,CAAC,EAAE;MAC3D,OAAO1M,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,sCAAsC,CAAC,CACxD;;IAGH,OAAO,IAAI,CAAClJ,MAAM,CACf+J,MAAM,CAAC;MACNC,QAAQ,EAAEzL,qBAAqB;MAC/BiL,SAAS,EAAE;QAAEtE,IAAI;QAAE+H,cAAc;QAAE6I,KAAK;QAAEC;MAAW;KACtD,CAAC,CACDnR,IAAI,CACHlI,GAAG,CAAEqM,MAAW,IAAI;MAClB,MAAMiN,KAAK,GAAGjN,MAAM,CAAClE,IAAI,EAAEgR,WAAW;MACtC,IAAI,CAACG,KAAK,EAAE;QACV,MAAM,IAAI9M,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,IAAI,CAACjJ,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,+BAA+B2J,KAAK,CAACnS,EAAE,EAAE,CAC1C;MACD,OAAOmS,KAAK;IACd,CAAC,CAAC,EACFrZ,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA+M,WAAWA,CAACC,OAAe,EAAEC,KAAU;IACrC,IAAI,CAAClW,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,mBAAmBuN,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO3Z,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAClJ,MAAM,CACf+J,MAAM,CAAC;MACNC,QAAQ,EAAExL,qBAAqB;MAC/BgL,SAAS,EAAE;QAAE3F,EAAE,EAAEqS,OAAO;QAAEC;MAAK;KAChC,CAAC,CACDvR,IAAI,CACHlI,GAAG,CAAEqM,MAAW,IAAI;MAClB,MAAMiN,KAAK,GAAGjN,MAAM,CAAClE,IAAI,EAAEoR,WAAW;MACtC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAI9M,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAACjJ,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,+BAA+B2J,KAAK,CAACnS,EAAE,EAAE,CAC1C;MACD,OAAOmS,KAAK;IACd,CAAC,CAAC,EACFrZ,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAkN,WAAWA,CACTF,OAAe;IAEf,IAAI,CAACjW,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,mBAAmBuN,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO3Z,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAClJ,MAAM,CACf+J,MAAM,CAAC;MACNC,QAAQ,EAAEvL,qBAAqB;MAC/B+K,SAAS,EAAE;QAAE3F,EAAE,EAAEqS;MAAO;KACzB,CAAC,CACDtR,IAAI,CACHlI,GAAG,CAAEqM,MAAW,IAAI;MAClB,MAAM6G,QAAQ,GAAG7G,MAAM,CAAClE,IAAI,EAAEuR,WAAW;MACzC,IAAI,CAACxG,QAAQ,EAAE;QACb,MAAM,IAAI1G,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAACjJ,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,+BAA+B6J,OAAO,EAAE,CACzC;MACD,OAAOtG,QAAQ;IACjB,CAAC,CAAC,EACFjT,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAmN,UAAUA,CACRH,OAAe;IAEf,IAAI,CAACjW,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,kBAAkBuN,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO3Z,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAClJ,MAAM,CACf+J,MAAM,CAAC;MACNC,QAAQ,EAAEtL,oBAAoB;MAC9B8K,SAAS,EAAE;QAAE0M;MAAO;KACrB,CAAC,CACDtR,IAAI,CACHlI,GAAG,CAAEqM,MAAW,IAAI;MAClB,MAAM6G,QAAQ,GAAG7G,MAAM,CAAClE,IAAI,EAAEwR,UAAU;MACxC,IAAI,CAACzG,QAAQ,EAAE;QACb,MAAM,IAAI1G,KAAK,CAAC,8BAA8B,CAAC;;MAEjD,IAAI,CAACjJ,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,4BAA4B6J,OAAO,EAAE,CACtC;MACD,OAAOtG,QAAQ;IACjB,CAAC,CAAC,EACFjT,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAoN,QAAQA,CAACJ,OAAe;IACtB,IAAI,CAACjW,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,kBAAkBuN,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO3Z,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAClJ,MAAM,CACf2E,KAAK,CAAC;MACLA,KAAK,EAAEhG,eAAe;MACtB6K,SAAS,EAAE;QAAE3F,EAAE,EAAEqS;MAAO,CAAE;MAC1BrN,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHlI,GAAG,CAAEqM,MAAW,IAAI;MAClB,MAAMiN,KAAK,GAAGjN,MAAM,CAAClE,IAAI,EAAEyR,QAAQ;MACnC,IAAI,CAACN,KAAK,EAAE;QACV,MAAM,IAAI9M,KAAK,CAAC,mBAAmB,CAAC;;MAEtC,IAAI,CAACjJ,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,iCAAiC6J,OAAO,EAAE,CAC3C;MACD,OAAOF,KAAK;IACd,CAAC,CAAC,EACFrZ,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,oCAAoC,CAAC,CACtD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAqN,aAAaA,CAACzK,MAAc;IAC1B,IAAI,CAAC7L,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,4BAA4BmD,MAAM,EAAE,CAAC;IAEzE,IAAI,CAACA,MAAM,EAAE;MACX,OAAOvP,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,4BAA4B,CAAC,CAAC;;IAGlE,OAAO,IAAI,CAAClJ,MAAM,CACf2E,KAAK,CAAC;MACLA,KAAK,EAAE/F,qBAAqB;MAC5B4K,SAAS,EAAE;QAAEsC;MAAM,CAAE;MACrBjD,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHlI,GAAG,CAAEqM,MAAW,IAAI;MAClB,MAAMwH,MAAM,GAAGxH,MAAM,CAAClE,IAAI,EAAE0R,aAAa,IAAI,EAAE;MAC/C,IAAI,CAACtW,MAAM,CAACoM,IAAI,CACd,gBAAgB,EAChB,aAAakE,MAAM,CAACtH,MAAM,qBAAqB6C,MAAM,EAAE,CACxD;MACD,OAAOyE,MAAM;IACf,CAAC,CAAC,EACF5T,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EASAsN,sBAAsBA,CAAClN,cAAsB;IAC3C,IAAI,CAACA,cAAc,EAAE;MACnB,OAAO/M,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,MAAMuN,QAAQ,GAAG,YAAYnN,cAAc,EAAE;IAC7C,IAAI,IAAI,CAAC3G,iBAAiB,CAACoL,GAAG,CAAC0I,QAAQ,CAAC,EAAE;MACxC,MAAMC,QAAQ,GAAG,IAAI,CAAC9T,oBAAoB,CAAC4N,GAAG,CAACiG,QAAQ,CAAC,IAAI,CAAC;MAC7D,IAAI,CAAC7T,oBAAoB,CAACkB,GAAG,CAAC2S,QAAQ,EAAEC,QAAQ,GAAG,CAAC,CAAC;MACrD,OAAO,IAAI,CAAC/T,iBAAiB,CAAC6N,GAAG,CAACiG,QAAQ,CAAE;;IAG9C;IACA,IAAI,CAAC,IAAI,CAACE,YAAY,EAAE,EAAE;MACxB,OAAOla,KAAK;;IAGd;IACA,IAAI,CAACma,WAAW,CAACC,UAAU,EAAE;MAC3BzQ,OAAO,CAACC,GAAG,CAAC,+BAA+BiD,cAAc,EAAE,CAAC;;IAE9DlD,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5C9I,yBAAyB,CAC1B;IACD6I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAAEiD;IAAc,CAAE,CAAC;IAEpE,MAAMwN,IAAI,GAAG,IAAI,CAAC9W,MAAM,CACrBuE,SAAS,CAA2B;MACnCI,KAAK,EAAEpH,yBAAyB;MAChCiM,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0C,MAAM,CAAC;MAClE3C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0C,MAAM,CAAClE,IAAI,CAAC;MAClDuB,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC0C,MAAM,CAAClE,IAAI,EAAEkS,WAAW,CACzB;IACH,CAAC,CAAC,EACFra,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAM2B,GAAG,GAAG3B,MAAM,CAAClE,IAAI,EAAEkS,WAAW;MACpC,IAAI,CAACrM,GAAG,EAAE;QACRtE,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD0C,MAAM,CACP;QACD,IAAI,CAAC9I,MAAM,CAACwP,IAAI,CAAC,gCAAgC,CAAC;QAClD,MAAM,IAAIvG,KAAK,CAAC,6BAA6B,CAAC;;MAGhD,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CACf,+CAA+C,EAC/C+B,GAAG,CACJ;MAED;MACA,IAAI,CAACA,GAAG,CAAC7G,EAAE,IAAI,CAAC6G,GAAG,CAACY,GAAG,EAAE;QACvB,IAAI,CAACrL,MAAM,CAACwP,IAAI,CACd,oDAAoD,CACrD;QACD/E,GAAG,CAAC7G,EAAE,GAAG,QAAQwG,IAAI,CAACkB,GAAG,EAAE,EAAE;;MAG/B,IAAI;QACF;QACA,MAAMyL,iBAAiB,GAAG,IAAI,CAAC/M,gBAAgB,CAACS,GAAG,CAAC;QAEpD,IAAI,CAACzK,MAAM,CAAC0I,KAAK,CACf,4CAA4C,EAC5CqO,iBAAiB,CAClB;QAED;QACA,IACEA,iBAAiB,CAACrP,IAAI,KAAK7K,WAAW,CAACkX,KAAK,IAC5CgD,iBAAiB,CAACrP,IAAI,KAAK7K,WAAW,CAACma,aAAa,IACnDD,iBAAiB,CAACE,WAAW,IAC5BF,iBAAiB,CAACE,WAAW,CAACC,IAAI,CAC/BC,GAAG,IAAKA,GAAG,CAACzP,IAAI,KAAK,OAAO,CAC7B,EACJ;UACA,IAAI,CAAC1H,MAAM,CAAC0I,KAAK,CACf,iDAAiD,CAClD;;QAGH;QACA,IAAI,CAACzI,IAAI,CAACmX,GAAG,CAAC,MAAK;UACjB,IAAI,CAACpX,MAAM,CAAC0I,KAAK,CACf,kDAAkD,CACnD;UACD,IAAI,CAAC2O,gCAAgC,CACnChO,cAAc,EACd0N,iBAAiB,CAClB;QACH,CAAC,CAAC;QAEF,OAAOA,iBAAiB;OACzB,CAAC,OAAOpQ,GAAG,EAAE;QACZ,IAAI,CAAC3G,MAAM,CAACmE,KAAK,CAAC,8BAA8B,EAAEwC,GAAG,CAAC;QAEtD;QACA,MAAM2Q,cAAc,GAAY;UAC9B1T,EAAE,EAAE6G,GAAG,CAAC7G,EAAE,IAAI6G,GAAG,CAACY,GAAG,IAAI,QAAQjB,IAAI,CAACkB,GAAG,EAAE,EAAE;UAC7CC,OAAO,EAAEd,GAAG,CAACc,OAAO,IAAI,EAAE;UAC1B7D,IAAI,EAAE+C,GAAG,CAAC/C,IAAI,IAAI7K,WAAW,CAAC2O,IAAI;UAClCd,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACT,GAAG,CAACC,SAAS,CAAC;UACvCe,MAAM,EAAE,KAAK;UACbN,MAAM,EAAEV,GAAG,CAACU,MAAM,GACd,IAAI,CAACC,aAAa,CAACX,GAAG,CAACU,MAAM,CAAC,GAC9B;YACEvH,EAAE,EAAE,IAAI,CAAC8H,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEjB;QAED,IAAI,CAAC3L,MAAM,CAAC0I,KAAK,CACf,sCAAsC,EACtC4O,cAAc,CACf;QACD,OAAOA,cAAc;;IAEzB,CAAC,CAAC,EACF5a,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD;MACA,OAAO3H,KAAK;IACd,CAAC,CAAC;IACF;IACAI,MAAM,CAAE8P,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC;IAC9B;IACAnQ,KAAK,CAAC,CAAC,CAAC,CACT;IAEH4J,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D,MAAMmR,GAAG,GAAGV,IAAI,CAACvS,SAAS,CAAC;MACzBR,IAAI,EAAG4I,OAAO,IAAI;QAChBvG,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEsG,OAAO,CAAC;QACnE;QACA,IAAI,CAAC1M,MAAM,CAAC0I,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEgE,OAAO,CAAC;QAErE;QACA,IAAI,CAAC2K,gCAAgC,CAAChO,cAAc,EAAEqD,OAAO,CAAC;MAChE,CAAC;MACDvI,KAAK,EAAGwC,GAAG,IAAI;QACbR,OAAO,CAAChC,KAAK,CAAC,8BAA8B,EAAEwC,GAAG,CAAC;QAClD,IAAI,CAAC3G,MAAM,CAACmE,KAAK,CAAC,gCAAgC,EAAEwC,GAAG,CAAC;MAC1D,CAAC;MACD8N,QAAQ,EAAEA,CAAA,KAAK;QACbtO,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD;KACD,CAAC;IAEF;IACAD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEmR,GAAG,CAAC;IAC1DpR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACrG,MAAM,CAAC;IAE1D,IAAI,CAACS,aAAa,CAACgQ,IAAI,CAAC+G,GAAG,CAAC;IAC5BpR,OAAO,CAACC,GAAG,CACT,2FAA2F,IAAI,CAAC5F,aAAa,CAACwI,MAAM,EAAE,CACvH;IACD,OAAO6N,IAAI;EACb;EAEA;;;;;EAKQQ,gCAAgCA,CACtChO,cAAsB,EACtBqD,OAAgB;IAEhB,IAAI,CAAC1M,MAAM,CAAC0I,KAAK,CACf,oCAAoCW,cAAc,qBAAqBqD,OAAO,CAAC9I,EAAE,EAAE,CACpF;IAED;IACA,IAAI,CAAC3D,IAAI,CAACmX,GAAG,CAAC,MAAK;MACjB;MACA,IAAI,CAAClX,kBAAkB,CAAC4D,IAAI,CAACuF,cAAc,CAAC;MAE5C,IAAI,CAACrJ,MAAM,CAAC0I,KAAK,CAAC,oDAAoD,CAAC;IACzE,CAAC,CAAC;IAEF;IACA8O,UAAU,CAAC,MAAK;MACd,IAAI,CAACrL,eAAe,CAAC9C,cAAc,CAAC,CAAC/E,SAAS,CAAC;QAC7CR,IAAI,EAAG2I,YAAY,IAAI;UACrB,IAAI,CAACzM,MAAM,CAAC0I,KAAK,CACf,8BAA8BW,cAAc,mBAC1CoD,YAAY,EAAEhD,QAAQ,EAAET,MAAM,IAAI,CACpC,WAAW,CACZ;QACH,CAAC;QACD7E,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gDAAgDkF,cAAc,GAAG,EACjElF,KAAK,CACN;QACH;OACD,CAAC;IACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACT;EAEA;;;EAGQsT,0BAA0BA,CAAA;IAChCtR,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IAErE;IACA,IAAI,CAAC+G,gBAAgB,CAAC,IAAI,CAAC,CAAC7I,SAAS,CAAC;MACpCR,IAAI,EAAG3D,aAAa,IAAI;QACtBgG,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjDjG,aAAa,CAAC6I,MAAM,CACrB;MACH,CAAC;MACD7E,KAAK,EAAGA,KAAK,IAAI;QACfgC,OAAO,CAAChC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACpE;KACD,CAAC;EACJ;EAEAM,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAC,IAAI,CAACiS,YAAY,EAAE,EAAE;MACxB,IAAI,CAAC1W,MAAM,CAACwP,IAAI,CACd,+EAA+E,CAChF;MACD,OAAOlT,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CAAC,iDAAiD,CAAC;IAEpE,MAAMmO,IAAI,GAAG,IAAI,CAAC9W,MAAM,CACrBuE,SAAS,CAA8B;MACtCI,KAAK,EAAEnH;KACR,CAAC,CACDoH,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IACT,IAAI,CAAC9I,MAAM,CAAC0I,KAAK,CACf,uDAAuD,EACvDI,MAAM,CACP,CACF,EACDrM,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMyM,IAAI,GAAGzM,MAAM,CAAClE,IAAI,EAAE8S,iBAAiB;MAC3C,IAAI,CAACnC,IAAI,EAAE;QACT,IAAI,CAACvV,MAAM,CAACmE,KAAK,CAAC,4BAA4B,CAAC;QAC/C,MAAM,IAAI8E,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO,IAAI,CAACmC,aAAa,CAACmK,IAAI,CAAC;IACjC,CAAC,CAAC,EACF7Y,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MAC/D,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,EACF1M,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMgb,GAAG,GAAGV,IAAI,CAACvS,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACgQ,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOV,IAAI;EACb;EACAc,8BAA8BA,CAC5BtO,cAAsB;IAEtB,MAAMwN,IAAI,GAAG,IAAI,CAAC9W,MAAM,CACrBuE,SAAS,CAAwC;MAChDI,KAAK,EAAEhH,iCAAiC;MACxC6L,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMmD,IAAI,GAAGnD,MAAM,CAAClE,IAAI,EAAEgT,mBAAmB;MAC7C,IAAI,CAAC3L,IAAI,EAAE,MAAM,IAAIhD,KAAK,CAAC,kCAAkC,CAAC;MAE9D,MAAMqD,sBAAsB,GAAiB;QAC3C,GAAGL,IAAI;QACPM,YAAY,EACVN,IAAI,CAACM,YAAY,EAAE9P,GAAG,CAAEwQ,CAAC,IAAK,IAAI,CAAC7B,aAAa,CAAC6B,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5D4K,WAAW,EAAE5L,IAAI,CAAC4L,WAAW,GACzB;UACE,GAAG5L,IAAI,CAAC4L,WAAW;UACnB1M,MAAM,EAAE,IAAI,CAACC,aAAa,CAACa,IAAI,CAAC4L,WAAW,CAAC1M,MAAM,CAAC;UACnDT,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACe,IAAI,CAAC4L,WAAW,CAACnN,SAAS,CAAC;UACpDP,MAAM,EAAE8B,IAAI,CAAC4L,WAAW,CAAC1N,MAAM,GAC3B,IAAI,CAACe,QAAQ,CAACe,IAAI,CAAC4L,WAAW,CAAC1N,MAAM,CAAC,GACtCrH,SAAS;UACb;UACAc,EAAE,EAAEqI,IAAI,CAAC4L,WAAW,CAACjU,EAAE;UACvB2H,OAAO,EAAEU,IAAI,CAAC4L,WAAW,CAACtM,OAAO;UACjC7D,IAAI,EAAEuE,IAAI,CAAC4L,WAAW,CAACnQ,IAAI;UAC3B+D,MAAM,EAAEQ,IAAI,CAAC4L,WAAW,CAACpM;UACzB;SACD,GACD,IAAI,CAAE;OACX;;MAED,OAAOa,sBAAsC,CAAC,CAAC;IACjD,CAAC,CAAC,EACF5P,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,kCAAkC,EAClCA,KAAK,CACN;MACD,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;IAEH,MAAMsO,GAAG,GAAGV,IAAI,CAACvS,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACgQ,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOV,IAAI;EACb;EACAiB,0BAA0BA,CACxBzO,cAAsB;IAEtB,MAAMwN,IAAI,GAAG,IAAI,CAAC9W,MAAM,CACrBuE,SAAS,CAAwB;MAChCI,KAAK,EAAEzG,6BAA6B;MACpCsL,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEmT,eAAe,CAAC,EAC7Cnb,MAAM,CAACob,OAAO,CAAC,EACftb,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACD,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;IAEH,MAAMsO,GAAG,GAAGV,IAAI,CAACvS,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACgQ,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOV,IAAI;EACb;EACQH,YAAYA,CAAA;IAClB,MAAMuB,KAAK,GAAG5U,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAC2U,KAAK,EAAE;MACV,IAAI,CAACjY,MAAM,CAACwP,IAAI,CAAC,oBAAoB,CAAC;MACtC,OAAO,KAAK;;IAGd,IAAI;MACF;MACA,MAAM0I,KAAK,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9B,IAAID,KAAK,CAAClP,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,CAAChJ,MAAM,CAACwP,IAAI,CAAC,0BAA0B,CAAC;QAC5C,OAAO,KAAK;;MAGd;MACA,MAAM4I,OAAO,GAAG7U,IAAI,CAACC,KAAK,CAAC6U,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAE1C;MACA,IAAI,CAACE,OAAO,CAACE,GAAG,EAAE;QAChB,IAAI,CAACtY,MAAM,CAACwP,IAAI,CAAC,8BAA8B,CAAC;QAChD,OAAO,KAAK;;MAGd,MAAM+I,cAAc,GAAG,IAAInO,IAAI,CAACgO,OAAO,CAACE,GAAG,GAAG,IAAI,CAAC;MACnD,MAAMhN,GAAG,GAAG,IAAIlB,IAAI,EAAE;MAEtB,IAAImO,cAAc,GAAGjN,GAAG,EAAE;QACxB,IAAI,CAACtL,MAAM,CAACwP,IAAI,CAAC,cAAc,EAAE;UAC/BgJ,UAAU,EAAED,cAAc,CAACxV,WAAW,EAAE;UACxCuI,GAAG,EAAEA,GAAG,CAACvI,WAAW;SACrB,CAAC;QACF,OAAO,KAAK;;MAGd,OAAO,IAAI;KACZ,CAAC,OAAOoB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0CAA0C,EAC1CA,KAAc,CACf;MACD,OAAO,KAAK;;EAEhB;EAEAI,4BAA4BA,CAAA;IAC1B;IACA,IAAI,CAAC,IAAI,CAACmS,YAAY,EAAE,EAAE;MACxB,IAAI,CAAC1W,MAAM,CAACwP,IAAI,CACd,2EAA2E,CAC5E;MACD,OAAOnT,EAAE,CAAC,EAAE,CAAC;;IAGf,IAAI,CAAC2D,MAAM,CAAC0I,KAAK,CAAC,kDAAkD,CAAC;IAErE,MAAMmO,IAAI,GAAG,IAAI,CAAC9W,MAAM,CACrBuE,SAAS,CAAyB;MACjCI,KAAK,EAAEzF;KACR,CAAC,CACD0F,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IACT,IAAI,CAAC9I,MAAM,CAAC0I,KAAK,CACf,wDAAwD,EACxDI,MAAM,CACP,CACF,EACDrM,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMsH,eAAe,GAAGtH,MAAM,CAAClE,IAAI,EAAE6T,iBAAiB,IAAI,EAAE;MAC5D,IAAI,CAACzY,MAAM,CAAC0I,KAAK,CACf,oCAAoC,EACpC0H,eAAe,CAChB;MACD,IAAI,CAACY,wBAAwB,CAACZ,eAAe,EAAE,IAAI,CAAC;MACpD,OAAOA,eAAe;IACxB,CAAC,CAAC,EACF1T,UAAU,CAAEiK,GAAG,IAAI;MACjB,IAAI,CAAC3G,MAAM,CAACmE,KAAK,CACf,wCAAwC,EACxCwC,GAAY,CACb;MACD;MACA,OAAOtK,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACF;IACAE,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMgb,GAAG,GAAGV,IAAI,CAACvS,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACgQ,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOV,IAAI;EACb;EACAxS,2BAA2BA,CAAA;IACzB;IACA,MAAM4T,KAAK,GAAG5U,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAC2U,KAAK,EAAE;MACV,IAAI,CAACjY,MAAM,CAACwP,IAAI,CACd,6DAA6D,CAC9D;MACD,OAAOhT,KAAK;;IAGd,IAAI,CAACwD,MAAM,CAAC0I,KAAK,CACf,4DAA4D,CAC7D;IAED,MAAMgQ,OAAO,GAAG,IAAI,CAAC3Y,MAAM,CAACuE,SAAS,CAA4B;MAC/DI,KAAK,EAAExH;KACR,CAAC;IAEF,MAAMyb,UAAU,GAAGD,OAAO,CAAC/T,IAAI,CAC7BlI,GAAG,CAAEqM,MAAM,IAAI;MACb,MAAMnF,YAAY,GAAGmF,MAAM,CAAClE,IAAI,EAAEgU,oBAAoB;MACtD,IAAI,CAACjV,YAAY,EAAE;QACjB,MAAM,IAAIsF,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CACf,sCAAsC,EACtC/E,YAAY,CACb;MAED,MAAMkV,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACnV,YAAY,CAAC;MAE3D;MACA,IAAI,IAAI,CAACvD,iBAAiB,CAAC0N,GAAG,CAAC+K,UAAU,CAACjV,EAAE,CAAC,EAAE;QAC7C,IAAI,CAAC5D,MAAM,CAAC0I,KAAK,CACf,mBAAmBmQ,UAAU,CAACjV,EAAE,6BAA6B,CAC9D;QACD,MAAM,IAAIqF,KAAK,CAAC,sCAAsC,CAAC;;MAGzD;MACA,IAAI,CAACjJ,MAAM,CAAC0I,KAAK,CAAC,iDAAiD,CAAC;MAEpE;MACA,MAAMqQ,oBAAoB,GAAG,IAAI,CAAC5Y,aAAa,CAACgP,KAAK;MACrD,MAAM6J,oBAAoB,GAAGD,oBAAoB,CAACjM,IAAI,CACnD4B,CAAC,IAAKA,CAAC,CAAC9K,EAAE,KAAKiV,UAAU,CAACjV,EAAE,CAC9B;MAED,IAAIoV,oBAAoB,EAAE;QACxB,IAAI,CAAChZ,MAAM,CAAC0I,KAAK,CACf,sDAAsD,EACtDmQ,UAAU,CAACjV,EAAE,CACd;QACD,OAAOiV,UAAU;;MAGnB;MACA,IAAI,CAAC3S,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAAC+S,uBAAuB,CAACJ,UAAU,CAAC;MAExC;MACA,IAAI,CAAC5Y,IAAI,CAACmX,GAAG,CAAC,MAAK;QACjB;QACA,MAAM8B,oBAAoB,GAAG,CAACL,UAAU,EAAE,GAAGE,oBAAoB,CAAC;QAElE,IAAI,CAAC/Y,MAAM,CAAC0I,KAAK,CACf,wDAAwDwQ,oBAAoB,CAAClQ,MAAM,SAAS,CAC7F;QAED,IAAI,CAAC7I,aAAa,CAAC2D,IAAI,CAACoV,oBAAoB,CAAC;QAC7C,IAAI,CAAC5Y,iBAAiB,CAACwD,IAAI,CAAC,IAAI,CAACxD,iBAAiB,CAAC6O,KAAK,GAAG,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEF,IAAI,CAACnP,MAAM,CAAC0I,KAAK,CACf,+CAA+C,EAC/CmQ,UAAU,CACX;MAED,OAAOA,UAAU;IACnB,CAAC,CAAC;IACF;IACAnc,UAAU,CAAEiK,GAAG,IAAI;MACjB,IACEA,GAAG,YAAYsC,KAAK,IACpBtC,GAAG,CAAC+F,OAAO,KAAK,sCAAsC,EACtD;QACA,OAAOlQ,KAAK;;MAGd,IAAI,CAACwD,MAAM,CAACmE,KAAK,CAAC,oCAAoC,EAAEwC,GAAY,CAAC;MACrE,OAAOnK,KAAK;IACd,CAAC,CAAC;IACF;IACAG,GAAG,CAAEgH,YAAY,IAAI;MACnB,IAAI,CAAC3D,MAAM,CAAC0I,KAAK,CACf,6CAA6C,EAC7C/E,YAAY,CACb;IACH,CAAC,CAAC,CACH;IAED,MAAM4T,GAAG,GAAGoB,UAAU,CAACrU,SAAS,CAAC;MAC/BR,IAAI,EAAGH,YAAY,IAAI;QACrB,IAAI,CAAC3D,MAAM,CAAC0I,KAAK,CACf,yCAAyC,EACzC/E,YAAY,CACb;MACH,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3D,aAAa,CAACgQ,IAAI,CAAC+G,GAAG,CAAC;IAC5B,IAAI,CAACvX,MAAM,CAAC0I,KAAK,CAAC,mDAAmD,CAAC;IACtE,OAAOiQ,UAAU;EACnB;EACA;EACA;EACA;EAEQzV,oBAAoBA,CAAA;IAC1B,IAAI,CAACiW,eAAe,GAAGC,WAAW,CAAC,MAAK;MACtC,IAAI,CAACC,2BAA2B,EAAE;IACpC,CAAC,EAAE,OAAO,CAAC;EACb;EACQA,2BAA2BA,CAAA;IACjC,MAAM/N,GAAG,GAAG,IAAIlB,IAAI,EAAE;IACtB,MAAMkP,aAAa,GAAG,IAAIlP,IAAI,CAACkB,GAAG,CAACiO,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAExE,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI,CAACpZ,iBAAiB,CAACsD,OAAO,CAAC,CAACC,YAAY,EAAEC,EAAE,KAAI;MAClD,MAAM6V,gBAAgB,GAAG,IAAIrP,IAAI,CAACzG,YAAY,CAAC+G,SAAS,CAAC;MACzD,IAAI+O,gBAAgB,GAAGH,aAAa,EAAE;QACpC,IAAI,CAAClZ,iBAAiB,CAACsZ,MAAM,CAAC9V,EAAE,CAAC;QACjC4V,YAAY,EAAE;;IAElB,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAACxZ,MAAM,CAAC0I,KAAK,CAAC,cAAc8Q,YAAY,wBAAwB,CAAC;MAErE;MACA,MAAMG,sBAAsB,GAAG5V,KAAK,CAACC,IAAI,CACvC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAChC;MACD,MAAMiK,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CACtDwL,sBAAsB,CACvB;MAED,IAAI,CAACxZ,aAAa,CAAC2D,IAAI,CAACoK,mBAAmB,CAAC;MAC5C,IAAI,CAAChK,iBAAiB,EAAE;;EAE5B;EACA;;;;;EAKQiK,uBAAuBA,CAC7BhO,aAA6B;IAE7B,OAAOA,aAAa,CAACyZ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjC;MACA,MAAMC,KAAK,GAAG,IAAI3P,IAAI,CAACyP,CAAC,CAACnP,SAAS,IAAI,CAAC,CAAC;MACxC,MAAMsP,KAAK,GAAG,IAAI5P,IAAI,CAAC0P,CAAC,CAACpP,SAAS,IAAI,CAAC,CAAC;MACxC,OAAOsP,KAAK,CAACT,OAAO,EAAE,GAAGQ,KAAK,CAACR,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ;;EAEQ7N,gBAAgBA,CAAA;IACtB,OAAOrI,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC7C;EACQ0G,gBAAgBA,CAAC0C,OAAgB;IACvC,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAAC1M,MAAM,CAACmE,KAAK,CACf,6DAA6D,CAC9D;MACD,MAAM,IAAI8E,KAAK,CAAC,4BAA4B,CAAC;;IAG/C,IAAI;MACF;MACA,IAAI,CAACyD,OAAO,CAAC9I,EAAE,IAAI,CAAC8I,OAAO,CAACrB,GAAG,EAAE;QAC/B,IAAI,CAACrL,MAAM,CAACmE,KAAK,CACf,wCAAwC,EACxCrB,SAAS,EACT4J,OAAO,CACR;QACD,MAAM,IAAIzD,KAAK,CAAC,wBAAwB,CAAC;;MAG3C;MACA,IAAIgR,gBAAgB;MACpB,IAAI;QACFA,gBAAgB,GAAGvN,OAAO,CAACvB,MAAM,GAC7B,IAAI,CAACC,aAAa,CAACsB,OAAO,CAACvB,MAAM,CAAC,GAClCrI,SAAS;OACd,CAAC,OAAOqB,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACwP,IAAI,CACd,yEAAyE,EACzErL,KAAK,CACN;QACD8V,gBAAgB,GAAG;UACjB5O,GAAG,EAAEqB,OAAO,CAACvD,QAAQ,IAAI,SAAS;UAClCvF,EAAE,EAAE8I,OAAO,CAACvD,QAAQ,IAAI,SAAS;UACjCwC,QAAQ,EAAE,cAAc;UACxBuO,KAAK,EAAE,qBAAqB;UAC5BC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE;SACX;;MAGH;MACA,IAAIC,kBAAkB;MACtB,IAAI3N,OAAO,CAAC4N,QAAQ,EAAE;QACpB,IAAI;UACFD,kBAAkB,GAAG,IAAI,CAACjP,aAAa,CAACsB,OAAO,CAAC4N,QAAQ,CAAC;SAC1D,CAAC,OAAOnW,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACwP,IAAI,CACd,2EAA2E,EAC3ErL,KAAK,CACN;UACDkW,kBAAkB,GAAG;YACnBhP,GAAG,EAAEqB,OAAO,CAACtD,UAAU,IAAI,SAAS;YACpCxF,EAAE,EAAE8I,OAAO,CAACtD,UAAU,IAAI,SAAS;YACnCuC,QAAQ,EAAE,cAAc;YACxBuO,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;WACX;;;MAIL;MACA,MAAMG,qBAAqB,GACzB7N,OAAO,CAACuK,WAAW,EAAExa,GAAG,CAAE0a,GAAG,KAAM;QACjCvT,EAAE,EAAEuT,GAAG,CAACvT,EAAE,IAAIuT,GAAG,CAAC9L,GAAG,IAAI,cAAcjB,IAAI,CAACkB,GAAG,EAAE,EAAE;QACnDkP,GAAG,EAAErD,GAAG,CAACqD,GAAG,IAAI,EAAE;QAClB9S,IAAI,EAAEyP,GAAG,CAACzP,IAAI,IAAI,SAAS;QAC3BzC,IAAI,EAAEkS,GAAG,CAAClS,IAAI,IAAI,YAAY;QAC9BsI,IAAI,EAAE4J,GAAG,CAAC5J,IAAI,IAAI,CAAC;QACnBlG,QAAQ,EAAE8P,GAAG,CAAC9P,QAAQ,IAAI;OAC3B,CAAC,CAAC,IAAI,EAAE;MAEX;MACA,MAAM0P,iBAAiB,GAAG;QACxB,GAAGrK,OAAO;QACVrB,GAAG,EAAEqB,OAAO,CAAC9I,EAAE,IAAI8I,OAAO,CAACrB,GAAG;QAC9BzH,EAAE,EAAE8I,OAAO,CAAC9I,EAAE,IAAI8I,OAAO,CAACrB,GAAG;QAC7BE,OAAO,EAAEmB,OAAO,CAACnB,OAAO,IAAI,EAAE;QAC9BJ,MAAM,EAAE8O,gBAAgB;QACxBvP,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC+B,OAAO,CAAChC,SAAS,CAAC;QAChDP,MAAM,EAAEuC,OAAO,CAACvC,MAAM,GAAG,IAAI,CAACQ,aAAa,CAAC+B,OAAO,CAACvC,MAAM,CAAC,GAAGrH,SAAS;QACvEmU,WAAW,EAAEsD,qBAAqB;QAClCE,QAAQ,EAAE/N,OAAO,CAAC+N,QAAQ,IAAI;OAC/B;MAED;MACA,IAAIJ,kBAAkB,EAAE;QACtBtD,iBAAiB,CAACuD,QAAQ,GAAGD,kBAAkB;;MAGjD,IAAI,CAACra,MAAM,CAAC0I,KAAK,CAAC,kDAAkD,EAAE;QACpEkB,SAAS,EAAEmN,iBAAiB,CAACnT,EAAE;QAC/BuF,QAAQ,EAAE4N,iBAAiB,CAAC5L,MAAM,EAAEvH;OACrC,CAAC;MAEF,OAAOmT,iBAAiB;KACzB,CAAC,OAAO5S,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,GAAG,IAAI8E,KAAK,CAACyR,MAAM,CAACvW,KAAK,CAAC,CAAC,EACzDuI,OAAO,CACR;MACD,MAAM,IAAIzD,KAAK,CACb,gCACE9E,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,CAACuI,OAAO,GAAGgO,MAAM,CAACvW,KAAK,CACvD,EAAE,CACH;;EAEL;EAEOiH,aAAaA,CAACmK,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAItM,KAAK,CAAC,yBAAyB,CAAC;;IAG5C;IACA,MAAM4C,MAAM,GAAG0J,IAAI,CAAC3R,EAAE,IAAI2R,IAAI,CAAClK,GAAG;IAClC,IAAI,CAACQ,MAAM,EAAE;MACX,MAAM,IAAI5C,KAAK,CAAC,qBAAqB,CAAC;;IAGxC;IACA,MAAM0C,QAAQ,GAAG4J,IAAI,CAAC5J,QAAQ,IAAI,cAAc;IAChD,MAAMuO,KAAK,GAAG3E,IAAI,CAAC2E,KAAK,IAAI,QAAQrO,MAAM,cAAc;IACxD,MAAMuO,QAAQ,GACZ7E,IAAI,CAAC6E,QAAQ,KAAKtX,SAAS,IAAIyS,IAAI,CAAC6E,QAAQ,KAAK,IAAI,GACjD7E,IAAI,CAAC6E,QAAQ,GACb,IAAI;IACV,MAAMD,IAAI,GAAG5E,IAAI,CAAC4E,IAAI,IAAI,MAAM;IAEhC;IACA,OAAO;MACL9O,GAAG,EAAEQ,MAAM;MACXjI,EAAE,EAAEiI,MAAM;MACVF,QAAQ,EAAEA,QAAQ;MAClBuO,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEA,IAAI;MACVC,QAAQ,EAAEA,QAAQ;MAClB;MACAO,KAAK,EAAEpF,IAAI,CAACoF,KAAK,IAAI,IAAI;MACzBC,GAAG,EAAErF,IAAI,CAACqF,GAAG;MACbzF,QAAQ,EAAEI,IAAI,CAACJ,QAAQ,IAAI,KAAK;MAChC0F,UAAU,EAAEtF,IAAI,CAACsF,UAAU,GAAG,IAAIzQ,IAAI,CAACmL,IAAI,CAACsF,UAAU,CAAC,GAAG/X,SAAS;MACnEgY,SAAS,EAAEvF,IAAI,CAACuF,SAAS,GAAG,IAAI1Q,IAAI,CAACmL,IAAI,CAACuF,SAAS,CAAC,GAAGhY,SAAS;MAChEiY,SAAS,EAAExF,IAAI,CAACwF,SAAS,GAAG,IAAI3Q,IAAI,CAACmL,IAAI,CAACwF,SAAS,CAAC,GAAGjY,SAAS;MAChEkY,cAAc,EAAEzF,IAAI,CAACyF,cAAc;MACnCC,cAAc,EAAE1F,IAAI,CAAC0F,cAAc;MACnCC,SAAS,EAAE3F,IAAI,CAAC2F;KACjB;EACH;EACQhP,qBAAqBA,CAACD,IAAkB;IAC9C,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACjM,MAAM,CAACmE,KAAK,CACf,kEAAkE,CACnE;MACD,MAAM,IAAI8E,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,IAAI;MACF;MACA,IAAI,CAACgD,IAAI,CAACrI,EAAE,IAAI,CAACqI,IAAI,CAACZ,GAAG,EAAE;QACzB,IAAI,CAACrL,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CrB,SAAS,EACTmJ,IAAI,CACL;QACD,MAAM,IAAIhD,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,MAAMkS,sBAAsB,GAAG,EAAE;MACjC,IAAIlP,IAAI,CAACM,YAAY,IAAIxI,KAAK,CAACqX,OAAO,CAACnP,IAAI,CAACM,YAAY,CAAC,EAAE;QACzD,KAAK,MAAM8O,WAAW,IAAIpP,IAAI,CAACM,YAAY,EAAE;UAC3C,IAAI;YACF,IAAI8O,WAAW,EAAE;cACfF,sBAAsB,CAAC3K,IAAI,CAAC,IAAI,CAACpF,aAAa,CAACiQ,WAAW,CAAC,CAAC;;WAE/D,CAAC,OAAOlX,KAAK,EAAE;YACd,IAAI,CAACnE,MAAM,CAACwP,IAAI,CACd,0DAA0D,EAC1DrL,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACnE,MAAM,CAACwP,IAAI,CACd,iFAAiF,EACjFvD,IAAI,CACL;;MAGH;MACA,MAAMqP,kBAAkB,GAAG,EAAE;MAC7B,IAAIrP,IAAI,CAACxC,QAAQ,IAAI1F,KAAK,CAACqX,OAAO,CAACnP,IAAI,CAACxC,QAAQ,CAAC,EAAE;QACjD,IAAI,CAACzJ,MAAM,CAAC0I,KAAK,CAAC,mDAAmD,EAAE;UACrEuH,KAAK,EAAEhE,IAAI,CAACxC,QAAQ,CAACT;SACtB,CAAC;QAEF,KAAK,MAAM0D,OAAO,IAAIT,IAAI,CAACxC,QAAQ,EAAE;UACnC,IAAI;YACF,IAAIiD,OAAO,EAAE;cACX,MAAMqK,iBAAiB,GAAG,IAAI,CAAC/M,gBAAgB,CAAC0C,OAAO,CAAC;cACxD,IAAI,CAAC1M,MAAM,CAAC0I,KAAK,CACf,kDAAkD,EAClD;gBACEkB,SAAS,EAAEmN,iBAAiB,CAACnT,EAAE;gBAC/B2H,OAAO,EAAEwL,iBAAiB,CAACxL,OAAO,EAAEsJ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACpD1J,MAAM,EAAE4L,iBAAiB,CAAC5L,MAAM,EAAEQ;eACnC,CACF;cACD2P,kBAAkB,CAAC9K,IAAI,CAACuG,iBAAiB,CAAC;;WAE7C,CAAC,OAAO5S,KAAK,EAAE;YACd,IAAI,CAACnE,MAAM,CAACwP,IAAI,CACd,sEAAsE,EACtErL,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACnE,MAAM,CAAC0I,KAAK,CACf,8EAA8E,CAC/E;;MAGH;MACA,IAAI6S,qBAAqB,GAAG,IAAI;MAChC,IAAItP,IAAI,CAAC4L,WAAW,EAAE;QACpB,IAAI;UACF0D,qBAAqB,GAAG,IAAI,CAACvR,gBAAgB,CAACiC,IAAI,CAAC4L,WAAW,CAAC;SAChE,CAAC,OAAO1T,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACwP,IAAI,CACd,6DAA6D,EAC7DrL,KAAK,CACN;;;MAIL;MACA,MAAMmI,sBAAsB,GAAG;QAC7B,GAAGL,IAAI;QACPZ,GAAG,EAAEY,IAAI,CAACrI,EAAE,IAAIqI,IAAI,CAACZ,GAAG;QACxBzH,EAAE,EAAEqI,IAAI,CAACrI,EAAE,IAAIqI,IAAI,CAACZ,GAAG;QACvBkB,YAAY,EAAE4O,sBAAsB;QACpC1R,QAAQ,EAAE6R,kBAAkB;QAC5BzD,WAAW,EAAE0D,qBAAqB;QAClCC,WAAW,EAAEvP,IAAI,CAACuP,WAAW,IAAI,CAAC;QAClCzO,OAAO,EAAE,CAAC,CAACd,IAAI,CAACc,OAAO;QACvB+N,SAAS,EAAE,IAAI,CAACnQ,aAAa,CAACsB,IAAI,CAAC6O,SAAS,CAAC;QAC7CC,SAAS,EAAE,IAAI,CAACpQ,aAAa,CAACsB,IAAI,CAAC8O,SAAS;OAC7C;MAED,IAAI,CAAC/a,MAAM,CAAC0I,KAAK,CACf,uDAAuD,EACvD;QACEW,cAAc,EAAEiD,sBAAsB,CAAC1I,EAAE;QACzC6X,gBAAgB,EAAEN,sBAAsB,CAACnS,MAAM;QAC/C0S,YAAY,EAAEJ,kBAAkB,CAACtS;OAClC,CACF;MAED,OAAOsD,sBAAsB;KAC9B,CAAC,OAAOnI,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,kDAAkD,EAClDA,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,GAAG,IAAI8E,KAAK,CAACyR,MAAM,CAACvW,KAAK,CAAC,CAAC,EACzD8H,IAAI,CACL;MACD,MAAM,IAAIhD,KAAK,CACb,qCACE9E,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,CAACuI,OAAO,GAAGgO,MAAM,CAACvW,KAAK,CACvD,EAAE,CACH;;EAEL;EACQwG,aAAaA,CAAC9H,IAA+B;IACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIuH,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOvH,IAAI,KAAK,QAAQ,GAAG,IAAIuH,IAAI,CAACvH,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOsB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACwP,IAAI,CAAC,yBAAyB3M,IAAI,EAAE,EAAEsB,KAAK,CAAC;MACxD,OAAO,IAAIiG,IAAI,EAAE;;EAErB;EAEA;EACQc,QAAQA,CAACrI,IAA+B;IAC9C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIuH,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOvH,IAAI,KAAK,QAAQ,GAAG,IAAIuH,IAAI,CAACvH,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOsB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACwP,IAAI,CAAC,+BAA+B3M,IAAI,EAAE,EAAEsB,KAAK,CAAC;MAC9D,OAAO,IAAIiG,IAAI,EAAE;;EAErB;EAOQ0O,qBAAqBA,CAACnV,YAA0B;IACtD,IAAI,CAAC3D,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1B/E,YAAY,CACb;IAED,IAAI,CAACA,YAAY,EAAE;MACjB,IAAI,CAAC3D,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;MACxE,MAAM,IAAI8E,KAAK,CAAC,0BAA0B,CAAC;;IAG7C;IACA,MAAMoG,cAAc,GAAG1L,YAAY,CAACC,EAAE,IAAKD,YAAoB,CAAC0H,GAAG;IACnE,IAAI,CAACgE,cAAc,EAAE;MACnB,IAAI,CAACrP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BR,YAAY,CACb;MACD,MAAM,IAAIsF,KAAK,CAAC,6BAA6B,CAAC;;IAGhD,IAAI,CAACtF,YAAY,CAAC+G,SAAS,EAAE;MAC3B,IAAI,CAAC1K,MAAM,CAACwP,IAAI,CACd,gBAAgB,EAChB,uDAAuD,EACvD7L,YAAY,CACb;MACDA,YAAY,CAAC+G,SAAS,GAAG,IAAIN,IAAI,EAAE;;IAGrC,IAAI;MACF,MAAMyO,UAAU,GAAG;QACjB,GAAGlV,YAAY;QACf0H,GAAG,EAAEgE,cAAc;QACnBzL,EAAE,EAAEyL,cAAc;QAClB3E,SAAS,EAAE,IAAIN,IAAI,CAACzG,YAAY,CAAC+G,SAAS,CAAC;QAC3C,IAAI/G,YAAY,CAACwF,QAAQ,IAAI;UAC3BA,QAAQ,EAAE,IAAI,CAACwS,eAAe,CAAChY,YAAY,CAACwF,QAAQ;SACrD,CAAC;QACF,IAAIxF,YAAY,CAAC+I,OAAO,IAAI;UAC1BA,OAAO,EAAE,IAAI,CAACkP,mBAAmB,CAACjY,YAAY,CAAC+I,OAAO;SACvD;OACF;MAED,IAAI,CAAC1M,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCmQ,UAAU,CACX;MACD,OAAOA,UAAU;KAClB,CAAC,OAAO1U,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCA,KAAK,CACN;MACD,MAAMA,KAAK;;EAEf;EACQwX,eAAeA,CAACxQ,MAAW;IACjC,OAAO;MACLvH,EAAE,EAAEuH,MAAM,CAACvH,EAAE;MACb+H,QAAQ,EAAER,MAAM,CAACQ,QAAQ;MACzB,IAAIR,MAAM,CAACwP,KAAK,IAAI;QAAEA,KAAK,EAAExP,MAAM,CAACwP;MAAK,CAAE;KAC5C;EACH;EAEA;;;;;EAKQiB,mBAAmBA,CAAClP,OAAY;IACtC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,OAAO;MACL9I,EAAE,EAAE8I,OAAO,CAAC9I,EAAE,IAAI8I,OAAO,CAACrB,GAAG;MAC7BE,OAAO,EAAEmB,OAAO,CAACnB,OAAO,IAAI,EAAE;MAC9B7D,IAAI,EAAEgF,OAAO,CAAChF,IAAI,IAAI,MAAM;MAC5BgD,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACwB,OAAO,CAAChC,SAAS,CAAC;MAC3CuM,WAAW,EAAEvK,OAAO,CAACuK,WAAW,IAAI,EAAE;MACtC,IAAIvK,OAAO,CAACvB,MAAM,IAAI;QAAEA,MAAM,EAAE,IAAI,CAACwQ,eAAe,CAACjP,OAAO,CAACvB,MAAM;MAAC,CAAE;KACvE;EACH;EACA;;;;;EAKQ6C,WAAWA,CACjB7N,aAA4C,EAC5C0b,cAAA,GAA0B,IAAI;IAE9B,MAAMC,iBAAiB,GAAG/X,KAAK,CAACqX,OAAO,CAACjb,aAAa,CAAC,GAClDA,aAAa,GACb,CAACA,aAAa,CAAC;IAEnB,IAAI,CAACH,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,oCAAoCoT,iBAAiB,CAAC9S,MAAM,gBAAgB,CAC7E;IAED,IAAI8S,iBAAiB,CAAC9S,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAAChJ,MAAM,CAACwP,IAAI,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;MACzE;;IAGF;IACA,MAAMuM,kBAAkB,GAAGD,iBAAiB,CAAClf,MAAM,CAChDiR,KAAK,IAAKA,KAAK,KAAKA,KAAK,CAACjK,EAAE,IAAKiK,KAAa,CAACxC,GAAG,CAAC,CACrD;IAED,IAAI0Q,kBAAkB,CAAC/S,MAAM,KAAK8S,iBAAiB,CAAC9S,MAAM,EAAE;MAC1D,IAAI,CAAChJ,MAAM,CAACwP,IAAI,CACd,gBAAgB,EAChB,SACEsM,iBAAiB,CAAC9S,MAAM,GAAG+S,kBAAkB,CAAC/S,MAChD,kCAAkC,CACnC;;IAGH,IAAIgT,UAAU,GAAG,CAAC;IAClB,IAAIC,YAAY,GAAG,CAAC;IAEpB;IACAF,kBAAkB,CAACrY,OAAO,CAAC,CAACmK,KAAK,EAAEE,KAAK,KAAI;MAC1C,IAAI;QACF;QACA,MAAMmO,OAAO,GAAGrO,KAAK,CAACjK,EAAE,IAAKiK,KAAa,CAACxC,GAAG;QAC9C,IAAI,CAAC6Q,OAAO,EAAE;UACZ,IAAI,CAAClc,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1B0J,KAAK,CACN;UACD;;QAGF;QACA,MAAMgL,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACjL,KAAK,CAAC;QAEpD;QACA,IAAIgO,cAAc,IAAI,IAAI,CAACzb,iBAAiB,CAAC0N,GAAG,CAAC+K,UAAU,CAACjV,EAAE,CAAC,EAAE;UAC/D,IAAI,CAAC5D,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,gBAAgBmQ,UAAU,CAACjV,EAAE,oCAAoC,CAClE;UACDqY,YAAY,EAAE;UACd;;QAGF;QACA,IAAI,CAAC7b,iBAAiB,CAACyD,GAAG,CAACgV,UAAU,CAACjV,EAAE,EAAEiV,UAAU,CAAC;QACrDmD,UAAU,EAAE;QAEZ,IAAI,CAAChc,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,sBAAsBmQ,UAAU,CAACjV,EAAE,WAAW,CAC/C;OACF,CAAC,OAAOO,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC4J,KAAK,GAAG,CAAC,GAAG,EAC7C5J,KAAK,CACN;;IAEL,CAAC,CAAC;IAEF,IAAI,CAACnE,MAAM,CAAC0I,KAAK,CACf,gBAAgB,EAChB,0BAA0BsT,UAAU,WAAWC,YAAY,oBAAoB,IAAI,CAAC7b,iBAAiB,CAACmN,IAAI,EAAE,CAC7G;IAED;IACA,IAAI,CAAC4O,8BAA8B,EAAE;EACvC;EACA;;;;EAIQA,8BAA8BA,CAAA;IACpC,MAAMC,gBAAgB,GAAGrY,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAAC;IAEpE;IACA,MAAMiK,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAACiO,gBAAgB,CAAC;IAE1E,IAAI,CAACpc,MAAM,CAAC0I,KAAK,CACf,cAAcwF,mBAAmB,CAAClF,MAAM,2DAA2D,CACpG;IAED,IAAI,CAAC7I,aAAa,CAAC2D,IAAI,CAACoK,mBAAmB,CAAC;IAC5C,IAAI,CAAChK,iBAAiB,EAAE;IACxB,IAAI,CAACkK,+BAA+B,EAAE;EACxC;EAEA;;;EAGQlK,iBAAiBA,CAAA;IACvB,MAAMkY,gBAAgB,GAAGrY,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAAC;IACpE,MAAMoY,mBAAmB,GAAGD,gBAAgB,CAACxf,MAAM,CAAE8R,CAAC,IAAK,CAACA,CAAC,CAACjD,MAAM,CAAC;IACrE,MAAMwE,KAAK,GAAGoM,mBAAmB,CAACrT,MAAM;IAExC;IACA,IAAI,CAAC/I,IAAI,CAACmX,GAAG,CAAC,MAAK;MACjB,IAAI,CAAC9W,iBAAiB,CAACwD,IAAI,CAACmM,KAAK,CAAC;MAElC;MACA3J,MAAM,CAACgW,aAAa,CAClB,IAAIC,WAAW,CAAC,0BAA0B,EAAE;QAC1CC,MAAM,EAAE;UAAEvM;QAAK;OAChB,CAAC,CACH;IACH,CAAC,CAAC;EACJ;EAEA;;;;EAIQgJ,uBAAuBA,CAACtV,YAA0B;IACxD,IAAI,CAACqK,WAAW,CAACrK,YAAY,EAAE,IAAI,CAAC;EACtC;EACA;;;;;EAKQqN,wBAAwBA,CAACyL,GAAa,EAAEhR,MAAe;IAC7DgR,GAAG,CAAC/Y,OAAO,CAAEE,EAAE,IAAI;MACjB,MAAMiK,KAAK,GAAG,IAAI,CAACzN,iBAAiB,CAACmQ,GAAG,CAAC3M,EAAE,CAAC;MAC5C,IAAIiK,KAAK,EAAE;QACT,IAAI,CAACzN,iBAAiB,CAACyD,GAAG,CAACD,EAAE,EAAE;UAC7B,GAAGiK,KAAK;UACRpC,MAAM;UACNtB,MAAM,EAAEsB,MAAM,GAAG,IAAIrB,IAAI,EAAE,CAACrH,WAAW,EAAE,GAAGD;SAC7C,CAAC;;IAEN,CAAC,CAAC;IACF,IAAI,CAACqZ,8BAA8B,EAAE;EACvC;EAEA;;;;;EAKQzM,4BAA4BA,CAACU,eAAyB;IAC5DjK,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5CgK,eAAe,CAACpH,MAAM,EACtB,eAAe,CAChB;IACD7C,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C,IAAI,CAAChG,iBAAiB,CAACmN,IAAI,CAC5B;IAED,IAAIkC,YAAY,GAAG,CAAC;IACpBW,eAAe,CAAC1M,OAAO,CAAEE,EAAE,IAAI;MAC7B,IAAI,IAAI,CAACxD,iBAAiB,CAAC0N,GAAG,CAAClK,EAAE,CAAC,EAAE;QAClCuC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAExC,EAAE,CAAC;QAChE,IAAI,CAACxD,iBAAiB,CAACsZ,MAAM,CAAC9V,EAAE,CAAC;QACjC6L,YAAY,EAAE;OACf,MAAM;QACLtJ,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzDxC,EAAE,CACH;;IAEL,CAAC,CAAC;IAEFuC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqJ,YAAY,EAAE,eAAe,CAAC;IAC5EtJ,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAChG,iBAAiB,CAACmN,IAAI,CAC5B;IAED,IAAIkC,YAAY,GAAG,CAAC,EAAE;MACpBtJ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,IAAI,CAAC+V,8BAA8B,EAAE;;IAGvC,OAAO1M,YAAY;EACrB;EAEA;;;;;;EAMQG,mBAAmBA,CACzBzL,KAAU,EACVuY,SAAiB,EACjBC,gBAAqB;IAErB,IAAI,CAAC3c,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,kBAAkBuY,SAAS,GAAG,EAAEvY,KAAK,CAAC;IAC1E,OAAO9H,EAAE,CAACsgB,gBAAgB,CAAC;EAC7B;EACA;EACAC,WAAWA,CAACvT,cAAsB;IAChC,MAAMwC,MAAM,GAAG,IAAI,CAACH,gBAAgB,EAAE;IACtC,IAAI,CAACG,MAAM,EAAE;MACX,IAAI,CAAC7L,MAAM,CAACwP,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOnT,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAAC0D,MAAM,CACf+J,MAAM,CAAsB;MAC3BC,QAAQ,EAAEhM,qBAAqB;MAC/BwL,SAAS,EAAE;QACT2M,KAAK,EAAE;UACL7M,cAAc;UACdwC;;;KAGL,CAAC,CACDlH,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEgY,WAAW,IAAI,KAAK,CAAC,EAClDlgB,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAO7H,UAAU,CACf,MAAM,IAAI2M,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEA4T,UAAUA,CAACxT,cAAsB;IAC/B,MAAMwC,MAAM,GAAG,IAAI,CAACH,gBAAgB,EAAE;IACtC,IAAI,CAACG,MAAM,EAAE;MACX,IAAI,CAAC7L,MAAM,CAACwP,IAAI,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;MACpE,OAAOnT,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAAC0D,MAAM,CACf+J,MAAM,CAAqB;MAC1BC,QAAQ,EAAE/L,oBAAoB;MAC9BuL,SAAS,EAAE;QACT2M,KAAK,EAAE;UACL7M,cAAc;UACdwC;;;KAGL,CAAC,CACDlH,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEiY,UAAU,IAAI,KAAK,CAAC,EACjDngB,UAAU,CAAEyH,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAO7H,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;;;;;;;EASA6T,WAAWA,CACT1T,UAAkB,EAClBmC,OAAe,EACfwR,IAAW,EACXC,WAAA,GAAmB,MAAM,EACzB3T,cAAuB;IAEvBlD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DgD,UAAU;MACVmC,OAAO,EAAEA,OAAO,EAAEsJ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MAClCoI,OAAO,EAAE,CAAC,CAACF,IAAI;MACfG,QAAQ,EAAEH,IAAI,EAAE9X,IAAI;MACpBkY,QAAQ,EAAEJ,IAAI,EAAErV,IAAI;MACpB0V,QAAQ,EAAEL,IAAI,EAAExP,IAAI;MACpByP,WAAW;MACX3T;KACD,CAAC;IAEF,IAAI,CAACD,UAAU,EAAE;MACf,MAAMjF,KAAK,GAAG,IAAI8E,KAAK,CAAC,yBAAyB,CAAC;MAClD9C,OAAO,CAAChC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAO7H,UAAU,CAAC,MAAM6H,KAAK,CAAC;;IAGhC;IACA,MAAMoF,SAAS,GAAQ;MACrBH,UAAU;MACVmC,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtB7D,IAAI,EAAEsV;KACP;IAED;IACA,IAAI3T,cAAc,EAAE;MAClBE,SAAS,CAACF,cAAc,GAAGA,cAAc;;IAG3C;IACA,IAAI0T,IAAI,EAAE;MACRxT,SAAS,CAACwT,IAAI,GAAGA,IAAI;MACrB5W,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;QAC1DnB,IAAI,EAAE8X,IAAI,CAAC9X,IAAI;QACfyC,IAAI,EAAEqV,IAAI,CAACrV,IAAI;QACf6F,IAAI,EAAEwP,IAAI,CAACxP;OACZ,CAAC;;IAGJpH,OAAO,CAACC,GAAG,CACT,sDAAsD,EACtDmD,SAAS,CACV;IAED,OAAO,IAAI,CAACxJ,MAAM,CACf+J,MAAM,CAAsB;MAC3BC,QAAQ,EAAE3M,qBAAqB;MAC/BmM,SAAS;MACT8T,OAAO,EAAE;QACPC,YAAY,EAAE,CAAC,CAACP,IAAI,CAAE;;KAEzB,CAAC,CACDpY,IAAI,CACHlI,GAAG,CAAEqM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD0C,MAAM,CACP;MAED,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEkY,WAAW,EAAE;QAC7B,MAAM,IAAI7T,KAAK,CAAC,sCAAsC,CAAC;;MAGzD,MAAMyD,OAAO,GAAG5D,MAAM,CAAClE,IAAI,CAACkY,WAAW;MACvC3W,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5DxC,EAAE,EAAE8I,OAAO,CAAC9I,EAAE;QACd8D,IAAI,EAAEgF,OAAO,CAAChF,IAAI;QAClB6D,OAAO,EAAEmB,OAAO,CAACnB,OAAO,EAAEsJ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1C0I,cAAc,EAAE,CAAC,CAAC7Q,OAAO,CAACuK,WAAW,EAAEjO;OACxC,CAAC;MAEF;MACA,MAAM+N,iBAAiB,GAAG,IAAI,CAAC/M,gBAAgB,CAAC0C,OAAO,CAAC;MACxDvG,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzC2Q,iBAAiB,CAClB;MAED,OAAOA,iBAAiB;IAC1B,CAAC,CAAC,EACFra,UAAU,CAAEyH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD;MACA,IAAIqZ,YAAY,GAAG,mCAAmC;MACtD,IAAIrZ,KAAK,CAACmK,YAAY,EAAE;QACtBkP,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAIrZ,KAAK,CAACkK,aAAa,EAAErF,MAAM,GAAG,CAAC,EAAE;QAC1CwU,YAAY,GAAGrZ,KAAK,CAACkK,aAAa,CAAC,CAAC,CAAC,CAAC3B,OAAO,IAAI8Q,YAAY;;MAG/D,OAAOlhB,UAAU,CAAC,MAAM,IAAI2M,KAAK,CAACuU,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGAC,iBAAiBA,CAAC/S,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,MAAM7H,IAAI,GAAG6H,SAAS,YAAYN,IAAI,GAAGM,SAAS,GAAG,IAAIN,IAAI,CAACM,SAAS,CAAC;MACxE,OAAO7H,IAAI,CAAC6a,kBAAkB,CAAC,EAAE,EAAE;QACjCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;OACT,CAAC;KACH,CAAC,OAAO1Z,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGA2Z,gBAAgBA,CAACjD,UAAqC;IACpD,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,MAAMkD,cAAc,GAClBlD,UAAU,YAAYzQ,IAAI,GAAGyQ,UAAU,GAAG,IAAIzQ,IAAI,CAACyQ,UAAU,CAAC;IAChE,MAAMvP,GAAG,GAAG,IAAIlB,IAAI,EAAE;IACtB,MAAM4T,SAAS,GACb7M,IAAI,CAAC8M,GAAG,CAAC3S,GAAG,CAACiO,OAAO,EAAE,GAAGwE,cAAc,CAACxE,OAAO,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAIyE,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO,UAAUD,cAAc,CAACL,kBAAkB,CAAC,EAAE,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;;IAEN,OAAO,UAAUG,cAAc,CAACG,kBAAkB,EAAE,EAAE;EACxD;EAEA;;;EAGAC,iBAAiBA,CAACzT,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IAErC,IAAI;MACF,MAAM7H,IAAI,GAAG6H,SAAS,YAAYN,IAAI,GAAGM,SAAS,GAAG,IAAIN,IAAI,CAACM,SAAS,CAAC;MACxE,MAAM0T,KAAK,GAAG,IAAIhU,IAAI,EAAE;MAExB,IAAIvH,IAAI,CAACwb,YAAY,EAAE,KAAKD,KAAK,CAACC,YAAY,EAAE,EAAE;QAChD,OAAOxb,IAAI,CAAC6a,kBAAkB,CAAC,EAAE,EAAE;UACjCC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC;;MAGJ,MAAMU,SAAS,GAAG,IAAIlU,IAAI,CAACgU,KAAK,CAAC;MACjCE,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;MAE1C,IAAI3b,IAAI,CAACwb,YAAY,EAAE,KAAKC,SAAS,CAACD,YAAY,EAAE,EAAE;QACpD,OAAO,SAASxb,IAAI,CAAC6a,kBAAkB,CAAC,EAAE,EAAE;UAC1CC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC,EAAE;;MAGN,MAAMa,GAAG,GAAG5b,IAAI,CACbqb,kBAAkB,CAAC,OAAO,EAAE;QAAEQ,OAAO,EAAE;MAAO,CAAE,CAAC,CACjDC,WAAW,EAAE;MAChB,OAAO,GAAGF,GAAG,MAAM5b,IAAI,CAAC6a,kBAAkB,CAAC,EAAE,EAAE;QAC7CC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;KACL,CAAC,OAAOzZ,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAya,oBAAoBA,CAACnV,QAAe,EAAEsE,KAAa;IACjD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,IAAI;MACF,MAAM8Q,UAAU,GAAGpV,QAAQ,CAACsE,KAAK,CAAC;MAClC,MAAM+Q,OAAO,GAAGrV,QAAQ,CAACsE,KAAK,GAAG,CAAC,CAAC;MAEnC,IAAI,CAAC8Q,UAAU,EAAEnU,SAAS,IAAI,CAACoU,OAAO,EAAEpU,SAAS,EAAE,OAAO,IAAI;MAE9D,MAAMqU,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAACH,UAAU,CAACnU,SAAS,CAAC;MACnE,MAAMuU,QAAQ,GAAG,IAAI,CAACD,oBAAoB,CAACF,OAAO,CAACpU,SAAS,CAAC;MAE7D,OAAOqU,WAAW,KAAKE,QAAQ;KAChC,CAAC,OAAO9a,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEQ6a,oBAAoBA,CAACtU,SAAoC;IAC/D,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,OAAO,CACLA,SAAS,YAAYN,IAAI,GAAGM,SAAS,GAAG,IAAIN,IAAI,CAACM,SAAS,CAAC,EAC3D2T,YAAY,EAAE;KACjB,CAAC,OAAOla,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGA+a,WAAWA,CAACC,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAC/B,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,UAAU;IACpD,IAAID,QAAQ,CAACjS,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,aAAa;IAClD,IAAIiS,QAAQ,CAACjS,QAAQ,CAAC,MAAM,CAAC,IAAIiS,QAAQ,CAACjS,QAAQ,CAAC,QAAQ,CAAC,EAC1D,OAAO,cAAc;IACvB,IAAIiS,QAAQ,CAACjS,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIiS,QAAQ,CAACjS,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,oBAAoB;IAChE,IAAIiS,QAAQ,CAACjS,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIiS,QAAQ,CAACjS,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIiS,QAAQ,CAACjS,QAAQ,CAAC,KAAK,CAAC,IAAIiS,QAAQ,CAACjS,QAAQ,CAAC,YAAY,CAAC,EAC7D,OAAO,iBAAiB;IAC1B,OAAO,SAAS;EAClB;EAEA;;;EAGAmS,WAAWA,CAACF,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAMG,OAAO,GAA2B;MACtC,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,KAAK;MACxB,oBAAoB,EAAE,UAAU;MAChC,yEAAyE,EACvE,UAAU;MACZ,0BAA0B,EAAE,OAAO;MACnC,mEAAmE,EACjE,OAAO;MACT,+BAA+B,EAAE,YAAY;MAC7C,2EAA2E,EACzE,YAAY;MACd,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,aAAa;MAChC,8BAA8B,EAAE;KACjC;IAED,KAAK,MAAM,CAACC,GAAG,EAAEpQ,KAAK,CAAC,IAAIrJ,MAAM,CAAC0Z,OAAO,CAACF,OAAO,CAAC,EAAE;MAClD,IAAIH,QAAQ,CAACjS,QAAQ,CAACqS,GAAG,CAAC,EAAE,OAAOpQ,KAAK;;IAE1C,OAAO,MAAM;EACf;EAEA;;;EAGAsQ,QAAQA,CAAC/S,OAAY;IACnB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACuK,WAAW,IAAIvK,OAAO,CAACuK,WAAW,CAACjO,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,KAAK;;IAGd,MAAM0W,UAAU,GAAGhT,OAAO,CAACuK,WAAW,CAAC,CAAC,CAAC;IACzC,IAAI,CAACyI,UAAU,IAAI,CAACA,UAAU,CAAChY,IAAI,EAAE;MACnC,OAAO,KAAK;;IAGd,MAAMA,IAAI,GAAGgY,UAAU,CAAChY,IAAI,CAACiN,QAAQ,EAAE;IACvC,OAAOjN,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO;EAC7C;EAEA;;;EAGAiY,cAAcA,CAACjT,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B;IACA,IACEA,OAAO,CAAChF,IAAI,KAAK7K,WAAW,CAACma,aAAa,IAC1CtK,OAAO,CAAChF,IAAI,KAAK7K,WAAW,CAACma,aAAa,EAC1C;MACA,OAAO,IAAI;;IAGb;IACA,IAAItK,OAAO,CAACuK,WAAW,IAAIvK,OAAO,CAACuK,WAAW,CAACjO,MAAM,GAAG,CAAC,EAAE;MACzD,OAAO0D,OAAO,CAACuK,WAAW,CAACC,IAAI,CAAEC,GAAQ,IAAI;QAC3C,MAAMzP,IAAI,GAAGyP,GAAG,CAACzP,IAAI,EAAEiN,QAAQ,EAAE;QACjC,OACEjN,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACvBgF,OAAO,CAAC+N,QAAQ,EAAEkF,cAAc,KAC9BjY,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,CAAE;MAE7C,CAAC,CAAC;;IAGJ;IACA,OAAO,CAAC,CAACgF,OAAO,CAAC+N,QAAQ,EAAEkF,cAAc;EAC3C;EAEA;;;EAGAC,kBAAkBA,CAAClT,OAAY;IAC7B,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACuK,WAAW,IAAIvK,OAAO,CAACuK,WAAW,CAACjO,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAM6W,eAAe,GAAGnT,OAAO,CAACuK,WAAW,CAACnK,IAAI,CAAEqK,GAAQ,IAAI;MAC5D,MAAMzP,IAAI,GAAGyP,GAAG,CAACzP,IAAI,EAAEiN,QAAQ,EAAE;MACjC,OACEjN,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;IAEpB,CAAC,CAAC;IAEF,OAAOmY,eAAe,EAAErF,GAAG,IAAI,EAAE;EACnC;EAEA;;;EAGAsF,uBAAuBA,CAACpT,OAAY;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IAEtB;IACA,IAAIA,OAAO,CAAC+N,QAAQ,EAAEpT,QAAQ,EAAE;MAC9B,OAAOqF,OAAO,CAAC+N,QAAQ,CAACpT,QAAQ;;IAGlC;IACA,IAAIqF,OAAO,CAACuK,WAAW,IAAIvK,OAAO,CAACuK,WAAW,CAACjO,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM6W,eAAe,GAAGnT,OAAO,CAACuK,WAAW,CAACnK,IAAI,CAAEqK,GAAQ,IAAI;QAC5D,MAAMzP,IAAI,GAAGyP,GAAG,CAACzP,IAAI,EAAEiN,QAAQ,EAAE;QACjC,OACEjN,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;MAEpB,CAAC,CAAC;MAEF,IAAImY,eAAe,IAAIA,eAAe,CAACxY,QAAQ,EAAE;QAC/C,OAAOwY,eAAe,CAACxY,QAAQ;;;IAInC,OAAO,CAAC;EACV;EAEA;;;EAGA0Y,iBAAiBA,CAAChS,KAAa;IAC7B,MAAMiS,OAAO,GAAG,CACd,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACxE;IACD,OAAOA,OAAO,CAACjS,KAAK,GAAGiS,OAAO,CAAChX,MAAM,CAAC;EACxC;EAEA;;;EAGAiX,mBAAmBA,CAACC,OAAe;IACjC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;MAC7B,OAAO,MAAM;;IAGf,MAAMC,OAAO,GAAGhP,IAAI,CAACiP,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGlP,IAAI,CAACiP,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAAC1L,QAAQ,EAAE,CAAC2L,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA;;;EAGAC,WAAWA,CAAC7T,OAAY;IACtB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACuK,WAAW,IAAIvK,OAAO,CAACuK,WAAW,CAACjO,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAM0W,UAAU,GAAGhT,OAAO,CAACuK,WAAW,CAAC,CAAC,CAAC;IACzC,OAAOyI,UAAU,EAAElF,GAAG,IAAI,EAAE;EAC9B;EAEA;;;EAGAgG,cAAcA,CAAC9T,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAO7P,WAAW,CAAC2O,IAAI;IAErC,IAAI;MACF,IAAIkB,OAAO,CAAChF,IAAI,EAAE;QAChB,MAAM+Y,OAAO,GAAG/T,OAAO,CAAChF,IAAI,CAACiN,QAAQ,EAAE;QACvC,IAAI8L,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UAC5C,OAAO5jB,WAAW,CAAC2O,IAAI;SACxB,MAAM,IAAIiV,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO5jB,WAAW,CAAC6jB,KAAK;SACzB,MAAM,IAAID,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UACnD,OAAO5jB,WAAW,CAAC8jB,IAAI;SACxB,MAAM,IAAIF,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO5jB,WAAW,CAACkX,KAAK;SACzB,MAAM,IAAI0M,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAO5jB,WAAW,CAAC+jB,KAAK;SACzB,MAAM,IAAIH,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;UACvD,OAAO5jB,WAAW,CAACgkB,MAAM;;;MAI7B,IAAInU,OAAO,CAACuK,WAAW,EAAEjO,MAAM,EAAE;QAC/B,MAAM0W,UAAU,GAAGhT,OAAO,CAACuK,WAAW,CAAC,CAAC,CAAC;QACzC,IAAIyI,UAAU,IAAIA,UAAU,CAAChY,IAAI,EAAE;UACjC,MAAMoZ,iBAAiB,GAAGpB,UAAU,CAAChY,IAAI,CAACiN,QAAQ,EAAE;UAEpD,IAAImM,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAOjkB,WAAW,CAAC6jB,KAAK;WACzB,MAAM,IACLI,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAOjkB,WAAW,CAAC8jB,IAAI;WACxB,MAAM,IACLG,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAOjkB,WAAW,CAACkX,KAAK;WACzB,MAAM,IACL+M,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAOjkB,WAAW,CAAC+jB,KAAK;;;QAI5B,OAAO/jB,WAAW,CAAC8jB,IAAI;;MAGzB,OAAO9jB,WAAW,CAAC2O,IAAI;KACxB,CAAC,OAAOrH,KAAK,EAAE;MACd,OAAOtH,WAAW,CAAC2O,IAAI;;EAE3B;EAEA;;;EAGAuV,eAAeA,CAAA;IACb,OAAO,CACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;EACH;EAEA;;;EAGAC,mBAAmBA,CAACtU,OAAY,EAAEE,aAA4B;IAC5D,IAAI,CAACF,OAAO,EAAE;MACZ,OAAO,kCAAkC;;IAG3C,IAAI;MACF,MAAMuU,aAAa,GACjBvU,OAAO,CAACvB,MAAM,EAAEvH,EAAE,KAAKgJ,aAAa,IACpCF,OAAO,CAACvB,MAAM,EAAEE,GAAG,KAAKuB,aAAa,IACrCF,OAAO,CAACvD,QAAQ,KAAKyD,aAAa;MAEpC,MAAMsU,SAAS,GAAGD,aAAa,GAC3B,kDAAkD,GAClD,qDAAqD;MAEzD,MAAMjE,WAAW,GAAG,IAAI,CAACwD,cAAc,CAAC9T,OAAO,CAAC;MAEhD,IAAIA,OAAO,CAACuK,WAAW,IAAIvK,OAAO,CAACuK,WAAW,CAACjO,MAAM,GAAG,CAAC,EAAE;QACzD,MAAM0W,UAAU,GAAGhT,OAAO,CAACuK,WAAW,CAAC,CAAC,CAAC;QACzC,IAAIyI,UAAU,IAAIA,UAAU,CAAChY,IAAI,EAAE;UACjC,MAAMoZ,iBAAiB,GAAGpB,UAAU,CAAChY,IAAI,CAACiN,QAAQ,EAAE;UACpD,IAAImM,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAO,cAAc;WACtB,MAAM,IACLA,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO,GAAGI,SAAS,MAAM;;;;MAK/B;MAEA,OAAO,GAAGA,SAAS,wDAAwD;KAC5E,CAAC,OAAO/c,KAAK,EAAE;MACd,OAAO,gEAAgE;;EAE3E;EAEA;EACA;EACA;EACA;EACA;EAEA;EACAgd,oBAAoBA,CAAA;IAClB,IAAI,CAAC3gB,aAAa,CAACkD,OAAO,CAAE6T,GAAG,IAAKA,GAAG,CAAC6J,WAAW,EAAE,CAAC;IACtD,IAAI,CAAC5gB,aAAa,GAAG,EAAE;IACvB,IAAI,IAAI,CAAC2Y,eAAe,EAAE;MACxBkI,aAAa,CAAC,IAAI,CAAClI,eAAe,CAAC;;IAErC,IAAI,CAAC/Y,iBAAiB,CAACqD,KAAK,EAAE;IAC9B,IAAI,CAACzD,MAAM,CAAC0I,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEA4Y,WAAWA,CAAA;IACT,IAAI,CAACH,oBAAoB,EAAE;EAC7B;;;uBApqIWthB,cAAc,EAAA0hB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAdhiB,cAAc;MAAAiiB,OAAA,EAAdjiB,cAAc,CAAAkiB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}