"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["common"],{

/***/ 24704:
/*!******************************************!*\
  !*** ./src/app/services/file.service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FileService: () => (/* binding */ FileService)
/* harmony export */ });
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);


class FileService {
  constructor() {}
  /**
   * Génère une URL de téléchargement pour un fichier
   * @param filePath Chemin du fichier
   * @returns URL de téléchargement
   */
  getDownloadUrl(filePath) {
    // Si le chemin est vide ou null, retourner une chaîne vide
    if (!filePath) return '';
    // Extraire uniquement le nom du fichier, peu importe le format du chemin
    let fileName = filePath;
    // Si c'est un chemin complet (contient C:/ ou autre)
    if (filePath.includes('C:') || filePath.includes('/') || filePath.includes('\\')) {
      // Prendre uniquement le nom du fichier (dernière partie après / ou \)
      const parts = filePath.split(/[\/\\]/);
      fileName = parts[parts.length - 1];
    }
    // Utiliser l'endpoint API spécifique pour le téléchargement
    return `${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}projets/telecharger/${fileName}`;
  }
  /**
   * Extrait le nom du fichier à partir d'un chemin
   * @param filePath Chemin du fichier
   * @returns Nom du fichier
   */
  getFileName(filePath) {
    if (!filePath) return 'fichier';
    // Si c'est un chemin complet (contient / ou \)
    if (filePath.includes('/') || filePath.includes('\\')) {
      const parts = filePath.split(/[\/\\]/);
      return parts[parts.length - 1];
    }
    return filePath;
  }
  static {
    this.ɵfac = function FileService_Factory(t) {
      return new (t || FileService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: FileService,
      factory: FileService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 91873:
/*!*********************************************!*\
  !*** ./src/app/services/projets.service.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProjetService: () => (/* binding */ ProjetService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common/http */ 46443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 77919);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 98764);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs/operators */ 61318);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);






class ProjetService {
  constructor(http) {
    this.http = http;
    // Correction de l'URL pour éviter la duplication de /api
    this.apiUrl = `${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}projets`;
  }
  getHeaders() {
    const token = localStorage.getItem('token');
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }
  getProjets() {
    console.log('Appel API pour récupérer les projets:', this.apiUrl);
    return this.http.get(this.apiUrl, {
      headers: this.getHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.tap)(projets => console.log('Projets récupérés:', projets)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
      console.error('Erreur lors de la récupération des projets:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error);
    }));
  }
  getProjetById(id) {
    return this.http.get(`${this.apiUrl}/${id}`, {
      headers: this.getHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error)));
  }
  addProjet(formData) {
    // Pour les requêtes multipart/form-data, ne pas définir Content-Type
    const headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    });
    return this.http.post(`${this.apiUrl}/create`, formData, {
      headers
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.tap)(response => console.log('Projet ajouté:', response)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
      console.error('Erreur lors de l\'ajout du projet:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error);
    }));
  }
  updateProjet(id, projet) {
    return this.http.put(`${this.apiUrl}/update/${id}`, projet, {
      headers: this.getHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error)));
  }
  deleteProjet(id) {
    // Assurez-vous que l'URL est correcte
    return this.http.delete(`${this.apiUrl}/delete/${id}`, {
      headers: this.getHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.tap)(response => console.log('Projet supprimé:', response)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
      console.error('Erreur lors de la suppression du projet:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error);
    }));
  }
  uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    // Utiliser les headers sans Content-Type pour permettre au navigateur de définir le boundary correct
    const headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    });
    return this.http.post(`${this.apiUrl}/uploads`, formData, {
      headers
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error)));
  }
  static {
    this.ɵfac = function ProjetService_Factory(t) {
      return new (t || ProjetService)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineInjectable"]({
      token: ProjetService,
      factory: ProjetService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 37169:
/*!********************************************!*\
  !*** ./src/app/services/rendus.service.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RendusService: () => (/* binding */ RendusService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common/http */ 46443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs */ 59452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 61318);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../environments/environment */ 45312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);






class RendusService {
  constructor(http) {
    this.http = http;
    this.apiUrl = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}rendus`;
  }
  getHeaders() {
    const token = localStorage.getItem('token');
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }
  // Soumettre un nouveau rendu
  submitRendu(renduData) {
    return this.http.post(`${this.apiUrl}/submit`, renduData);
  }
  // Vérifier si un étudiant a déjà soumis un rendu pour un projet
  checkRenduExists(projetId, etudiantId) {
    return this.http.get(`${this.apiUrl}/check/${projetId}/${etudiantId}`, {
      headers: this.getHeaders()
    });
  }
  // Récupérer tous les rendus
  getAllRendus() {
    return this.http.get(this.apiUrl, {
      headers: this.getHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.catchError)(error => {
      console.error('Erreur lors de la récupération des rendus:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_3__.of)([]);
    }));
  }
  // Récupérer un rendu par son ID
  getRenduById(id) {
    return this.http.get(`${this.apiUrl}/${id}`, {
      headers: this.getHeaders()
    });
  }
  // Récupérer les rendus par projet
  getRendusByProjet(projetId) {
    return this.http.get(`${this.apiUrl}/projet/${projetId}`, {
      headers: this.getHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.catchError)(error => {
      console.error('Erreur lors de la récupération des rendus par projet:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_3__.of)([]);
    }));
  }
  // Évaluer un rendu (manuellement ou via IA)
  evaluateRendu(renduId, evaluationData) {
    return this.http.post(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, {
      headers: this.getHeaders()
    });
  }
  // Mettre à jour une évaluation existante
  updateEvaluation(renduId, evaluationData) {
    return this.http.put(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, {
      headers: this.getHeaders()
    });
  }
  static {
    this.ɵfac = function RendusService_Factory(t) {
      return new (t || RendusService)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjectable"]({
      token: RendusService,
      factory: RendusService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 4866:
/*!******************************************!*\
  !*** ./src/app/services/role.service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RoleService: () => (/* binding */ RoleService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 75797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/angular-jwt */ 92389);



class RoleService {
  constructor(jwtHelper) {
    this.jwtHelper = jwtHelper;
    this.currentUserSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject(null);
    this.currentUser$ = this.currentUserSubject.asObservable();
    this.permissionsSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject(null);
    this.permissions$ = this.permissionsSubject.asObservable();
    this.loadCurrentUser();
  }
  /**
   * Charge l'utilisateur actuel depuis le token
   */
  loadCurrentUser() {
    const token = localStorage.getItem('token');
    if (token && !this.jwtHelper.isTokenExpired(token)) {
      const decodedToken = this.jwtHelper.decodeToken(token);
      const user = {
        _id: decodedToken.id,
        id: decodedToken.id,
        username: decodedToken.username,
        email: decodedToken.email,
        role: decodedToken.role,
        image: decodedToken.image,
        isActive: true
      };
      this.currentUserSubject.next(user);
      this.updatePermissions(user.role);
    }
  }
  /**
   * Met à jour les permissions basées sur le rôle
   */
  updatePermissions(role) {
    const permissions = {
      canCreatePlanning: this.canCreatePlanning(role),
      canEditPlanning: this.canEditPlanning(role),
      canDeletePlanning: this.canDeletePlanning(role),
      canCreateReunion: this.canCreateReunion(role),
      canEditReunion: this.canEditReunion(role),
      canDeleteReunion: this.canDeleteReunion(role),
      canViewAllUsers: this.canViewAllUsers(role),
      canManageUsers: this.canManageUsers(role),
      canAccessAdminPanel: this.canAccessAdminPanel(role),
      canForceDelete: this.canForceDelete(role),
      canViewDetailedReports: this.canViewDetailedReports(role)
    };
    this.permissionsSubject.next(permissions);
  }
  /**
   * Obtient l'utilisateur actuel
   */
  getCurrentUser() {
    return this.currentUserSubject.value;
  }
  /**
   * Obtient le rôle de l'utilisateur actuel
   */
  getCurrentUserRole() {
    const user = this.getCurrentUser();
    return user ? user.role : null;
  }
  /**
   * Vérifie si l'utilisateur actuel est admin
   */
  isAdmin() {
    return this.getCurrentUserRole() === 'admin';
  }
  /**
   * Vérifie si l'utilisateur actuel est tuteur
   */
  isTutor() {
    return this.getCurrentUserRole() === 'tutor';
  }
  /**
   * Vérifie si l'utilisateur actuel est étudiant
   */
  isStudent() {
    return this.getCurrentUserRole() === 'student';
  }
  /**
   * Vérifie si l'utilisateur actuel est alumni
   */
  isAlumni() {
    return this.getCurrentUserRole() === 'alumni';
  }
  /**
   * Vérifie si l'utilisateur peut créer des plannings
   */
  canCreatePlanning(role) {
    const userRole = role || this.getCurrentUserRole();
    return ['admin', 'tutor', 'alumni'].includes(userRole || '');
  }
  /**
   * Vérifie si l'utilisateur peut éditer des plannings
   */
  canEditPlanning(role) {
    const userRole = role || this.getCurrentUserRole();
    return ['admin', 'tutor', 'alumni'].includes(userRole || '');
  }
  /**
   * Vérifie si l'utilisateur peut supprimer des plannings
   */
  canDeletePlanning(role) {
    const userRole = role || this.getCurrentUserRole();
    return ['admin', 'tutor'].includes(userRole || '');
  }
  /**
   * Vérifie si l'utilisateur peut créer des réunions
   */
  canCreateReunion(role) {
    const userRole = role || this.getCurrentUserRole();
    return ['admin', 'tutor', 'alumni', 'student'].includes(userRole || '');
  }
  /**
   * Vérifie si l'utilisateur peut éditer des réunions
   */
  canEditReunion(role) {
    const userRole = role || this.getCurrentUserRole();
    return ['admin', 'tutor', 'alumni', 'student'].includes(userRole || '');
  }
  /**
   * Vérifie si l'utilisateur peut supprimer des réunions
   */
  canDeleteReunion(role) {
    const userRole = role || this.getCurrentUserRole();
    return ['admin', 'tutor', 'alumni'].includes(userRole || '');
  }
  /**
   * Vérifie si l'utilisateur peut voir tous les utilisateurs
   */
  canViewAllUsers(role) {
    const userRole = role || this.getCurrentUserRole();
    return ['admin', 'tutor'].includes(userRole || '');
  }
  /**
   * Vérifie si l'utilisateur peut gérer les utilisateurs
   */
  canManageUsers(role) {
    const userRole = role || this.getCurrentUserRole();
    return userRole === 'admin';
  }
  /**
   * Vérifie si l'utilisateur peut accéder au panel admin
   */
  canAccessAdminPanel(role) {
    const userRole = role || this.getCurrentUserRole();
    return userRole === 'admin';
  }
  /**
   * Vérifie si l'utilisateur peut forcer la suppression
   */
  canForceDelete(role) {
    const userRole = role || this.getCurrentUserRole();
    return userRole === 'admin';
  }
  /**
   * Vérifie si l'utilisateur peut voir les rapports détaillés
   */
  canViewDetailedReports(role) {
    const userRole = role || this.getCurrentUserRole();
    return ['admin', 'tutor'].includes(userRole || '');
  }
  /**
   * Vérifie si l'utilisateur est propriétaire d'une ressource
   */
  isOwner(resourceCreatorId) {
    const currentUser = this.getCurrentUser();
    return currentUser ? currentUser._id === resourceCreatorId : false;
  }
  /**
   * Vérifie si l'utilisateur peut modifier une ressource (propriétaire ou admin)
   */
  canModifyResource(resourceCreatorId) {
    return this.isAdmin() || this.isOwner(resourceCreatorId);
  }
  /**
   * Vérifie si l'utilisateur peut supprimer une ressource (propriétaire ou admin)
   */
  canDeleteResource(resourceCreatorId) {
    return this.isAdmin() || this.isOwner(resourceCreatorId);
  }
  /**
   * Met à jour l'utilisateur actuel (appelé après login)
   */
  updateCurrentUser(user) {
    this.currentUserSubject.next(user);
    this.updatePermissions(user.role);
  }
  /**
   * Nettoie les données utilisateur (appelé après logout)
   */
  clearUserData() {
    this.currentUserSubject.next(null);
    this.permissionsSubject.next(null);
  }
  /**
   * Obtient un message d'erreur personnalisé basé sur le rôle
   */
  getAccessDeniedMessage(action) {
    const role = this.getCurrentUserRole();
    const roleNames = {
      'admin': 'Administrateur',
      'tutor': 'Tuteur',
      'alumni': 'Alumni',
      'student': 'Étudiant'
    };
    return `Accès refusé. Votre rôle (${roleNames[role] || role}) ne vous permet pas de ${action}.`;
  }
  static {
    this.ɵfac = function RoleService_Factory(t) {
      return new (t || RoleService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_2__.JwtHelperService));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: RoleService,
      factory: RoleService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 88076:
/*!*********************************************************************************!*\
  !*** ./src/app/views/front/messages/message-layout/message-layout.component.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessageLayoutComponent: () => (/* binding */ MessageLayoutComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _app_services_message_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @app/services/message.service */ 54537);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _app_services_logger_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @app/services/logger.service */ 34798);




class MessageLayoutComponent {
  constructor(MessageService, route, logger) {
    this.MessageService = MessageService;
    this.route = route;
    this.logger = logger;
    this.subscriptions = [];
    this.context = 'messages';
  }
  ngOnInit() {
    // Détermine le contexte (messages ou notifications)
    this.context = this.route.snapshot.data['context'] || 'messages';
    if (this.context === 'messages') {
      // S'abonner aux changements de conversation active
      this.subscriptions.push(this.MessageService.activeConversation$.subscribe(conversationId => {
        // Ne s'abonner aux messages que si une conversation est sélectionnée
        if (conversationId) {
          this.conversationId = conversationId;
          // Désabonner de l'ancienne souscription si elle existe
          this.subscriptions.forEach(sub => sub.unsubscribe());
          this.subscriptions = [];
          // S'abonner aux nouveaux messages pour cette conversation
          this.subscriptions.push(this.MessageService.subscribeToNewMessages(conversationId).subscribe({
            next: message => {
              // Gestion des nouveaux messages
            },
            error: err => this.logger.error('MessageLayout', 'Error in message subscription', err)
          }));
        }
      }));
    }
    // Ajoutez ici la logique spécifique aux notifications si nécessaire
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  static {
    this.ɵfac = function MessageLayoutComponent_Factory(t) {
      return new (t || MessageLayoutComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_app_services_message_service__WEBPACK_IMPORTED_MODULE_0__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_app_services_logger_service__WEBPACK_IMPORTED_MODULE_1__.LoggerService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: MessageLayoutComponent,
      selectors: [["app-message-layout"]],
      decls: 3,
      vars: 0,
      consts: [[1, "layout-container"], [1, "main-content"]],
      template: function MessageLayoutComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterOutlet],
      styles: ["\n\n.layout-container[_ngcontent-%COMP%] {\n  display: flex;\n  height: 100vh;\n  width: 100%;\n  overflow: hidden;\n}\n\n.main-content[_ngcontent-%COMP%] {\n  flex: 1;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1lc3NhZ2UtbGF5b3V0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsaUNBQWlDO0FBQ2pDO0VBQ0UsYUFBYTtFQUNiLGFBQWE7RUFDYixXQUFXO0VBQ1gsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsT0FBTztFQUNQLFdBQVc7RUFDWCxZQUFZO0VBQ1osZ0JBQWdCO0FBQ2xCIiwiZmlsZSI6Im1lc3NhZ2UtbGF5b3V0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBMYXlvdXQgb3B0aW1pc8OpIHNhbnMgc2lkZWJhciAqL1xyXG4ubGF5b3V0LWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBoZWlnaHQ6IDEwMHZoO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuXHJcbi5tYWluLWNvbnRlbnQge1xyXG4gIGZsZXg6IDE7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuIl19 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvbWVzc2FnZS1sYXlvdXQvbWVzc2FnZS1sYXlvdXQuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxpQ0FBaUM7QUFDakM7RUFDRSxhQUFhO0VBQ2IsYUFBYTtFQUNiLFdBQVc7RUFDWCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxPQUFPO0VBQ1AsV0FBVztFQUNYLFlBQVk7RUFDWixnQkFBZ0I7QUFDbEI7O0FBRUEsNHRCQUE0dEIiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBMYXlvdXQgb3B0aW1pc8ODwqkgc2FucyBzaWRlYmFyICovXHJcbi5sYXlvdXQtY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGhlaWdodDogMTAwdmg7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG5cclxuLm1haW4tY29udGVudCB7XHJcbiAgZmxleDogMTtcclxuICB3aWR0aDogMTAwJTtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=common.js.map