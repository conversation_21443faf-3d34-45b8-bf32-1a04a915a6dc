{"version": 3, "file": "common.js", "mappings": ";;;;;;;;;;;;;;;AAC2D;;AAKrD,MAAOC,WAAW;EAEtBC,YAAA,GAAgB;EAEhB;;;;;EAKAC,cAAcA,CAACC,QAAgB;IAC7B;IACA,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MAChF;MACA,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAGT,qEAAW,CAACU,UAAU,uBAAuBL,QAAQ,EAAE;EACnE;EAEA;;;;;EAKAM,WAAWA,CAACP,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOL,QAAQ;EACjB;;;uBA1CWH,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAW,OAAA,EAAXX,WAAW,CAAAY,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;ACH2C;AACjB;AACG;AAEU;;;AAKrD,MAAOK,aAAa;EAIxBjB,YAAoBkB,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHxB;IACQ,KAAAC,MAAM,GAAG,GAAGrB,qEAAW,CAACU,UAAU,SAAS;EAEX;EAEhCY,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,IAAIV,6DAAW,CAAC;MACrB,eAAe,EAAE,UAAUQ,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEAG,UAAUA,CAAA;IACRC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACP,MAAM,CAAC;IACjE,OAAO,IAAI,CAACD,IAAI,CAACS,GAAG,CAAW,IAAI,CAACR,MAAM,EAAE;MAAES,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CACxES,IAAI,CACHb,mDAAG,CAACc,OAAO,IAAIL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,OAAO,CAAC,CAAC,EAC1Df,0DAAU,CAACgB,KAAK,IAAG;MACjBN,OAAO,CAACM,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,OAAOjB,gDAAU,CAAC,MAAMiB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAC,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACf,IAAI,CAACS,GAAG,CAAS,GAAG,IAAI,CAACR,MAAM,IAAIc,EAAE,EAAE,EAAE;MAAEL,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CACjFS,IAAI,CACHd,0DAAU,CAACgB,KAAK,IAAIjB,gDAAU,CAAC,MAAMiB,KAAK,CAAC,CAAC,CAC7C;EACL;EAEAG,SAASA,CAACC,QAAkB;IAC1B;IACA,MAAMP,OAAO,GAAG,IAAIf,6DAAW,CAAC;MAC9B,eAAe,EAAE,UAAUS,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;KACzD,CAAC;IAEF,OAAO,IAAI,CAACL,IAAI,CAACkB,IAAI,CAAC,GAAG,IAAI,CAACjB,MAAM,SAAS,EAAEgB,QAAQ,EAAE;MAAEP;IAAO,CAAE,CAAC,CAClEC,IAAI,CACHb,mDAAG,CAACqB,QAAQ,IAAIZ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEW,QAAQ,CAAC,CAAC,EACxDtB,0DAAU,CAACgB,KAAK,IAAG;MACjBN,OAAO,CAACM,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAOjB,gDAAU,CAAC,MAAMiB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAO,YAAYA,CAACL,EAAU,EAAEM,MAAc;IACrC,OAAO,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAAS,GAAG,IAAI,CAACrB,MAAM,WAAWc,EAAE,EAAE,EAAEM,MAAM,EAAE;MAAEX,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CAChGS,IAAI,CACHd,0DAAU,CAACgB,KAAK,IAAIjB,gDAAU,CAAC,MAAMiB,KAAK,CAAC,CAAC,CAC7C;EACL;EAEAU,YAAYA,CAACR,EAAU;IACrB;IACA,OAAO,IAAI,CAACf,IAAI,CAACwB,MAAM,CAAC,GAAG,IAAI,CAACvB,MAAM,WAAWc,EAAE,EAAE,EAAE;MAAEL,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CACnFS,IAAI,CACHb,mDAAG,CAACqB,QAAQ,IAAIZ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEW,QAAQ,CAAC,CAAC,EAC1DtB,0DAAU,CAACgB,KAAK,IAAG;MACjBN,OAAO,CAACM,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,OAAOjB,gDAAU,CAAC,MAAMiB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAY,UAAUA,CAACC,IAAU;IACnB,MAAMT,QAAQ,GAAG,IAAIU,QAAQ,EAAE;IAC/BV,QAAQ,CAACW,MAAM,CAAC,MAAM,EAAEF,IAAI,CAAC;IAE7B;IACA,MAAMhB,OAAO,GAAG,IAAIf,6DAAW,CAAC;MAC9B,eAAe,EAAE,UAAUS,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;KACzD,CAAC;IAEF,OAAO,IAAI,CAACL,IAAI,CAACkB,IAAI,CAAC,GAAG,IAAI,CAACjB,MAAM,UAAU,EAAEgB,QAAQ,EAAE;MAAEP;IAAO,CAAE,CAAC,CACnEC,IAAI,CACHd,0DAAU,CAACgB,KAAK,IAAIjB,gDAAU,CAAC,MAAMiB,KAAK,CAAC,CAAC,CAC7C;EACL;;;uBAjFWd,aAAa,EAAA8B,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAbhC,aAAa;MAAAP,OAAA,EAAbO,aAAa,CAAAN,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;ACP2C;AACzB;AACM;AACiB;;;AAKvD,MAAOwC,aAAa;EAGxBpD,YAAoBkB,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGrB,kEAAW,CAACU,UAAU,QAAQ;EAEV;EAEhCY,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,IAAIV,6DAAW,CAAC;MACrB,eAAe,EAAE,UAAUQ,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;EACAgC,WAAWA,CAACC,SAAmB;IAC7B,OAAO,IAAI,CAACpC,IAAI,CAACkB,IAAI,CAAC,GAAG,IAAI,CAACjB,MAAM,SAAS,EAAEmC,SAAS,CAAC;EAC3D;EAEA;EACAC,gBAAgBA,CAACC,QAAgB,EAAEC,UAAkB;IACnD,OAAO,IAAI,CAACvC,IAAI,CAACS,GAAG,CAAC,GAAG,IAAI,CAACR,MAAM,UAAUqC,QAAQ,IAAIC,UAAU,EAAE,EAAE;MAAE7B,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC;EACxG;EAEA;EACAsC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACxC,IAAI,CAACS,GAAG,CAAQ,IAAI,CAACR,MAAM,EAAE;MAAES,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CACrES,IAAI,CACHd,0DAAU,CAACgB,KAAK,IAAG;MACjBN,OAAO,CAACM,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,OAAOoB,wCAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEA;EACAQ,YAAYA,CAAC1B,EAAU;IACrB,OAAO,IAAI,CAACf,IAAI,CAACS,GAAG,CAAM,GAAG,IAAI,CAACR,MAAM,IAAIc,EAAE,EAAE,EAAE;MAAEL,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC;EACnF;EAEA;EACAwC,iBAAiBA,CAACJ,QAAgB;IAChC,OAAO,IAAI,CAACtC,IAAI,CAACS,GAAG,CAAQ,GAAG,IAAI,CAACR,MAAM,WAAWqC,QAAQ,EAAE,EAAE;MAAE5B,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CAC7FS,IAAI,CACHd,0DAAU,CAACgB,KAAK,IAAG;MACjBN,OAAO,CAACM,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;MAC7E,OAAOoB,wCAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEA;EACAU,aAAaA,CAACC,OAAe,EAAEC,cAAmB;IAChD,OAAO,IAAI,CAAC7C,IAAI,CAACkB,IAAI,CAAC,GAAG,IAAI,CAACjB,MAAM,gBAAgB2C,OAAO,EAAE,EAAEC,cAAc,EAAE;MAAEnC,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC;EAChH;EAEA;EACA4C,gBAAgBA,CAACF,OAAe,EAAEC,cAAmB;IACnD,OAAO,IAAI,CAAC7C,IAAI,CAACsB,GAAG,CAAC,GAAG,IAAI,CAACrB,MAAM,gBAAgB2C,OAAO,EAAE,EAAEC,cAAc,EAAE;MAAEnC,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC;EAC/G;;;uBA1DWgC,aAAa,EAAAL,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAbG,aAAa;MAAA1C,OAAA,EAAb0C,aAAa,CAAAzC,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA;;;;;;;;;;;;;;;;;;ACL+B;;;AAoB7C,MAAOsD,WAAW;EAOtBlE,YAAoBmE,SAA2B;IAA3B,KAAAA,SAAS,GAATA,SAAS;IANrB,KAAAC,kBAAkB,GAAG,IAAIH,iDAAe,CAAc,IAAI,CAAC;IAC5D,KAAAI,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAEpD,KAAAC,kBAAkB,GAAG,IAAIN,iDAAe,CAAyB,IAAI,CAAC;IACvE,KAAAO,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACD,YAAY,EAAE;IAG1D,IAAI,CAACG,eAAe,EAAE;EACxB;EAEA;;;EAGQA,eAAeA,CAAA;IACrB,MAAMpD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,IAAI,CAAC,IAAI,CAAC8C,SAAS,CAACO,cAAc,CAACrD,KAAK,CAAC,EAAE;MAClD,MAAMsD,YAAY,GAAG,IAAI,CAACR,SAAS,CAACS,WAAW,CAACvD,KAAK,CAAC;MACtD,MAAMwD,IAAI,GAAS;QACjBC,GAAG,EAAEH,YAAY,CAAC1C,EAAE;QACpBA,EAAE,EAAE0C,YAAY,CAAC1C,EAAE;QACnB8C,QAAQ,EAAEJ,YAAY,CAACI,QAAQ;QAC/BC,KAAK,EAAEL,YAAY,CAACK,KAAK;QACzBC,IAAI,EAAEN,YAAY,CAACM,IAAI;QACvBC,KAAK,EAAEP,YAAY,CAACO,KAAK;QACzBC,QAAQ,EAAE;OACX;MACD,IAAI,CAACf,kBAAkB,CAACgB,IAAI,CAACP,IAAI,CAAC;MAClC,IAAI,CAACQ,iBAAiB,CAACR,IAAI,CAACI,IAAI,CAAC;;EAErC;EAEA;;;EAGQI,iBAAiBA,CAACJ,IAAY;IACpC,MAAMK,WAAW,GAAoB;MACnCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACN,IAAI,CAAC;MAC/CO,eAAe,EAAE,IAAI,CAACA,eAAe,CAACP,IAAI,CAAC;MAC3CQ,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACR,IAAI,CAAC;MAC/CS,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACT,IAAI,CAAC;MAC7CU,cAAc,EAAE,IAAI,CAACA,cAAc,CAACV,IAAI,CAAC;MACzCW,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACX,IAAI,CAAC;MAC7CY,eAAe,EAAE,IAAI,CAACA,eAAe,CAACZ,IAAI,CAAC;MAC3Ca,cAAc,EAAE,IAAI,CAACA,cAAc,CAACb,IAAI,CAAC;MACzCc,mBAAmB,EAAE,IAAI,CAACA,mBAAmB,CAACd,IAAI,CAAC;MACnDe,cAAc,EAAE,IAAI,CAACA,cAAc,CAACf,IAAI,CAAC;MACzCgB,sBAAsB,EAAE,IAAI,CAACA,sBAAsB,CAAChB,IAAI;KACzD;IACD,IAAI,CAACV,kBAAkB,CAACa,IAAI,CAACE,WAAW,CAAC;EAC3C;EAEA;;;EAGAY,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC9B,kBAAkB,CAAC+B,KAAK;EACtC;EAEA;;;EAGAC,kBAAkBA,CAAA;IAChB,MAAMvB,IAAI,GAAG,IAAI,CAACqB,cAAc,EAAE;IAClC,OAAOrB,IAAI,GAAGA,IAAI,CAACI,IAAI,GAAG,IAAI;EAChC;EAEA;;;EAGAoB,OAAOA,CAAA;IACL,OAAO,IAAI,CAACD,kBAAkB,EAAE,KAAK,OAAO;EAC9C;EAEA;;;EAGAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACF,kBAAkB,EAAE,KAAK,OAAO;EAC9C;EAEA;;;EAGAG,SAASA,CAAA;IACP,OAAO,IAAI,CAACH,kBAAkB,EAAE,KAAK,SAAS;EAChD;EAEA;;;EAGAI,QAAQA,CAAA;IACN,OAAO,IAAI,CAACJ,kBAAkB,EAAE,KAAK,QAAQ;EAC/C;EAEA;;;EAGAb,iBAAiBA,CAACN,IAAa;IAC7B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAChG,QAAQ,CAACqG,QAAQ,IAAI,EAAE,CAAC;EAC9D;EAEA;;;EAGAjB,eAAeA,CAACP,IAAa;IAC3B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAChG,QAAQ,CAACqG,QAAQ,IAAI,EAAE,CAAC;EAC9D;EAEA;;;EAGAhB,iBAAiBA,CAACR,IAAa;IAC7B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAChG,QAAQ,CAACqG,QAAQ,IAAI,EAAE,CAAC;EACpD;EAEA;;;EAGAf,gBAAgBA,CAACT,IAAa;IAC5B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAChG,QAAQ,CAACqG,QAAQ,IAAI,EAAE,CAAC;EACzE;EAEA;;;EAGAd,cAAcA,CAACV,IAAa;IAC1B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAChG,QAAQ,CAACqG,QAAQ,IAAI,EAAE,CAAC;EACzE;EAEA;;;EAGAb,gBAAgBA,CAACX,IAAa;IAC5B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAChG,QAAQ,CAACqG,QAAQ,IAAI,EAAE,CAAC;EAC9D;EAEA;;;EAGAZ,eAAeA,CAACZ,IAAa;IAC3B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAChG,QAAQ,CAACqG,QAAQ,IAAI,EAAE,CAAC;EACpD;EAEA;;;EAGAX,cAAcA,CAACb,IAAa;IAC1B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAOK,QAAQ,KAAK,OAAO;EAC7B;EAEA;;;EAGAV,mBAAmBA,CAACd,IAAa;IAC/B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAOK,QAAQ,KAAK,OAAO;EAC7B;EAEA;;;EAGAT,cAAcA,CAACf,IAAa;IAC1B,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAOK,QAAQ,KAAK,OAAO;EAC7B;EAEA;;;EAGAR,sBAAsBA,CAAChB,IAAa;IAClC,MAAMwB,QAAQ,GAAGxB,IAAI,IAAI,IAAI,CAACmB,kBAAkB,EAAE;IAClD,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAChG,QAAQ,CAACqG,QAAQ,IAAI,EAAE,CAAC;EACpD;EAEA;;;EAGAC,OAAOA,CAACC,iBAAyB;IAC/B,MAAMC,WAAW,GAAG,IAAI,CAACV,cAAc,EAAE;IACzC,OAAOU,WAAW,GAAGA,WAAW,CAAC9B,GAAG,KAAK6B,iBAAiB,GAAG,KAAK;EACpE;EAEA;;;EAGAE,iBAAiBA,CAACF,iBAAyB;IACzC,OAAO,IAAI,CAACN,OAAO,EAAE,IAAI,IAAI,CAACK,OAAO,CAACC,iBAAiB,CAAC;EAC1D;EAEA;;;EAGAG,iBAAiBA,CAACH,iBAAyB;IACzC,OAAO,IAAI,CAACN,OAAO,EAAE,IAAI,IAAI,CAACK,OAAO,CAACC,iBAAiB,CAAC;EAC1D;EAEA;;;EAGAI,iBAAiBA,CAAClC,IAAU;IAC1B,IAAI,CAACT,kBAAkB,CAACgB,IAAI,CAACP,IAAI,CAAC;IAClC,IAAI,CAACQ,iBAAiB,CAACR,IAAI,CAACI,IAAI,CAAC;EACnC;EAEA;;;EAGA+B,aAAaA,CAAA;IACX,IAAI,CAAC5C,kBAAkB,CAACgB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACb,kBAAkB,CAACa,IAAI,CAAC,IAAI,CAAC;EACpC;EAEA;;;EAGA6B,sBAAsBA,CAACC,MAAc;IACnC,MAAMjC,IAAI,GAAG,IAAI,CAACmB,kBAAkB,EAAE;IACtC,MAAMe,SAAS,GAAG;MAChB,OAAO,EAAE,gBAAgB;MACzB,OAAO,EAAE,QAAQ;MACjB,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE;KACZ;IAED,OAAO,6BAA6BA,SAAS,CAAClC,IAA8B,CAAC,IAAIA,IAAI,2BAA2BiC,MAAM,GAAG;EAC3H;;;uBA1OWhD,WAAW,EAAAnB,sDAAA,CAAAE,gEAAA;IAAA;EAAA;;;aAAXiB,WAAW;MAAAxD,OAAA,EAAXwD,WAAW,CAAAvD,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;ACTd,MAAOyG,sBAAsB;EAIjCrH,YACUsH,cAA8B,EAC9BC,KAAqB,EACrBC,MAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IANR,KAAAC,aAAa,GAAmB,EAAE;IAC1C,KAAAC,OAAO,GAAW,UAAU;EAMzB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,OAAO,GAAG,IAAI,CAACH,KAAK,CAACK,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC,IAAI,UAAU;IAEhE,IAAI,IAAI,CAACH,OAAO,KAAK,UAAU,EAAE;MAC/B;MACA,IAAI,CAACD,aAAa,CAACK,IAAI,CACrB,IAAI,CAACR,cAAc,CAACS,mBAAmB,CAACC,SAAS,CAAEC,cAAc,IAAI;QACnE;QACA,IAAIA,cAAc,EAAE;UAClB,IAAI,CAACA,cAAc,GAAGA,cAAc;UAEpC;UACA,IAAI,CAACR,aAAa,CAACS,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;UACtD,IAAI,CAACX,aAAa,GAAG,EAAE;UAEvB;UACA,IAAI,CAACA,aAAa,CAACK,IAAI,CACrB,IAAI,CAACR,cAAc,CAACe,sBAAsB,CACxCJ,cAAc,CACf,CAACD,SAAS,CAAC;YACV5C,IAAI,EAAGkD,OAAO,IAAI;cAChB;YAAA,CACD;YACDvG,KAAK,EAAGwG,GAAG,IACT,IAAI,CAACf,MAAM,CAACzF,KAAK,CACf,eAAe,EACf,+BAA+B,EAC/BwG,GAAG;WAER,CAAC,CACH;;MAEL,CAAC,CAAC,CACH;;IAEH;EACF;;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACf,aAAa,CAACS,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;;;uBAnDWf,sBAAsB,EAAAtE,+DAAA,CAAAE,yEAAA,GAAAF,+DAAA,CAAA2F,2DAAA,GAAA3F,+DAAA,CAAA6F,uEAAA;IAAA;EAAA;;;YAAtBvB,sBAAsB;MAAAyB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXnCrG,4DAAA,aAA8B;UAE1BA,uDAAA,oBAA+B;UACjCA,0DAAA,EAAM", "sources": ["./src/app/services/file.service.ts", "./src/app/services/projets.service.ts", "./src/app/services/rendus.service.ts", "./src/app/services/role.service.ts", "./src/app/views/front/messages/message-layout/message-layout.component.ts", "./src/app/views/front/messages/message-layout/message-layout.component.html"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { environment } from 'src/environments/environment';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class FileService {\n  \n  constructor() { }\n  \n  /**\n   * Génère une URL de téléchargement pour un fichier\n   * @param filePath Chemin du fichier\n   * @returns URL de téléchargement\n   */\n  getDownloadUrl(filePath: string): string {\n    // Si le chemin est vide ou null, retourner une chaîne vide\n    if (!filePath) return '';\n    \n    // Extraire uniquement le nom du fichier, peu importe le format du chemin\n    let fileName = filePath;\n    \n    // Si c'est un chemin complet (contient C:/ ou autre)\n    if (filePath.includes('C:') || filePath.includes('/') || filePath.includes('\\\\')) {\n      // Prendre uniquement le nom du fichier (dernière partie après / ou \\)\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    \n    // Utiliser l'endpoint API spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  \n  /**\n   * Extrait le nom du fichier à partir d'un chemin\n   * @param filePath Chemin du fichier\n   * @returns Nom du fichier\n   */\n  getFileName(filePath: string): string {\n    if (!filePath) return 'fichier';\n    \n    // Si c'est un chemin complet (contient / ou \\)\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    \n    return filePath;\n  }\n}\n\n\n\n", "import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { Projet } from '../models/projet.model';\nimport { environment } from 'src/environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProjetService {\n  // Correction de l'URL pour éviter la duplication de /api\n  private apiUrl = `${environment.urlBackend}projets`;\n\n  constructor(private http: HttpClient) { }\n\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n\n  getProjets(): Observable<Projet[]> {\n    console.log('Appel API pour récupérer les projets:', this.apiUrl);\n    return this.http.get<Projet[]>(this.apiUrl, { headers: this.getHeaders() })\n      .pipe(\n        tap(projets => console.log('Projets récupérés:', projets)),\n        catchError(error => {\n          console.error('Erreur lors de la récupération des projets:', error);\n          return throwError(() => error);\n        })\n      );\n  }\n\n  getProjetById(id: string): Observable<Projet> {\n    return this.http.get<Projet>(`${this.apiUrl}/${id}`, { headers: this.getHeaders() })\n      .pipe(\n        catchError(error => throwError(() => error))\n      );\n  }\n\n  addProjet(formData: FormData): Observable<any> {\n    // Pour les requêtes multipart/form-data, ne pas définir Content-Type\n    const headers = new HttpHeaders({\n      'Authorization': `Bearer ${localStorage.getItem('token')}`\n    });\n    \n    return this.http.post(`${this.apiUrl}/create`, formData, { headers })\n      .pipe(\n        tap(response => console.log('Projet ajouté:', response)),\n        catchError(error => {\n          console.error('Erreur lors de l\\'ajout du projet:', error);\n          return throwError(() => error);\n        })\n      );\n  }\n\n  updateProjet(id: string, projet: Projet): Observable<Projet> {\n    return this.http.put<Projet>(`${this.apiUrl}/update/${id}`, projet, { headers: this.getHeaders() })\n      .pipe(\n        catchError(error => throwError(() => error))\n      );\n  }\n\n  deleteProjet(id: string): Observable<any> {\n    // Assurez-vous que l'URL est correcte\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, { headers: this.getHeaders() })\n      .pipe(\n        tap(response => console.log('Projet supprimé:', response)),\n        catchError(error => {\n          console.error('Erreur lors de la suppression du projet:', error);\n          return throwError(() => error);\n        })\n      );\n  }\n\n  uploadFile(file: File): Observable<any> {\n    const formData = new FormData();\n    formData.append('file', file);\n    \n    // Utiliser les headers sans Content-Type pour permettre au navigateur de définir le boundary correct\n    const headers = new HttpHeaders({\n      'Authorization': `Bearer ${localStorage.getItem('token')}`\n    });\n    \n    return this.http.post(`${this.apiUrl}/uploads`, formData, { headers })\n      .pipe(\n        catchError(error => throwError(() => error))\n      );\n  }\n}\n\n\n\n\n\n\n\n\n", "import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RendusService {\n  private apiUrl = `${environment.urlBackend}rendus`;\n\n  constructor(private http: HttpClient) { }\n\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n\n  // Soumettre un nouveau rendu\n  submitRendu(renduData: FormData): Observable<any> {\n    return this.http.post(`${this.apiUrl}/submit`, renduData);\n  }\n\n  // Vérifier si un étudiant a déjà soumis un rendu pour un projet\n  checkRenduExists(projetId: string, etudiantId: string): Observable<any> {\n    return this.http.get(`${this.apiUrl}/check/${projetId}/${etudiantId}`, { headers: this.getHeaders() });\n  }\n\n  // Récupérer tous les rendus\n  getAllRendus(): Observable<any[]> {\n    return this.http.get<any[]>(this.apiUrl, { headers: this.getHeaders() })\n      .pipe(\n        catchError(error => {\n          console.error('Erreur lors de la récupération des rendus:', error);\n          return of([]);\n        })\n      );\n  }\n\n  // Récupérer un rendu par son ID\n  getRenduById(id: string): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/${id}`, { headers: this.getHeaders() });\n  }\n\n  // Récupérer les rendus par projet\n  getRendusByProjet(projetId: string): Observable<any[]> {\n    return this.http.get<any[]>(`${this.apiUrl}/projet/${projetId}`, { headers: this.getHeaders() })\n      .pipe(\n        catchError(error => {\n          console.error('Erreur lors de la récupération des rendus par projet:', error);\n          return of([]);\n        })\n      );\n  }\n\n  // Évaluer un rendu (manuellement ou via IA)\n  evaluateRendu(renduId: string, evaluationData: any): Observable<any> {\n    return this.http.post(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, { headers: this.getHeaders() });\n  }\n\n  // Mettre à jour une évaluation existante\n  updateEvaluation(renduId: string, evaluationData: any): Observable<any> {\n    return this.http.put(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, { headers: this.getHeaders() });\n  }\n}\n\n\n\n", "import { Injectable } from '@angular/core';\r\nimport { JwtHelperService } from '@auth0/angular-jwt';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { User } from '@app/models/user.model';\r\n\r\nexport interface UserPermissions {\r\n  canCreatePlanning: boolean;\r\n  canEditPlanning: boolean;\r\n  canDeletePlanning: boolean;\r\n  canCreateReunion: boolean;\r\n  canEditReunion: boolean;\r\n  canDeleteReunion: boolean;\r\n  canViewAllUsers: boolean;\r\n  canManageUsers: boolean;\r\n  canAccessAdminPanel: boolean;\r\n  canForceDelete: boolean;\r\n  canViewDetailedReports: boolean;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RoleService {\r\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n\r\n  private permissionsSubject = new BehaviorSubject<UserPermissions | null>(null);\r\n  public permissions$ = this.permissionsSubject.asObservable();\r\n\r\n  constructor(private jwtHelper: JwtHelperService) {\r\n    this.loadCurrentUser();\r\n  }\r\n\r\n  /**\r\n   * Charge l'utilisateur actuel depuis le token\r\n   */\r\n  private loadCurrentUser(): void {\r\n    const token = localStorage.getItem('token');\r\n    if (token && !this.jwtHelper.isTokenExpired(token)) {\r\n      const decodedToken = this.jwtHelper.decodeToken(token);\r\n      const user: User = {\r\n        _id: decodedToken.id,\r\n        id: decodedToken.id,\r\n        username: decodedToken.username,\r\n        email: decodedToken.email,\r\n        role: decodedToken.role,\r\n        image: decodedToken.image,\r\n        isActive: true\r\n      };\r\n      this.currentUserSubject.next(user);\r\n      this.updatePermissions(user.role);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Met à jour les permissions basées sur le rôle\r\n   */\r\n  private updatePermissions(role: string): void {\r\n    const permissions: UserPermissions = {\r\n      canCreatePlanning: this.canCreatePlanning(role),\r\n      canEditPlanning: this.canEditPlanning(role),\r\n      canDeletePlanning: this.canDeletePlanning(role),\r\n      canCreateReunion: this.canCreateReunion(role),\r\n      canEditReunion: this.canEditReunion(role),\r\n      canDeleteReunion: this.canDeleteReunion(role),\r\n      canViewAllUsers: this.canViewAllUsers(role),\r\n      canManageUsers: this.canManageUsers(role),\r\n      canAccessAdminPanel: this.canAccessAdminPanel(role),\r\n      canForceDelete: this.canForceDelete(role),\r\n      canViewDetailedReports: this.canViewDetailedReports(role)\r\n    };\r\n    this.permissionsSubject.next(permissions);\r\n  }\r\n\r\n  /**\r\n   * Obtient l'utilisateur actuel\r\n   */\r\n  getCurrentUser(): User | null {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n  /**\r\n   * Obtient le rôle de l'utilisateur actuel\r\n   */\r\n  getCurrentUserRole(): string | null {\r\n    const user = this.getCurrentUser();\r\n    return user ? user.role : null;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur actuel est admin\r\n   */\r\n  isAdmin(): boolean {\r\n    return this.getCurrentUserRole() === 'admin';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur actuel est tuteur\r\n   */\r\n  isTutor(): boolean {\r\n    return this.getCurrentUserRole() === 'tutor';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur actuel est étudiant\r\n   */\r\n  isStudent(): boolean {\r\n    return this.getCurrentUserRole() === 'student';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur actuel est alumni\r\n   */\r\n  isAlumni(): boolean {\r\n    return this.getCurrentUserRole() === 'alumni';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut créer des plannings\r\n   */\r\n  canCreatePlanning(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut éditer des plannings\r\n   */\r\n  canEditPlanning(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut supprimer des plannings\r\n   */\r\n  canDeletePlanning(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut créer des réunions\r\n   */\r\n  canCreateReunion(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni', 'student'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut éditer des réunions\r\n   */\r\n  canEditReunion(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni', 'student'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut supprimer des réunions\r\n   */\r\n  canDeleteReunion(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor', 'alumni'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut voir tous les utilisateurs\r\n   */\r\n  canViewAllUsers(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut gérer les utilisateurs\r\n   */\r\n  canManageUsers(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return userRole === 'admin';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut accéder au panel admin\r\n   */\r\n  canAccessAdminPanel(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return userRole === 'admin';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut forcer la suppression\r\n   */\r\n  canForceDelete(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return userRole === 'admin';\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut voir les rapports détaillés\r\n   */\r\n  canViewDetailedReports(role?: string): boolean {\r\n    const userRole = role || this.getCurrentUserRole();\r\n    return ['admin', 'tutor'].includes(userRole || '');\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur est propriétaire d'une ressource\r\n   */\r\n  isOwner(resourceCreatorId: string): boolean {\r\n    const currentUser = this.getCurrentUser();\r\n    return currentUser ? currentUser._id === resourceCreatorId : false;\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut modifier une ressource (propriétaire ou admin)\r\n   */\r\n  canModifyResource(resourceCreatorId: string): boolean {\r\n    return this.isAdmin() || this.isOwner(resourceCreatorId);\r\n  }\r\n\r\n  /**\r\n   * Vérifie si l'utilisateur peut supprimer une ressource (propriétaire ou admin)\r\n   */\r\n  canDeleteResource(resourceCreatorId: string): boolean {\r\n    return this.isAdmin() || this.isOwner(resourceCreatorId);\r\n  }\r\n\r\n  /**\r\n   * Met à jour l'utilisateur actuel (appelé après login)\r\n   */\r\n  updateCurrentUser(user: User): void {\r\n    this.currentUserSubject.next(user);\r\n    this.updatePermissions(user.role);\r\n  }\r\n\r\n  /**\r\n   * Nettoie les données utilisateur (appelé après logout)\r\n   */\r\n  clearUserData(): void {\r\n    this.currentUserSubject.next(null);\r\n    this.permissionsSubject.next(null);\r\n  }\r\n\r\n  /**\r\n   * Obtient un message d'erreur personnalisé basé sur le rôle\r\n   */\r\n  getAccessDeniedMessage(action: string): string {\r\n    const role = this.getCurrentUserRole();\r\n    const roleNames = {\r\n      'admin': 'Administrateur',\r\n      'tutor': 'Tuteur',\r\n      'alumni': 'Alumni',\r\n      'student': 'Étudiant'\r\n    };\r\n\r\n    return `Accès refusé. Votre rôle (${roleNames[role as keyof typeof roleNames] || role}) ne vous permet pas de ${action}.`;\r\n  }\r\n}", "import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { ActivatedRoute } from '@angular/router';\nimport { MessageService } from '@app/services/message.service';\nimport { LoggerService } from '@app/services/logger.service';\n@Component({\n  selector: 'app-message-layout',\n  templateUrl: './message-layout.component.html',\n  styleUrls: ['./message-layout.component.css'],\n  // schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})\nexport class MessageLayoutComponent implements OnInit, OnDestroy {\n  private subscriptions: Subscription[] = [];\n  context: string = 'messages';\n  conversationId: any;\n  constructor(\n    private MessageService: MessageService,\n    private route: ActivatedRoute,\n    private logger: LoggerService\n  ) {}\n\n  ngOnInit() {\n    // Détermine le contexte (messages ou notifications)\n    this.context = this.route.snapshot.data['context'] || 'messages';\n\n    if (this.context === 'messages') {\n      // S'abonner aux changements de conversation active\n      this.subscriptions.push(\n        this.MessageService.activeConversation$.subscribe((conversationId) => {\n          // Ne s'abonner aux messages que si une conversation est sélectionnée\n          if (conversationId) {\n            this.conversationId = conversationId;\n\n            // Désabonner de l'ancienne souscription si elle existe\n            this.subscriptions.forEach((sub) => sub.unsubscribe());\n            this.subscriptions = [];\n\n            // S'abonner aux nouveaux messages pour cette conversation\n            this.subscriptions.push(\n              this.MessageService.subscribeToNewMessages(\n                conversationId\n              ).subscribe({\n                next: (message) => {\n                  // Gestion des nouveaux messages\n                },\n                error: (err) =>\n                  this.logger.error(\n                    'MessageLayout',\n                    'Error in message subscription',\n                    err\n                  ),\n              })\n            );\n          }\n        })\n      );\n    }\n    // Ajoutez ici la logique spécifique aux notifications si nécessaire\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n", "<div class=\"layout-container\">\n  <div class=\"main-content\">\n    <router-outlet></router-outlet>\n  </div>\n</div>\n"], "names": ["environment", "FileService", "constructor", "getDownloadUrl", "filePath", "fileName", "includes", "parts", "split", "length", "urlBackend", "getFileName", "factory", "ɵfac", "providedIn", "HttpHeaders", "throwError", "catchError", "tap", "ProjetService", "http", "apiUrl", "getHeaders", "token", "localStorage", "getItem", "getProjets", "console", "log", "get", "headers", "pipe", "projets", "error", "getProjetById", "id", "addProjet", "formData", "post", "response", "updateProjet", "projet", "put", "deleteProjet", "delete", "uploadFile", "file", "FormData", "append", "i0", "ɵɵinject", "i1", "HttpClient", "of", "RendusService", "submitRendu", "renduData", "checkRenduExists", "projetId", "etudiantId", "getAllRendus", "getRenduById", "getRendusByProjet", "evaluateRendu", "renduId", "evaluationData", "updateEvaluation", "BehaviorSubject", "RoleService", "jwtHelper", "currentUserSubject", "currentUser$", "asObservable", "permissionsSubject", "permissions$", "loadCurrentUser", "isTokenExpired", "decodedToken", "decodeToken", "user", "_id", "username", "email", "role", "image", "isActive", "next", "updatePermissions", "permissions", "canCreatePlanning", "canEditPlanning", "canDeletePlanning", "canCreateReunion", "canEditReunion", "canDeleteReunion", "canViewAllUsers", "canManageUsers", "canAccessAdminPanel", "canForceDelete", "canViewDetailedReports", "getCurrentUser", "value", "getCurrentUserRole", "isAdmin", "isTutor", "isStudent", "<PERSON><PERSON><PERSON><PERSON>", "userRole", "isOwner", "resourceCreatorId", "currentUser", "canModifyResource", "canDeleteResource", "updateCurrentUser", "clearUserData", "getAccessDeniedMessage", "action", "roleNames", "JwtHelperService", "MessageLayoutComponent", "MessageService", "route", "logger", "subscriptions", "context", "ngOnInit", "snapshot", "data", "push", "activeConversation$", "subscribe", "conversationId", "for<PERSON>ach", "sub", "unsubscribe", "subscribeToNewMessages", "message", "err", "ngOnDestroy", "ɵɵdirectiveInject", "i2", "ActivatedRoute", "i3", "LoggerService", "selectors", "decls", "vars", "consts", "template", "MessageLayoutComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}