{"version": 3, "file": "default-node_modules_angular_cdk_fesm2022_a11y_mjs-node_modules_angular_cdk_fesm2022_scrolling_mjs.js", "mappings": ";;;;;;;;;;;;;;;;AAAuC;AACa;AACY;AACzD,SAASG,KAAKA,CAACC,gBAAgB,EAAE;EACpC,OAAOJ,mDAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,IAAIC,UAAU,GAAG,KAAK;IACtB,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACtBF,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACG,WAAW,CAAC,CAAC;MACxGH,kBAAkB,GAAG,IAAI;MACzB,IAAIF,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,MAAMM,KAAK,GAAGL,SAAS;QACvBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACQ,IAAI,CAACD,KAAK,CAAC;MAC1B;MACAH,UAAU,IAAIJ,UAAU,CAACS,QAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC1BP,kBAAkB,GAAG,IAAI;MACzBC,UAAU,IAAIJ,UAAU,CAACS,QAAQ,CAAC,CAAC;IACvC,CAAC;IACDV,MAAM,CAACY,SAAS,CAACf,6EAAwB,CAACI,UAAU,EAAGO,KAAK,IAAK;MAC7DN,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGK,KAAK;MACjB,IAAI,CAACJ,kBAAkB,EAAE;QACrBR,gEAAS,CAACG,gBAAgB,CAACS,KAAK,CAAC,CAAC,CAACI,SAAS,CAAER,kBAAkB,GAAGP,6EAAwB,CAACI,UAAU,EAAEK,WAAW,EAAEK,eAAe,CAAE,CAAC;MAC3I;IACJ,CAAC,EAAE,MAAM;MACLN,UAAU,GAAG,IAAI;MACjB,CAAC,CAACH,QAAQ,IAAI,CAACE,kBAAkB,IAAIA,kBAAkB,CAACS,MAAM,KAAKZ,UAAU,CAACS,QAAQ,CAAC,CAAC;IAC5F,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;;;;;;;;;;;;;;;;;ACnCoD;AACpB;AACY;AACrC,SAASM,SAASA,CAACC,QAAQ,EAAEC,SAAS,GAAGJ,4DAAc,EAAE;EAC5D,OAAOhB,6CAAK,CAAC,MAAMiB,wDAAK,CAACE,QAAQ,EAAEC,SAAS,CAAC,CAAC;AAClD;;;;;;;;;;;;;;;;ACLuC;AACyB;AACzD,SAASC,QAAQA,CAAA,EAAG;EACvB,OAAOxB,mDAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAImB,IAAI;IACR,IAAIC,OAAO,GAAG,KAAK;IACnBrB,MAAM,CAACY,SAAS,CAACf,6EAAwB,CAACI,UAAU,EAAGO,KAAK,IAAK;MAC7D,MAAMc,CAAC,GAAGF,IAAI;MACdA,IAAI,GAAGZ,KAAK;MACZa,OAAO,IAAIpB,UAAU,CAACQ,IAAI,CAAC,CAACa,CAAC,EAAEd,KAAK,CAAC,CAAC;MACtCa,OAAO,GAAG,IAAI;IAClB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;;;;;;;;;;;;;;;ACbkC;AAC3B,SAASG,IAAIA,CAACC,KAAK,EAAE;EACxB,OAAOF,+CAAM,CAAC,CAACG,CAAC,EAAEC,KAAK,KAAKF,KAAK,IAAIE,KAAK,CAAC;AAC/C;;;;;;;;;;;;;;;;ACH4C;AACsB;AAC3D,MAAMG,oBAAoB,SAASF,qDAAW,CAAC;EAClDG,WAAWA,CAACb,SAAS,EAAEc,IAAI,EAAE;IACzB,KAAK,CAACd,SAAS,EAAEc,IAAI,CAAC;IACtB,IAAI,CAACd,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACc,IAAI,GAAGA,IAAI;EACpB;EACAC,cAAcA,CAACf,SAAS,EAAEgB,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7B,OAAO,KAAK,CAACF,cAAc,CAACf,SAAS,EAAEgB,EAAE,EAAEC,KAAK,CAAC;IACrD;IACAjB,SAAS,CAACkB,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5B,OAAOnB,SAAS,CAACoB,UAAU,KAAKpB,SAAS,CAACoB,UAAU,GAAGT,2EAAsB,CAACU,qBAAqB,CAAC,MAAMrB,SAAS,CAACsB,KAAK,CAACC,SAAS,CAAC,CAAC,CAAC;EAC1I;EACAC,cAAcA,CAACxB,SAAS,EAAEgB,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIQ,EAAE;IACN,IAAIR,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,EAAE;MAC5C,OAAO,KAAK,CAACO,cAAc,CAACxB,SAAS,EAAEgB,EAAE,EAAEC,KAAK,CAAC;IACrD;IACA,MAAM;MAAEC;IAAQ,CAAC,GAAGlB,SAAS;IAC7B,IAAIgB,EAAE,IAAI,IAAI,IAAIA,EAAE,KAAKhB,SAAS,CAACoB,UAAU,IAAI,CAAC,CAACK,EAAE,GAAGP,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACT,EAAE,MAAMA,EAAE,EAAE;MACrIL,2EAAsB,CAACgB,oBAAoB,CAACX,EAAE,CAAC;MAC/ChB,SAAS,CAACoB,UAAU,GAAGG,SAAS;IACpC;IACA,OAAOA,SAAS;EACpB;AACJ;;;;;;;;;;;;;;;AC3BkD;AAC3C,MAAMM,uBAAuB,SAASD,2DAAc,CAAC;EACxDN,KAAKA,CAACQ,MAAM,EAAE;IACV,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAIC,OAAO;IACX,IAAIF,MAAM,EAAE;MACRE,OAAO,GAAGF,MAAM,CAACd,EAAE;IACvB,CAAC,MACI;MACDgB,OAAO,GAAG,IAAI,CAACZ,UAAU;MACzB,IAAI,CAACA,UAAU,GAAGG,SAAS;IAC/B;IACA,MAAM;MAAEL;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAIe,KAAK;IACTH,MAAM,GAAGA,MAAM,IAAIZ,OAAO,CAACgB,KAAK,CAAC,CAAC;IAClC,GAAG;MACC,IAAKD,KAAK,GAAGH,MAAM,CAACK,OAAO,CAACL,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACb,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ,CAAC,QAAQ,CAACa,MAAM,GAAGZ,OAAO,CAAC,CAAC,CAAC,KAAKY,MAAM,CAACd,EAAE,KAAKgB,OAAO,IAAId,OAAO,CAACgB,KAAK,CAAC,CAAC;IAC1E,IAAI,CAACH,OAAO,GAAG,KAAK;IACpB,IAAIE,KAAK,EAAE;MACP,OAAO,CAACH,MAAM,GAAGZ,OAAO,CAAC,CAAC,CAAC,KAAKY,MAAM,CAACd,EAAE,KAAKgB,OAAO,IAAId,OAAO,CAACgB,KAAK,CAAC,CAAC,EAAE;QACtEJ,MAAM,CAACzC,WAAW,CAAC,CAAC;MACxB;MACA,MAAM4C,KAAK;IACf;EACJ;AACJ;;;;;;;;;;;;;;;;AC5B4C;AACY;AACjD,MAAMK,UAAU,SAAS5B,qDAAW,CAAC;EACxCG,WAAWA,CAACb,SAAS,EAAEc,IAAI,EAAE;IACzB,KAAK,CAACd,SAAS,EAAEc,IAAI,CAAC;IACtB,IAAI,CAACd,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACc,IAAI,GAAGA,IAAI;EACpB;EACAC,cAAcA,CAACf,SAAS,EAAEgB,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7B,OAAO,KAAK,CAACF,cAAc,CAACf,SAAS,EAAEgB,EAAE,EAAEC,KAAK,CAAC;IACrD;IACAjB,SAAS,CAACkB,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5B,OAAOnB,SAAS,CAACoB,UAAU,KAAKpB,SAAS,CAACoB,UAAU,GAAGiB,iEAAiB,CAACE,YAAY,CAACvC,SAAS,CAACsB,KAAK,CAACkB,IAAI,CAACxC,SAAS,EAAEuB,SAAS,CAAC,CAAC,CAAC;EACtI;EACAC,cAAcA,CAACxB,SAAS,EAAEgB,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIQ,EAAE;IACN,IAAIR,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,EAAE;MAC5C,OAAO,KAAK,CAACO,cAAc,CAACxB,SAAS,EAAEgB,EAAE,EAAEC,KAAK,CAAC;IACrD;IACA,MAAM;MAAEC;IAAQ,CAAC,GAAGlB,SAAS;IAC7B,IAAIgB,EAAE,IAAI,IAAI,IAAI,CAAC,CAACS,EAAE,GAAGP,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACT,EAAE,MAAMA,EAAE,EAAE;MACtGqB,iEAAiB,CAACI,cAAc,CAACzB,EAAE,CAAC;MACpC,IAAIhB,SAAS,CAACoB,UAAU,KAAKJ,EAAE,EAAE;QAC7BhB,SAAS,CAACoB,UAAU,GAAGG,SAAS;MACpC;IACJ;IACA,OAAOA,SAAS;EACpB;AACJ;;;;;;;;;;;;;;;AC7BkD;AAC3C,MAAMmB,aAAa,SAASd,2DAAc,CAAC;EAC9CN,KAAKA,CAACQ,MAAM,EAAE;IACV,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,MAAMC,OAAO,GAAG,IAAI,CAACZ,UAAU;IAC/B,IAAI,CAACA,UAAU,GAAGG,SAAS;IAC3B,MAAM;MAAEL;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAIe,KAAK;IACTH,MAAM,GAAGA,MAAM,IAAIZ,OAAO,CAACgB,KAAK,CAAC,CAAC;IAClC,GAAG;MACC,IAAKD,KAAK,GAAGH,MAAM,CAACK,OAAO,CAACL,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACb,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ,CAAC,QAAQ,CAACa,MAAM,GAAGZ,OAAO,CAAC,CAAC,CAAC,KAAKY,MAAM,CAACd,EAAE,KAAKgB,OAAO,IAAId,OAAO,CAACgB,KAAK,CAAC,CAAC;IAC1E,IAAI,CAACH,OAAO,GAAG,KAAK;IACpB,IAAIE,KAAK,EAAE;MACP,OAAO,CAACH,MAAM,GAAGZ,OAAO,CAAC,CAAC,CAAC,KAAKY,MAAM,CAACd,EAAE,KAAKgB,OAAO,IAAId,OAAO,CAACgB,KAAK,CAAC,CAAC,EAAE;QACtEJ,MAAM,CAACzC,WAAW,CAAC,CAAC;MACxB;MACA,MAAM4C,KAAK;IACf;EACJ;AACJ;;;;;;;;;;;;;;;;;ACtB8D;AACM;AAC7D,MAAMU,uBAAuB,GAAG,IAAId,6EAAuB,CAACjB,uEAAoB,CAAC;AACjF,MAAMgC,cAAc,GAAGD,uBAAuB;;;;;;;;;;;;;;;ACHN;AACxC,MAAMhC,sBAAsB,GAAG;EAClCmC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAIC,OAAO,GAAG3B,qBAAqB;IACnC,IAAI4B,MAAM,GAAGtB,oBAAoB;IACjC,MAAM;MAAEuB;IAAS,CAAC,GAAGvC,sBAAsB;IAC3C,IAAIuC,QAAQ,EAAE;MACVF,OAAO,GAAGE,QAAQ,CAAC7B,qBAAqB;MACxC4B,MAAM,GAAGC,QAAQ,CAACvB,oBAAoB;IAC1C;IACA,MAAMwB,MAAM,GAAGH,OAAO,CAAEI,SAAS,IAAK;MAClCH,MAAM,GAAG1B,SAAS;MAClBwB,QAAQ,CAACK,SAAS,CAAC;IACvB,CAAC,CAAC;IACF,OAAO,IAAIP,uDAAY,CAAC,MAAMI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,MAAM,CAAC,CAAC;EACjG,CAAC;EACD9B,qBAAqBA,CAAC,GAAGgC,IAAI,EAAE;IAC3B,MAAM;MAAEH;IAAS,CAAC,GAAGvC,sBAAsB;IAC3C,OAAO,CAAC,CAACuC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC7B,qBAAqB,KAAKA,qBAAqB,EAAE,GAAGgC,IAAI,CAAC;EACnI,CAAC;EACD1B,oBAAoBA,CAAC,GAAG0B,IAAI,EAAE;IAC1B,MAAM;MAAEH;IAAS,CAAC,GAAGvC,sBAAsB;IAC3C,OAAO,CAAC,CAACuC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvB,oBAAoB,KAAKA,oBAAoB,EAAE,GAAG0B,IAAI,CAAC;EACjI,CAAC;EACDH,QAAQ,EAAE3B;AACd,CAAC;;;;;;;;;;;;;;;;;ACzByC;AACM;AACzC,MAAM+B,aAAa,GAAG,IAAIZ,yDAAa,CAACJ,mDAAU,CAAC;AACnD,MAAMiB,IAAI,GAAGD,aAAa;;;;;;;;;;;;;;;ACHa;AAC9C,MAAM;EAAEf,YAAY;EAAEE;AAAe,CAAC,GAAGe,sDAAS;AAC3C,MAAMnB,iBAAiB,GAAG;EAC7BE,YAAYA,CAAC,GAAGc,IAAI,EAAE;IAClB,MAAM;MAAEH;IAAS,CAAC,GAAGb,iBAAiB;IACtC,OAAO,CAAC,CAACa,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACX,YAAY,KAAKA,YAAY,EAAE,GAAGc,IAAI,CAAC;EACjH,CAAC;EACDZ,cAAcA,CAACU,MAAM,EAAE;IACnB,MAAM;MAAED;IAAS,CAAC,GAAGb,iBAAiB;IACtC,OAAO,CAAC,CAACa,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACT,cAAc,KAAKA,cAAc,EAAEU,MAAM,CAAC;EACpH,CAAC;EACDD,QAAQ,EAAE3B;AACd,CAAC;;;;;;;;;;;;;;;ACZD,IAAIkC,UAAU,GAAG,CAAC;AAClB,IAAIC,QAAQ;AACZ,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,SAASC,kBAAkBA,CAACT,MAAM,EAAE;EAChC,IAAIA,MAAM,IAAIQ,aAAa,EAAE;IACzB,OAAOA,aAAa,CAACR,MAAM,CAAC;IAC5B,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACO,MAAMK,SAAS,GAAG;EACrBjB,YAAYA,CAACsB,EAAE,EAAE;IACb,MAAMV,MAAM,GAAGM,UAAU,EAAE;IAC3BE,aAAa,CAACR,MAAM,CAAC,GAAG,IAAI;IAC5B,IAAI,CAACO,QAAQ,EAAE;MACXA,QAAQ,GAAGI,OAAO,CAACC,OAAO,CAAC,CAAC;IAChC;IACAL,QAAQ,CAACM,IAAI,CAAC,MAAMJ,kBAAkB,CAACT,MAAM,CAAC,IAAIU,EAAE,CAAC,CAAC,CAAC;IACvD,OAAOV,MAAM;EACjB,CAAC;EACDV,cAAcA,CAACU,MAAM,EAAE;IACnBS,kBAAkB,CAACT,MAAM,CAAC;EAC9B;AACJ,CAAC;AACM,MAAMc,SAAS,GAAG;EACrBC,OAAOA,CAAA,EAAG;IACN,OAAOC,MAAM,CAACC,IAAI,CAACT,aAAa,CAAC,CAACjC,MAAM;EAC5C;AACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5B0C;AACP;AACsH;AAC9G;AACgG;AAC1E;AAC+H;AACpF;AAChC;AAC9B;AACU;AACA;;AAEzD;AACA,MAAM+F,YAAY,GAAG,GAAG;AACxB;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,EAAE,EAAEC,IAAI,EAAE5G,EAAE,EAAE;EACvC,MAAM6G,GAAG,GAAGC,mBAAmB,CAACH,EAAE,EAAEC,IAAI,CAAC;EACzC,IAAIC,GAAG,CAACE,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACC,IAAI,CAAC,CAAC,IAAIjH,EAAE,CAACiH,IAAI,CAAC,CAAC,CAAC,EAAE;IACxD;EACJ;EACAJ,GAAG,CAAC1G,IAAI,CAACH,EAAE,CAACiH,IAAI,CAAC,CAAC,CAAC;EACnBN,EAAE,CAACO,YAAY,CAACN,IAAI,EAAEC,GAAG,CAACM,IAAI,CAACV,YAAY,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASW,sBAAsBA,CAACT,EAAE,EAAEC,IAAI,EAAE5G,EAAE,EAAE;EAC1C,MAAM6G,GAAG,GAAGC,mBAAmB,CAACH,EAAE,EAAEC,IAAI,CAAC;EACzC,MAAMS,WAAW,GAAGR,GAAG,CAACxH,MAAM,CAACiI,GAAG,IAAIA,GAAG,IAAItH,EAAE,CAACiH,IAAI,CAAC,CAAC,CAAC;EACvD,IAAII,WAAW,CAAC3G,MAAM,EAAE;IACpBiG,EAAE,CAACO,YAAY,CAACN,IAAI,EAAES,WAAW,CAACF,IAAI,CAACV,YAAY,CAAC,CAAC;EACzD,CAAC,MACI;IACDE,EAAE,CAACY,eAAe,CAACX,IAAI,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAACH,EAAE,EAAEC,IAAI,EAAE;EACnC;EACA,OAAO,CAACD,EAAE,CAACa,YAAY,CAACZ,IAAI,CAAC,IAAI,EAAE,EAAEa,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,mCAAmC;AACjE;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,yBAAyB;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,sBAAsB;AAC7D;AACA,IAAIC,MAAM,GAAG,CAAC;AACd;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBjI,WAAWA,CAACkI,SAAS;EACrB;AACJ;AACA;AACA;EACIC,SAAS,EAAE;IACP,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAACC,GAAG,GAAI,GAAEP,MAAM,EAAG,EAAC;IACxB,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACK,GAAG,GAAG7E,qDAAM,CAACC,iDAAM,CAAC,GAAG,GAAG,GAAGqE,MAAM,EAAE;EAC9C;EACAQ,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAACC,eAAe,CAACH,WAAW,EAAEC,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMG,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;MAC7B;MACAK,YAAY,CAACL,OAAO,EAAE,IAAI,CAACH,GAAG,CAAC;MAC/B,IAAI,CAACH,gBAAgB,CAACY,GAAG,CAACH,GAAG,EAAE;QAAEI,cAAc,EAAEP,OAAO;QAAEQ,cAAc,EAAE;MAAE,CAAC,CAAC;IAClF,CAAC,MACI,IAAI,CAAC,IAAI,CAACd,gBAAgB,CAACe,GAAG,CAACN,GAAG,CAAC,EAAE;MACtC,IAAI,CAACO,qBAAqB,CAACV,OAAO,EAAEC,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC,IAAI,CAACU,4BAA4B,CAACZ,WAAW,EAAEI,GAAG,CAAC,EAAE;MACtD,IAAI,CAACS,oBAAoB,CAACb,WAAW,EAAEI,GAAG,CAAC;IAC/C;EACJ;EACAU,iBAAiBA,CAACd,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IAC1C,IAAI,CAACD,OAAO,IAAI,CAAC,IAAI,CAACc,cAAc,CAACf,WAAW,CAAC,EAAE;MAC/C;IACJ;IACA,MAAMI,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,IAAI,CAACU,4BAA4B,CAACZ,WAAW,EAAEI,GAAG,CAAC,EAAE;MACrD,IAAI,CAACY,uBAAuB,CAAChB,WAAW,EAAEI,GAAG,CAAC;IAClD;IACA;IACA;IACA,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;MAC7B,MAAMgB,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;MACxD,IAAIa,iBAAiB,IAAIA,iBAAiB,CAACR,cAAc,KAAK,CAAC,EAAE;QAC7D,IAAI,CAACU,qBAAqB,CAACf,GAAG,CAAC;MACnC;IACJ;IACA,IAAI,IAAI,CAACP,kBAAkB,EAAEuB,UAAU,CAAChJ,MAAM,KAAK,CAAC,EAAE;MAClD,IAAI,CAACyH,kBAAkB,CAACwB,MAAM,CAAC,CAAC;MAChC,IAAI,CAACxB,kBAAkB,GAAG,IAAI;IAClC;EACJ;EACA;EACAyB,WAAWA,CAAA,EAAG;IACV,MAAMC,iBAAiB,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,gBAAgB,CAAE,IAAGlC,8BAA+B,KAAI,IAAI,CAACQ,GAAI,IAAG,CAAC;IAC9G,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,iBAAiB,CAACnJ,MAAM,EAAEqJ,CAAC,EAAE,EAAE;MAC/C,IAAI,CAACC,iCAAiC,CAACH,iBAAiB,CAACE,CAAC,CAAC,CAAC;MAC5DF,iBAAiB,CAACE,CAAC,CAAC,CAACxC,eAAe,CAACK,8BAA8B,CAAC;IACxE;IACA,IAAI,CAACO,kBAAkB,EAAEwB,MAAM,CAAC,CAAC;IACjC,IAAI,CAACxB,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACF,gBAAgB,CAACgC,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACIhB,qBAAqBA,CAACV,OAAO,EAAEC,IAAI,EAAE;IACjC,MAAMM,cAAc,GAAG,IAAI,CAACf,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC1DtB,YAAY,CAACE,cAAc,EAAE,IAAI,CAACV,GAAG,CAAC;IACtCU,cAAc,CAACqB,WAAW,GAAG5B,OAAO;IACpC,IAAIC,IAAI,EAAE;MACNM,cAAc,CAAC5B,YAAY,CAAC,MAAM,EAAEsB,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC4B,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACjC,kBAAkB,CAACkC,WAAW,CAACvB,cAAc,CAAC;IACnD,IAAI,CAACb,gBAAgB,CAACY,GAAG,CAACF,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC,EAAE;MAAEM,cAAc;MAAEC,cAAc,EAAE;IAAE,CAAC,CAAC;EAC3F;EACA;EACAU,qBAAqBA,CAACf,GAAG,EAAE;IACvB,IAAI,CAACT,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC,EAAEI,cAAc,EAAEa,MAAM,CAAC,CAAC;IACxD,IAAI,CAAC1B,gBAAgB,CAACqC,MAAM,CAAC5B,GAAG,CAAC;EACrC;EACA;EACA0B,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACjC,kBAAkB,EAAE;MACzB;IACJ;IACA,MAAMoC,kBAAkB,GAAG,mCAAmC;IAC9D,MAAMC,gBAAgB,GAAG,IAAI,CAACzC,SAAS,CAAC+B,gBAAgB,CAAE,IAAGS,kBAAmB,qBAAoB,CAAC;IACrG,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,gBAAgB,CAAC9J,MAAM,EAAEqJ,CAAC,EAAE,EAAE;MAC9C;MACA;MACA;MACA;MACAS,gBAAgB,CAACT,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC;IAChC;IACA,MAAMc,iBAAiB,GAAG,IAAI,CAAC1C,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC7D;IACA;IACA;IACA;IACAO,iBAAiB,CAACC,KAAK,CAACC,UAAU,GAAG,QAAQ;IAC7C;IACA;IACAF,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAACN,kBAAkB,CAAC;IACnDE,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACtD;IACA,IAAI,IAAI,CAAC7C,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAC8C,SAAS,EAAE;MAC7CL,iBAAiB,CAACvD,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IACxD;IACA,IAAI,CAACa,SAAS,CAACgD,IAAI,CAACV,WAAW,CAACI,iBAAiB,CAAC;IAClD,IAAI,CAACtC,kBAAkB,GAAGsC,iBAAiB;EAC/C;EACA;EACAT,iCAAiCA,CAACgB,OAAO,EAAE;IACvC;IACA,MAAMC,oBAAoB,GAAGnE,mBAAmB,CAACkE,OAAO,EAAE,kBAAkB,CAAC,CAAC3L,MAAM,CAACW,EAAE,IAAIA,EAAE,CAACkL,OAAO,CAACvD,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACtIqD,OAAO,CAAC9D,YAAY,CAAC,kBAAkB,EAAE+D,oBAAoB,CAAC9D,IAAI,CAAC,GAAG,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;EACIgC,oBAAoBA,CAAC6B,OAAO,EAAEtC,GAAG,EAAE;IAC/B,MAAMa,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxD;IACA;IACAhC,mBAAmB,CAACsE,OAAO,EAAE,kBAAkB,EAAEzB,iBAAiB,CAACT,cAAc,CAAC9I,EAAE,CAAC;IACrFgL,OAAO,CAAC9D,YAAY,CAACU,8BAA8B,EAAE,IAAI,CAACQ,GAAG,CAAC;IAC9DmB,iBAAiB,CAACR,cAAc,EAAE;EACtC;EACA;AACJ;AACA;AACA;EACIO,uBAAuBA,CAAC0B,OAAO,EAAEtC,GAAG,EAAE;IAClC,MAAMa,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxDa,iBAAiB,CAACR,cAAc,EAAE;IAClC3B,sBAAsB,CAAC4D,OAAO,EAAE,kBAAkB,EAAEzB,iBAAiB,CAACT,cAAc,CAAC9I,EAAE,CAAC;IACxFgL,OAAO,CAACzD,eAAe,CAACK,8BAA8B,CAAC;EAC3D;EACA;EACAsB,4BAA4BA,CAAC8B,OAAO,EAAEtC,GAAG,EAAE;IACvC,MAAMyC,YAAY,GAAGrE,mBAAmB,CAACkE,OAAO,EAAE,kBAAkB,CAAC;IACrE,MAAMzB,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxD,MAAM0C,SAAS,GAAG7B,iBAAiB,IAAIA,iBAAiB,CAACT,cAAc,CAAC9I,EAAE;IAC1E,OAAO,CAAC,CAACoL,SAAS,IAAID,YAAY,CAACD,OAAO,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;EAC/D;EACA;EACA3C,eAAeA,CAACuC,OAAO,EAAEzC,OAAO,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACc,cAAc,CAAC2B,OAAO,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAIzC,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MACxC;MACA;MACA;MACA,OAAO,IAAI;IACf;IACA,MAAM8C,cAAc,GAAG9C,OAAO,IAAI,IAAI,GAAG,EAAE,GAAI,GAAEA,OAAQ,EAAC,CAACtB,IAAI,CAAC,CAAC;IACjE,MAAMqE,SAAS,GAAGN,OAAO,CAACxD,YAAY,CAAC,YAAY,CAAC;IACpD;IACA;IACA,OAAO6D,cAAc,GAAG,CAACC,SAAS,IAAIA,SAAS,CAACrE,IAAI,CAAC,CAAC,KAAKoE,cAAc,GAAG,KAAK;EACrF;EACA;EACAhC,cAAcA,CAAC2B,OAAO,EAAE;IACpB,OAAOA,OAAO,CAACO,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY;EAC3D;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwF7D,aAAa,EAAvBxE,sDAAE,CAAuCD,qDAAQ,GAAjDC,sDAAE,CAA4Da,2DAAW;IAAA,CAA6C;EAAE;EACxN;IAAS,IAAI,CAAC2H,KAAK,kBAD6ExI,gEAAE;MAAA0I,KAAA,EACYlE,aAAa;MAAAmE,OAAA,EAAbnE,aAAa,CAAA2D,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG7I,+DAAE,CAGXwE,aAAa,EAAc,CAAC;IAC3GuE,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAC9DD,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,qDAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAElI,2DAAW0H;IAAC,CAAC,CAAC;EAAE,CAAC;AAAA;AAC/C;AACA,SAASlD,MAAMA,CAACJ,OAAO,EAAEC,IAAI,EAAE;EAC3B,OAAO,OAAOD,OAAO,KAAK,QAAQ,GAAI,GAAEC,IAAI,IAAI,EAAG,IAAGD,OAAQ,EAAC,GAAGA,OAAO;AAC7E;AACA;AACA,SAASK,YAAYA,CAACoC,OAAO,EAAEuB,SAAS,EAAE;EACtC,IAAI,CAACvB,OAAO,CAAChL,EAAE,EAAE;IACbgL,OAAO,CAAChL,EAAE,GAAI,GAAE2H,yBAA0B,IAAG4E,SAAU,IAAG1E,MAAM,EAAG,EAAC;EACxE;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM2E,cAAc,CAAC;EACjB3M,WAAWA,CAAC4M,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,gBAAgB,GAAG,IAAIrI,yCAAO,CAAC,CAAC;IACrC,IAAI,CAACsI,sBAAsB,GAAGjL,8CAAY,CAACkL,KAAK;IAChD,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,cAAc,GAAG;MAAEC,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAG,CAAC;IACnD;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAIC,IAAI,IAAKA,IAAI,CAACC,QAAQ;IAC/C;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAIlJ,yCAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACmJ,MAAM,GAAG,IAAInJ,yCAAO,CAAC,CAAC;IAC3B;IACA;IACA;IACA,IAAIiI,MAAM,YAAY9I,oDAAS,EAAE;MAC7B,IAAI,CAACiK,wBAAwB,GAAGnB,MAAM,CAACoB,OAAO,CAACnP,SAAS,CAAEoP,QAAQ,IAAK;QACnE,IAAI,IAAI,CAACnB,WAAW,EAAE;UAClB,MAAMoB,SAAS,GAAGD,QAAQ,CAACE,OAAO,CAAC,CAAC;UACpC,MAAMC,QAAQ,GAAGF,SAAS,CAAC7C,OAAO,CAAC,IAAI,CAACyB,WAAW,CAAC;UACpD,IAAIsB,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAACvB,gBAAgB,EAAE;YACrD,IAAI,CAACA,gBAAgB,GAAGuB,QAAQ;UACpC;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAACb,gBAAgB,GAAGa,SAAS;IACjC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,QAAQA,CAACC,UAAU,GAAG,IAAI,EAAE;IACxB,IAAI,CAACzB,KAAK,GAAGyB,UAAU;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,uBAAuBA,CAAClB,OAAO,GAAG,IAAI,EAAE;IACpC,IAAI,CAACJ,SAAS,GAAGI,OAAO;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACImB,yBAAyBA,CAACC,SAAS,EAAE;IACjC,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,uBAAuBA,CAACtL,IAAI,EAAE;IAC1B,IAAI,CAAC6J,oBAAoB,GAAG7J,IAAI;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIuL,aAAaA,CAACC,gBAAgB,GAAG,GAAG,EAAE;IAClC,IAAI,CAAC,OAAOzC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9C,IAAI,CAACM,MAAM,CAAC/L,MAAM,IAClB,IAAI,CAAC+L,MAAM,CAAC1F,IAAI,CAACwG,IAAI,IAAI,OAAOA,IAAI,CAACsB,QAAQ,KAAK,UAAU,CAAC,EAAE;MAC/D,MAAMC,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,IAAI,CAAChC,sBAAsB,CAACzO,WAAW,CAAC,CAAC;IACzC;IACA;IACA;IACA,IAAI,CAACyO,sBAAsB,GAAG,IAAI,CAACD,gBAAgB,CAC9CkC,IAAI,CAACjJ,mDAAG,CAACkJ,MAAM,IAAI,IAAI,CAACvB,eAAe,CAACtN,IAAI,CAAC6O,MAAM,CAAC,CAAC,EAAEjJ,4DAAY,CAAC6I,gBAAgB,CAAC,EAAEvP,sDAAM,CAAC,MAAM,IAAI,CAACoO,eAAe,CAAC/M,MAAM,GAAG,CAAC,CAAC,EAAEsF,mDAAG,CAAC,MAAM,IAAI,CAACyH,eAAe,CAACtG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/KzI,SAAS,CAACuQ,WAAW,IAAI;MAC1B,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC;MACA;MACA,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,KAAK,CAACxO,MAAM,GAAG,CAAC,EAAEqJ,CAAC,EAAE,EAAE;QACvC,MAAMtK,KAAK,GAAG,CAAC,IAAI,CAACiN,gBAAgB,GAAG3C,CAAC,IAAImF,KAAK,CAACxO,MAAM;QACxD,MAAM6M,IAAI,GAAG2B,KAAK,CAACzP,KAAK,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC6N,gBAAgB,CAACC,IAAI,CAAC,IAC5BA,IAAI,CAACsB,QAAQ,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAACnI,IAAI,CAAC,CAAC,CAACiE,OAAO,CAAC+D,WAAW,CAAC,KAAK,CAAC,EAAE;UACjE,IAAI,CAACI,aAAa,CAAC5P,KAAK,CAAC;UACzB;QACJ;MACJ;MACA,IAAI,CAACgO,eAAe,GAAG,EAAE;IAC7B,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA;EACA6B,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC7B,eAAe,GAAG,EAAE;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI8B,cAAcA,CAACnC,OAAO,GAAG,IAAI,EAAE;IAC3B,IAAI,CAACF,WAAW,GAAGE,OAAO;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoC,cAAcA,CAACpC,OAAO,GAAG,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAE;IACvC,IAAI,CAACF,cAAc,GAAG;MAAEC,OAAO;MAAEC;IAAM,CAAC;IACxC,OAAO,IAAI;EACf;EACAgC,aAAaA,CAAC9B,IAAI,EAAE;IAChB,MAAMkC,kBAAkB,GAAG,IAAI,CAAC9C,WAAW;IAC3C,IAAI,CAAC+C,gBAAgB,CAACnC,IAAI,CAAC;IAC3B,IAAI,IAAI,CAACZ,WAAW,KAAK8C,kBAAkB,EAAE;MACzC,IAAI,CAAC9B,MAAM,CAACpP,IAAI,CAAC,IAAI,CAACmO,gBAAgB,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;EACIiD,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC7B,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;IAC9D,MAAMC,iBAAiB,GAAGD,SAAS,CAACE,KAAK,CAACC,QAAQ,IAAI;MAClD,OAAO,CAACL,KAAK,CAACK,QAAQ,CAAC,IAAI,IAAI,CAAChD,oBAAoB,CAAC/B,OAAO,CAAC+E,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/E,CAAC,CAAC;IACF,QAAQJ,OAAO;MACX,KAAKrK,sDAAG;QACJ,IAAI,CAACkI,MAAM,CAACnP,IAAI,CAAC,CAAC;QAClB;MACJ,KAAKgH,6DAAU;QACX,IAAI,IAAI,CAACyH,SAAS,IAAI+C,iBAAiB,EAAE;UACrC,IAAI,CAACG,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK5K,2DAAQ;QACT,IAAI,IAAI,CAAC0H,SAAS,IAAI+C,iBAAiB,EAAE;UACrC,IAAI,CAACI,qBAAqB,CAAC,CAAC;UAC5B;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK9K,8DAAW;QACZ,IAAI,IAAI,CAACoJ,WAAW,IAAIsB,iBAAiB,EAAE;UACvC,IAAI,CAACtB,WAAW,KAAK,KAAK,GAAG,IAAI,CAAC0B,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK9K,6DAAU;QACX,IAAI,IAAI,CAACqJ,WAAW,IAAIsB,iBAAiB,EAAE;UACvC,IAAI,CAACtB,WAAW,KAAK,KAAK,GAAG,IAAI,CAACyB,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKhL,uDAAI;QACL,IAAI,IAAI,CAAC+H,WAAW,IAAI6C,iBAAiB,EAAE;UACvC,IAAI,CAACK,kBAAkB,CAAC,CAAC;UACzB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKlL,sDAAG;QACJ,IAAI,IAAI,CAACgI,WAAW,IAAI6C,iBAAiB,EAAE;UACvC,IAAI,CAACM,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKpL,0DAAO;QACR,IAAI,IAAI,CAACkI,cAAc,CAACC,OAAO,IAAI2C,iBAAiB,EAAE;UAClD,MAAMO,WAAW,GAAG,IAAI,CAAC5D,gBAAgB,GAAG,IAAI,CAACS,cAAc,CAACE,KAAK;UACrE,IAAI,CAACkD,qBAAqB,CAACD,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;UAChE;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKtL,4DAAS;QACV,IAAI,IAAI,CAACmI,cAAc,CAACC,OAAO,IAAI2C,iBAAiB,EAAE;UAClD,MAAMO,WAAW,GAAG,IAAI,CAAC5D,gBAAgB,GAAG,IAAI,CAACS,cAAc,CAACE,KAAK;UACrE,MAAMmD,WAAW,GAAG,IAAI,CAACrB,cAAc,CAAC,CAAC,CAACzO,MAAM;UAChD,IAAI,CAAC6P,qBAAqB,CAACD,WAAW,GAAGE,WAAW,GAAGF,WAAW,GAAGE,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;UACzF;QACJ,CAAC,MACI;UACD;QACJ;MACJ;QACI,IAAIT,iBAAiB,IAAIpL,qEAAc,CAACiL,KAAK,EAAE,UAAU,CAAC,EAAE;UACxD;UACA;UACA,IAAIA,KAAK,CAAClH,GAAG,IAAIkH,KAAK,CAAClH,GAAG,CAAChI,MAAM,KAAK,CAAC,EAAE;YACrC,IAAI,CAACmM,gBAAgB,CAACtO,IAAI,CAACqR,KAAK,CAAClH,GAAG,CAAC+H,iBAAiB,CAAC,CAAC,CAAC;UAC7D,CAAC,MACI,IAAKZ,OAAO,IAAIjL,oDAAC,IAAIiL,OAAO,IAAIhL,oDAAC,IAAMgL,OAAO,IAAI/K,uDAAI,IAAI+K,OAAO,IAAI9K,uDAAK,EAAE;YAC7E,IAAI,CAAC8H,gBAAgB,CAACtO,IAAI,CAACmS,MAAM,CAACC,YAAY,CAACd,OAAO,CAAC,CAAC;UAC5D;QACJ;QACA;QACA;QACA;IACR;IACA,IAAI,CAACpC,eAAe,GAAG,EAAE;IACzBmC,KAAK,CAACgB,cAAc,CAAC,CAAC;EAC1B;EACA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACnE,gBAAgB;EAChC;EACA;EACA,IAAIoE,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnE,WAAW;EAC3B;EACA;EACAoE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACtD,eAAe,CAAC/M,MAAM,GAAG,CAAC;EAC1C;EACA;EACA0P,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACG,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC;EACA;EACAF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAAC9D,MAAM,CAAC/L,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1D;EACA;EACAwP,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACxD,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC0D,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACY,qBAAqB,CAAC,CAAC,CAAC;EACzF;EACA;EACAb,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACzD,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAACE,KAAK,GACjC,IAAI,CAACyD,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACW,qBAAqB,CAAC,CAAC,CAAC,CAAC;EACxC;EACAtB,gBAAgBA,CAACnC,IAAI,EAAE;IACnB,MAAMQ,SAAS,GAAG,IAAI,CAACoB,cAAc,CAAC,CAAC;IACvC,MAAM1P,KAAK,GAAG,OAAO8N,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGQ,SAAS,CAAC7C,OAAO,CAACqC,IAAI,CAAC;IACvE,MAAMuD,UAAU,GAAG/C,SAAS,CAACtO,KAAK,CAAC;IACnC;IACA,IAAI,CAACkN,WAAW,GAAGmE,UAAU,IAAI,IAAI,GAAG,IAAI,GAAGA,UAAU;IACzD,IAAI,CAACpE,gBAAgB,GAAGjN,KAAK;EACjC;EACA;EACAwR,OAAOA,CAAA,EAAG;IACN,IAAI,CAACnE,sBAAsB,CAACzO,WAAW,CAAC,CAAC;IACzC,IAAI,CAACuP,wBAAwB,EAAEvP,WAAW,CAAC,CAAC;IAC5C,IAAI,CAACwO,gBAAgB,CAACrO,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACkP,MAAM,CAAClP,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACmP,MAAM,CAACnP,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACiP,eAAe,GAAG,EAAE;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIuD,qBAAqBA,CAAC3D,KAAK,EAAE;IACzB,IAAI,CAACT,KAAK,GAAG,IAAI,CAACsE,oBAAoB,CAAC7D,KAAK,CAAC,GAAG,IAAI,CAAC8D,uBAAuB,CAAC9D,KAAK,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;EACI6D,oBAAoBA,CAAC7D,KAAK,EAAE;IACxB,MAAM6B,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImF,KAAK,CAACxO,MAAM,EAAEqJ,CAAC,EAAE,EAAE;MACpC,MAAMtK,KAAK,GAAG,CAAC,IAAI,CAACiN,gBAAgB,GAAGW,KAAK,GAAGtD,CAAC,GAAGmF,KAAK,CAACxO,MAAM,IAAIwO,KAAK,CAACxO,MAAM;MAC/E,MAAM6M,IAAI,GAAG2B,KAAK,CAACzP,KAAK,CAAC;MACzB,IAAI,CAAC,IAAI,CAAC6N,gBAAgB,CAACC,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC8B,aAAa,CAAC5P,KAAK,CAAC;QACzB;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0R,uBAAuBA,CAAC9D,KAAK,EAAE;IAC3B,IAAI,CAACkD,qBAAqB,CAAC,IAAI,CAAC7D,gBAAgB,GAAGW,KAAK,EAAEA,KAAK,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;EACIkD,qBAAqBA,CAAC9Q,KAAK,EAAE2R,aAAa,EAAE;IACxC,MAAMlC,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,CAACD,KAAK,CAACzP,KAAK,CAAC,EAAE;MACf;IACJ;IACA,OAAO,IAAI,CAAC6N,gBAAgB,CAAC4B,KAAK,CAACzP,KAAK,CAAC,CAAC,EAAE;MACxCA,KAAK,IAAI2R,aAAa;MACtB,IAAI,CAAClC,KAAK,CAACzP,KAAK,CAAC,EAAE;QACf;MACJ;IACJ;IACA,IAAI,CAAC4P,aAAa,CAAC5P,KAAK,CAAC;EAC7B;EACA;EACA0P,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1C,MAAM,YAAY9I,oDAAS,GAAG,IAAI,CAAC8I,MAAM,CAACuB,OAAO,CAAC,CAAC,GAAG,IAAI,CAACvB,MAAM;EACjF;AACJ;AAEA,MAAM4E,0BAA0B,SAAS7E,cAAc,CAAC;EACpD6C,aAAaA,CAAC5P,KAAK,EAAE;IACjB,IAAI,IAAI,CAACqR,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACQ,iBAAiB,CAAC,CAAC;IACvC;IACA,KAAK,CAACjC,aAAa,CAAC5P,KAAK,CAAC;IAC1B,IAAI,IAAI,CAACqR,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACS,eAAe,CAAC,CAAC;IACrC;EACJ;AACJ;AAEA,MAAMC,eAAe,SAAShF,cAAc,CAAC;EACzC3M,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG4R,SAAS,CAAC;IACnB,IAAI,CAACC,OAAO,GAAG,SAAS;EAC5B;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAACC,MAAM,EAAE;IACnB,IAAI,CAACF,OAAO,GAAGE,MAAM;IACrB,OAAO,IAAI;EACf;EACAvC,aAAaA,CAAC9B,IAAI,EAAE;IAChB,KAAK,CAAC8B,aAAa,CAAC9B,IAAI,CAAC;IACzB,IAAI,IAAI,CAACuD,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACe,KAAK,CAAC,IAAI,CAACH,OAAO,CAAC;IACvC;EACJ;AACJ;;AAEA;AACA;AACA;AACA,MAAMI,iBAAiB,CAAC;EACpBjS,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACkS,gBAAgB,GAAG,KAAK;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBnS,WAAWA,CAACmI,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiK,UAAUA,CAACjH,OAAO,EAAE;IAChB;IACA;IACA,OAAOA,OAAO,CAACkH,YAAY,CAAC,UAAU,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACnH,OAAO,EAAE;IACf,OAAOoH,WAAW,CAACpH,OAAO,CAAC,IAAIqH,gBAAgB,CAACrH,OAAO,CAAC,CAACL,UAAU,KAAK,SAAS;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2H,UAAUA,CAACtH,OAAO,EAAE;IAChB;IACA,IAAI,CAAC,IAAI,CAAChD,SAAS,CAAC8C,SAAS,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMyH,YAAY,GAAGC,eAAe,CAACC,SAAS,CAACzH,OAAO,CAAC,CAAC;IACxD,IAAIuH,YAAY,EAAE;MACd;MACA,IAAIG,gBAAgB,CAACH,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MAChB;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACI,YAAY,CAAC,EAAE;QAC/B,OAAO,KAAK;MAChB;IACJ;IACA,IAAII,QAAQ,GAAG3H,OAAO,CAAC2H,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC7C,IAAIC,aAAa,GAAGH,gBAAgB,CAAC1H,OAAO,CAAC;IAC7C,IAAIA,OAAO,CAACkH,YAAY,CAAC,iBAAiB,CAAC,EAAE;MACzC,OAAOW,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MAChD;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA;IACA,IAAI,IAAI,CAAC3K,SAAS,CAAC8K,MAAM,IAAI,IAAI,CAAC9K,SAAS,CAAC+K,GAAG,IAAI,CAACC,wBAAwB,CAAChI,OAAO,CAAC,EAAE;MACnF,OAAO,KAAK;IAChB;IACA,IAAI2H,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA,IAAI,CAAC3H,OAAO,CAACkH,YAAY,CAAC,UAAU,CAAC,EAAE;QACnC,OAAO,KAAK;MAChB;MACA;MACA;MACA,OAAOW,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA;MACA;MACA,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB;MACA;MACA;MACA,IAAIA,aAAa,KAAK,IAAI,EAAE;QACxB,OAAO,IAAI;MACf;MACA;MACA;MACA;MACA,OAAO,IAAI,CAAC7K,SAAS,CAACiL,OAAO,IAAIjI,OAAO,CAACkH,YAAY,CAAC,UAAU,CAAC;IACrE;IACA,OAAOlH,OAAO,CAACkI,QAAQ,IAAI,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACnI,OAAO,EAAEoI,MAAM,EAAE;IACzB;IACA;IACA,OAAQC,sBAAsB,CAACrI,OAAO,CAAC,IACnC,CAAC,IAAI,CAACiH,UAAU,CAACjH,OAAO,CAAC,KACxBoI,MAAM,EAAErB,gBAAgB,IAAI,IAAI,CAACI,SAAS,CAACnH,OAAO,CAAC,CAAC;EAC7D;EACA;IAAS,IAAI,CAACS,IAAI,YAAA6H,6BAAA3H,CAAA;MAAA,YAAAA,CAAA,IAAwFqG,oBAAoB,EAthB9B1O,sDAAE,CAshB8Ca,2DAAW;IAAA,CAA6C;EAAE;EAC1M;IAAS,IAAI,CAAC2H,KAAK,kBAvhB6ExI,gEAAE;MAAA0I,KAAA,EAuhBYgG,oBAAoB;MAAA/F,OAAA,EAApB+F,oBAAoB,CAAAvG,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAC/J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzhBoG7I,+DAAE,CAyhBX0O,oBAAoB,EAAc,CAAC;IAClH3F,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElI,2DAAW0H;IAAC,CAAC,CAAC;EAAE,CAAC;AAAA;AAC3E;AACA;AACA;AACA;AACA;AACA,SAAS2G,eAAeA,CAACe,MAAM,EAAE;EAC7B,IAAI;IACA,OAAOA,MAAM,CAAChB,YAAY;EAC9B,CAAC,CACD,MAAM;IACF,OAAO,IAAI;EACf;AACJ;AACA;AACA,SAASH,WAAWA,CAACpH,OAAO,EAAE;EAC1B;EACA;EACA,OAAO,CAAC,EAAEA,OAAO,CAACwI,WAAW,IACzBxI,OAAO,CAACyI,YAAY,IACnB,OAAOzI,OAAO,CAAC0I,cAAc,KAAK,UAAU,IAAI1I,OAAO,CAAC0I,cAAc,CAAC,CAAC,CAAChT,MAAO,CAAC;AAC1F;AACA;AACA,SAASiT,mBAAmBA,CAAC3I,OAAO,EAAE;EAClC,IAAI2H,QAAQ,GAAG3H,OAAO,CAAC2H,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,OAAQD,QAAQ,KAAK,OAAO,IACxBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA,SAASiB,aAAaA,CAAC5I,OAAO,EAAE;EAC5B,OAAO6I,cAAc,CAAC7I,OAAO,CAAC,IAAIA,OAAO,CAACqB,IAAI,IAAI,QAAQ;AAC9D;AACA;AACA,SAASyH,gBAAgBA,CAAC9I,OAAO,EAAE;EAC/B,OAAO+I,eAAe,CAAC/I,OAAO,CAAC,IAAIA,OAAO,CAACkH,YAAY,CAAC,MAAM,CAAC;AACnE;AACA;AACA,SAAS2B,cAAcA,CAAC7I,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAAC2H,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,OAAO;AACpD;AACA;AACA,SAASmB,eAAeA,CAAC/I,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAAC2H,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,GAAG;AAChD;AACA;AACA,SAASoB,gBAAgBA,CAAChJ,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,CAACkH,YAAY,CAAC,UAAU,CAAC,IAAIlH,OAAO,CAACkI,QAAQ,KAAK3S,SAAS,EAAE;IACrE,OAAO,KAAK;EAChB;EACA,IAAI2S,QAAQ,GAAGlI,OAAO,CAACxD,YAAY,CAAC,UAAU,CAAC;EAC/C,OAAO,CAAC,EAAE0L,QAAQ,IAAI,CAACe,KAAK,CAACC,QAAQ,CAAChB,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,SAASR,gBAAgBA,CAAC1H,OAAO,EAAE;EAC/B,IAAI,CAACgJ,gBAAgB,CAAChJ,OAAO,CAAC,EAAE;IAC5B,OAAO,IAAI;EACf;EACA;EACA,MAAMkI,QAAQ,GAAGgB,QAAQ,CAAClJ,OAAO,CAACxD,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;EACrE,OAAOyM,KAAK,CAACf,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAGA,QAAQ;AAC1C;AACA;AACA,SAASF,wBAAwBA,CAAChI,OAAO,EAAE;EACvC,IAAI2H,QAAQ,GAAG3H,OAAO,CAAC2H,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIuB,SAAS,GAAGxB,QAAQ,KAAK,OAAO,IAAI3H,OAAO,CAACqB,IAAI;EACpD,OAAQ8H,SAAS,KAAK,MAAM,IACxBA,SAAS,KAAK,UAAU,IACxBxB,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA;AACA;AACA;AACA,SAASU,sBAAsBA,CAACrI,OAAO,EAAE;EACrC;EACA,IAAI4I,aAAa,CAAC5I,OAAO,CAAC,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,OAAQ2I,mBAAmB,CAAC3I,OAAO,CAAC,IAChC8I,gBAAgB,CAAC9I,OAAO,CAAC,IACzBA,OAAO,CAACkH,YAAY,CAAC,iBAAiB,CAAC,IACvC8B,gBAAgB,CAAChJ,OAAO,CAAC;AACjC;AACA;AACA,SAASyH,SAASA,CAAC2B,IAAI,EAAE;EACrB;EACA,OAAQA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACC,WAAW,IAAKf,MAAM;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,SAAS,CAAC;EACZ;EACA,IAAInH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoH,QAAQ;EACxB;EACA,IAAIpH,OAAOA,CAAC9O,KAAK,EAAE;IACf,IAAI,CAACkW,QAAQ,GAAGlW,KAAK;IACrB,IAAI,IAAI,CAACmW,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAACrW,KAAK,EAAE,IAAI,CAACmW,YAAY,CAAC;MACpD,IAAI,CAACE,qBAAqB,CAACrW,KAAK,EAAE,IAAI,CAACoW,UAAU,CAAC;IACtD;EACJ;EACA7U,WAAWA,CAAC+U,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE/M,SAAS,EAAEgN,YAAY,GAAG,KAAK,EAAE;IACtE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC/M,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACiN,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,mBAAmB,GAAG,MAAM,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAG,MAAM,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC/D,IAAI,CAACZ,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACO,YAAY,EAAE;MACf,IAAI,CAACM,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACApE,OAAOA,CAAA,EAAG;IACN,MAAMqE,WAAW,GAAG,IAAI,CAACb,YAAY;IACrC,MAAMc,SAAS,GAAG,IAAI,CAACb,UAAU;IACjC,IAAIY,WAAW,EAAE;MACbA,WAAW,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACP,mBAAmB,CAAC;MAClEK,WAAW,CAAC3L,MAAM,CAAC,CAAC;IACxB;IACA,IAAI4L,SAAS,EAAE;MACXA,SAAS,CAACC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACL,iBAAiB,CAAC;MAC9DI,SAAS,CAAC5L,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAAC8K,YAAY,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI;IAC1C,IAAI,CAACM,YAAY,GAAG,KAAK;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACL,YAAY,EAAE;MACnB,OAAO,IAAI;IACf;IACA,IAAI,CAACF,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC,IAAI,CAAChB,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACiB,aAAa,CAAC,CAAC;QACxC,IAAI,CAACjB,YAAY,CAACkB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACV,mBAAmB,CAAC;MACzE;MACA,IAAI,CAAC,IAAI,CAACP,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI,CAACgB,aAAa,CAAC,CAAC;QACtC,IAAI,CAAChB,UAAU,CAACiB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACR,iBAAiB,CAAC;MACrE;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACP,QAAQ,CAACgB,UAAU,EAAE;MAC1B,IAAI,CAAChB,QAAQ,CAACgB,UAAU,CAACC,YAAY,CAAC,IAAI,CAACpB,YAAY,EAAE,IAAI,CAACG,QAAQ,CAAC;MACvE,IAAI,CAACA,QAAQ,CAACgB,UAAU,CAACC,YAAY,CAAC,IAAI,CAACnB,UAAU,EAAE,IAAI,CAACE,QAAQ,CAACkB,WAAW,CAAC;MACjF,IAAI,CAACd,YAAY,GAAG,IAAI;IAC5B;IACA,OAAO,IAAI,CAACA,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIe,4BAA4BA,CAACC,OAAO,EAAE;IAClC,OAAO,IAAIlT,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACkT,gBAAgB,CAAC,MAAMlT,OAAO,CAAC,IAAI,CAACmT,mBAAmB,CAACF,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,kCAAkCA,CAACH,OAAO,EAAE;IACxC,OAAO,IAAIlT,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACkT,gBAAgB,CAAC,MAAMlT,OAAO,CAAC,IAAI,CAACqS,yBAAyB,CAACY,OAAO,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,iCAAiCA,CAACJ,OAAO,EAAE;IACvC,OAAO,IAAIlT,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACkT,gBAAgB,CAAC,MAAMlT,OAAO,CAAC,IAAI,CAACmS,wBAAwB,CAACc,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIK,kBAAkBA,CAACC,KAAK,EAAE;IACtB;IACA,MAAMC,OAAO,GAAG,IAAI,CAAC3B,QAAQ,CAAC9K,gBAAgB,CAAE,qBAAoBwM,KAAM,KAAI,GAAI,kBAAiBA,KAAM,KAAI,GAAI,cAAaA,KAAM,GAAE,CAAC;IACvI,IAAI,OAAOnK,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwM,OAAO,CAAC7V,MAAM,EAAEqJ,CAAC,EAAE,EAAE;QACrC;QACA,IAAIwM,OAAO,CAACxM,CAAC,CAAC,CAACmI,YAAY,CAAE,aAAYoE,KAAM,EAAC,CAAC,EAAE;UAC/CE,OAAO,CAACC,IAAI,CAAE,gDAA+CH,KAAM,KAAI,GAClE,sBAAqBA,KAAM,4BAA2B,GACtD,qCAAoC,EAAEC,OAAO,CAACxM,CAAC,CAAC,CAAC;QAC1D,CAAC,MACI,IAAIwM,OAAO,CAACxM,CAAC,CAAC,CAACmI,YAAY,CAAE,oBAAmBoE,KAAM,EAAC,CAAC,EAAE;UAC3DE,OAAO,CAACC,IAAI,CAAE,uDAAsDH,KAAM,KAAI,GACzE,sBAAqBA,KAAM,sCAAqC,GAChE,2BAA0B,EAAEC,OAAO,CAACxM,CAAC,CAAC,CAAC;QAChD;MACJ;IACJ;IACA,IAAIuM,KAAK,IAAI,OAAO,EAAE;MAClB,OAAOC,OAAO,CAAC7V,MAAM,GAAG6V,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACG,wBAAwB,CAAC,IAAI,CAAC9B,QAAQ,CAAC;IACrF;IACA,OAAO2B,OAAO,CAAC7V,MAAM,GACf6V,OAAO,CAACA,OAAO,CAAC7V,MAAM,GAAG,CAAC,CAAC,GAC3B,IAAI,CAACiW,uBAAuB,CAAC,IAAI,CAAC/B,QAAQ,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACIsB,mBAAmBA,CAACF,OAAO,EAAE;IACzB;IACA,MAAMY,iBAAiB,GAAG,IAAI,CAAChC,QAAQ,CAACiC,aAAa,CAAE,uBAAsB,GAAI,mBAAkB,CAAC;IACpG,IAAID,iBAAiB,EAAE;MACnB;MACA,IAAI,CAAC,OAAOzK,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9CyK,iBAAiB,CAAC1E,YAAY,CAAE,mBAAkB,CAAC,EAAE;QACrDsE,OAAO,CAACC,IAAI,CAAE,yDAAwD,GACjE,0DAAyD,GACzD,0BAAyB,EAAEG,iBAAiB,CAAC;MACtD;MACA;MACA;MACA,IAAI,CAAC,OAAOzK,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9C,CAAC,IAAI,CAAC0I,QAAQ,CAAC1B,WAAW,CAACyD,iBAAiB,CAAC,EAAE;QAC/CJ,OAAO,CAACC,IAAI,CAAE,wDAAuD,EAAEG,iBAAiB,CAAC;MAC7F;MACA,IAAI,CAAC,IAAI,CAAC/B,QAAQ,CAAC1B,WAAW,CAACyD,iBAAiB,CAAC,EAAE;QAC/C,MAAME,cAAc,GAAG,IAAI,CAACJ,wBAAwB,CAACE,iBAAiB,CAAC;QACvEE,cAAc,EAAEjF,KAAK,CAACmE,OAAO,CAAC;QAC9B,OAAO,CAAC,CAACc,cAAc;MAC3B;MACAF,iBAAiB,CAAC/E,KAAK,CAACmE,OAAO,CAAC;MAChC,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACZ,yBAAyB,CAACY,OAAO,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACIZ,yBAAyBA,CAACY,OAAO,EAAE;IAC/B,MAAMY,iBAAiB,GAAG,IAAI,CAACP,kBAAkB,CAAC,OAAO,CAAC;IAC1D,IAAIO,iBAAiB,EAAE;MACnBA,iBAAiB,CAAC/E,KAAK,CAACmE,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAACY,iBAAiB;EAC9B;EACA;AACJ;AACA;AACA;EACI1B,wBAAwBA,CAACc,OAAO,EAAE;IAC9B,MAAMY,iBAAiB,GAAG,IAAI,CAACP,kBAAkB,CAAC,KAAK,CAAC;IACxD,IAAIO,iBAAiB,EAAE;MACnBA,iBAAiB,CAAC/E,KAAK,CAACmE,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAACY,iBAAiB;EAC9B;EACA;AACJ;AACA;EACIG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/B,YAAY;EAC5B;EACA;EACA0B,wBAAwBA,CAACM,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACnC,QAAQ,CAAC1B,WAAW,CAAC6D,IAAI,CAAC,IAAI,IAAI,CAACnC,QAAQ,CAACvC,UAAU,CAAC0E,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC9B,KAAK,IAAIlN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkN,QAAQ,CAACvW,MAAM,EAAEqJ,CAAC,EAAE,EAAE;MACtC,MAAMmN,aAAa,GAAGD,QAAQ,CAAClN,CAAC,CAAC,CAACwB,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY,GACpE,IAAI,CAACkL,wBAAwB,CAACO,QAAQ,CAAClN,CAAC,CAAC,CAAC,GAC1C,IAAI;MACV,IAAImN,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAP,uBAAuBA,CAACK,IAAI,EAAE;IAC1B,IAAI,IAAI,CAACnC,QAAQ,CAAC1B,WAAW,CAAC6D,IAAI,CAAC,IAAI,IAAI,CAACnC,QAAQ,CAACvC,UAAU,CAAC0E,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC9B,KAAK,IAAIlN,CAAC,GAAGkN,QAAQ,CAACvW,MAAM,GAAG,CAAC,EAAEqJ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAMmN,aAAa,GAAGD,QAAQ,CAAClN,CAAC,CAAC,CAACwB,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY,GACpE,IAAI,CAACmL,uBAAuB,CAACM,QAAQ,CAAClN,CAAC,CAAC,CAAC,GACzC,IAAI;MACV,IAAImN,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAxB,aAAaA,CAAA,EAAG;IACZ,MAAMyB,MAAM,GAAG,IAAI,CAACpP,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAClD,IAAI,CAACyK,qBAAqB,CAAC,IAAI,CAACH,QAAQ,EAAE2C,MAAM,CAAC;IACjDA,MAAM,CAACvM,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CsM,MAAM,CAACvM,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAC7CsM,MAAM,CAACjQ,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1C,OAAOiQ,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIxC,qBAAqBA,CAACyC,SAAS,EAAED,MAAM,EAAE;IACrC;IACA;IACAC,SAAS,GAAGD,MAAM,CAACjQ,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,GAAGiQ,MAAM,CAAC5P,eAAe,CAAC,UAAU,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACI8P,aAAaA,CAACjK,OAAO,EAAE;IACnB,IAAI,IAAI,CAACqH,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAACvH,OAAO,EAAE,IAAI,CAACqH,YAAY,CAAC;MACtD,IAAI,CAACE,qBAAqB,CAACvH,OAAO,EAAE,IAAI,CAACsH,UAAU,CAAC;IACxD;EACJ;EACA;EACAuB,gBAAgBA,CAACqB,EAAE,EAAE;IACjB,IAAI,IAAI,CAACxC,OAAO,CAACyC,QAAQ,EAAE;MACvBD,EAAE,CAAC,CAAC;IACR,CAAC,MACI;MACD,IAAI,CAACxC,OAAO,CAAC0C,QAAQ,CAACzI,IAAI,CAAC9I,qDAAI,CAAC,CAAC,CAAC,CAAC,CAACvH,SAAS,CAAC4Y,EAAE,CAAC;IACrD;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,CAAC;EACnB5X,WAAWA,CAACgV,QAAQ,EAAEC,OAAO,EAAE/M,SAAS,EAAE;IACtC,IAAI,CAAC8M,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC/M,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2P,MAAMA,CAAC1M,OAAO,EAAE2M,oBAAoB,GAAG,KAAK,EAAE;IAC1C,OAAO,IAAIpD,SAAS,CAACvJ,OAAO,EAAE,IAAI,CAAC6J,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC/M,SAAS,EAAE4P,oBAAoB,CAAC;EACpG;EACA;IAAS,IAAI,CAAClM,IAAI,YAAAmM,yBAAAjM,CAAA;MAAA,YAAAA,CAAA,IAAwF8L,gBAAgB,EAh6B1BnU,sDAAE,CAg6B0C0O,oBAAoB,GAh6BhE1O,sDAAE,CAg6B2EA,iDAAS,GAh6BtFA,sDAAE,CAg6BiGD,qDAAQ;IAAA,CAA6C;EAAE;EAC1P;IAAS,IAAI,CAACyI,KAAK,kBAj6B6ExI,gEAAE;MAAA0I,KAAA,EAi6BYyL,gBAAgB;MAAAxL,OAAA,EAAhBwL,gBAAgB,CAAAhM,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAn6BoG7I,+DAAE,CAm6BXmU,gBAAgB,EAAc,CAAC;IAC9GpL,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE2F;IAAqB,CAAC,EAAE;MAAE3F,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QACnHD,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,qDAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA,MAAMyU,YAAY,CAAC;EACf;EACA,IAAI1K,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC2K,SAAS,CAAC3K,OAAO;EACjC;EACA,IAAIA,OAAOA,CAAC9O,KAAK,EAAE;IACf,IAAI,CAACyZ,SAAS,CAAC3K,OAAO,GAAGhH,6EAAqB,CAAC9H,KAAK,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACI,IAAI0Z,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAAC1Z,KAAK,EAAE;IACnB,IAAI,CAAC2Z,YAAY,GAAG7R,6EAAqB,CAAC9H,KAAK,CAAC;EACpD;EACAuB,WAAWA,CAACqY,WAAW,EAAEC,iBAAiB;EAC1C;AACJ;AACA;AACA;EACIpQ,SAAS,EAAE;IACP,IAAI,CAACmQ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACrC,IAAI,CAACL,SAAS,GAAG,IAAI,CAACI,iBAAiB,CAACT,MAAM,CAAC,IAAI,CAACQ,WAAW,CAACG,aAAa,EAAE,IAAI,CAAC;EACxF;EACAzO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmO,SAAS,CAAC9G,OAAO,CAAC,CAAC;IACxB;IACA;IACA,IAAI,IAAI,CAACmH,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACvG,KAAK,CAAC,CAAC;MACtC,IAAI,CAACuG,yBAAyB,GAAG,IAAI;IACzC;EACJ;EACAE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACP,SAAS,CAAC1C,aAAa,CAAC,CAAC;IAC9B,IAAI,IAAI,CAAC2C,WAAW,EAAE;MAClB,IAAI,CAACO,aAAa,CAAC,CAAC;IACxB;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACT,SAAS,CAAChB,WAAW,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACgB,SAAS,CAAC1C,aAAa,CAAC,CAAC;IAClC;EACJ;EACAoD,WAAWA,CAAC5K,OAAO,EAAE;IACjB,MAAM6K,iBAAiB,GAAG7K,OAAO,CAAC,aAAa,CAAC;IAChD,IAAI6K,iBAAiB,IACjB,CAACA,iBAAiB,CAACC,WAAW,IAC9B,IAAI,CAACX,WAAW,IAChB,IAAI,CAACD,SAAS,CAAChB,WAAW,CAAC,CAAC,EAAE;MAC9B,IAAI,CAACwB,aAAa,CAAC,CAAC;IACxB;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACH,yBAAyB,GAAGhU,wFAAiC,CAAC,CAAC;IACpE,IAAI,CAAC2T,SAAS,CAAChC,4BAA4B,CAAC,CAAC;EACjD;EACA;IAAS,IAAI,CAACtK,IAAI,YAAAmN,qBAAAjN,CAAA;MAAA,YAAAA,CAAA,IAAwFmM,YAAY,EA1+BtBxU,+DAAE,CA0+BsCA,qDAAa,GA1+BrDA,+DAAE,CA0+BgEmU,gBAAgB,GA1+BlFnU,+DAAE,CA0+B6FD,qDAAQ;IAAA,CAA4C;EAAE;EACrP;IAAS,IAAI,CAAC0V,IAAI,kBA3+B8EzV,+DAAE;MAAA+I,IAAA,EA2+BJyL,YAAY;MAAAmB,SAAA;MAAAC,MAAA;QAAA9L,OAAA;QAAA4K,WAAA;MAAA;MAAAmB,QAAA;MAAAC,QAAA,GA3+BV9V,kEAAE;IAAA,EA2+BiN;EAAE;AACzT;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KA7+BoG7I,+DAAE,CA6+BXwU,YAAY,EAAc,CAAC;IAC1GzL,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,gBAAgB;MAC1BH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9M,IAAI,EAAE/I,qDAAawV;IAAC,CAAC,EAAE;MAAEzM,IAAI,EAAEoL;IAAiB,CAAC,EAAE;MAAEpL,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QACnHD,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,qDAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+J,OAAO,EAAE,CAAC;MACtCf,IAAI,EAAExI,gDAAK;MACXxB,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAE2V,WAAW,EAAE,CAAC;MACd3L,IAAI,EAAExI,gDAAK;MACXxB,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkX,qBAAqB,SAAShF,SAAS,CAAC;EAC1C;EACA,IAAInH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoH,QAAQ;EACxB;EACA,IAAIpH,OAAOA,CAAC9O,KAAK,EAAE;IACf,IAAI,CAACkW,QAAQ,GAAGlW,KAAK;IACrB,IAAI,IAAI,CAACkW,QAAQ,EAAE;MACf,IAAI,CAACgF,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACD,iBAAiB,CAACE,UAAU,CAAC,IAAI,CAAC;IAC3C;EACJ;EACA7Z,WAAWA,CAAC+U,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE/M,SAAS,EAAEyR,iBAAiB,EAAEG,cAAc,EAAEvG,MAAM,EAAE;IAC3F,KAAK,CAACwB,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE/M,SAAS,EAAEqL,MAAM,CAACwG,KAAK,CAAC;IAC3D,IAAI,CAACJ,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACH,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAAC;EACzC;EACA;EACAxI,OAAOA,CAAA,EAAG;IACN,IAAI,CAACuI,iBAAiB,CAACE,UAAU,CAAC,IAAI,CAAC;IACvC,KAAK,CAACzI,OAAO,CAAC,CAAC;EACnB;EACA;EACA4I,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,cAAc,CAACG,YAAY,CAAC,IAAI,CAAC;IACtC,IAAI,CAACzC,aAAa,CAAC,IAAI,CAAC;EAC5B;EACA;EACA0C,QAAQA,CAAA,EAAG;IACP,IAAI,CAACJ,cAAc,CAACK,UAAU,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC3C,aAAa,CAAC,KAAK,CAAC;EAC7B;AACJ;;AAEA;AACA,MAAM4C,yBAAyB,GAAG,IAAInW,yDAAc,CAAC,2BAA2B,CAAC;;AAEjF;AACA;AACA;AACA;AACA,MAAMoW,mCAAmC,CAAC;EACtCra,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACsa,SAAS,GAAG,IAAI;EACzB;EACA;EACAL,YAAYA,CAAC/B,SAAS,EAAE;IACpB;IACA,IAAI,IAAI,CAACoC,SAAS,EAAE;MAChBpC,SAAS,CAAChQ,SAAS,CAACyN,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC2E,SAAS,EAAE,IAAI,CAAC;IAC1E;IACA,IAAI,CAACA,SAAS,GAAIC,CAAC,IAAK,IAAI,CAACC,UAAU,CAACtC,SAAS,EAAEqC,CAAC,CAAC;IACrDrC,SAAS,CAACjD,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACtCsC,SAAS,CAAChQ,SAAS,CAAC4N,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACwE,SAAS,EAAE,IAAI,CAAC;IACvE,CAAC,CAAC;EACN;EACA;EACAH,UAAUA,CAACjC,SAAS,EAAE;IAClB,IAAI,CAAC,IAAI,CAACoC,SAAS,EAAE;MACjB;IACJ;IACApC,SAAS,CAAChQ,SAAS,CAACyN,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC2E,SAAS,EAAE,IAAI,CAAC;IACtE,IAAI,CAACA,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,UAAUA,CAACtC,SAAS,EAAEnI,KAAK,EAAE;IACzB,MAAM0K,MAAM,GAAG1K,KAAK,CAAC0K,MAAM;IAC3B,MAAMC,aAAa,GAAGxC,SAAS,CAACnD,QAAQ;IACxC;IACA;IACA,IAAI0F,MAAM,IAAI,CAACC,aAAa,CAACC,QAAQ,CAACF,MAAM,CAAC,IAAI,CAACA,MAAM,CAACG,OAAO,GAAG,sBAAsB,CAAC,EAAE;MACxF;MACA;MACA;MACAC,UAAU,CAAC,MAAM;QACb;QACA,IAAI3C,SAAS,CAAC3K,OAAO,IAAI,CAACmN,aAAa,CAACC,QAAQ,CAACzC,SAAS,CAAChQ,SAAS,CAAC4S,aAAa,CAAC,EAAE;UACjF5C,SAAS,CAAC3C,yBAAyB,CAAC,CAAC;QACzC;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;;AAEA;AACA,MAAMwF,gBAAgB,CAAC;EACnB/a,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACgb,eAAe,GAAG,EAAE;EAC7B;EACA;AACJ;AACA;AACA;EACIpB,QAAQA,CAAC1B,SAAS,EAAE;IAChB;IACA,IAAI,CAAC8C,eAAe,GAAG,IAAI,CAACA,eAAe,CAACxb,MAAM,CAACyb,EAAE,IAAIA,EAAE,KAAK/C,SAAS,CAAC;IAC1E,IAAIgD,KAAK,GAAG,IAAI,CAACF,eAAe;IAChC,IAAIE,KAAK,CAACra,MAAM,EAAE;MACdqa,KAAK,CAACA,KAAK,CAACra,MAAM,GAAG,CAAC,CAAC,CAACqZ,QAAQ,CAAC,CAAC;IACtC;IACAgB,KAAK,CAAC5a,IAAI,CAAC4X,SAAS,CAAC;IACrBA,SAAS,CAAC8B,OAAO,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIH,UAAUA,CAAC3B,SAAS,EAAE;IAClBA,SAAS,CAACgC,QAAQ,CAAC,CAAC;IACpB,MAAMgB,KAAK,GAAG,IAAI,CAACF,eAAe;IAClC,MAAM9Q,CAAC,GAAGgR,KAAK,CAAC7P,OAAO,CAAC6M,SAAS,CAAC;IAClC,IAAIhO,CAAC,KAAK,CAAC,CAAC,EAAE;MACVgR,KAAK,CAACC,MAAM,CAACjR,CAAC,EAAE,CAAC,CAAC;MAClB,IAAIgR,KAAK,CAACra,MAAM,EAAE;QACdqa,KAAK,CAACA,KAAK,CAACra,MAAM,GAAG,CAAC,CAAC,CAACmZ,OAAO,CAAC,CAAC;MACrC;IACJ;EACJ;EACA;IAAS,IAAI,CAACpO,IAAI,YAAAwP,yBAAAtP,CAAA;MAAA,YAAAA,CAAA,IAAwFiP,gBAAgB;IAAA,CAAoD;EAAE;EAChL;IAAS,IAAI,CAAC9O,KAAK,kBAvoC6ExI,gEAAE;MAAA0I,KAAA,EAuoCY4O,gBAAgB;MAAA3O,OAAA,EAAhB2O,gBAAgB,CAAAnP,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzoCoG7I,+DAAE,CAyoCXsX,gBAAgB,EAAc,CAAC;IAC9GvO,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMgP,4BAA4B,CAAC;EAC/Brb,WAAWA,CAACgV,QAAQ,EAAEC,OAAO,EAAE0E,iBAAiB,EAAEzR,SAAS,EAAE4R,cAAc,EAAE;IACzE,IAAI,CAAC9E,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC0E,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACzR,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAAC4R,cAAc,GAAGA,cAAc,IAAI,IAAIO,mCAAmC,CAAC,CAAC;EACrF;EACAxC,MAAMA,CAAC1M,OAAO,EAAEoI,MAAM,GAAG;IAAEwG,KAAK,EAAE;EAAM,CAAC,EAAE;IACvC,IAAIuB,YAAY;IAChB,IAAI,OAAO/H,MAAM,KAAK,SAAS,EAAE;MAC7B+H,YAAY,GAAG;QAAEvB,KAAK,EAAExG;MAAO,CAAC;IACpC,CAAC,MACI;MACD+H,YAAY,GAAG/H,MAAM;IACzB;IACA,OAAO,IAAImG,qBAAqB,CAACvO,OAAO,EAAE,IAAI,CAAC6J,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC/M,SAAS,EAAE,IAAI,CAACyR,iBAAiB,EAAE,IAAI,CAACG,cAAc,EAAEwB,YAAY,CAAC;EACrJ;EACA;IAAS,IAAI,CAAC1P,IAAI,YAAA2P,qCAAAzP,CAAA;MAAA,YAAAA,CAAA,IAAwFuP,4BAA4B,EAlqCtC5X,sDAAE,CAkqCsD0O,oBAAoB,GAlqC5E1O,sDAAE,CAkqCuFA,iDAAS,GAlqClGA,sDAAE,CAkqC6GsX,gBAAgB,GAlqC/HtX,sDAAE,CAkqC0ID,qDAAQ,GAlqCpJC,sDAAE,CAkqC+J2W,yBAAyB;IAAA,CAA6D;EAAE;EACzV;IAAS,IAAI,CAACnO,KAAK,kBAnqC6ExI,gEAAE;MAAA0I,KAAA,EAmqCYkP,4BAA4B;MAAAjP,OAAA,EAA5BiP,4BAA4B,CAAAzP,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACvK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArqCoG7I,+DAAE,CAqqCX4X,4BAA4B,EAAc,CAAC;IAC1H7O,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE2F;IAAqB,CAAC,EAAE;MAAE3F,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAEuO;IAAiB,CAAC,EAAE;MAAEvO,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAC/ID,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,qDAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAAC4X,yBAAyB;MACpC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,SAASoB,+BAA+BA,CAACzL,KAAK,EAAE;EAC5C;EACA;EACA;EACA;EACA;EACA,OAAOA,KAAK,CAAC0L,OAAO,KAAK,CAAC,IAAI1L,KAAK,CAAC2L,MAAM,KAAK,CAAC;AACpD;AACA;AACA,SAASC,gCAAgCA,CAAC5L,KAAK,EAAE;EAC7C,MAAM6L,KAAK,GAAI7L,KAAK,CAAC8L,OAAO,IAAI9L,KAAK,CAAC8L,OAAO,CAAC,CAAC,CAAC,IAAM9L,KAAK,CAAC+L,cAAc,IAAI/L,KAAK,CAAC+L,cAAc,CAAC,CAAC,CAAE;EACtG;EACA;EACA;EACA;EACA,OAAQ,CAAC,CAACF,KAAK,IACXA,KAAK,CAACG,UAAU,KAAK,CAAC,CAAC,KACtBH,KAAK,CAACI,OAAO,IAAI,IAAI,IAAIJ,KAAK,CAACI,OAAO,KAAK,CAAC,CAAC,KAC7CJ,KAAK,CAACK,OAAO,IAAI,IAAI,IAAIL,KAAK,CAACK,OAAO,KAAK,CAAC,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG,IAAIjY,yDAAc,CAAC,qCAAqC,CAAC;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkY,uCAAuC,GAAG;EAC5CC,UAAU,EAAE,CAACxW,sDAAG,EAAEC,0DAAO,EAAEC,2DAAQ,EAAEC,uDAAI,EAAEC,wDAAK;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqW,eAAe,GAAG,GAAG;AAC3B;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,GAAG9X,sFAA+B,CAAC;EACjE+X,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,SAAS,CAACle,KAAK;EAC/B;EACAuB,WAAWA,CAACmI,SAAS,EAAEyU,MAAM,EAAEC,QAAQ,EAAE1G,OAAO,EAAE;IAC9C,IAAI,CAAChO,SAAS,GAAGA,SAAS;IAC1B;AACR;AACA;AACA;IACQ,IAAI,CAAC2U,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACH,SAAS,GAAG,IAAI/X,kDAAe,CAAC,IAAI,CAAC;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAACmY,YAAY,GAAG,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAIjN,KAAK,IAAK;MACzB;MACA;MACA,IAAI,IAAI,CAACkN,QAAQ,EAAEb,UAAU,EAAElV,IAAI,CAAC8I,OAAO,IAAIA,OAAO,KAAKD,KAAK,CAACC,OAAO,CAAC,EAAE;QACvE;MACJ;MACA,IAAI,CAAC2M,SAAS,CAACje,IAAI,CAAC,UAAU,CAAC;MAC/B,IAAI,CAACoe,iBAAiB,GAAGrY,sEAAe,CAACsL,KAAK,CAAC;IACnD,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACmN,YAAY,GAAInN,KAAK,IAAK;MAC3B;MACA;MACA;MACA,IAAIoN,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACL,YAAY,GAAGV,eAAe,EAAE;QAClD;MACJ;MACA;MACA;MACA,IAAI,CAACM,SAAS,CAACje,IAAI,CAAC8c,+BAA+B,CAACzL,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,CAAC;MAClF,IAAI,CAAC+M,iBAAiB,GAAGrY,sEAAe,CAACsL,KAAK,CAAC;IACnD,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACsN,aAAa,GAAItN,KAAK,IAAK;MAC5B;MACA;MACA,IAAI4L,gCAAgC,CAAC5L,KAAK,CAAC,EAAE;QACzC,IAAI,CAAC4M,SAAS,CAACje,IAAI,CAAC,UAAU,CAAC;QAC/B;MACJ;MACA;MACA;MACA,IAAI,CAACqe,YAAY,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACT,SAAS,CAACje,IAAI,CAAC,OAAO,CAAC;MAC5B,IAAI,CAACoe,iBAAiB,GAAGrY,sEAAe,CAACsL,KAAK,CAAC;IACnD,CAAC;IACD,IAAI,CAACkN,QAAQ,GAAG;MACZ,GAAGd,uCAAuC;MAC1C,GAAGhG;IACP,CAAC;IACD;IACA,IAAI,CAACmH,gBAAgB,GAAG,IAAI,CAACX,SAAS,CAACzN,IAAI,CAACzP,qDAAI,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC8d,eAAe,GAAG,IAAI,CAACD,gBAAgB,CAACpO,IAAI,CAAC7I,qEAAoB,CAAC,CAAC,CAAC;IACzE;IACA;IACA,IAAI8B,SAAS,CAAC8C,SAAS,EAAE;MACrB2R,MAAM,CAAChH,iBAAiB,CAAC,MAAM;QAC3BiH,QAAQ,CAAC/G,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACkH,UAAU,EAAEV,4BAA4B,CAAC;QACnFO,QAAQ,CAAC/G,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACoH,YAAY,EAAEZ,4BAA4B,CAAC;QACvFO,QAAQ,CAAC/G,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACuH,aAAa,EAAEf,4BAA4B,CAAC;MAC7F,CAAC,CAAC;IACN;EACJ;EACAvS,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4S,SAAS,CAAChe,QAAQ,CAAC,CAAC;IACzB,IAAI,IAAI,CAACwJ,SAAS,CAAC8C,SAAS,EAAE;MAC1B4R,QAAQ,CAAClH,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACqH,UAAU,EAAEV,4BAA4B,CAAC;MACtFO,QAAQ,CAAClH,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACuH,YAAY,EAAEZ,4BAA4B,CAAC;MAC1FO,QAAQ,CAAClH,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC0H,aAAa,EAAEf,4BAA4B,CAAC;IAChG;EACJ;EACA;IAAS,IAAI,CAAC1Q,IAAI,YAAA4R,8BAAA1R,CAAA;MAAA,YAAAA,CAAA,IAAwF2Q,qBAAqB,EAx1C/BhZ,sDAAE,CAw1C+Ca,2DAAW,GAx1C5Db,sDAAE,CAw1CuEA,iDAAS,GAx1ClFA,sDAAE,CAw1C6FD,qDAAQ,GAx1CvGC,sDAAE,CAw1CkHyY,+BAA+B;IAAA,CAA6D;EAAE;EAClT;IAAS,IAAI,CAACjQ,KAAK,kBAz1C6ExI,gEAAE;MAAA0I,KAAA,EAy1CYsQ,qBAAqB;MAAArQ,OAAA,EAArBqQ,qBAAqB,CAAA7Q,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAChK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA31CoG7I,+DAAE,CA21CXgZ,qBAAqB,EAAc,CAAC;IACnHjQ,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElI,2DAAW0H;IAAC,CAAC,EAAE;MAAEQ,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAEiR,QAAQ;MAAEhR,UAAU,EAAE,CAAC;QACzGD,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,qDAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAAC0Z,+BAA+B;MAC1C,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMwB,4BAA4B,GAAG,IAAIzZ,yDAAc,CAAC,sBAAsB,EAAE;EAC5EoI,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEuR;AACb,CAAC,CAAC;AACF;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO,IAAI;AACf;AACA;AACA,MAAMC,8BAA8B,GAAG,IAAI3Z,yDAAc,CAAC,gCAAgC,CAAC;AAE3F,IAAI4Z,SAAS,GAAG,CAAC;AACjB,MAAMC,aAAa,CAAC;EAChB9d,WAAWA,CAAC+d,YAAY,EAAE9I,OAAO,EAAE/M,SAAS,EAAE8V,eAAe,EAAE;IAC3D,IAAI,CAAC/I,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC+I,eAAe,GAAGA,eAAe;IACtC;IACA;IACA;IACA,IAAI,CAAC9V,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC+V,YAAY,GAAGF,YAAY,IAAI,IAAI,CAACG,kBAAkB,CAAC,CAAC;EACjE;EACAC,QAAQA,CAACzV,OAAO,EAAE,GAAGlG,IAAI,EAAE;IACvB,MAAM4b,cAAc,GAAG,IAAI,CAACJ,eAAe;IAC3C,IAAIK,UAAU;IACd,IAAInf,QAAQ;IACZ,IAAIsD,IAAI,CAAC3B,MAAM,KAAK,CAAC,IAAI,OAAO2B,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAClDtD,QAAQ,GAAGsD,IAAI,CAAC,CAAC,CAAC;IACtB,CAAC,MACI;MACD,CAAC6b,UAAU,EAAEnf,QAAQ,CAAC,GAAGsD,IAAI;IACjC;IACA,IAAI,CAAC4H,KAAK,CAAC,CAAC;IACZkU,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACnC,IAAI,CAACF,UAAU,EAAE;MACbA,UAAU,GACND,cAAc,IAAIA,cAAc,CAACC,UAAU,GAAGD,cAAc,CAACC,UAAU,GAAG,QAAQ;IAC1F;IACA,IAAInf,QAAQ,IAAI,IAAI,IAAIkf,cAAc,EAAE;MACpClf,QAAQ,GAAGkf,cAAc,CAAClf,QAAQ;IACtC;IACA;IACA,IAAI,CAAC+e,YAAY,CAAC5W,YAAY,CAAC,WAAW,EAAEgX,UAAU,CAAC;IACvD,IAAI,IAAI,CAACJ,YAAY,CAAC9d,EAAE,EAAE;MACtB,IAAI,CAACqe,wBAAwB,CAAC,IAAI,CAACP,YAAY,CAAC9d,EAAE,CAAC;IACvD;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAI,CAAC8U,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAAC6I,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAIxb,OAAO,CAACC,OAAO,IAAK,IAAI,CAACwb,eAAe,GAAGxb,OAAQ,CAAC;MACnF;MACAob,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG1D,UAAU,CAAC,MAAM;QACrC,IAAI,CAACoD,YAAY,CAAC3T,WAAW,GAAG5B,OAAO;QACvC,IAAI,OAAOxJ,QAAQ,KAAK,QAAQ,EAAE;UAC9B,IAAI,CAACqf,gBAAgB,GAAG1D,UAAU,CAAC,MAAM,IAAI,CAACzQ,KAAK,CAAC,CAAC,EAAElL,QAAQ,CAAC;QACpE;QACA,IAAI,CAACwf,eAAe,CAAC,CAAC;QACtB,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGhe,SAAS;MAC3D,CAAC,EAAE,GAAG,CAAC;MACP,OAAO,IAAI,CAAC+d,eAAe;IAC/B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIrU,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC6T,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC3T,WAAW,GAAG,EAAE;IACtC;EACJ;EACAP,WAAWA,CAAA,EAAG;IACVuU,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACnC,IAAI,CAACN,YAAY,EAAEnU,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACmU,YAAY,GAAG,IAAI;IACxB,IAAI,CAACS,eAAe,GAAG,CAAC;IACxB,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGhe,SAAS;EAC3D;EACAwd,kBAAkBA,CAAA,EAAG;IACjB,MAAMS,YAAY,GAAG,4BAA4B;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAAC1W,SAAS,CAAC2W,sBAAsB,CAACF,YAAY,CAAC;IAC5E,MAAMG,MAAM,GAAG,IAAI,CAAC5W,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAClD;IACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0U,gBAAgB,CAAC/d,MAAM,EAAEqJ,CAAC,EAAE,EAAE;MAC9C0U,gBAAgB,CAAC1U,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC;IAChC;IACAgV,MAAM,CAAC/T,SAAS,CAACC,GAAG,CAAC2T,YAAY,CAAC;IAClCG,MAAM,CAAC/T,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3C8T,MAAM,CAACzX,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1CyX,MAAM,CAACzX,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC1CyX,MAAM,CAAC3e,EAAE,GAAI,sBAAqB0d,SAAS,EAAG,EAAC;IAC/C,IAAI,CAAC3V,SAAS,CAACgD,IAAI,CAACV,WAAW,CAACsU,MAAM,CAAC;IACvC,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIN,wBAAwBA,CAACre,EAAE,EAAE;IACzB;IACA;IACA;IACA;IACA;IACA;IACA,MAAM4e,MAAM,GAAG,IAAI,CAAC7W,SAAS,CAAC+B,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6U,MAAM,CAACle,MAAM,EAAEqJ,CAAC,EAAE,EAAE;MACpC,MAAM8U,KAAK,GAAGD,MAAM,CAAC7U,CAAC,CAAC;MACvB,MAAM+U,QAAQ,GAAGD,KAAK,CAACrX,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAACsX,QAAQ,EAAE;QACXD,KAAK,CAAC3X,YAAY,CAAC,WAAW,EAAElH,EAAE,CAAC;MACvC,CAAC,MACI,IAAI8e,QAAQ,CAAC5T,OAAO,CAAClL,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClC6e,KAAK,CAAC3X,YAAY,CAAC,WAAW,EAAE4X,QAAQ,GAAG,GAAG,GAAG9e,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;IAAS,IAAI,CAACyL,IAAI,YAAAsT,sBAAApT,CAAA;MAAA,YAAAA,CAAA,IAAwFgS,aAAa,EAp+CvBra,sDAAE,CAo+CuCia,4BAA4B,MAp+CrEja,sDAAE,CAo+CgGA,iDAAS,GAp+C3GA,sDAAE,CAo+CsHD,qDAAQ,GAp+ChIC,sDAAE,CAo+C2Ima,8BAA8B;IAAA,CAA6D;EAAE;EAC1U;IAAS,IAAI,CAAC3R,KAAK,kBAr+C6ExI,gEAAE;MAAA0I,KAAA,EAq+CY2R,aAAa;MAAA1R,OAAA,EAAb0R,aAAa,CAAAlS,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAv+CoG7I,+DAAE,CAu+CXqa,aAAa,EAAc,CAAC;IAC3GtR,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAC9DD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACkb,4BAA4B;MACvC,CAAC;IAAE,CAAC,EAAE;MAAElR,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QACvDD,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,qDAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACob,8BAA8B;MACzC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA,MAAMuB,WAAW,CAAC;EACd;EACA,IAAId,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACe,WAAW;EAC3B;EACA,IAAIf,UAAUA,CAAC5f,KAAK,EAAE;IAClB,IAAI,CAAC2gB,WAAW,GAAG3gB,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,QAAQ;IAC9E,IAAI,IAAI,CAAC2gB,WAAW,KAAK,KAAK,EAAE;MAC5B,IAAI,IAAI,CAACC,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAAC7gB,WAAW,CAAC,CAAC;QAChC,IAAI,CAAC6gB,aAAa,GAAG,IAAI;MAC7B;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MAC1B,IAAI,CAACA,aAAa,GAAG,IAAI,CAACpK,OAAO,CAACW,iBAAiB,CAAC,MAAM;QACtD,OAAO,IAAI,CAAC0J,gBAAgB,CAACC,OAAO,CAAC,IAAI,CAAClH,WAAW,CAAC,CAACxZ,SAAS,CAAC,MAAM;UACnE;UACA,MAAM2gB,WAAW,GAAG,IAAI,CAACnH,WAAW,CAACG,aAAa,CAAClO,WAAW;UAC9D;UACA;UACA,IAAIkV,WAAW,KAAK,IAAI,CAACC,sBAAsB,EAAE;YAC7C,IAAI,CAACC,cAAc,CAACvB,QAAQ,CAACqB,WAAW,EAAE,IAAI,CAACJ,WAAW,EAAE,IAAI,CAAClgB,QAAQ,CAAC;YAC1E,IAAI,CAACugB,sBAAsB,GAAGD,WAAW;UAC7C;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACAxf,WAAWA,CAACqY,WAAW,EAAEqH,cAAc,EAAEJ,gBAAgB,EAAErK,OAAO,EAAE;IAChE,IAAI,CAACoD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACqH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACJ,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACrK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmK,WAAW,GAAG,QAAQ;EAC/B;EACArV,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACsV,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC7gB,WAAW,CAAC,CAAC;IACpC;EACJ;EACA;IAAS,IAAI,CAACoN,IAAI,YAAA+T,oBAAA7T,CAAA;MAAA,YAAAA,CAAA,IAAwFqT,WAAW,EApiDrB1b,+DAAE,CAoiDqCA,qDAAa,GApiDpDA,+DAAE,CAoiD+Dqa,aAAa,GApiD9Era,+DAAE,CAoiDyFgD,oEAAoB,GApiD/GhD,+DAAE,CAoiD0HA,iDAAS;IAAA,CAA4C;EAAE;EACnR;IAAS,IAAI,CAACyV,IAAI,kBAriD8EzV,+DAAE;MAAA+I,IAAA,EAqiDJ2S,WAAW;MAAA/F,SAAA;MAAAC,MAAA;QAAAgF,UAAA;QAAAnf,QAAA;MAAA;MAAAoa,QAAA;IAAA,EAA6K;EAAE;AAC5R;AACA;EAAA,QAAAhN,SAAA,oBAAAA,SAAA,KAviDoG7I,+DAAE,CAuiDX0b,WAAW,EAAc,CAAC;IACzG3S,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,eAAe;MACzBH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9M,IAAI,EAAE/I,qDAAawV;IAAC,CAAC,EAAE;MAAEzM,IAAI,EAAEsR;IAAc,CAAC,EAAE;MAAEtR,IAAI,EAAE/F,oEAAoBmZ;IAAC,CAAC,EAAE;MAAEpT,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqG,UAAU,EAAE,CAAC;MAC5K7R,IAAI,EAAExI,gDAAK;MACXxB,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEtD,QAAQ,EAAE,CAAC;MACXsN,IAAI,EAAExI,gDAAK;MACXxB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMqd,6BAA6B,GAAG,IAAI5b,yDAAc,CAAC,mCAAmC,CAAC;AAC7F;AACA;AACA;AACA;AACA,MAAM6b,2BAA2B,GAAGtb,sFAA+B,CAAC;EAChE+X,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMuD,YAAY,CAAC;EACf/f,WAAWA,CAACiV,OAAO,EAAE9M,SAAS,EAAE6X,sBAAsB,EACtD;EACAnD,QAAQ,EAAE1G,OAAO,EAAE;IACf,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC9M,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC6X,sBAAsB,GAAGA,sBAAsB;IACpD;IACA,IAAI,CAACnO,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACoO,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;IACA,IAAI,CAACC,YAAY,GAAG,IAAI9X,GAAG,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC+X,sBAAsB,GAAG,CAAC;IAC/B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,2BAA2B,GAAG,IAAIhY,GAAG,CAAC,CAAC;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAACiY,oBAAoB,GAAG,MAAM;MAC9B;MACA;MACA,IAAI,CAACL,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACM,qBAAqB,GAAG7M,MAAM,CAACmH,UAAU,CAAC,MAAO,IAAI,CAACoF,cAAc,GAAG,KAAM,CAAC;IACvF,CAAC;IACD;IACA,IAAI,CAACO,0BAA0B,GAAG,IAAI7b,yCAAO,CAAC,CAAC;IAC/C;AACR;AACA;AACA;IACQ,IAAI,CAAC8b,6BAA6B,GAAI1Q,KAAK,IAAK;MAC5C,MAAM0K,MAAM,GAAGhW,sEAAe,CAACsL,KAAK,CAAC;MACrC;MACA,KAAK,IAAI5E,OAAO,GAAGsP,MAAM,EAAEtP,OAAO,EAAEA,OAAO,GAAGA,OAAO,CAACuV,aAAa,EAAE;QACjE,IAAI3Q,KAAK,CAACvD,IAAI,KAAK,OAAO,EAAE;UACxB,IAAI,CAACmU,QAAQ,CAAC5Q,KAAK,EAAE5E,OAAO,CAAC;QACjC,CAAC,MACI;UACD,IAAI,CAACyV,OAAO,CAAC7Q,KAAK,EAAE5E,OAAO,CAAC;QAChC;MACJ;IACJ,CAAC;IACD,IAAI,CAACjD,SAAS,GAAG2U,QAAQ;IACzB,IAAI,CAACgE,cAAc,GAAG1K,OAAO,EAAE2K,aAAa,IAAI,CAAC,CAAC;EACtD;;EACAC,OAAOA,CAAC5V,OAAO,EAAE6V,aAAa,GAAG,KAAK,EAAE;IACpC,MAAMxI,aAAa,GAAGhS,qEAAa,CAAC2E,OAAO,CAAC;IAC5C;IACA,IAAI,CAAC,IAAI,CAAChD,SAAS,CAAC8C,SAAS,IAAIuN,aAAa,CAAC9M,QAAQ,KAAK,CAAC,EAAE;MAC3D;MACA,OAAO7G,yCAAE,CAAC,CAAC;IACf;IACA;IACA;IACA;IACA,MAAMoc,QAAQ,GAAGvc,qEAAc,CAAC8T,aAAa,CAAC,IAAI,IAAI,CAAC0I,YAAY,CAAC,CAAC;IACrE,MAAMC,UAAU,GAAG,IAAI,CAAChB,YAAY,CAACxW,GAAG,CAAC6O,aAAa,CAAC;IACvD;IACA,IAAI2I,UAAU,EAAE;MACZ,IAAIH,aAAa,EAAE;QACf;QACA;QACA;QACAG,UAAU,CAACH,aAAa,GAAG,IAAI;MACnC;MACA,OAAOG,UAAU,CAACC,OAAO;IAC7B;IACA;IACA,MAAMC,IAAI,GAAG;MACTL,aAAa,EAAEA,aAAa;MAC5BI,OAAO,EAAE,IAAIzc,yCAAO,CAAC,CAAC;MACtBsc;IACJ,CAAC;IACD,IAAI,CAACd,YAAY,CAACnX,GAAG,CAACwP,aAAa,EAAE6I,IAAI,CAAC;IAC1C,IAAI,CAACC,wBAAwB,CAACD,IAAI,CAAC;IACnC,OAAOA,IAAI,CAACD,OAAO;EACvB;EACAG,cAAcA,CAACpW,OAAO,EAAE;IACpB,MAAMqN,aAAa,GAAGhS,qEAAa,CAAC2E,OAAO,CAAC;IAC5C,MAAMqW,WAAW,GAAG,IAAI,CAACrB,YAAY,CAACxW,GAAG,CAAC6O,aAAa,CAAC;IACxD,IAAIgJ,WAAW,EAAE;MACbA,WAAW,CAACJ,OAAO,CAACziB,QAAQ,CAAC,CAAC;MAC9B,IAAI,CAAC8iB,WAAW,CAACjJ,aAAa,CAAC;MAC/B,IAAI,CAAC2H,YAAY,CAAC1V,MAAM,CAAC+N,aAAa,CAAC;MACvC,IAAI,CAACkJ,sBAAsB,CAACF,WAAW,CAAC;IAC5C;EACJ;EACAG,QAAQA,CAACxW,OAAO,EAAE4G,MAAM,EAAEoE,OAAO,EAAE;IAC/B,MAAMqC,aAAa,GAAGhS,qEAAa,CAAC2E,OAAO,CAAC;IAC5C,MAAMyW,cAAc,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC,CAACpG,aAAa;IACxD;IACA;IACA;IACA,IAAItC,aAAa,KAAKoJ,cAAc,EAAE;MAClC,IAAI,CAACC,uBAAuB,CAACrJ,aAAa,CAAC,CAACsJ,OAAO,CAAC,CAAC,CAACC,cAAc,EAAEV,IAAI,CAAC,KAAK,IAAI,CAACW,cAAc,CAACD,cAAc,EAAEhQ,MAAM,EAAEsP,IAAI,CAAC,CAAC;IACtI,CAAC,MACI;MACD,IAAI,CAACY,UAAU,CAAClQ,MAAM,CAAC;MACvB;MACA,IAAI,OAAOyG,aAAa,CAACxG,KAAK,KAAK,UAAU,EAAE;QAC3CwG,aAAa,CAACxG,KAAK,CAACmE,OAAO,CAAC;MAChC;IACJ;EACJ;EACApM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoW,YAAY,CAAC2B,OAAO,CAAC,CAACI,KAAK,EAAE/W,OAAO,KAAK,IAAI,CAACoW,cAAc,CAACpW,OAAO,CAAC,CAAC;EAC/E;EACA;EACA+V,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAChZ,SAAS,IAAI2U,QAAQ;EACrC;EACA;EACAsF,UAAUA,CAAA,EAAG;IACT,MAAMC,GAAG,GAAG,IAAI,CAAClB,YAAY,CAAC,CAAC;IAC/B,OAAOkB,GAAG,CAAC3N,WAAW,IAAIf,MAAM;EACpC;EACA2O,eAAeA,CAACC,gBAAgB,EAAE;IAC9B,IAAI,IAAI,CAACzQ,OAAO,EAAE;MACd;MACA;MACA,IAAI,IAAI,CAACqO,2BAA2B,EAAE;QAClC,OAAO,IAAI,CAACqC,0BAA0B,CAACD,gBAAgB,CAAC,GAAG,OAAO,GAAG,SAAS;MAClF,CAAC,MACI;QACD,OAAO,IAAI,CAACzQ,OAAO;MACvB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACoO,cAAc,IAAI,IAAI,CAACuC,gBAAgB,EAAE;MAC9C,OAAO,IAAI,CAACA,gBAAgB;IAChC;IACA;IACA;IACA;IACA;IACA,IAAIF,gBAAgB,IAAI,IAAI,CAACG,gCAAgC,CAACH,gBAAgB,CAAC,EAAE;MAC7E,OAAO,OAAO;IAClB;IACA,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,0BAA0BA,CAACD,gBAAgB,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAQ,IAAI,CAACzB,cAAc,KAAK,CAAC,CAAC,4CAC9B,CAAC,CAACyB,gBAAgB,EAAE3H,QAAQ,CAAC,IAAI,CAACqF,sBAAsB,CAAClD,iBAAiB,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACI2E,WAAWA,CAACtW,OAAO,EAAE4G,MAAM,EAAE;IACzB5G,OAAO,CAACJ,SAAS,CAAC2X,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC3Q,MAAM,CAAC;IACjD5G,OAAO,CAACJ,SAAS,CAAC2X,MAAM,CAAC,mBAAmB,EAAE3Q,MAAM,KAAK,OAAO,CAAC;IACjE5G,OAAO,CAACJ,SAAS,CAAC2X,MAAM,CAAC,sBAAsB,EAAE3Q,MAAM,KAAK,UAAU,CAAC;IACvE5G,OAAO,CAACJ,SAAS,CAAC2X,MAAM,CAAC,mBAAmB,EAAE3Q,MAAM,KAAK,OAAO,CAAC;IACjE5G,OAAO,CAACJ,SAAS,CAAC2X,MAAM,CAAC,qBAAqB,EAAE3Q,MAAM,KAAK,SAAS,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIkQ,UAAUA,CAAClQ,MAAM,EAAE4Q,iBAAiB,GAAG,KAAK,EAAE;IAC1C,IAAI,CAAC1N,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC/D,OAAO,GAAGE,MAAM;MACrB,IAAI,CAACmO,2BAA2B,GAAGnO,MAAM,KAAK,OAAO,IAAI4Q,iBAAiB;MAC1E;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC9B,cAAc,KAAK,CAAC,CAAC,2CAA2C;QACrEvC,YAAY,CAAC,IAAI,CAACsE,gBAAgB,CAAC;QACnC,MAAMC,EAAE,GAAG,IAAI,CAAC3C,2BAA2B,GAAG7D,eAAe,GAAG,CAAC;QACjE,IAAI,CAACuG,gBAAgB,GAAG/H,UAAU,CAAC,MAAO,IAAI,CAAChJ,OAAO,GAAG,IAAK,EAAEgR,EAAE,CAAC;MACvE;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIlC,QAAQA,CAAC5Q,KAAK,EAAE5E,OAAO,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMqW,WAAW,GAAG,IAAI,CAACrB,YAAY,CAACxW,GAAG,CAACwB,OAAO,CAAC;IAClD,MAAMmX,gBAAgB,GAAG7d,sEAAe,CAACsL,KAAK,CAAC;IAC/C,IAAI,CAACyR,WAAW,IAAK,CAACA,WAAW,CAACR,aAAa,IAAI7V,OAAO,KAAKmX,gBAAiB,EAAE;MAC9E;IACJ;IACA,IAAI,CAACN,cAAc,CAAC7W,OAAO,EAAE,IAAI,CAACkX,eAAe,CAACC,gBAAgB,CAAC,EAAEd,WAAW,CAAC;EACrF;EACA;AACJ;AACA;AACA;AACA;EACIZ,OAAOA,CAAC7Q,KAAK,EAAE5E,OAAO,EAAE;IACpB;IACA;IACA,MAAMqW,WAAW,GAAG,IAAI,CAACrB,YAAY,CAACxW,GAAG,CAACwB,OAAO,CAAC;IAClD,IAAI,CAACqW,WAAW,IACXA,WAAW,CAACR,aAAa,IACtBjR,KAAK,CAAC+S,aAAa,YAAYC,IAAI,IACnC5X,OAAO,CAACwP,QAAQ,CAAC5K,KAAK,CAAC+S,aAAa,CAAE,EAAE;MAC5C;IACJ;IACA,IAAI,CAACrB,WAAW,CAACtW,OAAO,CAAC;IACzB,IAAI,CAAC6X,WAAW,CAACxB,WAAW,EAAE,IAAI,CAAC;EACvC;EACAwB,WAAWA,CAAC3B,IAAI,EAAEtP,MAAM,EAAE;IACtB,IAAIsP,IAAI,CAACD,OAAO,CAAC6B,SAAS,CAACpiB,MAAM,EAAE;MAC/B,IAAI,CAACoU,OAAO,CAACiO,GAAG,CAAC,MAAM7B,IAAI,CAACD,OAAO,CAAC1iB,IAAI,CAACqT,MAAM,CAAC,CAAC;IACrD;EACJ;EACAuP,wBAAwBA,CAACE,WAAW,EAAE;IAClC,IAAI,CAAC,IAAI,CAACrZ,SAAS,CAAC8C,SAAS,EAAE;MAC3B;IACJ;IACA,MAAMgW,QAAQ,GAAGO,WAAW,CAACP,QAAQ;IACrC,MAAMkC,sBAAsB,GAAG,IAAI,CAAC9C,2BAA2B,CAAC1W,GAAG,CAACsX,QAAQ,CAAC,IAAI,CAAC;IAClF,IAAI,CAACkC,sBAAsB,EAAE;MACzB,IAAI,CAAClO,OAAO,CAACW,iBAAiB,CAAC,MAAM;QACjCqL,QAAQ,CAACnL,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC2K,6BAA6B,EAAEX,2BAA2B,CAAC;QACnGmB,QAAQ,CAACnL,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC2K,6BAA6B,EAAEX,2BAA2B,CAAC;MACtG,CAAC,CAAC;IACN;IACA,IAAI,CAACO,2BAA2B,CAACrX,GAAG,CAACiY,QAAQ,EAAEkC,sBAAsB,GAAG,CAAC,CAAC;IAC1E;IACA,IAAI,EAAE,IAAI,CAAC/C,sBAAsB,KAAK,CAAC,EAAE;MACrC;MACA;MACA,IAAI,CAACnL,OAAO,CAACW,iBAAiB,CAAC,MAAM;QACjC,MAAMlC,MAAM,GAAG,IAAI,CAACyO,UAAU,CAAC,CAAC;QAChCzO,MAAM,CAACoC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACwK,oBAAoB,CAAC;MAC/D,CAAC,CAAC;MACF;MACA,IAAI,CAACN,sBAAsB,CAAC1C,gBAAgB,CACvCpO,IAAI,CAAC5I,0DAAS,CAAC,IAAI,CAACka,0BAA0B,CAAC,CAAC,CAChD3hB,SAAS,CAACukB,QAAQ,IAAI;QACvB,IAAI,CAACnB,UAAU,CAACmB,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;;EACA1B,sBAAsBA,CAACF,WAAW,EAAE;IAChC,MAAMP,QAAQ,GAAGO,WAAW,CAACP,QAAQ;IACrC,IAAI,IAAI,CAACZ,2BAA2B,CAAClX,GAAG,CAAC8X,QAAQ,CAAC,EAAE;MAChD,MAAMkC,sBAAsB,GAAG,IAAI,CAAC9C,2BAA2B,CAAC1W,GAAG,CAACsX,QAAQ,CAAC;MAC7E,IAAIkC,sBAAsB,GAAG,CAAC,EAAE;QAC5B,IAAI,CAAC9C,2BAA2B,CAACrX,GAAG,CAACiY,QAAQ,EAAEkC,sBAAsB,GAAG,CAAC,CAAC;MAC9E,CAAC,MACI;QACDlC,QAAQ,CAACtL,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC8K,6BAA6B,EAAEX,2BAA2B,CAAC;QACtGmB,QAAQ,CAACtL,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC8K,6BAA6B,EAAEX,2BAA2B,CAAC;QACrG,IAAI,CAACO,2BAA2B,CAAC5V,MAAM,CAACwW,QAAQ,CAAC;MACrD;IACJ;IACA;IACA,IAAI,CAAC,GAAE,IAAI,CAACb,sBAAsB,EAAE;MAChC,MAAM1M,MAAM,GAAG,IAAI,CAACyO,UAAU,CAAC,CAAC;MAChCzO,MAAM,CAACiC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC2K,oBAAoB,CAAC;MAC9D;MACA,IAAI,CAACE,0BAA0B,CAAC9hB,IAAI,CAAC,CAAC;MACtC;MACA4f,YAAY,CAAC,IAAI,CAACiC,qBAAqB,CAAC;MACxCjC,YAAY,CAAC,IAAI,CAACsE,gBAAgB,CAAC;IACvC;EACJ;EACA;EACAZ,cAAcA,CAAC7W,OAAO,EAAE4G,MAAM,EAAEyP,WAAW,EAAE;IACzC,IAAI,CAACC,WAAW,CAACtW,OAAO,EAAE4G,MAAM,CAAC;IACjC,IAAI,CAACiR,WAAW,CAACxB,WAAW,EAAEzP,MAAM,CAAC;IACrC,IAAI,CAACyQ,gBAAgB,GAAGzQ,MAAM;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI8P,uBAAuBA,CAAC1W,OAAO,EAAE;IAC7B,MAAMkY,OAAO,GAAG,EAAE;IAClB,IAAI,CAAClD,YAAY,CAAC2B,OAAO,CAAC,CAACT,IAAI,EAAEU,cAAc,KAAK;MAChD,IAAIA,cAAc,KAAK5W,OAAO,IAAKkW,IAAI,CAACL,aAAa,IAAIe,cAAc,CAACpH,QAAQ,CAACxP,OAAO,CAAE,EAAE;QACxFkY,OAAO,CAAC/iB,IAAI,CAAC,CAACyhB,cAAc,EAAEV,IAAI,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAOgC,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIZ,gCAAgCA,CAACH,gBAAgB,EAAE;IAC/C,MAAM;MAAExF,iBAAiB,EAAEwG,gBAAgB;MAAE5G;IAAmB,CAAC,GAAG,IAAI,CAACsD,sBAAsB;IAC/F;IACA;IACA;IACA,IAAItD,kBAAkB,KAAK,OAAO,IAC9B,CAAC4G,gBAAgB,IACjBA,gBAAgB,KAAKhB,gBAAgB,IACpCA,gBAAgB,CAACxP,QAAQ,KAAK,OAAO,IAAIwP,gBAAgB,CAACxP,QAAQ,KAAK,UAAW,IACnFwP,gBAAgB,CAAC3U,QAAQ,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAM4V,MAAM,GAAGjB,gBAAgB,CAACiB,MAAM;IACtC,IAAIA,MAAM,EAAE;MACR,KAAK,IAAIrZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqZ,MAAM,CAAC1iB,MAAM,EAAEqJ,CAAC,EAAE,EAAE;QACpC,IAAIqZ,MAAM,CAACrZ,CAAC,CAAC,CAACyQ,QAAQ,CAAC2I,gBAAgB,CAAC,EAAE;UACtC,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB;EACA;IAAS,IAAI,CAAC1X,IAAI,YAAA4X,qBAAA1X,CAAA;MAAA,YAAAA,CAAA,IAAwFiU,YAAY,EAx6DtBtc,sDAAE,CAw6DsCA,iDAAS,GAx6DjDA,sDAAE,CAw6D4Da,2DAAW,GAx6DzEb,sDAAE,CAw6DoFgZ,qBAAqB,GAx6D3GhZ,sDAAE,CAw6DsHD,qDAAQ,MAx6DhIC,sDAAE,CAw6D2Joc,6BAA6B;IAAA,CAA6D;EAAE;EACzV;IAAS,IAAI,CAAC5T,KAAK,kBAz6D6ExI,gEAAE;MAAA0I,KAAA,EAy6DY4T,YAAY;MAAA3T,OAAA,EAAZ2T,YAAY,CAAAnU,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACvJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA36DoG7I,+DAAE,CA26DXsc,YAAY,EAAc,CAAC;IAC1GvT,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAElI,2DAAW0H;IAAC,CAAC,EAAE;MAAEQ,IAAI,EAAEiQ;IAAsB,CAAC,EAAE;MAAEjQ,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAC3ID,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,qDAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACqd,6BAA6B;MACxC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4D,eAAe,CAAC;EAClBzjB,WAAWA,CAACqY,WAAW,EAAEqL,aAAa,EAAE;IACpC,IAAI,CAACrL,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACqL,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,cAAc,GAAG,IAAIzf,uDAAY,CAAC,CAAC;EAC5C;EACA,IAAI0f,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,YAAY;EAC5B;EACAG,eAAeA,CAAA,EAAG;IACd,MAAM3Y,OAAO,GAAG,IAAI,CAACkN,WAAW,CAACG,aAAa;IAC9C,IAAI,CAACuL,oBAAoB,GAAG,IAAI,CAACL,aAAa,CACzC3C,OAAO,CAAC5V,OAAO,EAAEA,OAAO,CAACO,QAAQ,KAAK,CAAC,IAAIP,OAAO,CAACkH,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAC1FxT,SAAS,CAACkT,MAAM,IAAI;MACrB,IAAI,CAAC4R,YAAY,GAAG5R,MAAM;MAC1B,IAAI,CAAC6R,cAAc,CAACI,IAAI,CAACjS,MAAM,CAAC;IACpC,CAAC,CAAC;EACN;EACAhI,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2Z,aAAa,CAACnC,cAAc,CAAC,IAAI,CAAClJ,WAAW,CAAC;IACnD,IAAI,IAAI,CAAC0L,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAACvlB,WAAW,CAAC,CAAC;IAC3C;EACJ;EACA;IAAS,IAAI,CAACoN,IAAI,YAAAqY,wBAAAnY,CAAA;MAAA,YAAAA,CAAA,IAAwF2X,eAAe,EA39DzBhgB,+DAAE,CA29DyCA,qDAAa,GA39DxDA,+DAAE,CA29DmEsc,YAAY;IAAA,CAA4C;EAAE;EAC/N;IAAS,IAAI,CAAC7G,IAAI,kBA59D8EzV,+DAAE;MAAA+I,IAAA,EA49DJiX,eAAe;MAAArK,SAAA;MAAA8K,OAAA;QAAAN,cAAA;MAAA;MAAAtK,QAAA;IAAA,EAA+J;EAAE;AAClR;AACA;EAAA,QAAAhN,SAAA,oBAAAA,SAAA,KA99DoG7I,+DAAE,CA89DXggB,eAAe,EAAc,CAAC;IAC7GjX,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,oDAAoD;MAC9DH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9M,IAAI,EAAE/I,qDAAawV;IAAC,CAAC,EAAE;MAAEzM,IAAI,EAAEuT;IAAa,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE6D,cAAc,EAAE,CAAC;MAC1HpX,IAAI,EAAEpI,iDAAMA;IAChB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM+f,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,mCAAmC,GAAG,0BAA0B;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3BtkB,WAAWA,CAACmI,SAAS,EAAE0U,QAAQ,EAAE;IAC7B,IAAI,CAAC1U,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,SAAS,GAAG2U,QAAQ;IACzB,IAAI,CAAC0H,uBAAuB,GAAG7gB,qDAAM,CAACiD,oEAAkB,CAAC,CACpD4Y,OAAO,CAAC,yBAAyB,CAAC,CAClC1gB,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAAC2lB,2BAA2B,EAAE;QAClC,IAAI,CAACA,2BAA2B,GAAG,KAAK;QACxC,IAAI,CAACC,oCAAoC,CAAC,CAAC;MAC/C;IACJ,CAAC,CAAC;EACN;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACvc,SAAS,CAAC8C,SAAS,EAAE;MAC3B,OAAO,CAAC,CAAC;IACb;IACA;IACA;IACA;IACA,MAAM0Z,WAAW,GAAG,IAAI,CAACzc,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IACvDsa,WAAW,CAAC9Z,KAAK,CAAC+Z,eAAe,GAAG,YAAY;IAChDD,WAAW,CAAC9Z,KAAK,CAACga,QAAQ,GAAG,UAAU;IACvC,IAAI,CAAC3c,SAAS,CAACgD,IAAI,CAACV,WAAW,CAACma,WAAW,CAAC;IAC5C;IACA;IACA;IACA;IACA,MAAMG,cAAc,GAAG,IAAI,CAAC5c,SAAS,CAACuM,WAAW,IAAIf,MAAM;IAC3D,MAAMqR,aAAa,GAAGD,cAAc,IAAIA,cAAc,CAACtS,gBAAgB,GACjEsS,cAAc,CAACtS,gBAAgB,CAACmS,WAAW,CAAC,GAC5C,IAAI;IACV,MAAMK,aAAa,GAAG,CAAED,aAAa,IAAIA,aAAa,CAACH,eAAe,IAAK,EAAE,EAAEK,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAChGN,WAAW,CAAC7a,MAAM,CAAC,CAAC;IACpB,QAAQkb,aAAa;MACjB;MACA,KAAK,YAAY;MACjB;MACA,KAAK,eAAe;MACpB,KAAK,eAAe;QAChB,OAAO,CAAC,CAAC;MACb;MACA,KAAK,kBAAkB;MACvB;MACA,KAAK,kBAAkB;QACnB,OAAO,CAAC,CAAC;IACjB;;IACA,OAAO,CAAC,CAAC;EACb;;EACAjb,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwa,uBAAuB,CAAC/lB,WAAW,CAAC,CAAC;EAC9C;EACA;EACAimB,oCAAoCA,CAAA,EAAG;IACnC,IAAI,CAAC,IAAI,CAACD,2BAA2B,IAAI,IAAI,CAACrc,SAAS,CAAC8C,SAAS,IAAI,IAAI,CAAC/C,SAAS,CAACgD,IAAI,EAAE;MACtF,MAAMga,WAAW,GAAG,IAAI,CAAChd,SAAS,CAACgD,IAAI,CAACH,SAAS;MACjDma,WAAW,CAACpb,MAAM,CAACua,mCAAmC,EAAEF,wBAAwB,EAAEC,wBAAwB,CAAC;MAC3G,IAAI,CAACI,2BAA2B,GAAG,IAAI;MACvC,MAAMW,IAAI,GAAG,IAAI,CAACT,mBAAmB,CAAC,CAAC;MACvC,IAAIS,IAAI,KAAK,CAAC,CAAC,uCAAuC;QAClDD,WAAW,CAACla,GAAG,CAACqZ,mCAAmC,EAAEF,wBAAwB,CAAC;MAClF,CAAC,MACI,IAAIgB,IAAI,KAAK,CAAC,CAAC,uCAAuC;QACvDD,WAAW,CAACla,GAAG,CAACqZ,mCAAmC,EAAED,wBAAwB,CAAC;MAClF;IACJ;EACJ;EACA;IAAS,IAAI,CAACxY,IAAI,YAAAwZ,iCAAAtZ,CAAA;MAAA,YAAAA,CAAA,IAAwFwY,wBAAwB,EA7jElC7gB,sDAAE,CA6jEkDa,2DAAW,GA7jE/Db,sDAAE,CA6jE0ED,qDAAQ;IAAA,CAA6C;EAAE;EACnO;IAAS,IAAI,CAACyI,KAAK,kBA9jE6ExI,gEAAE;MAAA0I,KAAA,EA8jEYmY,wBAAwB;MAAAlY,OAAA,EAAxBkY,wBAAwB,CAAA1Y,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACnK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhkEoG7I,+DAAE,CAgkEX6gB,wBAAwB,EAAc,CAAC;IACtH9X,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElI,2DAAW0H;IAAC,CAAC,EAAE;MAAEQ,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QACrFD,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,qDAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM6hB,UAAU,CAAC;EACbrlB,WAAWA,CAACslB,wBAAwB,EAAE;IAClCA,wBAAwB,CAACb,oCAAoC,CAAC,CAAC;EACnE;EACA;IAAS,IAAI,CAAC7Y,IAAI,YAAA2Z,mBAAAzZ,CAAA;MAAA,YAAAA,CAAA,IAAwFuZ,UAAU,EA5kEpB5hB,sDAAE,CA4kEoC6gB,wBAAwB;IAAA,CAA2C;EAAE;EAC3M;IAAS,IAAI,CAACkB,IAAI,kBA7kE8E/hB,8DAAE;MAAA+I,IAAA,EA6kES6Y;IAAU,EAAkJ;EAAE;EACzQ;IAAS,IAAI,CAACK,IAAI,kBA9kE8EjiB,8DAAE;MAAAmiB,OAAA,GA8kE+Blf,oEAAe;IAAA,EAAI;EAAE;AAC1J;AACA;EAAA,QAAA4F,SAAA,oBAAAA,SAAA,KAhlEoG7I,+DAAE,CAglEX4hB,UAAU,EAAc,CAAC;IACxG7Y,IAAI,EAAEnI,mDAAQ;IACd7B,IAAI,EAAE,CAAC;MACCojB,OAAO,EAAE,CAAClf,oEAAe,CAAC;MAC1Bmf,YAAY,EAAE,CAAC1G,WAAW,EAAElH,YAAY,EAAEwL,eAAe,CAAC;MAC1DqC,OAAO,EAAE,CAAC3G,WAAW,EAAElH,YAAY,EAAEwL,eAAe;IACxD,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjX,IAAI,EAAE8X;IAAyB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExF;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACn1EoC;AACmG;AAC5F;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyB,YAAY,GAAG,IAAI9hB,yDAAc,CAAC,aAAa,EAAE;EACnDoI,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAE4Z;AACb,CAAC,CAAC;AACF;AACA,SAASA,oBAAoBA,CAAA,EAAG;EAC5B,OAAOtiB,qDAAM,CAACF,qDAAQ,CAAC;AAC3B;;AAEA;AACA,MAAMyiB,kBAAkB,GAAG,oHAAoH;AAC/I;AACA,SAASC,sBAAsBA,CAACC,QAAQ,EAAE;EACtC,MAAM1nB,KAAK,GAAG0nB,QAAQ,EAAEpT,WAAW,CAAC,CAAC,IAAI,EAAE;EAC3C,IAAItU,KAAK,KAAK,MAAM,IAAI,OAAO2nB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAEC,QAAQ,EAAE;IAC7E,OAAOJ,kBAAkB,CAACK,IAAI,CAACF,SAAS,CAACC,QAAQ,CAAC,GAAG,KAAK,GAAG,KAAK;EACtE;EACA,OAAO5nB,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;AAC1C;AACA;AACA;AACA;AACA;AACA,MAAM8nB,cAAc,CAAC;EACjBvmB,WAAWA,CAACkI,SAAS,EAAE;IACnB;IACA,IAAI,CAACzJ,KAAK,GAAG,KAAK;IAClB;IACA,IAAI,CAACqP,MAAM,GAAG,IAAI3J,uDAAY,CAAC,CAAC;IAChC,IAAI+D,SAAS,EAAE;MACX,MAAMse,OAAO,GAAGte,SAAS,CAACgD,IAAI,GAAGhD,SAAS,CAACgD,IAAI,CAACub,GAAG,GAAG,IAAI;MAC1D,MAAMC,OAAO,GAAGxe,SAAS,CAACye,eAAe,GAAGze,SAAS,CAACye,eAAe,CAACF,GAAG,GAAG,IAAI;MAChF,IAAI,CAAChoB,KAAK,GAAGynB,sBAAsB,CAACM,OAAO,IAAIE,OAAO,IAAI,KAAK,CAAC;IACpE;EACJ;EACA3c,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+D,MAAM,CAACnP,QAAQ,CAAC,CAAC;EAC1B;EACA;IAAS,IAAI,CAACiN,IAAI,YAAAgb,uBAAA9a,CAAA;MAAA,YAAAA,CAAA,IAAwFya,cAAc,EAAxB9iB,sDAAE,CAAwCsiB,YAAY;IAAA,CAA6D;EAAE;EACrN;IAAS,IAAI,CAAC9Z,KAAK,kBAD6ExI,gEAAE;MAAA0I,KAAA,EACYoa,cAAc;MAAAna,OAAA,EAAdma,cAAc,CAAA3a,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACzJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG7I,+DAAE,CAGX8iB,cAAc,EAAc,CAAC;IAC5G/Z,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAC9DD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACujB,YAAY;MACvB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,GAAG,CAAC;EACN7mB,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC8mB,IAAI,GAAG,KAAK;IACjB;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACjZ,MAAM,GAAG,IAAI3J,uDAAY,CAAC,CAAC;EACpC;EACA;EACA,IAAIsiB,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACK,IAAI;EACpB;EACA,IAAIL,GAAGA,CAAChoB,KAAK,EAAE;IACX,MAAMuoB,aAAa,GAAG,IAAI,CAACF,IAAI;IAC/B;IACA;IACA;IACA,IAAI,CAACA,IAAI,GAAGZ,sBAAsB,CAACznB,KAAK,CAAC;IACzC,IAAI,CAACwoB,OAAO,GAAGxoB,KAAK;IACpB,IAAIuoB,aAAa,KAAK,IAAI,CAACF,IAAI,IAAI,IAAI,CAACC,cAAc,EAAE;MACpD,IAAI,CAACjZ,MAAM,CAACkW,IAAI,CAAC,IAAI,CAAC8C,IAAI,CAAC;IAC/B;EACJ;EACA;EACA,IAAIroB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACgoB,GAAG;EACnB;EACA;EACAhO,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACsO,cAAc,GAAG,IAAI;EAC9B;EACAhd,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+D,MAAM,CAACnP,QAAQ,CAAC,CAAC;EAC1B;EACA;IAAS,IAAI,CAACiN,IAAI,YAAAsb,YAAApb,CAAA;MAAA,YAAAA,CAAA,IAAwF+a,GAAG;IAAA,CAAmD;EAAE;EAClK;IAAS,IAAI,CAAC3N,IAAI,kBAvD8EzV,+DAAE;MAAA+I,IAAA,EAuDJqa,GAAG;MAAAzN,SAAA;MAAA+N,QAAA;MAAAC,YAAA,WAAAC,iBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvDD7jB,yDAAE,QAAA8jB,GAAA,CAAAN,OAAA;QAAA;MAAA;MAAA5N,MAAA;QAAAoN,GAAA;MAAA;MAAAvC,OAAA;QAAApW,MAAA;MAAA;MAAAwL,QAAA;MAAAC,QAAA,GAAF9V,gEAAE,CAuD0I,CAAC;QAAEikB,OAAO,EAAEnB,cAAc;QAAEoB,WAAW,EAAEd;MAAI,CAAC,CAAC;IAAA,EAAoC;EAAE;AACrU;AACA;EAAA,QAAAva,SAAA,oBAAAA,SAAA,KAzDoG7I,+DAAE,CAyDXojB,GAAG,EAAc,CAAC;IACjGra,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,OAAO;MACjBmO,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEnB,cAAc;QAAEoB,WAAW,EAAEd;MAAI,CAAC,CAAC;MAC1DgB,IAAI,EAAE;QAAE,YAAY,EAAE;MAAU,CAAC;MACjCvO,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAExL,MAAM,EAAE,CAAC;MACvBtB,IAAI,EAAEpI,iDAAM;MACZ5B,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEikB,GAAG,EAAE,CAAC;MACNja,IAAI,EAAExI,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8jB,UAAU,CAAC;EACb;IAAS,IAAI,CAAClc,IAAI,YAAAmc,mBAAAjc,CAAA;MAAA,YAAAA,CAAA,IAAwFgc,UAAU;IAAA,CAAkD;EAAE;EACxK;IAAS,IAAI,CAACtC,IAAI,kBA1E8E/hB,8DAAE;MAAA+I,IAAA,EA0ESsb;IAAU,EAAwC;EAAE;EAC/J;IAAS,IAAI,CAACpC,IAAI,kBA3E8EjiB,8DAAE,IA2EsB;EAAE;AAC9H;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KA7EoG7I,+DAAE,CA6EXqkB,UAAU,EAAc,CAAC;IACxGtb,IAAI,EAAEnI,mDAAQ;IACd7B,IAAI,EAAE,CAAC;MACCsjB,OAAO,EAAE,CAACe,GAAG,CAAC;MACdhB,YAAY,EAAE,CAACgB,GAAG;IACtB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;AChJ2C;;AAE3C;AACA,SAAStgB,qBAAqBA,CAAC9H,KAAK,EAAE;EAClC,OAAOA,KAAK,IAAI,IAAI,IAAK,GAAEA,KAAM,EAAC,KAAK,OAAO;AAClD;AAEA,SAASupB,oBAAoBA,CAACvpB,KAAK,EAAEwpB,aAAa,GAAG,CAAC,EAAE;EACpD,OAAOC,cAAc,CAACzpB,KAAK,CAAC,GAAG0pB,MAAM,CAAC1pB,KAAK,CAAC,GAAGwpB,aAAa;AAChE;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACzpB,KAAK,EAAE;EAC3B;EACA;EACA;EACA,OAAO,CAAC2V,KAAK,CAACgU,UAAU,CAAC3pB,KAAK,CAAC,CAAC,IAAI,CAAC2V,KAAK,CAAC+T,MAAM,CAAC1pB,KAAK,CAAC,CAAC;AAC7D;AAEA,SAAS4pB,WAAWA,CAAC5pB,KAAK,EAAE;EACxB,OAAO6pB,KAAK,CAACC,OAAO,CAAC9pB,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AACjD;;AAEA;AACA,SAAS+pB,mBAAmBA,CAAC/pB,KAAK,EAAE;EAChC,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAO,EAAE;EACb;EACA,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAI,GAAEA,KAAM,IAAG;AAC3D;;AAEA;AACA;AACA;AACA;AACA,SAAS+H,aAAaA,CAACiiB,YAAY,EAAE;EACjC,OAAOA,YAAY,YAAYxP,qDAAU,GAAGwP,YAAY,CAACjQ,aAAa,GAAGiQ,YAAY;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACjqB,KAAK,EAAEkqB,SAAS,GAAG,KAAK,EAAE;EACjD,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAInqB,KAAK,IAAI,IAAI,EAAE;IACf,MAAMoqB,YAAY,GAAGP,KAAK,CAACC,OAAO,CAAC9pB,KAAK,CAAC,GAAGA,KAAK,GAAI,GAAEA,KAAM,EAAC,CAACqqB,KAAK,CAACH,SAAS,CAAC;IAC/E,KAAK,MAAMI,WAAW,IAAIF,YAAY,EAAE;MACpC,MAAMG,aAAa,GAAI,GAAED,WAAY,EAAC,CAAC3hB,IAAI,CAAC,CAAC;MAC7C,IAAI4hB,aAAa,EAAE;QACfJ,MAAM,CAACtoB,IAAI,CAAC0oB,aAAa,CAAC;MAC9B;IACJ;EACJ;EACA,OAAOJ,MAAM;AACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtEwE;AACpC;AACuB;AAE3D,MAAMO,UAAU,CAAC;AAEjB;AACA,SAASC,YAAYA,CAAC3qB,KAAK,EAAE;EACzB;EACA;EACA;EACA;EACA,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAAC4qB,OAAO,KAAK,UAAU,IAAI,EAAE5qB,KAAK,YAAYwqB,uDAAqB,CAAC;AACpG;;AAEA;AACA,MAAMK,eAAe,SAASH,UAAU,CAAC;EACrCnpB,WAAWA,CAACupB,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAF,OAAOA,CAAA,EAAG;IACN,OAAOH,kDAAY,CAAC,IAAI,CAACK,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG1kB,wCAAE,CAAC,IAAI,CAAC0kB,KAAK,CAAC;EACjE;EACAC,UAAUA,CAAA,EAAG,CAAE;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/BC,YAAYA,CAAC1b,OAAO,EAAE2b,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5F9b,OAAO,CAAC+b,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B,MAAMC,aAAa,GAAGV,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QACrFC,IAAI,GAAGR,gBAAgB,CAACY,kBAAkB,CAACD,aAAa,CAACE,WAAW,EAAEF,aAAa,CAACG,OAAO,EAAEH,aAAa,CAAC1qB,KAAK,CAAC;QACjHwqB,SAAS,GAAG,CAAC,CAAC;MAClB,CAAC,MACI,IAAIF,YAAY,IAAI,IAAI,EAAE;QAC3BP,gBAAgB,CAAC7f,MAAM,CAACmgB,qBAAqB,CAAC;QAC9CG,SAAS,GAAG,CAAC,CAAC;MAClB,CAAC,MACI;QACDD,IAAI,GAAGR,gBAAgB,CAAChgB,GAAG,CAACsgB,qBAAqB,CAAC;QAClDN,gBAAgB,CAACe,IAAI,CAACP,IAAI,EAAED,YAAY,CAAC;QACzCE,SAAS,GAAG,CAAC,CAAC;MAClB;;MACA,IAAIN,eAAe,EAAE;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,EAAEM,OAAO;UACtBL,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAW,MAAMA,CAAA,EAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/B5qB,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAAC6qB,aAAa,GAAG,EAAE;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,EAAE;EACxB;EACA;EACApB,YAAYA,CAAC1b,OAAO,EAAE2b,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5F;IACA9b,OAAO,CAAC+b,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B;QACA,MAAMU,eAAe,GAAGA,CAAA,KAAMnB,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QAC7FC,IAAI,GAAG,IAAI,CAACa,WAAW,CAACD,eAAe,EAAEb,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACnGI,SAAS,GAAGD,IAAI,GAAG,CAAC,CAAC,wCAAwC,CAAC,CAAC;MACnE,CAAC,MACI,IAAID,YAAY,IAAI,IAAI,EAAE;QAC3B;QACA,IAAI,CAACe,mBAAmB,CAAChB,qBAAqB,EAAEN,gBAAgB,CAAC;QACjES,SAAS,GAAG,CAAC,CAAC;MAClB,CAAC,MACI;QACD;QACAD,IAAI,GAAG,IAAI,CAACe,SAAS,CAACjB,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACvGI,SAAS,GAAG,CAAC,CAAC;MAClB;;MACA,IAAIN,eAAe,EAAE;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,EAAEM,OAAO;UACtBL,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAW,MAAMA,CAAA,EAAG;IACL,KAAK,MAAMR,IAAI,IAAI,IAAI,CAACW,UAAU,EAAE;MAChCX,IAAI,CAAC/Y,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,CAAC0Z,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIE,WAAWA,CAACD,eAAe,EAAEb,YAAY,EAAEP,gBAAgB,EAAElrB,KAAK,EAAE;IAChE,MAAM0sB,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAAClB,YAAY,EAAEP,gBAAgB,CAAC;IAC5E,IAAIwB,UAAU,EAAE;MACZA,UAAU,CAACV,OAAO,CAACY,SAAS,GAAG5sB,KAAK;MACpC,OAAOiC,SAAS;IACpB;IACA,MAAM4qB,QAAQ,GAAGP,eAAe,CAAC,CAAC;IAClC,OAAOpB,gBAAgB,CAACY,kBAAkB,CAACe,QAAQ,CAACd,WAAW,EAAEc,QAAQ,CAACb,OAAO,EAAEa,QAAQ,CAAC1rB,KAAK,CAAC;EACtG;EACA;EACAqrB,mBAAmBA,CAACrrB,KAAK,EAAE+pB,gBAAgB,EAAE;IACzC,MAAM4B,YAAY,GAAG5B,gBAAgB,CAACgB,MAAM,CAAC/qB,KAAK,CAAC;IACnD,IAAI,CAAC4rB,eAAe,CAACD,YAAY,EAAE5B,gBAAgB,CAAC;EACxD;EACA;EACAuB,SAASA,CAACjB,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAElrB,KAAK,EAAE;IACpE,MAAM0rB,IAAI,GAAGR,gBAAgB,CAAChgB,GAAG,CAACsgB,qBAAqB,CAAC;IACxDN,gBAAgB,CAACe,IAAI,CAACP,IAAI,EAAED,YAAY,CAAC;IACzCC,IAAI,CAACM,OAAO,CAACY,SAAS,GAAG5sB,KAAK;IAC9B,OAAO0rB,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIqB,eAAeA,CAACrB,IAAI,EAAER,gBAAgB,EAAE;IACpC,IAAI,IAAI,CAACmB,UAAU,CAACjqB,MAAM,GAAG,IAAI,CAACgqB,aAAa,EAAE;MAC7C,IAAI,CAACC,UAAU,CAACxqB,IAAI,CAAC6pB,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,MAAMvqB,KAAK,GAAG+pB,gBAAgB,CAACte,OAAO,CAAC8e,IAAI,CAAC;MAC5C;MACA;MACA;MACA;MACA,IAAIvqB,KAAK,KAAK,CAAC,CAAC,EAAE;QACduqB,IAAI,CAAC/Y,OAAO,CAAC,CAAC;MAClB,CAAC,MACI;QACDuY,gBAAgB,CAAC7f,MAAM,CAAClK,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;EACAwrB,oBAAoBA,CAACxrB,KAAK,EAAE+pB,gBAAgB,EAAE;IAC1C,MAAMwB,UAAU,GAAG,IAAI,CAACL,UAAU,CAACW,GAAG,CAAC,CAAC;IACxC,IAAIN,UAAU,EAAE;MACZxB,gBAAgB,CAAC+B,MAAM,CAACP,UAAU,EAAEvrB,KAAK,CAAC;IAC9C;IACA,OAAOurB,UAAU,IAAI,IAAI;EAC7B;AACJ;;AAEA;AACA;AACA;AACA,MAAMQ,cAAc,CAAC;EACjB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAGvD,KAAK,CAACwD,IAAI,CAAC,IAAI,CAACC,UAAU,CAACC,MAAM,CAAC,CAAC,CAAC;IACzD;IACA,OAAO,IAAI,CAACH,SAAS;EACzB;EACA7rB,WAAWA,CAACisB,SAAS,GAAG,KAAK,EAAEC,uBAAuB,EAAEC,YAAY,GAAG,IAAI,EAAEC,WAAW,EAAE;IACtF,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACL,UAAU,GAAG,IAAIM,GAAG,CAAC,CAAC;IAC3B;IACA,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;IACA,IAAI,CAACC,OAAO,GAAG,IAAI7nB,yCAAO,CAAC,CAAC;IAC5B,IAAIunB,uBAAuB,IAAIA,uBAAuB,CAACrrB,MAAM,EAAE;MAC3D,IAAIorB,SAAS,EAAE;QACXC,uBAAuB,CAACpK,OAAO,CAACrjB,KAAK,IAAI,IAAI,CAACguB,aAAa,CAAChuB,KAAK,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAACguB,aAAa,CAACP,uBAAuB,CAAC,CAAC,CAAC,CAAC;MAClD;MACA;MACA,IAAI,CAACK,eAAe,CAAC1rB,MAAM,GAAG,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6rB,MAAMA,CAAC,GAAGV,MAAM,EAAE;IACd,IAAI,CAACW,sBAAsB,CAACX,MAAM,CAAC;IACnCA,MAAM,CAAClK,OAAO,CAACrjB,KAAK,IAAI,IAAI,CAACguB,aAAa,CAAChuB,KAAK,CAAC,CAAC;IAClD,MAAM+tB,OAAO,GAAG,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOL,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,QAAQA,CAAC,GAAGd,MAAM,EAAE;IAChB,IAAI,CAACW,sBAAsB,CAACX,MAAM,CAAC;IACnCA,MAAM,CAAClK,OAAO,CAACrjB,KAAK,IAAI,IAAI,CAACsuB,eAAe,CAACtuB,KAAK,CAAC,CAAC;IACpD,MAAM+tB,OAAO,GAAG,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOL,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIQ,YAAYA,CAAC,GAAGhB,MAAM,EAAE;IACpB,IAAI,CAACW,sBAAsB,CAACX,MAAM,CAAC;IACnC,MAAMiB,SAAS,GAAG,IAAI,CAACrB,QAAQ;IAC/B,MAAMsB,cAAc,GAAG,IAAIb,GAAG,CAACL,MAAM,CAAC;IACtCA,MAAM,CAAClK,OAAO,CAACrjB,KAAK,IAAI,IAAI,CAACguB,aAAa,CAAChuB,KAAK,CAAC,CAAC;IAClDwuB,SAAS,CACJztB,MAAM,CAACf,KAAK,IAAI,CAACyuB,cAAc,CAAC/jB,GAAG,CAAC1K,KAAK,CAAC,CAAC,CAC3CqjB,OAAO,CAACrjB,KAAK,IAAI,IAAI,CAACsuB,eAAe,CAACtuB,KAAK,CAAC,CAAC;IAClD,MAAM+tB,OAAO,GAAG,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOL,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9J,MAAMA,CAACjkB,KAAK,EAAE;IACV,OAAO,IAAI,CAAC0uB,UAAU,CAAC1uB,KAAK,CAAC,GAAG,IAAI,CAACquB,QAAQ,CAACruB,KAAK,CAAC,GAAG,IAAI,CAACiuB,MAAM,CAACjuB,KAAK,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2L,KAAKA,CAACgjB,UAAU,GAAG,IAAI,EAAE;IACrB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,MAAMb,OAAO,GAAG,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxC,IAAIQ,UAAU,EAAE;MACZ,IAAI,CAACP,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAOL,OAAO;EAClB;EACA;AACJ;AACA;EACIW,UAAUA,CAAC1uB,KAAK,EAAE;IACd,OAAO,IAAI,CAACstB,UAAU,CAAC5iB,GAAG,CAAC,IAAI,CAACmkB,iBAAiB,CAAC7uB,KAAK,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;EACI8uB,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACxB,UAAU,CAACyB,IAAI,KAAK,CAAC;EACrC;EACA;AACJ;AACA;EACIrvB,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACovB,OAAO,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;EACIE,IAAIA,CAACnf,SAAS,EAAE;IACZ,IAAI,IAAI,CAAC2d,SAAS,IAAI,IAAI,CAACL,QAAQ,EAAE;MACjC,IAAI,CAACC,SAAS,CAAC4B,IAAI,CAACnf,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;EACIof,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACzB,SAAS;EACzB;EACA;EACAY,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAI,CAAChB,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACU,eAAe,CAAC1rB,MAAM,IAAI,IAAI,CAACyrB,iBAAiB,CAACzrB,MAAM,EAAE;MAC9D,IAAI,CAAC2rB,OAAO,CAAC9tB,IAAI,CAAC;QACdT,MAAM,EAAE,IAAI;QACZ0vB,KAAK,EAAE,IAAI,CAACpB,eAAe;QAC3BqB,OAAO,EAAE,IAAI,CAACtB;MAClB,CAAC,CAAC;MACF,IAAI,CAACA,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACC,eAAe,GAAG,EAAE;IAC7B;EACJ;EACA;EACAE,aAAaA,CAAChuB,KAAK,EAAE;IACjBA,KAAK,GAAG,IAAI,CAAC6uB,iBAAiB,CAAC7uB,KAAK,CAAC;IACrC,IAAI,CAAC,IAAI,CAAC0uB,UAAU,CAAC1uB,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC,IAAI,CAACwtB,SAAS,EAAE;QACjB,IAAI,CAACoB,UAAU,CAAC,CAAC;MACrB;MACA,IAAI,CAAC,IAAI,CAACF,UAAU,CAAC1uB,KAAK,CAAC,EAAE;QACzB,IAAI,CAACstB,UAAU,CAAC/gB,GAAG,CAACvM,KAAK,CAAC;MAC9B;MACA,IAAI,IAAI,CAAC0tB,YAAY,EAAE;QACnB,IAAI,CAACI,eAAe,CAACjsB,IAAI,CAAC7B,KAAK,CAAC;MACpC;IACJ;EACJ;EACA;EACAsuB,eAAeA,CAACtuB,KAAK,EAAE;IACnBA,KAAK,GAAG,IAAI,CAAC6uB,iBAAiB,CAAC7uB,KAAK,CAAC;IACrC,IAAI,IAAI,CAAC0uB,UAAU,CAAC1uB,KAAK,CAAC,EAAE;MACxB,IAAI,CAACstB,UAAU,CAACthB,MAAM,CAAChM,KAAK,CAAC;MAC7B,IAAI,IAAI,CAAC0tB,YAAY,EAAE;QACnB,IAAI,CAACG,iBAAiB,CAAChsB,IAAI,CAAC7B,KAAK,CAAC;MACtC;IACJ;EACJ;EACA;EACA4uB,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACE,OAAO,CAAC,CAAC,EAAE;MACjB,IAAI,CAACxB,UAAU,CAACjK,OAAO,CAACrjB,KAAK,IAAI,IAAI,CAACsuB,eAAe,CAACtuB,KAAK,CAAC,CAAC;IACjE;EACJ;EACA;AACJ;AACA;AACA;EACIkuB,sBAAsBA,CAACX,MAAM,EAAE;IAC3B,IAAIA,MAAM,CAACnrB,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACorB,SAAS,KAAK,OAAO3f,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzF,MAAMuhB,uCAAuC,CAAC,CAAC;IACnD;EACJ;EACA;EACAjB,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,EAAE,IAAI,CAACN,iBAAiB,CAACzrB,MAAM,IAAI,IAAI,CAAC0rB,eAAe,CAAC1rB,MAAM,CAAC;EAC3E;EACA;EACAysB,iBAAiBA,CAACQ,UAAU,EAAE;IAC1B,IAAI,CAAC,IAAI,CAAC1B,WAAW,EAAE;MACnB,OAAO0B,UAAU;IACrB,CAAC,MACI;MACD,KAAK,IAAIC,aAAa,IAAI,IAAI,CAAChC,UAAU,EAAE;QACvC,IAAI,IAAI,CAACK,WAAW,CAAC0B,UAAU,EAAEC,aAAa,CAAC,EAAE;UAC7C,OAAOA,aAAa;QACxB;MACJ;MACA,OAAOD,UAAU;IACrB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,uCAAuCA,CAAA,EAAG;EAC/C,OAAO5e,KAAK,CAAC,yEAAyE,CAAC;AAC3F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+e,yBAAyB,CAAC;EAC5BhuB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiuB,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAAC/tB,EAAE,EAAEguB,IAAI,EAAE;IACb,KAAK,IAAIC,QAAQ,IAAI,IAAI,CAACH,UAAU,EAAE;MAClCG,QAAQ,CAACjuB,EAAE,EAAEguB,IAAI,CAAC;IACtB;EACJ;EACA;AACJ;AACA;AACA;EACIE,MAAMA,CAACD,QAAQ,EAAE;IACb,IAAI,CAACH,UAAU,CAAC3tB,IAAI,CAAC8tB,QAAQ,CAAC;IAC9B,OAAO,MAAM;MACT,IAAI,CAACH,UAAU,GAAG,IAAI,CAACA,UAAU,CAACzuB,MAAM,CAAE8uB,UAAU,IAAK;QACrD,OAAOF,QAAQ,KAAKE,UAAU;MAClC,CAAC,CAAC;IACN,CAAC;EACL;EACAvkB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkkB,UAAU,GAAG,EAAE;EACxB;EACA;IAAS,IAAI,CAACriB,IAAI,YAAA2iB,kCAAAziB,CAAA;MAAA,YAAAA,CAAA,IAAwFkiB,yBAAyB;IAAA,CAAoD;EAAE;EACzL;IAAS,IAAI,CAAC/hB,KAAK,kBAD6ExI,gEAAE;MAAA0I,KAAA,EACY6hB,yBAAyB;MAAA5hB,OAAA,EAAzB4hB,yBAAyB,CAAApiB,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACpK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG7I,+DAAE,CAGXuqB,yBAAyB,EAAc,CAAC;IACvHxhB,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMmiB,uBAAuB,GAAG,IAAIvqB,yDAAc,CAAC,eAAe,CAAC;;AAEnE;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1cA,MAAMwqB,SAAS,GAAG,CAAC;AACnB,MAAMC,SAAS,GAAG,CAAC;AACnB,MAAM/oB,GAAG,GAAG,CAAC;AACb,MAAMgpB,UAAU,GAAG,EAAE;AACrB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAM5oB,KAAK,GAAG,EAAE;AAChB,MAAMH,OAAO,GAAG,EAAE;AAClB,MAAMD,GAAG,GAAG,EAAE;AACd,MAAMipB,KAAK,GAAG,EAAE;AAChB,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,MAAM,GAAG,EAAE;AACjB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAM5pB,OAAO,GAAG,EAAE;AAClB,MAAMD,SAAS,GAAG,EAAE;AACpB,MAAME,GAAG,GAAG,EAAE;AACd,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAME,QAAQ,GAAG,EAAE;AACnB,MAAMD,WAAW,GAAG,EAAE;AACtB,MAAME,UAAU,GAAG,EAAE;AACrB,MAAMupB,SAAS,GAAG,EAAE;AACpB,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,MAAM,GAAG,EAAE;AACjB,MAAMC,MAAM,GAAG,EAAE;AACjB,MAAMnqB,IAAI,GAAG,EAAE;AACf,MAAMoqB,GAAG,GAAG,EAAE;AACd,MAAMC,GAAG,GAAG,EAAE;AACd,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,GAAG,GAAG,EAAE;AACd,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAM1qB,IAAI,GAAG,EAAE;AACf,MAAM2qB,YAAY,GAAG,EAAE,CAAC,CAAC;AACzB,MAAMC,SAAS,GAAG,EAAE,CAAC,CAAC;AACtB,MAAMC,aAAa,GAAG,EAAE;AACxB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMjrB,CAAC,GAAG,EAAE;AACZ,MAAMkrB,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMC,CAAC,GAAG,EAAE;AACZ,MAAMxsB,CAAC,GAAG,EAAE;AACZ,MAAMe,IAAI,GAAG,EAAE,CAAC,CAAC;AACjB,MAAM0rB,eAAe,GAAG,EAAE;AAC1B,MAAMC,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,WAAW,GAAG,EAAE;AACtB,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,UAAU,GAAG,GAAG;AACtB,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,eAAe,GAAG,GAAG;AAC3B,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAMC,aAAa,GAAG,GAAG;AACzB,MAAMC,aAAa,GAAG,GAAG;AACzB,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,EAAE,GAAG,GAAG;AACd,MAAMC,GAAG,GAAG,GAAG;AACf,MAAMC,GAAG,GAAG,GAAG;AACf,MAAMC,GAAG,GAAG,GAAG;AACf,MAAMC,QAAQ,GAAG,GAAG;AACpB,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,QAAQ,GAAG,GAAG;AACpB,MAAMC,IAAI,GAAG,GAAG,CAAC,CAAC;AAClB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;AACzB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;AACvB,MAAMC,OAAO,GAAG,GAAG;AACnB,MAAMC,cAAc,GAAG,GAAG;AAC1B,MAAMC,UAAU,GAAG,GAAG;AACtB,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;AACvB,MAAMC,MAAM,GAAG,GAAG,CAAC,CAAC;AACpB,MAAMC,KAAK,GAAG,GAAG;AACjB,MAAMC,IAAI,GAAG,GAAG,CAAC,CAAC;AAClB,MAAMC,MAAM,GAAG,GAAG;AAClB,MAAMC,KAAK,GAAG,GAAG;AACjB,MAAMC,UAAU,GAAG,GAAG;AACtB,MAAMC,KAAK,GAAG,GAAG;AACjB,MAAMC,mBAAmB,GAAG,GAAG;AAC/B,MAAMC,SAAS,GAAG,GAAG;AACrB,MAAMC,oBAAoB,GAAG,GAAG;AAChC,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAM/uB,QAAQ,GAAG,GAAG;;AAEpB;AACA;AACA;AACA;AACA,SAAShB,cAAcA,CAACiL,KAAK,EAAE,GAAGE,SAAS,EAAE;EACzC,IAAIA,SAAS,CAACpP,MAAM,EAAE;IAClB,OAAOoP,SAAS,CAAC/I,IAAI,CAACkJ,QAAQ,IAAIL,KAAK,CAACK,QAAQ,CAAC,CAAC;EACtD;EACA,OAAOL,KAAK,CAAC+kB,MAAM,IAAI/kB,KAAK,CAACglB,QAAQ,IAAIhlB,KAAK,CAACilB,OAAO,IAAIjlB,KAAK,CAACklB,OAAO;AAC3E;;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrIoC;AAC8C;AAC9B;AACc;AACmB;AACzC;AAE5C,MAAMM,YAAY,CAAC;EACf;IAAS,IAAI,CAAC3pB,IAAI,YAAA4pB,qBAAA1pB,CAAA;MAAA,YAAAA,CAAA,IAAwFypB,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAAC/P,IAAI,kBAD8E/hB,8DAAE;MAAA+I,IAAA,EACS+oB;IAAY,EAAG;EAAE;EAC5H;IAAS,IAAI,CAAC7P,IAAI,kBAF8EjiB,8DAAE,IAEwB;EAAE;AAChI;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KAJoG7I,+DAAE,CAIX8xB,YAAY,EAAc,CAAC;IAC1G/oB,IAAI,EAAEnI,mDAAQ;IACd7B,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMizB,kCAAkC,GAAG,IAAIpJ,GAAG,CAAC,CAAC;AACpD;AACA,IAAIqJ,mBAAmB;AACvB;AACA,MAAMC,YAAY,CAAC;EACf31B,WAAWA,CAACmI,SAAS,EAAEytB,MAAM,EAAE;IAC3B,IAAI,CAACztB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACytB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GACZ,IAAI,CAAC1tB,SAAS,CAAC8C,SAAS,IAAIyI,MAAM,CAACoiB,UAAU;IACvC;IACE;IACApiB,MAAM,CAACoiB,UAAU,CAACn0B,IAAI,CAAC+R,MAAM,CAAC,GAChCqiB,cAAc;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACID,UAAUA,CAACE,KAAK,EAAE;IACd,IAAI,IAAI,CAAC7tB,SAAS,CAAC8K,MAAM,IAAI,IAAI,CAAC9K,SAAS,CAAC8tB,KAAK,EAAE;MAC/CC,oBAAoB,CAACF,KAAK,EAAE,IAAI,CAACJ,MAAM,CAAC;IAC5C;IACA,OAAO,IAAI,CAACC,WAAW,CAACG,KAAK,CAAC;EAClC;EACA;IAAS,IAAI,CAACpqB,IAAI,YAAAuqB,qBAAArqB,CAAA;MAAA,YAAAA,CAAA,IAAwF6pB,YAAY,EArCtBlyB,sDAAE,CAqCsCa,2DAAW,GArCnDb,sDAAE,CAqC8DyxB,oDAAS;IAAA,CAA6D;EAAE;EACxO;IAAS,IAAI,CAACjpB,KAAK,kBAtC6ExI,gEAAE;MAAA0I,KAAA,EAsCYwpB,YAAY;MAAAvpB,OAAA,EAAZupB,YAAY,CAAA/pB,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACvJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxCoG7I,+DAAE,CAwCXkyB,YAAY,EAAc,CAAC;IAC1GnpB,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElI,2DAAW0H;IAAC,CAAC,EAAE;MAAEQ,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QACrFD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAAC0yB,oDAAS;MACpB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,oBAAoBA,CAACF,KAAK,EAAEI,KAAK,EAAE;EACxC,IAAIX,kCAAkC,CAACtsB,GAAG,CAAC6sB,KAAK,CAAC,EAAE;IAC/C;EACJ;EACA,IAAI;IACA,IAAI,CAACN,mBAAmB,EAAE;MACtBA,mBAAmB,GAAG7Y,QAAQ,CAACxS,aAAa,CAAC,OAAO,CAAC;MACrD,IAAI+rB,KAAK,EAAE;QACPV,mBAAmB,CAACU,KAAK,GAAGA,KAAK;MACrC;MACAV,mBAAmB,CAACruB,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MACpDwV,QAAQ,CAACwZ,IAAI,CAAC7rB,WAAW,CAACkrB,mBAAmB,CAAC;IAClD;IACA,IAAIA,mBAAmB,CAACY,KAAK,EAAE;MAC3BZ,mBAAmB,CAACY,KAAK,CAACC,UAAU,CAAE,UAASP,KAAM,YAAW,EAAE,CAAC,CAAC;MACpEP,kCAAkC,CAACzqB,GAAG,CAACgrB,KAAK,CAAC;IACjD;EACJ,CAAC,CACD,OAAOzb,CAAC,EAAE;IACN5D,OAAO,CAACvV,KAAK,CAACmZ,CAAC,CAAC;EACpB;AACJ;AACA;AACA,SAASwb,cAAcA,CAACC,KAAK,EAAE;EAC3B;EACA;EACA,OAAO;IACHQ,OAAO,EAAER,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,EAAE;IACxCS,KAAK,EAAET,KAAK;IACZU,WAAW,EAAEA,CAAA,KAAM,CAAE,CAAC;IACtBC,cAAc,EAAEA,CAAA,KAAM,CAAE;EAC5B,CAAC;AACL;;AAEA;AACA,MAAMhwB,kBAAkB,CAAC;EACrB3G,WAAWA,CAAC42B,aAAa,EAAEC,KAAK,EAAE;IAC9B,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIzuB,GAAG,CAAC,CAAC;IACzB;IACA,IAAI,CAAC0uB,eAAe,GAAG,IAAIpyB,yCAAO,CAAC,CAAC;EACxC;EACA;EACAoF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgtB,eAAe,CAACr4B,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACq4B,eAAe,CAACp4B,QAAQ,CAAC,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;EACIq4B,SAASA,CAACv4B,KAAK,EAAE;IACb,MAAMw4B,OAAO,GAAGC,YAAY,CAAC7O,kEAAW,CAAC5pB,KAAK,CAAC,CAAC;IAChD,OAAOw4B,OAAO,CAAC/vB,IAAI,CAACiwB,UAAU,IAAI,IAAI,CAACC,cAAc,CAACD,UAAU,CAAC,CAACE,GAAG,CAACb,OAAO,CAAC;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIjX,OAAOA,CAAC9gB,KAAK,EAAE;IACX,MAAMw4B,OAAO,GAAGC,YAAY,CAAC7O,kEAAW,CAAC5pB,KAAK,CAAC,CAAC;IAChD,MAAM64B,WAAW,GAAGL,OAAO,CAAC9wB,GAAG,CAAC6vB,KAAK,IAAI,IAAI,CAACoB,cAAc,CAACpB,KAAK,CAAC,CAACuB,UAAU,CAAC;IAC/E,IAAIC,eAAe,GAAGrC,mDAAa,CAACmC,WAAW,CAAC;IAChD;IACAE,eAAe,GAAGpC,4CAAM,CAACoC,eAAe,CAACtoB,IAAI,CAAC9I,oDAAI,CAAC,CAAC,CAAC,CAAC,EAAEoxB,eAAe,CAACtoB,IAAI,CAACzP,oDAAI,CAAC,CAAC,CAAC,EAAEyG,4DAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACvG,OAAOsxB,eAAe,CAACtoB,IAAI,CAAC/I,mDAAG,CAACsxB,gBAAgB,IAAI;MAChD,MAAMC,QAAQ,GAAG;QACblB,OAAO,EAAE,KAAK;QACdmB,WAAW,EAAE,CAAC;MAClB,CAAC;MACDF,gBAAgB,CAAC3V,OAAO,CAAC,CAAC;QAAE0U,OAAO;QAAER;MAAM,CAAC,KAAK;QAC7C0B,QAAQ,CAAClB,OAAO,GAAGkB,QAAQ,CAAClB,OAAO,IAAIA,OAAO;QAC9CkB,QAAQ,CAACC,WAAW,CAAC3B,KAAK,CAAC,GAAGQ,OAAO;MACzC,CAAC,CAAC;MACF,OAAOkB,QAAQ;IACnB,CAAC,CAAC,CAAC;EACP;EACA;EACAN,cAAcA,CAACpB,KAAK,EAAE;IAClB;IACA,IAAI,IAAI,CAACc,QAAQ,CAAC3tB,GAAG,CAAC6sB,KAAK,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACc,QAAQ,CAACntB,GAAG,CAACqsB,KAAK,CAAC;IACnC;IACA,MAAMqB,GAAG,GAAG,IAAI,CAACT,aAAa,CAACd,UAAU,CAACE,KAAK,CAAC;IAChD;IACA,MAAM4B,eAAe,GAAG,IAAIvC,6CAAU,CAAEwC,QAAQ,IAAK;MACjD;MACA;MACA;MACA;MACA;MACA,MAAMC,OAAO,GAAIvd,CAAC,IAAK,IAAI,CAACsc,KAAK,CAAC3T,GAAG,CAAC,MAAM2U,QAAQ,CAACn5B,IAAI,CAAC6b,CAAC,CAAC,CAAC;MAC7D8c,GAAG,CAACX,WAAW,CAACoB,OAAO,CAAC;MACxB,OAAO,MAAM;QACTT,GAAG,CAACV,cAAc,CAACmB,OAAO,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAAC5oB,IAAI,CAAComB,0DAAS,CAAC+B,GAAG,CAAC,EAAElxB,mDAAG,CAAC,CAAC;MAAEqwB;IAAQ,CAAC,MAAM;MAAER,KAAK;MAAEQ;IAAQ,CAAC,CAAC,CAAC,EAAElwB,0DAAS,CAAC,IAAI,CAACywB,eAAe,CAAC,CAAC;IACpG;IACA,MAAMgB,MAAM,GAAG;MAAER,UAAU,EAAEK,eAAe;MAAEP;IAAI,CAAC;IACnD,IAAI,CAACP,QAAQ,CAAC9tB,GAAG,CAACgtB,KAAK,EAAE+B,MAAM,CAAC;IAChC,OAAOA,MAAM;EACjB;EACA;IAAS,IAAI,CAACnsB,IAAI,YAAAosB,2BAAAlsB,CAAA;MAAA,YAAAA,CAAA,IAAwFnF,kBAAkB,EArK5BlD,sDAAE,CAqK4CkyB,YAAY,GArK1DlyB,sDAAE,CAqKqEA,iDAAS;IAAA,CAA6C;EAAE;EAC/N;IAAS,IAAI,CAACwI,KAAK,kBAtK6ExI,gEAAE;MAAA0I,KAAA,EAsKYxF,kBAAkB;MAAAyF,OAAA,EAAlBzF,kBAAkB,CAAAiF,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAC7J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxKoG7I,+DAAE,CAwKXkD,kBAAkB,EAAc,CAAC;IAChH6F,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEmpB;IAAa,CAAC,EAAE;MAAEnpB,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,CAAC;EAAE,CAAC;AAAA;AACjG;AACA;AACA;AACA;AACA,SAASkf,YAAYA,CAACD,OAAO,EAAE;EAC3B,OAAOA,OAAO,CACT9wB,GAAG,CAAC6vB,KAAK,IAAIA,KAAK,CAAClN,KAAK,CAAC,GAAG,CAAC,CAAC,CAC9BmP,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAAC9C,MAAM,CAAC+C,EAAE,CAAC,CAAC,CACjChyB,GAAG,CAAC6vB,KAAK,IAAIA,KAAK,CAAC5uB,IAAI,CAAC,CAAC,CAAC;AACnC;;AAEA;AACA;AACA,MAAMgxB,WAAW,GAAG;EAChBC,MAAM,EAAE,uBAAuB;EAC/BC,KAAK,EAAE,8CAA8C;EACrDC,MAAM,EAAE,+CAA+C;EACvDC,KAAK,EAAE,gDAAgD;EACvDC,MAAM,EAAE,qBAAqB;EAC7BC,OAAO,EAAE,qDAAqD,GAC1D,oDAAoD;EACxDC,MAAM,EAAE,4EAA4E,GAChF,4EAA4E;EAChFC,GAAG,EAAE,kDAAkD,GACnD,kDAAkD;EACtDC,eAAe,EAAE,mDAAmD;EACpEC,cAAc,EAAE,0EAA0E;EAC1FC,WAAW,EAAE,gDAAgD;EAC7DC,gBAAgB,EAAE,oDAAoD;EACtEC,eAAe,EAAE,4EAA4E;EAC7FC,YAAY,EAAE;AAClB,CAAC;;AAED;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;ACvNmG;AAC/D;AACyD;AAClD;AACG;;AAE9C;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1BthB,MAAMA,CAAC3V,QAAQ,EAAE;IACb,OAAO,OAAOk3B,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,gBAAgB,CAACl3B,QAAQ,CAAC;EAC1F;EACA;IAAS,IAAI,CAAC0J,IAAI,YAAAytB,gCAAAvtB,CAAA;MAAA,YAAAA,CAAA,IAAwFqtB,uBAAuB;IAAA,CAAoD;EAAE;EACvL;IAAS,IAAI,CAACltB,KAAK,kBAD6ExI,gEAAE;MAAA0I,KAAA,EACYgtB,uBAAuB;MAAA/sB,OAAA,EAAvB+sB,uBAAuB,CAAAvtB,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAClK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG7I,+DAAE,CAGX01B,uBAAuB,EAAc,CAAC;IACrH3sB,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,MAAMuT,eAAe,CAAC;EAClB5f,WAAWA,CAACs5B,wBAAwB,EAAE;IAClC,IAAI,CAACA,wBAAwB,GAAGA,wBAAwB;IACxD;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIlxB,GAAG,CAAC,CAAC;EACtC;EACA0B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwvB,iBAAiB,CAACzX,OAAO,CAAC,CAACniB,CAAC,EAAEwL,OAAO,KAAK,IAAI,CAACquB,gBAAgB,CAACruB,OAAO,CAAC,CAAC;EAClF;EACAoU,OAAOA,CAACkJ,YAAY,EAAE;IAClB,MAAMtd,OAAO,GAAG3E,oEAAa,CAACiiB,YAAY,CAAC;IAC3C,OAAO,IAAI4M,4CAAU,CAAEwC,QAAQ,IAAK;MAChC,MAAM4B,MAAM,GAAG,IAAI,CAACC,eAAe,CAACvuB,OAAO,CAAC;MAC5C,MAAMwuB,YAAY,GAAGF,MAAM,CAAC56B,SAAS,CAACg5B,QAAQ,CAAC;MAC/C,OAAO,MAAM;QACT8B,YAAY,CAACn7B,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACo7B,iBAAiB,CAACzuB,OAAO,CAAC;MACnC,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIuuB,eAAeA,CAACvuB,OAAO,EAAE;IACrB,IAAI,CAAC,IAAI,CAACouB,iBAAiB,CAACpwB,GAAG,CAACgC,OAAO,CAAC,EAAE;MACtC,MAAMsuB,MAAM,GAAG,IAAI90B,yCAAO,CAAC,CAAC;MAC5B,MAAMkzB,QAAQ,GAAG,IAAI,CAACyB,wBAAwB,CAACzhB,MAAM,CAACgiB,SAAS,IAAIJ,MAAM,CAAC/6B,IAAI,CAACm7B,SAAS,CAAC,CAAC;MAC1F,IAAIhC,QAAQ,EAAE;QACVA,QAAQ,CAACtY,OAAO,CAACpU,OAAO,EAAE;UACtB2uB,aAAa,EAAE,IAAI;UACnBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE;QACb,CAAC,CAAC;MACN;MACA,IAAI,CAACT,iBAAiB,CAACvwB,GAAG,CAACmC,OAAO,EAAE;QAAE0sB,QAAQ;QAAE4B,MAAM;QAAE/5B,KAAK,EAAE;MAAE,CAAC,CAAC;IACvE,CAAC,MACI;MACD,IAAI,CAAC65B,iBAAiB,CAAC5vB,GAAG,CAACwB,OAAO,CAAC,CAACzL,KAAK,EAAE;IAC/C;IACA,OAAO,IAAI,CAAC65B,iBAAiB,CAAC5vB,GAAG,CAACwB,OAAO,CAAC,CAACsuB,MAAM;EACrD;EACA;AACJ;AACA;AACA;EACIG,iBAAiBA,CAACzuB,OAAO,EAAE;IACvB,IAAI,IAAI,CAACouB,iBAAiB,CAACpwB,GAAG,CAACgC,OAAO,CAAC,EAAE;MACrC,IAAI,CAACouB,iBAAiB,CAAC5vB,GAAG,CAACwB,OAAO,CAAC,CAACzL,KAAK,EAAE;MAC3C,IAAI,CAAC,IAAI,CAAC65B,iBAAiB,CAAC5vB,GAAG,CAACwB,OAAO,CAAC,CAACzL,KAAK,EAAE;QAC5C,IAAI,CAAC85B,gBAAgB,CAACruB,OAAO,CAAC;MAClC;IACJ;EACJ;EACA;EACAquB,gBAAgBA,CAACruB,OAAO,EAAE;IACtB,IAAI,IAAI,CAACouB,iBAAiB,CAACpwB,GAAG,CAACgC,OAAO,CAAC,EAAE;MACrC,MAAM;QAAE0sB,QAAQ;QAAE4B;MAAO,CAAC,GAAG,IAAI,CAACF,iBAAiB,CAAC5vB,GAAG,CAACwB,OAAO,CAAC;MAChE,IAAI0sB,QAAQ,EAAE;QACVA,QAAQ,CAACrO,UAAU,CAAC,CAAC;MACzB;MACAiQ,MAAM,CAAC96B,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAC46B,iBAAiB,CAAC9uB,MAAM,CAACU,OAAO,CAAC;IAC1C;EACJ;EACA;IAAS,IAAI,CAACS,IAAI,YAAAquB,wBAAAnuB,CAAA;MAAA,YAAAA,CAAA,IAAwF8T,eAAe,EAzEzBnc,sDAAE,CAyEyC01B,uBAAuB;IAAA,CAA6C;EAAE;EACjN;IAAS,IAAI,CAACltB,KAAK,kBA1E6ExI,gEAAE;MAAA0I,KAAA,EA0EYyT,eAAe;MAAAxT,OAAA,EAAfwT,eAAe,CAAAhU,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5EoG7I,+DAAE,CA4EXmc,eAAe,EAAc,CAAC;IAC7GpT,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE2sB;IAAwB,CAAC,CAAC;EAAE,CAAC;AAAA;AACvF;AACA;AACA;AACA;AACA,MAAMe,iBAAiB,CAAC;EACpB;AACJ;AACA;AACA;EACI,IAAIvsB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACwsB,SAAS;EACzB;EACA,IAAIxsB,QAAQA,CAAClP,KAAK,EAAE;IAChB,IAAI,CAAC07B,SAAS,GAAG5zB,4EAAqB,CAAC9H,KAAK,CAAC;IAC7C,IAAI,CAAC07B,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;EAC5D;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC77B,KAAK,EAAE;IAChB,IAAI,CAAC87B,SAAS,GAAGvS,2EAAoB,CAACvpB,KAAK,CAAC;IAC5C,IAAI,CAAC47B,UAAU,CAAC,CAAC;EACrB;EACAr6B,WAAWA,CAACsf,gBAAgB,EAAEjH,WAAW,EAAEpD,OAAO,EAAE;IAChD,IAAI,CAACqK,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACjH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACpD,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAAClF,KAAK,GAAG,IAAI5L,uDAAY,CAAC,CAAC;IAC/B,IAAI,CAACg2B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACK,oBAAoB,GAAG,IAAI;EACpC;EACA/hB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC+hB,oBAAoB,IAAI,CAAC,IAAI,CAAC7sB,QAAQ,EAAE;MAC9C,IAAI,CAAC0sB,UAAU,CAAC,CAAC;IACrB;EACJ;EACAtwB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqwB,YAAY,CAAC,CAAC;EACvB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACD,YAAY,CAAC,CAAC;IACnB,MAAMX,MAAM,GAAG,IAAI,CAACna,gBAAgB,CAACC,OAAO,CAAC,IAAI,CAAClH,WAAW,CAAC;IAC9D;IACA;IACA;IACA;IACA,IAAI,CAACpD,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC4kB,oBAAoB,GAAG,CAAC,IAAI,CAACF,QAAQ,GAAGb,MAAM,CAACvqB,IAAI,CAAChJ,4DAAY,CAAC,IAAI,CAACo0B,QAAQ,CAAC,CAAC,GAAGb,MAAM,EAAE56B,SAAS,CAAC,IAAI,CAACkR,KAAK,CAAC;IACzH,CAAC,CAAC;EACN;EACAqqB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACI,oBAAoB,EAAEh8B,WAAW,CAAC,CAAC;EAC5C;EACA;IAAS,IAAI,CAACoN,IAAI,YAAA6uB,0BAAA3uB,CAAA;MAAA,YAAAA,CAAA,IAAwFouB,iBAAiB,EAvI3Bz2B,+DAAE,CAuI2Cmc,eAAe,GAvI5Dnc,+DAAE,CAuIuEA,qDAAa,GAvItFA,+DAAE,CAuIiGA,iDAAS;IAAA,CAA4C;EAAE;EAC1P;IAAS,IAAI,CAACyV,IAAI,kBAxI8EzV,+DAAE;MAAA+I,IAAA,EAwIJ0tB,iBAAiB;MAAA9gB,SAAA;MAAAC,MAAA;QAAA1L,QAAA;QAAA2sB,QAAA;MAAA;MAAApW,OAAA;QAAAnU,KAAA;MAAA;MAAAuJ,QAAA;IAAA,EAAmN;EAAE;AACxU;AACA;EAAA,QAAAhN,SAAA,oBAAAA,SAAA,KA1IoG7I,+DAAE,CA0IXy2B,iBAAiB,EAAc,CAAC;IAC/G1tB,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,qBAAqB;MAC/BH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9M,IAAI,EAAEoT;IAAgB,CAAC,EAAE;MAAEpT,IAAI,EAAE/I,qDAAawV;IAAC,CAAC,EAAE;MAAEzM,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEjI,KAAK,EAAE,CAAC;MACzIvD,IAAI,EAAEpI,iDAAM;MACZ5B,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEmL,QAAQ,EAAE,CAAC;MACXnB,IAAI,EAAExI,gDAAK;MACXxB,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE83B,QAAQ,EAAE,CAAC;MACX9tB,IAAI,EAAExI,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM0C,eAAe,CAAC;EAClB;IAAS,IAAI,CAACkF,IAAI,YAAA8uB,wBAAA5uB,CAAA;MAAA,YAAAA,CAAA,IAAwFpF,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAAC8e,IAAI,kBA3J8E/hB,8DAAE;MAAA+I,IAAA,EA2JS9F;IAAe,EAAoE;EAAE;EAChM;IAAS,IAAI,CAACgf,IAAI,kBA5J8EjiB,8DAAE;MAAAmkB,SAAA,EA4JqC,CAACuR,uBAAuB;IAAC,EAAG;EAAE;AACzK;AACA;EAAA,QAAA7sB,SAAA,oBAAAA,SAAA,KA9JoG7I,+DAAE,CA8JXiD,eAAe,EAAc,CAAC;IAC7G8F,IAAI,EAAEnI,mDAAQ;IACd7B,IAAI,EAAE,CAAC;MACCsjB,OAAO,EAAE,CAACoU,iBAAiB,CAAC;MAC5BrU,YAAY,EAAE,CAACqU,iBAAiB,CAAC;MACjCtS,SAAS,EAAE,CAACuR,uBAAuB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvLoC;AACsC;AACtB;;AAEpD;AACA;AACA,IAAI0B,kBAAkB;AACtB;AACA;AACA;AACA;AACA;AACA,IAAI;EACAA,kBAAkB,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,eAAe;AAC5E,CAAC,CACD,MAAM;EACFF,kBAAkB,GAAG,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAM7uB,QAAQ,CAAC;EACXhM,WAAWA,CAACg7B,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B;IACA;IACA;IACA;IACA,IAAI,CAAC/vB,SAAS,GAAG,IAAI,CAAC+vB,WAAW,GAC3BJ,kEAAiB,CAAC,IAAI,CAACI,WAAW,CAAC,GACnC,OAAOne,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACA,QAAQ;IAChD;IACA,IAAI,CAACoe,IAAI,GAAG,IAAI,CAAChwB,SAAS,IAAI,SAAS,CAACqb,IAAI,CAACF,SAAS,CAAC8U,SAAS,CAAC;IACjE;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAAClwB,SAAS,IAAI,iBAAiB,CAACqb,IAAI,CAACF,SAAS,CAAC8U,SAAS,CAAC;IAC5E;IACA;IACA,IAAI,CAACjF,KAAK,GAAG,IAAI,CAAChrB,SAAS,IACvB,CAAC,EAAEyI,MAAM,CAAC0nB,MAAM,IAAIP,kBAAkB,CAAC,IACvC,OAAOQ,GAAG,KAAK,WAAW,IAC1B,CAAC,IAAI,CAACJ,IAAI,IACV,CAAC,IAAI,CAACE,OAAO;IACjB;IACA;IACA;IACA,IAAI,CAACloB,MAAM,GAAG,IAAI,CAAChI,SAAS,IACxB,cAAc,CAACqb,IAAI,CAACF,SAAS,CAAC8U,SAAS,CAAC,IACxC,CAAC,IAAI,CAACjF,KAAK,IACX,CAAC,IAAI,CAACgF,IAAI,IACV,CAAC,IAAI,CAACE,OAAO;IACjB;IACA,IAAI,CAACjoB,GAAG,GAAG,IAAI,CAACjI,SAAS,IAAI,kBAAkB,CAACqb,IAAI,CAACF,SAAS,CAAC8U,SAAS,CAAC,IAAI,EAAE,UAAU,IAAIxnB,MAAM,CAAC;IACpG;IACA;IACA;IACA;IACA;IACA,IAAI,CAACN,OAAO,GAAG,IAAI,CAACnI,SAAS,IAAI,sBAAsB,CAACqb,IAAI,CAACF,SAAS,CAAC8U,SAAS,CAAC;IACjF;IACA;IACA,IAAI,CAACI,OAAO,GAAG,IAAI,CAACrwB,SAAS,IAAI,UAAU,CAACqb,IAAI,CAACF,SAAS,CAAC8U,SAAS,CAAC,IAAI,CAAC,IAAI,CAACC,OAAO;IACtF;IACA;IACA;IACA;IACA,IAAI,CAACI,MAAM,GAAG,IAAI,CAACtwB,SAAS,IAAI,SAAS,CAACqb,IAAI,CAACF,SAAS,CAAC8U,SAAS,CAAC,IAAI,IAAI,CAACjoB,MAAM;EACtF;EACA;IAAS,IAAI,CAACrH,IAAI,YAAA4vB,iBAAA1vB,CAAA;MAAA,YAAAA,CAAA,IAAwFE,QAAQ,EAAlBvI,sDAAE,CAAkCk3B,sDAAW;IAAA,CAA6C;EAAE;EAC9L;IAAS,IAAI,CAAC1uB,KAAK,kBAD6ExI,gEAAE;MAAA0I,KAAA,EACYH,QAAQ;MAAAI,OAAA,EAARJ,QAAQ,CAAAJ,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACnJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG7I,+DAAE,CAGXuI,QAAQ,EAAc,CAAC;IACtGQ,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElJ,MAAM;MAAEmJ,UAAU,EAAE,CAAC;QAC3DD,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACm4B,sDAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMc,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC7vB,IAAI,YAAA8vB,uBAAA5vB,CAAA;MAAA,YAAAA,CAAA,IAAwF2vB,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACjW,IAAI,kBAb8E/hB,8DAAE;MAAA+I,IAAA,EAaSivB;IAAc,EAAG;EAAE;EAC9H;IAAS,IAAI,CAAC/V,IAAI,kBAd8EjiB,8DAAE,IAc0B;EAAE;AAClI;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KAhBoG7I,+DAAE,CAgBXg4B,cAAc,EAAc,CAAC;IAC5GjvB,IAAI,EAAEnI,mDAAQ;IACd7B,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;;AAEV;AACA,IAAIm5B,mBAAmB;AACvB;AACA,MAAMC,mBAAmB,GAAG;AACxB;AACA;AACA;AACA;AACA,OAAO,EACP,QAAQ,EACR,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,CACT;AACD;AACA,SAASC,sBAAsBA,CAAA,EAAG;EAC9B;EACA,IAAIF,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB;EAC9B;EACA;EACA;EACA;EACA,IAAI,OAAO9e,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;IAC3C8e,mBAAmB,GAAG,IAAItP,GAAG,CAACuP,mBAAmB,CAAC;IAClD,OAAOD,mBAAmB;EAC9B;EACA,IAAIG,gBAAgB,GAAGjf,QAAQ,CAACxS,aAAa,CAAC,OAAO,CAAC;EACtDsxB,mBAAmB,GAAG,IAAItP,GAAG,CAACuP,mBAAmB,CAACp8B,MAAM,CAACf,KAAK,IAAI;IAC9Dq9B,gBAAgB,CAACz0B,YAAY,CAAC,MAAM,EAAE5I,KAAK,CAAC;IAC5C,OAAOq9B,gBAAgB,CAACtvB,IAAI,KAAK/N,KAAK;EAC1C,CAAC,CAAC,CAAC;EACH,OAAOk9B,mBAAmB;AAC9B;;AAEA;AACA,IAAII,qBAAqB;AACzB;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAAA,EAAG;EACrC,IAAID,qBAAqB,IAAI,IAAI,IAAI,OAAOroB,MAAM,KAAK,WAAW,EAAE;IAChE,IAAI;MACAA,MAAM,CAACoC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAExS,MAAM,CAAC24B,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;QACvEtyB,GAAG,EAAEA,CAAA,KAAOoyB,qBAAqB,GAAG;MACxC,CAAC,CAAC,CAAC;IACP,CAAC,SACO;MACJA,qBAAqB,GAAGA,qBAAqB,IAAI,KAAK;IAC1D;EACJ;EACA,OAAOA,qBAAqB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASv3B,+BAA+BA,CAAC2R,OAAO,EAAE;EAC9C,OAAO6lB,6BAA6B,CAAC,CAAC,GAAG7lB,OAAO,GAAG,CAAC,CAACA,OAAO,CAACqG,OAAO;AACxE;;AAEA;AACA,IAAI0f,iBAAiB;AACrB;AACA,IAAIC,uBAAuB;AAC3B;AACA,SAASC,sBAAsBA,CAAA,EAAG;EAC9B,IAAID,uBAAuB,IAAI,IAAI,EAAE;IACjC;IACA;IACA,IAAI,OAAOtf,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,IAAI,OAAOwf,OAAO,KAAK,UAAU,IAAI,CAACA,OAAO,EAAE;MACxFF,uBAAuB,GAAG,KAAK;MAC/B,OAAOA,uBAAuB;IAClC;IACA;IACA,IAAI,gBAAgB,IAAItf,QAAQ,CAAC8J,eAAe,CAAC9b,KAAK,EAAE;MACpDsxB,uBAAuB,GAAG,IAAI;IAClC,CAAC,MACI;MACD;MACA;MACA,MAAMG,gBAAgB,GAAGD,OAAO,CAACE,SAAS,CAACC,QAAQ;MACnD,IAAIF,gBAAgB,EAAE;QAClB;QACA;QACA;QACA;QACAH,uBAAuB,GAAG,CAAC,2BAA2B,CAAC7V,IAAI,CAACgW,gBAAgB,CAACG,QAAQ,CAAC,CAAC,CAAC;MAC5F,CAAC,MACI;QACDN,uBAAuB,GAAG,KAAK;MACnC;IACJ;EACJ;EACA,OAAOA,uBAAuB;AAClC;AACA;AACA;AACA;AACA;AACA,SAASO,oBAAoBA,CAAA,EAAG;EAC5B;EACA,IAAI,OAAO7f,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;IAC3C,OAAO,CAAC,CAAC;EACb;;EACA,IAAIqf,iBAAiB,IAAI,IAAI,EAAE;IAC3B;IACA,MAAMS,eAAe,GAAG9f,QAAQ,CAACxS,aAAa,CAAC,KAAK,CAAC;IACrD,MAAMuyB,cAAc,GAAGD,eAAe,CAAC9xB,KAAK;IAC5C8xB,eAAe,CAAClW,GAAG,GAAG,KAAK;IAC3BmW,cAAc,CAACC,KAAK,GAAG,KAAK;IAC5BD,cAAc,CAACE,QAAQ,GAAG,MAAM;IAChCF,cAAc,CAAC9xB,UAAU,GAAG,QAAQ;IACpC8xB,cAAc,CAACG,aAAa,GAAG,MAAM;IACrCH,cAAc,CAAC/X,QAAQ,GAAG,UAAU;IACpC,MAAMmY,OAAO,GAAGngB,QAAQ,CAACxS,aAAa,CAAC,KAAK,CAAC;IAC7C,MAAM4yB,YAAY,GAAGD,OAAO,CAACnyB,KAAK;IAClCoyB,YAAY,CAACJ,KAAK,GAAG,KAAK;IAC1BI,YAAY,CAACC,MAAM,GAAG,KAAK;IAC3BP,eAAe,CAACnyB,WAAW,CAACwyB,OAAO,CAAC;IACpCngB,QAAQ,CAAC3R,IAAI,CAACV,WAAW,CAACmyB,eAAe,CAAC;IAC1CT,iBAAiB,GAAG,CAAC,CAAC;IACtB;IACA;IACA;IACA,IAAIS,eAAe,CAACQ,UAAU,KAAK,CAAC,EAAE;MAClC;MACA;MACA;MACA;MACAR,eAAe,CAACQ,UAAU,GAAG,CAAC;MAC9BjB,iBAAiB,GACbS,eAAe,CAACQ,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,kCAAkC,CAAC,CAAC;IACjF;;IACAR,eAAe,CAAC7yB,MAAM,CAAC,CAAC;EAC5B;EACA,OAAOoyB,iBAAiB;AAC5B;AAEA,IAAIkB,oBAAoB;AACxB;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,IAAID,oBAAoB,IAAI,IAAI,EAAE;IAC9B,MAAM/G,IAAI,GAAG,OAAOxZ,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACwZ,IAAI,GAAG,IAAI;IACnE+G,oBAAoB,GAAG,CAAC,EAAE/G,IAAI,KAAKA,IAAI,CAACiH,gBAAgB,IAAIjH,IAAI,CAACkH,YAAY,CAAC,CAAC;EACnF;EACA,OAAOH,oBAAoB;AAC/B;AACA;AACA,SAAS14B,cAAcA,CAACyG,OAAO,EAAE;EAC7B,IAAIkyB,kBAAkB,CAAC,CAAC,EAAE;IACtB,MAAMpc,QAAQ,GAAG9V,OAAO,CAACqyB,WAAW,GAAGryB,OAAO,CAACqyB,WAAW,CAAC,CAAC,GAAG,IAAI;IACnE;IACA;IACA,IAAI,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU,IAAIxc,QAAQ,YAAYwc,UAAU,EAAE;MACnF,OAAOxc,QAAQ;IACnB;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,SAAS1c,iCAAiCA,CAAA,EAAG;EACzC,IAAIuW,aAAa,GAAG,OAAO+B,QAAQ,KAAK,WAAW,IAAIA,QAAQ,GACzDA,QAAQ,CAAC/B,aAAa,GACtB,IAAI;EACV,OAAOA,aAAa,IAAIA,aAAa,CAAC4iB,UAAU,EAAE;IAC9C,MAAMC,gBAAgB,GAAG7iB,aAAa,CAAC4iB,UAAU,CAAC5iB,aAAa;IAC/D,IAAI6iB,gBAAgB,KAAK7iB,aAAa,EAAE;MACpC;IACJ,CAAC,MACI;MACDA,aAAa,GAAG6iB,gBAAgB;IACpC;EACJ;EACA,OAAO7iB,aAAa;AACxB;AACA;AACA,SAASrW,eAAeA,CAACsL,KAAK,EAAE;EAC5B;EACA;EACA,OAAQA,KAAK,CAAC6tB,YAAY,GAAG7tB,KAAK,CAAC6tB,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG7tB,KAAK,CAAC0K,MAAM;AACvE;;AAEA;AACA,SAASojB,kBAAkBA,CAAA,EAAG;EAC1B;EACA;EACA;EACA;EACA;IACA;IACC,OAAOC,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;IAC5C;IACC,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAQ;IAC7C;IACC,OAAOC,IAAI,KAAK,WAAW,IAAI,CAAC,CAACA,IAAK;IACvC;IACC,OAAOC,KAAK,KAAK,WAAW,IAAI,CAAC,CAACA;EAAM;AACjD;;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1TmG;AAC/D;AACyL;AAC/F;AACG;AACrF;AACmD;AACpD;AACH;AACO;AACE;AAC+E;;AAEhI;AAAA,MAAAY,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,uBAAuB,GAAG,IAAI96B,yDAAc,CAAC,yBAAyB,CAAC;;AAE7E;AACA,MAAM+6B,8BAA8B,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACIh/B,WAAWA,CAACi/B,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAC5C,IAAI,CAACC,oBAAoB,GAAG,IAAIz6B,yCAAO,CAAC,CAAC;IACzC;IACA,IAAI,CAAC06B,mBAAmB,GAAG,IAAI,CAACD,oBAAoB,CAAClwB,IAAI,CAAC7I,oEAAoB,CAAC,CAAC,CAAC;IACjF;IACA,IAAI,CAACi5B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAGN,QAAQ;IACzB,IAAI,CAACO,YAAY,GAAGN,WAAW;IAC/B,IAAI,CAACO,YAAY,GAAGN,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIO,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACL,SAAS,GAAGK,QAAQ;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAlV,MAAMA,CAAA,EAAG;IACL,IAAI,CAACyU,oBAAoB,CAACzgC,QAAQ,CAAC,CAAC;IACpC,IAAI,CAAC2gC,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIQ,uBAAuBA,CAACb,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACxD,IAAIA,WAAW,GAAGD,WAAW,KAAK,OAAO5yB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC9E,MAAM2C,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,IAAI,CAACswB,SAAS,GAAGN,QAAQ;IACzB,IAAI,CAACO,YAAY,GAAGN,WAAW;IAC/B,IAAI,CAACO,YAAY,GAAGN,WAAW;IAC/B,IAAI,CAACS,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACF,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAG,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACJ,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAI,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;EACAC,uBAAuBA,CAAA,EAAG;IACtB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACvgC,KAAK,EAAEwgC,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACd,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACe,cAAc,CAACzgC,KAAK,GAAG,IAAI,CAAC2/B,SAAS,EAAEa,QAAQ,CAAC;IACnE;EACJ;EACA;EACAR,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACN,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACA,SAAS,CAACgB,mBAAmB,CAAC,IAAI,CAAChB,SAAS,CAACiB,aAAa,CAAC,CAAC,GAAG,IAAI,CAAChB,SAAS,CAAC;EACvF;EACA;EACAM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACP,SAAS,EAAE;MACjB;IACJ;IACA,MAAMkB,aAAa,GAAG,IAAI,CAAClB,SAAS,CAACmB,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG;MAAEC,KAAK,EAAEH,aAAa,CAACG,KAAK;MAAEC,GAAG,EAAEJ,aAAa,CAACI;IAAI,CAAC;IACvE,MAAMC,YAAY,GAAG,IAAI,CAACvB,SAAS,CAACwB,eAAe,CAAC,CAAC;IACrD,MAAMC,UAAU,GAAG,IAAI,CAACzB,SAAS,CAACiB,aAAa,CAAC,CAAC;IACjD,IAAIS,YAAY,GAAG,IAAI,CAAC1B,SAAS,CAAC2B,mBAAmB,CAAC,CAAC;IACvD;IACA,IAAIC,iBAAiB,GAAG,IAAI,CAAC3B,SAAS,GAAG,CAAC,GAAGyB,YAAY,GAAG,IAAI,CAACzB,SAAS,GAAG,CAAC;IAC9E;IACA,IAAImB,QAAQ,CAACE,GAAG,GAAGG,UAAU,EAAE;MAC3B;MACA,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAACR,YAAY,GAAG,IAAI,CAACtB,SAAS,CAAC;MAChE,MAAM+B,eAAe,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACN,iBAAiB,EAAEH,UAAU,GAAGI,eAAe,CAAC,CAAC;MAC9F;MACA;MACA,IAAID,iBAAiB,IAAII,eAAe,EAAE;QACtCJ,iBAAiB,GAAGI,eAAe;QACnCN,YAAY,GAAGM,eAAe,GAAG,IAAI,CAAC/B,SAAS;QAC/CmB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC;MAClD;MACAR,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACC,KAAK,GAAGQ,eAAe,CAAC,CAAC;IACtF;IACA,MAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAK,GAAG,IAAI,CAACpB,SAAS;IAClE,IAAImC,WAAW,GAAG,IAAI,CAAClC,YAAY,IAAIkB,QAAQ,CAACC,KAAK,IAAI,CAAC,EAAE;MACxD,MAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC5B,YAAY,GAAGiC,WAAW,IAAI,IAAI,CAACnC,SAAS,CAAC;MACjFmB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEb,QAAQ,CAACC,KAAK,GAAGgB,WAAW,CAAC;MAC1DjB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEK,IAAI,CAACC,IAAI,CAACH,iBAAiB,GAAG,CAACL,YAAY,GAAG,IAAI,CAACrB,YAAY,IAAI,IAAI,CAACD,SAAS,CAAC,CAAC;IAC3H,CAAC,MACI;MACD,MAAMqC,SAAS,GAAGlB,QAAQ,CAACE,GAAG,GAAG,IAAI,CAACrB,SAAS,IAAIyB,YAAY,GAAGH,YAAY,CAAC;MAC/E,IAAIe,SAAS,GAAG,IAAI,CAACpC,YAAY,IAAIkB,QAAQ,CAACE,GAAG,IAAIG,UAAU,EAAE;QAC7D,MAAMc,SAAS,GAAGT,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC5B,YAAY,GAAGmC,SAAS,IAAI,IAAI,CAACrC,SAAS,CAAC;QAC7E,IAAIsC,SAAS,GAAG,CAAC,EAAE;UACfnB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACE,GAAG,GAAGiB,SAAS,CAAC;UAC7DnB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACK,KAAK,CAACP,iBAAiB,GAAG,IAAI,CAAC1B,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC;QACpG;MACJ;IACJ;IACA,IAAI,CAACD,SAAS,CAACwC,gBAAgB,CAACpB,QAAQ,CAAC;IACzC,IAAI,CAACpB,SAAS,CAACyC,wBAAwB,CAAC,IAAI,CAACxC,SAAS,GAAGmB,QAAQ,CAACC,KAAK,CAAC;IACxE,IAAI,CAACvB,oBAAoB,CAAC1gC,IAAI,CAAC0iC,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC,CAAC;EACjE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,sCAAsCA,CAACC,YAAY,EAAE;EAC1D,OAAOA,YAAY,CAACC,eAAe;AACvC;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BniC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACu/B,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,YAAY,GAAG,GAAG;IACvB,IAAI,CAACC,YAAY,GAAG,GAAG;IACvB;IACA,IAAI,CAACyC,eAAe,GAAG,IAAIlD,8BAA8B,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EAChH;EACA;EACA,IAAIF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACM,SAAS;EACzB;EACA,IAAIN,QAAQA,CAACxgC,KAAK,EAAE;IAChB,IAAI,CAAC8gC,SAAS,GAAGvX,2EAAoB,CAACvpB,KAAK,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACI,IAAIygC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACM,YAAY;EAC5B;EACA,IAAIN,WAAWA,CAACzgC,KAAK,EAAE;IACnB,IAAI,CAAC+gC,YAAY,GAAGxX,2EAAoB,CAACvpB,KAAK,CAAC;EACnD;EACA;AACJ;AACA;EACI,IAAI0gC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACM,YAAY;EAC5B;EACA,IAAIN,WAAWA,CAAC1gC,KAAK,EAAE;IACnB,IAAI,CAACghC,YAAY,GAAGzX,2EAAoB,CAACvpB,KAAK,CAAC;EACnD;EACAma,WAAWA,CAAA,EAAG;IACV,IAAI,CAACspB,eAAe,CAACpC,uBAAuB,CAAC,IAAI,CAACb,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EACnG;EACA;IAAS,IAAI,CAACvzB,IAAI,YAAAw2B,kCAAAt2B,CAAA;MAAA,YAAAA,CAAA,IAAwFq2B,yBAAyB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAACjpB,IAAI,kBAD8EzV,+DAAE;MAAA+I,IAAA,EACJ21B,yBAAyB;MAAA/oB,SAAA;MAAAC,MAAA;QAAA4lB,QAAA;QAAAC,WAAA;QAAAC,WAAA;MAAA;MAAAkD,UAAA;MAAA9oB,QAAA,GADvB9V,gEAAE,CACmM,CAC7R;QACIikB,OAAO,EAAEqX,uBAAuB;QAChCuD,UAAU,EAAEN,sCAAsC;QAClDO,IAAI,EAAE,CAACrE,yDAAU,CAAC,MAAMiE,yBAAyB,CAAC;MACtD,CAAC,CACJ,GAP2F1+B,kEAAE;IAAA,EAOvD;EAAE;AACjD;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KAToG7I,+DAAE,CASX0+B,yBAAyB,EAAc,CAAC;IACvH31B,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,uCAAuC;MACjD4oB,UAAU,EAAE,IAAI;MAChBza,SAAS,EAAE,CACP;QACIF,OAAO,EAAEqX,uBAAuB;QAChCuD,UAAU,EAAEN,sCAAsC;QAClDO,IAAI,EAAE,CAACrE,yDAAU,CAAC,MAAMiE,yBAAyB,CAAC;MACtD,CAAC;IAET,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElD,QAAQ,EAAE,CAAC;MACzBzyB,IAAI,EAAExI,gDAAKA;IACf,CAAC,CAAC;IAAEk7B,WAAW,EAAE,CAAC;MACd1yB,IAAI,EAAExI,gDAAKA;IACf,CAAC,CAAC;IAAEm7B,WAAW,EAAE,CAAC;MACd3yB,IAAI,EAAExI,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMw+B,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBziC,WAAWA,CAACiV,OAAO,EAAE9M,SAAS,EAAE0U,QAAQ,EAAE;IACtC,IAAI,CAAC5H,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC9M,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACu6B,SAAS,GAAG,IAAI/9B,yCAAO,CAAC,CAAC;IAC9B;IACA,IAAI,CAACg+B,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAIx6B,GAAG,CAAC,CAAC;IACjC,IAAI,CAACH,SAAS,GAAG2U,QAAQ;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIjD,QAAQA,CAACkpB,UAAU,EAAE;IACjB,IAAI,CAAC,IAAI,CAACD,gBAAgB,CAAC15B,GAAG,CAAC25B,UAAU,CAAC,EAAE;MACxC,IAAI,CAACD,gBAAgB,CAAC75B,GAAG,CAAC85B,UAAU,EAAEA,UAAU,CAACC,eAAe,CAAC,CAAC,CAAClkC,SAAS,CAAC,MAAM,IAAI,CAAC6jC,SAAS,CAAChkC,IAAI,CAACokC,UAAU,CAAC,CAAC,CAAC;IACxH;EACJ;EACA;AACJ;AACA;AACA;EACIjpB,UAAUA,CAACipB,UAAU,EAAE;IACnB,MAAME,mBAAmB,GAAG,IAAI,CAACH,gBAAgB,CAACl5B,GAAG,CAACm5B,UAAU,CAAC;IACjE,IAAIE,mBAAmB,EAAE;MACrBA,mBAAmB,CAACxkC,WAAW,CAAC,CAAC;MACjC,IAAI,CAACqkC,gBAAgB,CAACp4B,MAAM,CAACq4B,UAAU,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,QAAQA,CAACC,aAAa,GAAGV,mBAAmB,EAAE;IAC1C,IAAI,CAAC,IAAI,CAACr6B,SAAS,CAAC8C,SAAS,EAAE;MAC3B,OAAOpG,wCAAE,CAAC,CAAC;IACf;IACA,OAAO,IAAIwwB,4CAAU,CAAEwC,QAAQ,IAAK;MAChC,IAAI,CAAC,IAAI,CAAC8K,mBAAmB,EAAE;QAC3B,IAAI,CAACQ,kBAAkB,CAAC,CAAC;MAC7B;MACA;MACA;MACA,MAAMxJ,YAAY,GAAGuJ,aAAa,GAAG,CAAC,GAChC,IAAI,CAACR,SAAS,CAACxzB,IAAI,CAACjQ,yDAAS,CAACikC,aAAa,CAAC,CAAC,CAACrkC,SAAS,CAACg5B,QAAQ,CAAC,GACjE,IAAI,CAAC6K,SAAS,CAAC7jC,SAAS,CAACg5B,QAAQ,CAAC;MACxC,IAAI,CAAC+K,cAAc,EAAE;MACrB,OAAO,MAAM;QACTjJ,YAAY,CAACn7B,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACokC,cAAc,EAAE;QACrB,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;UACtB,IAAI,CAACQ,qBAAqB,CAAC,CAAC;QAChC;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAr5B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACq5B,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACP,gBAAgB,CAAC/gB,OAAO,CAAC,CAACniB,CAAC,EAAE0jC,SAAS,KAAK,IAAI,CAACxpB,UAAU,CAACwpB,SAAS,CAAC,CAAC;IAC3E,IAAI,CAACX,SAAS,CAAC/jC,QAAQ,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2kC,gBAAgBA,CAACC,mBAAmB,EAAEL,aAAa,EAAE;IACjD,MAAMM,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAACF,mBAAmB,CAAC;IACvE,OAAO,IAAI,CAACN,QAAQ,CAACC,aAAa,CAAC,CAACh0B,IAAI,CAAC1P,sDAAM,CAACib,MAAM,IAAI;MACtD,OAAO,CAACA,MAAM,IAAI+oB,SAAS,CAACn4B,OAAO,CAACoP,MAAM,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;EACP;EACA;EACAgpB,2BAA2BA,CAACF,mBAAmB,EAAE;IAC7C,MAAMG,mBAAmB,GAAG,EAAE;IAC9B,IAAI,CAACb,gBAAgB,CAAC/gB,OAAO,CAAC,CAACzC,aAAa,EAAEyjB,UAAU,KAAK;MACzD,IAAI,IAAI,CAACa,0BAA0B,CAACb,UAAU,EAAES,mBAAmB,CAAC,EAAE;QAClEG,mBAAmB,CAACpjC,IAAI,CAACwiC,UAAU,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAOY,mBAAmB;EAC9B;EACA;EACAvhB,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACja,SAAS,CAACuM,WAAW,IAAIf,MAAM;EAC/C;EACA;EACAiwB,0BAA0BA,CAACb,UAAU,EAAES,mBAAmB,EAAE;IACxD,IAAIp4B,OAAO,GAAG3E,oEAAa,CAAC+8B,mBAAmB,CAAC;IAChD,IAAIK,iBAAiB,GAAGd,UAAU,CAACe,aAAa,CAAC,CAAC,CAACrrB,aAAa;IAChE;IACA;IACA,GAAG;MACC,IAAIrN,OAAO,IAAIy4B,iBAAiB,EAAE;QAC9B,OAAO,IAAI;MACf;IACJ,CAAC,QAASz4B,OAAO,GAAGA,OAAO,CAACuV,aAAa;IACzC,OAAO,KAAK;EAChB;EACA;EACAyiB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACR,mBAAmB,GAAG,IAAI,CAAC1tB,OAAO,CAACW,iBAAiB,CAAC,MAAM;MAC5D,MAAMlC,MAAM,GAAG,IAAI,CAACyO,UAAU,CAAC,CAAC;MAChC,OAAOqc,+CAAS,CAAC9qB,MAAM,CAACmJ,QAAQ,EAAE,QAAQ,CAAC,CAAChe,SAAS,CAAC,MAAM,IAAI,CAAC6jC,SAAS,CAAChkC,IAAI,CAAC,CAAC,CAAC;IACtF,CAAC,CAAC;EACN;EACA;EACA0kC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACT,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACnkC,WAAW,CAAC,CAAC;MACtC,IAAI,CAACmkC,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACA;IAAS,IAAI,CAAC/2B,IAAI,YAAAk4B,yBAAAh4B,CAAA;MAAA,YAAAA,CAAA,IAAwF22B,gBAAgB,EArK1Bh/B,sDAAE,CAqK0CA,iDAAS,GArKrDA,sDAAE,CAqKgEa,2DAAW,GArK7Eb,sDAAE,CAqKwFD,sDAAQ;IAAA,CAA6D;EAAE;EACjQ;IAAS,IAAI,CAACyI,KAAK,kBAtK6ExI,gEAAE;MAAA0I,KAAA,EAsKYs2B,gBAAgB;MAAAr2B,OAAA,EAAhBq2B,gBAAgB,CAAA72B,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxKoG7I,+DAAE,CAwKXg/B,gBAAgB,EAAc,CAAC;IAC9Gj2B,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAElI,2DAAW0H;IAAC,CAAC,EAAE;MAAEQ,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAC1GD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,sDAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA,MAAMugC,aAAa,CAAC;EAChB/jC,WAAWA,CAACgkC,UAAU,EAAEC,gBAAgB,EAAErnB,MAAM,EAAE6J,GAAG,EAAE;IACnD,IAAI,CAACud,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACrnB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC6J,GAAG,GAAGA,GAAG;IACd,IAAI,CAACyd,UAAU,GAAG,IAAIv/B,yCAAO,CAAC,CAAC;IAC/B,IAAI,CAACw/B,gBAAgB,GAAG,IAAI9O,4CAAU,CAAEwC,QAAQ,IAAK,IAAI,CAACjb,MAAM,CAAChH,iBAAiB,CAAC,MAAM4oB,+CAAS,CAAC,IAAI,CAACwF,UAAU,CAACxrB,aAAa,EAAE,QAAQ,CAAC,CACtItJ,IAAI,CAAC5I,0DAAS,CAAC,IAAI,CAAC49B,UAAU,CAAC,CAAC,CAChCrlC,SAAS,CAACg5B,QAAQ,CAAC,CAAC,CAAC;EAC9B;EACAuM,QAAQA,CAAA,EAAG;IACP,IAAI,CAACH,gBAAgB,CAACrqB,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA7P,WAAWA,CAAA,EAAG;IACV,IAAI,CAACk6B,gBAAgB,CAACpqB,UAAU,CAAC,IAAI,CAAC;IACtC,IAAI,CAACqqB,UAAU,CAACxlC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACwlC,UAAU,CAACvlC,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAokC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACoB,gBAAgB;EAChC;EACA;EACAN,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACG,UAAU;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIxH,QAAQA,CAACrmB,OAAO,EAAE;IACd,MAAMrP,EAAE,GAAG,IAAI,CAACk9B,UAAU,CAACxrB,aAAa;IACxC,MAAM6rB,KAAK,GAAG,IAAI,CAAC5d,GAAG,IAAI,IAAI,CAACA,GAAG,CAAChoB,KAAK,IAAI,KAAK;IACjD;IACA,IAAI0X,OAAO,CAACmuB,IAAI,IAAI,IAAI,EAAE;MACtBnuB,OAAO,CAACmuB,IAAI,GAAGD,KAAK,GAAGluB,OAAO,CAACyqB,GAAG,GAAGzqB,OAAO,CAACwqB,KAAK;IACtD;IACA,IAAIxqB,OAAO,CAACouB,KAAK,IAAI,IAAI,EAAE;MACvBpuB,OAAO,CAACouB,KAAK,GAAGF,KAAK,GAAGluB,OAAO,CAACwqB,KAAK,GAAGxqB,OAAO,CAACyqB,GAAG;IACvD;IACA;IACA,IAAIzqB,OAAO,CAACquB,MAAM,IAAI,IAAI,EAAE;MACxBruB,OAAO,CAACsuB,GAAG,GACP39B,EAAE,CAAC49B,YAAY,GAAG59B,EAAE,CAAC69B,YAAY,GAAGxuB,OAAO,CAACquB,MAAM;IAC1D;IACA;IACA,IAAIH,KAAK,IAAI3H,2EAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,gCAAgC;MACrE,IAAIvmB,OAAO,CAACmuB,IAAI,IAAI,IAAI,EAAE;QACtBnuB,OAAO,CAACouB,KAAK,GACTz9B,EAAE,CAAC89B,WAAW,GAAG99B,EAAE,CAAC+9B,WAAW,GAAG1uB,OAAO,CAACmuB,IAAI;MACtD;MACA,IAAI5H,2EAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,kCAAkC;QAC9DvmB,OAAO,CAACmuB,IAAI,GAAGnuB,OAAO,CAACouB,KAAK;MAChC,CAAC,MACI,IAAI7H,2EAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,iCAAiC;QAClEvmB,OAAO,CAACmuB,IAAI,GAAGnuB,OAAO,CAACouB,KAAK,GAAG,CAACpuB,OAAO,CAACouB,KAAK,GAAGpuB,OAAO,CAACouB,KAAK;MACjE;IACJ,CAAC,MACI;MACD,IAAIpuB,OAAO,CAACouB,KAAK,IAAI,IAAI,EAAE;QACvBpuB,OAAO,CAACmuB,IAAI,GACRx9B,EAAE,CAAC89B,WAAW,GAAG99B,EAAE,CAAC+9B,WAAW,GAAG1uB,OAAO,CAACouB,KAAK;MACvD;IACJ;IACA,IAAI,CAACO,qBAAqB,CAAC3uB,OAAO,CAAC;EACvC;EACA2uB,qBAAqBA,CAAC3uB,OAAO,EAAE;IAC3B,MAAMrP,EAAE,GAAG,IAAI,CAACk9B,UAAU,CAACxrB,aAAa;IACxC,IAAI4jB,6EAAsB,CAAC,CAAC,EAAE;MAC1Bt1B,EAAE,CAAC01B,QAAQ,CAACrmB,OAAO,CAAC;IACxB,CAAC,MACI;MACD,IAAIA,OAAO,CAACsuB,GAAG,IAAI,IAAI,EAAE;QACrB39B,EAAE,CAACi+B,SAAS,GAAG5uB,OAAO,CAACsuB,GAAG;MAC9B;MACA,IAAItuB,OAAO,CAACmuB,IAAI,IAAI,IAAI,EAAE;QACtBx9B,EAAE,CAACq2B,UAAU,GAAGhnB,OAAO,CAACmuB,IAAI;MAChC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrD,mBAAmBA,CAACnV,IAAI,EAAE;IACtB,MAAMkZ,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMn+B,EAAE,GAAG,IAAI,CAACk9B,UAAU,CAACxrB,aAAa;IACxC,IAAIsT,IAAI,IAAI,KAAK,EAAE;MACf,OAAOhlB,EAAE,CAACi+B,SAAS;IACvB;IACA,IAAIjZ,IAAI,IAAI,QAAQ,EAAE;MAClB,OAAOhlB,EAAE,CAAC49B,YAAY,GAAG59B,EAAE,CAAC69B,YAAY,GAAG79B,EAAE,CAACi+B,SAAS;IAC3D;IACA;IACA,MAAMV,KAAK,GAAG,IAAI,CAAC5d,GAAG,IAAI,IAAI,CAACA,GAAG,CAAChoB,KAAK,IAAI,KAAK;IACjD,IAAIqtB,IAAI,IAAI,OAAO,EAAE;MACjBA,IAAI,GAAGuY,KAAK,GAAGY,KAAK,GAAGD,IAAI;IAC/B,CAAC,MACI,IAAIlZ,IAAI,IAAI,KAAK,EAAE;MACpBA,IAAI,GAAGuY,KAAK,GAAGW,IAAI,GAAGC,KAAK;IAC/B;IACA,IAAIZ,KAAK,IAAI3H,2EAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,kCAAkC;MACvE;MACA;MACA,IAAI5Q,IAAI,IAAIkZ,IAAI,EAAE;QACd,OAAOl+B,EAAE,CAAC89B,WAAW,GAAG99B,EAAE,CAAC+9B,WAAW,GAAG/9B,EAAE,CAACq2B,UAAU;MAC1D,CAAC,MACI;QACD,OAAOr2B,EAAE,CAACq2B,UAAU;MACxB;IACJ,CAAC,MACI,IAAIkH,KAAK,IAAI3H,2EAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,iCAAiC;MAC3E;MACA;MACA,IAAI5Q,IAAI,IAAIkZ,IAAI,EAAE;QACd,OAAOl+B,EAAE,CAACq2B,UAAU,GAAGr2B,EAAE,CAAC89B,WAAW,GAAG99B,EAAE,CAAC+9B,WAAW;MAC1D,CAAC,MACI;QACD,OAAO,CAAC/9B,EAAE,CAACq2B,UAAU;MACzB;IACJ,CAAC,MACI;MACD;MACA;MACA,IAAIrR,IAAI,IAAIkZ,IAAI,EAAE;QACd,OAAOl+B,EAAE,CAACq2B,UAAU;MACxB,CAAC,MACI;QACD,OAAOr2B,EAAE,CAAC89B,WAAW,GAAG99B,EAAE,CAAC+9B,WAAW,GAAG/9B,EAAE,CAACq2B,UAAU;MAC1D;IACJ;EACJ;EACA;IAAS,IAAI,CAACvxB,IAAI,YAAAs5B,sBAAAp5B,CAAA;MAAA,YAAAA,CAAA,IAAwFi4B,aAAa,EAtUvBtgC,+DAAE,CAsUuCA,qDAAa,GAtUtDA,+DAAE,CAsUiEg/B,gBAAgB,GAtUnFh/B,+DAAE,CAsU8FA,iDAAS,GAtUzGA,+DAAE,CAsUoHk7B,8DAAiB;IAAA,CAA4D;EAAE;EACrS;IAAS,IAAI,CAACzlB,IAAI,kBAvU8EzV,+DAAE;MAAA+I,IAAA,EAuUJu3B,aAAa;MAAA3qB,SAAA;MAAAipB,UAAA;IAAA,EAAoF;EAAE;AACrM;AACA;EAAA,QAAA/1B,SAAA,oBAAAA,SAAA,KAzUoG7I,+DAAE,CAyUXsgC,aAAa,EAAc,CAAC;IAC3Gv3B,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,mCAAmC;MAC7C4oB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE71B,IAAI,EAAE/I,qDAAawV;IAAC,CAAC,EAAE;MAAEzM,IAAI,EAAEi2B;IAAiB,CAAC,EAAE;MAAEj2B,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAEmyB,8DAAiB;MAAElyB,UAAU,EAAE,CAAC;QAChJD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,MAAMihC,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBplC,WAAWA,CAACmI,SAAS,EAAEyU,MAAM,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAAC1U,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACk9B,OAAO,GAAG,IAAI1gC,yCAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC2gC,eAAe,GAAIv1B,KAAK,IAAK;MAC9B,IAAI,CAACs1B,OAAO,CAAC3mC,IAAI,CAACqR,KAAK,CAAC;IAC5B,CAAC;IACD,IAAI,CAAC7H,SAAS,GAAG2U,QAAQ;IACzBD,MAAM,CAAChH,iBAAiB,CAAC,MAAM;MAC3B,IAAIzN,SAAS,CAAC8C,SAAS,EAAE;QACrB,MAAMyI,MAAM,GAAG,IAAI,CAACyO,UAAU,CAAC,CAAC;QAChC;QACA;QACAzO,MAAM,CAACoC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACwvB,eAAe,CAAC;QACvD5xB,MAAM,CAACoC,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAACwvB,eAAe,CAAC;MACtE;MACA;MACA;MACA,IAAI,CAACx3B,MAAM,CAAC,CAAC,CAACjP,SAAS,CAAC,MAAO,IAAI,CAAC0mC,aAAa,GAAG,IAAK,CAAC;IAC9D,CAAC,CAAC;EACN;EACAx7B,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC5B,SAAS,CAAC8C,SAAS,EAAE;MAC1B,MAAMyI,MAAM,GAAG,IAAI,CAACyO,UAAU,CAAC,CAAC;MAChCzO,MAAM,CAACiC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC2vB,eAAe,CAAC;MAC1D5xB,MAAM,CAACiC,mBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAAC2vB,eAAe,CAAC;IACzE;IACA,IAAI,CAACD,OAAO,CAAC1mC,QAAQ,CAAC,CAAC;EAC3B;EACA;EACAmiC,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACyE,aAAa,EAAE;MACrB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9B;IACA,MAAMzN,MAAM,GAAG;MAAE8E,KAAK,EAAE,IAAI,CAAC0I,aAAa,CAAC1I,KAAK;MAAEK,MAAM,EAAE,IAAI,CAACqI,aAAa,CAACrI;IAAO,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAAC/0B,SAAS,CAAC8C,SAAS,EAAE;MAC3B,IAAI,CAACs6B,aAAa,GAAG,IAAI;IAC7B;IACA,OAAOxN,MAAM;EACjB;EACA;EACA0N,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACvD,MAAM;MAAE9I,KAAK;MAAEK;IAAO,CAAC,GAAG,IAAI,CAAC4D,eAAe,CAAC,CAAC;IAChD,OAAO;MACH2D,GAAG,EAAEiB,cAAc,CAACjB,GAAG;MACvBH,IAAI,EAAEoB,cAAc,CAACpB,IAAI;MACzBE,MAAM,EAAEkB,cAAc,CAACjB,GAAG,GAAGvH,MAAM;MACnCqH,KAAK,EAAEmB,cAAc,CAACpB,IAAI,GAAGzH,KAAK;MAClCK,MAAM;MACNL;IACJ,CAAC;EACL;EACA;EACA8I,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA,IAAI,CAAC,IAAI,CAACx9B,SAAS,CAAC8C,SAAS,EAAE;MAC3B,OAAO;QAAEw5B,GAAG,EAAE,CAAC;QAAEH,IAAI,EAAE;MAAE,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMznB,QAAQ,GAAG,IAAI,CAAC3U,SAAS;IAC/B,MAAMwL,MAAM,GAAG,IAAI,CAACyO,UAAU,CAAC,CAAC;IAChC,MAAMwE,eAAe,GAAG9J,QAAQ,CAAC8J,eAAe;IAChD,MAAMif,YAAY,GAAGjf,eAAe,CAACkf,qBAAqB,CAAC,CAAC;IAC5D,MAAMpB,GAAG,GAAG,CAACmB,YAAY,CAACnB,GAAG,IACzB5nB,QAAQ,CAAC3R,IAAI,CAAC65B,SAAS,IACvBrxB,MAAM,CAACoyB,OAAO,IACdnf,eAAe,CAACoe,SAAS,IACzB,CAAC;IACL,MAAMT,IAAI,GAAG,CAACsB,YAAY,CAACtB,IAAI,IAC3BznB,QAAQ,CAAC3R,IAAI,CAACiyB,UAAU,IACxBzpB,MAAM,CAACqyB,OAAO,IACdpf,eAAe,CAACwW,UAAU,IAC1B,CAAC;IACL,OAAO;MAAEsH,GAAG;MAAEH;IAAK,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIx2B,MAAMA,CAACk4B,YAAY,GAAGb,mBAAmB,EAAE;IACvC,OAAOa,YAAY,GAAG,CAAC,GAAG,IAAI,CAACX,OAAO,CAACn2B,IAAI,CAACjQ,yDAAS,CAAC+mC,YAAY,CAAC,CAAC,GAAG,IAAI,CAACX,OAAO;EACvF;EACA;EACAljB,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACja,SAAS,CAACuM,WAAW,IAAIf,MAAM;EAC/C;EACA;EACA8xB,mBAAmBA,CAAA,EAAG;IAClB,MAAM9xB,MAAM,GAAG,IAAI,CAACyO,UAAU,CAAC,CAAC;IAChC,IAAI,CAACojB,aAAa,GAAG,IAAI,CAACp9B,SAAS,CAAC8C,SAAS,GACvC;MAAE4xB,KAAK,EAAEnpB,MAAM,CAACuyB,UAAU;MAAE/I,MAAM,EAAExpB,MAAM,CAACwyB;IAAY,CAAC,GACxD;MAAErJ,KAAK,EAAE,CAAC;MAAEK,MAAM,EAAE;IAAE,CAAC;EACjC;EACA;IAAS,IAAI,CAACtxB,IAAI,YAAAu6B,sBAAAr6B,CAAA;MAAA,YAAAA,CAAA,IAAwFs5B,aAAa,EA1cvB3hC,sDAAE,CA0cuCa,2DAAW,GA1cpDb,sDAAE,CA0c+DA,iDAAS,GA1c1EA,sDAAE,CA0cqFD,sDAAQ;IAAA,CAA6D;EAAE;EAC9P;IAAS,IAAI,CAACyI,KAAK,kBA3c6ExI,gEAAE;MAAA0I,KAAA,EA2cYi5B,aAAa;MAAAh5B,OAAA,EAAbg5B,aAAa,CAAAx5B,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7coG7I,+DAAE,CA6cX2hC,aAAa,EAAc,CAAC;IAC3G54B,IAAI,EAAE5I,qDAAU;IAChBpB,IAAI,EAAE,CAAC;MAAE6J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElI,2DAAW0H;IAAC,CAAC,EAAE;MAAEQ,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAC1GD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgB,sDAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM4iC,kBAAkB,GAAG,IAAIniC,yDAAc,CAAC,oBAAoB,CAAC;AACnE;AACA;AACA;AACA,MAAMoiC,oBAAoB,SAAStC,aAAa,CAAC;EAC7C/jC,WAAWA,CAACgkC,UAAU,EAAEC,gBAAgB,EAAErnB,MAAM,EAAE6J,GAAG,EAAE;IACnD,KAAK,CAACud,UAAU,EAAEC,gBAAgB,EAAErnB,MAAM,EAAE6J,GAAG,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;EACI6f,mBAAmBA,CAACC,WAAW,EAAE;IAC7B,MAAMC,UAAU,GAAG,IAAI,CAACxC,UAAU,CAACxrB,aAAa;IAChD,OAAO+tB,WAAW,KAAK,YAAY,GAAGC,UAAU,CAAC3B,WAAW,GAAG2B,UAAU,CAAC7B,YAAY;EAC1F;EACA;IAAS,IAAI,CAAC/4B,IAAI,YAAA66B,6BAAA36B,CAAA;MAAA,YAAAA,CAAA,IAAwFu6B,oBAAoB,EAxe9B5iC,+DAAE,CAwe8CA,qDAAa,GAxe7DA,+DAAE,CAwewEg/B,gBAAgB,GAxe1Fh/B,+DAAE,CAweqGA,iDAAS,GAxehHA,+DAAE,CAwe2Hk7B,8DAAiB;IAAA,CAA4D;EAAE;EAC5S;IAAS,IAAI,CAACzlB,IAAI,kBAze8EzV,+DAAE;MAAA+I,IAAA,EAyeJ65B,oBAAoB;MAAA9sB,QAAA,GAzelB9V,wEAAE;IAAA,EAyewD;EAAE;AAChK;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KA3eoG7I,+DAAE,CA2eX4iC,oBAAoB,EAAc,CAAC;IAClH75B,IAAI,EAAEzI,oDAASA;EACnB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEyI,IAAI,EAAE/I,qDAAawV;IAAC,CAAC,EAAE;MAAEzM,IAAI,EAAEi2B;IAAiB,CAAC,EAAE;MAAEj2B,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAEmyB,8DAAiB;MAAElyB,UAAU,EAAE,CAAC;QAChJD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,SAASyiC,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACzB,OAAOD,EAAE,CAACjG,KAAK,IAAIkG,EAAE,CAAClG,KAAK,IAAIiG,EAAE,CAAChG,GAAG,IAAIiG,EAAE,CAACjG,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkG,gBAAgB,GAAG,OAAOtmC,qBAAqB,KAAK,WAAW,GAAGsB,0DAAuB,GAAGW,gDAAa;AAC/G;AACA,MAAMskC,wBAAwB,SAASV,oBAAoB,CAAC;EACxD;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACS,YAAY;EAC5B;EACA,IAAIT,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACS,YAAY,KAAKT,WAAW,EAAE;MACnC,IAAI,CAACS,YAAY,GAAGT,WAAW;MAC/B,IAAI,CAACU,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACzoC,KAAK,EAAE;IAClB,IAAI,CAAC0oC,WAAW,GAAG5gC,4EAAqB,CAAC9H,KAAK,CAAC;EACnD;EACAuB,WAAWA,CAACgkC,UAAU,EAAEoD,kBAAkB,EAAExqB,MAAM,EAAEslB,eAAe,EAAEzb,GAAG,EAAEwd,gBAAgB,EAAEoD,aAAa,EAAEvE,UAAU,EAAE;IACnH,KAAK,CAACkB,UAAU,EAAEC,gBAAgB,EAAErnB,MAAM,EAAE6J,GAAG,CAAC;IAChD,IAAI,CAACud,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACoD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAClF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACY,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC36B,SAAS,GAAGzE,qDAAM,CAACsI,2DAAQ,CAAC;IACjC;IACA,IAAI,CAACs7B,gBAAgB,GAAG,IAAI3iC,yCAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAAC4iC,qBAAqB,GAAG,IAAI5iC,yCAAO,CAAC,CAAC;IAC1C,IAAI,CAACqiC,YAAY,GAAG,UAAU;IAC9B,IAAI,CAACG,WAAW,GAAG,KAAK;IACxB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC9H,mBAAmB,GAAG,IAAIhK,4CAAU,CAAEwC,QAAQ,IAAK,IAAI,CAACqK,eAAe,CAAC7C,mBAAmB,CAACxgC,SAAS,CAACe,KAAK,IAAIqD,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACyZ,MAAM,CAACsG,GAAG,CAAC,MAAM2U,QAAQ,CAACn5B,IAAI,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/L;IACA,IAAI,CAAC4nC,mBAAmB,GAAG,IAAI,CAACD,qBAAqB;IACrD;AACR;AACA;IACQ,IAAI,CAACE,iBAAiB,GAAG,CAAC;IAC1B;IACA,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B;IACA,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACC,cAAc,GAAG;MAAEjH,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IAC1C;IACA,IAAI,CAACiH,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACtC,aAAa,GAAG,CAAC;IACtB;IACA,IAAI,CAACuC,sBAAsB,GAAG,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,kCAAkC,GAAG,KAAK;IAC/C;IACA,IAAI,CAACC,yBAAyB,GAAG,KAAK;IACtC;IACA,IAAI,CAACC,wBAAwB,GAAG,EAAE;IAClC;IACA,IAAI,CAACC,gBAAgB,GAAGlmC,+CAAY,CAACkL,KAAK;IAC1C,IAAI,CAACg1B,eAAe,KAAK,OAAO51B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAM2C,KAAK,CAAC,gFAAgF,CAAC;IACjG;IACA,IAAI,CAACi5B,gBAAgB,GAAGb,aAAa,CAACv5B,MAAM,CAAC,CAAC,CAACjP,SAAS,CAAC,MAAM;MAC3D,IAAI,CAACspC,iBAAiB,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAACrF,UAAU,EAAE;MAClB;MACA,IAAI,CAACkB,UAAU,CAACxrB,aAAa,CAACzN,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrE,IAAI,CAAC83B,UAAU,GAAG,IAAI;IAC1B;EACJ;EACAsB,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAAC,IAAI,CAACj8B,SAAS,CAAC8C,SAAS,EAAE;MAC3B;IACJ;IACA,IAAI,IAAI,CAAC63B,UAAU,KAAK,IAAI,EAAE;MAC1B,KAAK,CAACsB,QAAQ,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA;IACA,IAAI,CAACxnB,MAAM,CAAChH,iBAAiB,CAAC,MAAM3S,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAC7D,IAAI,CAACilC,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAClG,eAAe,CAACxC,MAAM,CAAC,IAAI,CAAC;MACjC,IAAI,CAACoD,UAAU,CACVC,eAAe,CAAC,CAAC,CACjB7zB,IAAI;MACT;MACAomB,0DAAS,CAAC,IAAI,CAAC;MACf;MACA;MACA;MACAr2B,yDAAS,CAAC,CAAC,EAAE6nC,gBAAgB,CAAC;MAC9B;MACA;MACA;MACAxgC,0DAAS,CAAC,IAAI,CAAC49B,UAAU,CAAC,CAAC,CACtBrlC,SAAS,CAAC,MAAM,IAAI,CAACqjC,eAAe,CAACnC,iBAAiB,CAAC,CAAC,CAAC;MAC9D,IAAI,CAACsI,0BAA0B,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;EACP;EACAt+B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4gB,MAAM,CAAC,CAAC;IACb,IAAI,CAACuX,eAAe,CAACvX,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC4c,qBAAqB,CAAC5oC,QAAQ,CAAC,CAAC;IACrC,IAAI,CAAC2oC,gBAAgB,CAAC3oC,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACupC,gBAAgB,CAAC1pC,WAAW,CAAC,CAAC;IACnC,KAAK,CAACuL,WAAW,CAAC,CAAC;EACvB;EACA;EACA21B,MAAMA,CAAC4I,KAAK,EAAE;IACV,IAAI,IAAI,CAACC,MAAM,KAAK,OAAOj8B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChE,MAAM2C,KAAK,CAAC,+CAA+C,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAAC2N,MAAM,CAAChH,iBAAiB,CAAC,MAAM;MAChC,IAAI,CAAC2yB,MAAM,GAAGD,KAAK;MACnB,IAAI,CAACC,MAAM,CAACC,UAAU,CAACt5B,IAAI,CAAC5I,0DAAS,CAAC,IAAI,CAACghC,gBAAgB,CAAC,CAAC,CAACzoC,SAAS,CAAC4pC,IAAI,IAAI;QAC5E,MAAMC,SAAS,GAAGD,IAAI,CAAC5nC,MAAM;QAC7B,IAAI6nC,SAAS,KAAK,IAAI,CAACb,WAAW,EAAE;UAChC,IAAI,CAACA,WAAW,GAAGa,SAAS;UAC5B,IAAI,CAACxG,eAAe,CAAClC,mBAAmB,CAAC,CAAC;QAC9C;QACA,IAAI,CAAC2I,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAhe,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC4d,MAAM,GAAG,IAAI;IAClB,IAAI,CAACjB,gBAAgB,CAAC5oC,IAAI,CAAC,CAAC;EAChC;EACA;EACA6hC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACsH,WAAW;EAC3B;EACA;EACA/G,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACyE,aAAa;EAC7B;EACA;EACA;EACA;EACA;EACA;EACA9E,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACmH,cAAc;EAC9B;EACAgB,yCAAyCA,CAAC9c,IAAI,EAAE;IAC5C,OAAO,IAAI,CAAC+X,aAAa,CAAC,CAAC,CAACrrB,aAAa,CAACqtB,qBAAqB,CAAC,CAAC,CAAC/Z,IAAI,CAAC;EAC3E;EACA;AACJ;AACA;AACA;EACIwU,mBAAmBA,CAAC9S,IAAI,EAAE;IACtB,IAAI,IAAI,CAACia,iBAAiB,KAAKja,IAAI,EAAE;MACjC,IAAI,CAACia,iBAAiB,GAAGja,IAAI;MAC7B,IAAI,CAACyZ,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACoB,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;EACAvG,gBAAgBA,CAAC+G,KAAK,EAAE;IACpB,IAAI,CAAClC,WAAW,CAAC,IAAI,CAACiB,cAAc,EAAEiB,KAAK,CAAC,EAAE;MAC1C,IAAI,IAAI,CAAC3B,UAAU,EAAE;QACjB2B,KAAK,GAAG;UAAElI,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAEQ,IAAI,CAACG,GAAG,CAAC,IAAI,CAACqG,cAAc,CAAChH,GAAG,EAAEiI,KAAK,CAACjI,GAAG;QAAE,CAAC;MAC3E;MACA,IAAI,CAAC2G,qBAAqB,CAAC7oC,IAAI,CAAE,IAAI,CAACkpC,cAAc,GAAGiB,KAAM,CAAC;MAC9D,IAAI,CAACR,0BAA0B,CAAC,MAAM,IAAI,CAACnG,eAAe,CAACjC,iBAAiB,CAAC,CAAC,CAAC;IACnF;EACJ;EACA;AACJ;AACA;EACI6I,+BAA+BA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACf,kCAAkC,GAAG,IAAI,GAAG,IAAI,CAACD,sBAAsB;EACvF;EACA;AACJ;AACA;AACA;EACI/F,wBAAwBA,CAACgH,MAAM,EAAEC,EAAE,GAAG,UAAU,EAAE;IAC9C;IACAD,MAAM,GAAG,IAAI,CAAC7B,UAAU,IAAI8B,EAAE,KAAK,UAAU,GAAG,CAAC,GAAGD,MAAM;IAC1D;IACA;IACA,MAAM1E,KAAK,GAAG,IAAI,CAAC5d,GAAG,IAAI,IAAI,CAACA,GAAG,CAAChoB,KAAK,IAAI,KAAK;IACjD,MAAMwqC,YAAY,GAAG,IAAI,CAAC1C,WAAW,IAAI,YAAY;IACrD,MAAM2C,IAAI,GAAGD,YAAY,GAAG,GAAG,GAAG,GAAG;IACrC,MAAME,aAAa,GAAGF,YAAY,IAAI5E,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACpD,IAAI+E,SAAS,GAAI,YAAWF,IAAK,IAAG/gB,MAAM,CAACghB,aAAa,GAAGJ,MAAM,CAAE,KAAI;IACvE,IAAI,CAACjB,sBAAsB,GAAGiB,MAAM;IACpC,IAAIC,EAAE,KAAK,QAAQ,EAAE;MACjBI,SAAS,IAAK,aAAYF,IAAK,SAAQ;MACvC;MACA;MACA;MACA,IAAI,CAACnB,kCAAkC,GAAG,IAAI;IAClD;IACA,IAAI,IAAI,CAACsB,yBAAyB,IAAID,SAAS,EAAE;MAC7C;MACA;MACA,IAAI,CAACC,yBAAyB,GAAGD,SAAS;MAC1C,IAAI,CAACf,0BAA0B,CAAC,MAAM;QAClC,IAAI,IAAI,CAACN,kCAAkC,EAAE;UACzC,IAAI,CAACD,sBAAsB,IAAI,IAAI,CAACwB,0BAA0B,CAAC,CAAC;UAChE,IAAI,CAACvB,kCAAkC,GAAG,KAAK;UAC/C,IAAI,CAAChG,wBAAwB,CAAC,IAAI,CAAC+F,sBAAsB,CAAC;QAC9D,CAAC,MACI;UACD,IAAI,CAAC5F,eAAe,CAAChC,uBAAuB,CAAC,CAAC;QAClD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,cAAcA,CAAC0I,MAAM,EAAE3I,QAAQ,GAAG,MAAM,EAAE;IACtC,MAAMjqB,OAAO,GAAG;MAAEiqB;IAAS,CAAC;IAC5B,IAAI,IAAI,CAACmG,WAAW,KAAK,YAAY,EAAE;MACnCpwB,OAAO,CAACwqB,KAAK,GAAGoI,MAAM;IAC1B,CAAC,MACI;MACD5yB,OAAO,CAACsuB,GAAG,GAAGsE,MAAM;IACxB;IACA,IAAI,CAACjG,UAAU,CAACtG,QAAQ,CAACrmB,OAAO,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIgqB,aAAaA,CAACvgC,KAAK,EAAEwgC,QAAQ,GAAG,MAAM,EAAE;IACpC,IAAI,CAAC8B,eAAe,CAAC/B,aAAa,CAACvgC,KAAK,EAAEwgC,QAAQ,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIa,mBAAmBA,CAACnV,IAAI,EAAE;IACtB;IACA,IAAImV,mBAAmB;IACvB,IAAI,IAAI,CAAC6B,UAAU,IAAI,IAAI,EAAE;MACzB7B,mBAAmB,GAAIsI,KAAK,IAAK,KAAK,CAACtI,mBAAmB,CAACsI,KAAK,CAAC;IACrE,CAAC,MACI;MACDtI,mBAAmB,GAAIsI,KAAK,IAAK,IAAI,CAACzG,UAAU,CAAC7B,mBAAmB,CAACsI,KAAK,CAAC;IAC/E;IACA,OAAOnI,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEN,mBAAmB,CAACnV,IAAI,KAAK,IAAI,CAACya,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,GACjG,IAAI,CAACiD,qBAAqB,CAAC,CAAC,CAAC;EACrC;EACA;AACJ;AACA;AACA;EACIA,qBAAqBA,CAAC1d,IAAI,EAAE;IACxB,IAAI2d,QAAQ;IACZ,MAAMzE,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMZ,KAAK,GAAG,IAAI,CAAC5d,GAAG,EAAEhoB,KAAK,IAAI,KAAK;IACtC,IAAIqtB,IAAI,IAAI,OAAO,EAAE;MACjB2d,QAAQ,GAAGpF,KAAK,GAAGY,KAAK,GAAGD,IAAI;IACnC,CAAC,MACI,IAAIlZ,IAAI,IAAI,KAAK,EAAE;MACpB2d,QAAQ,GAAGpF,KAAK,GAAGW,IAAI,GAAGC,KAAK;IACnC,CAAC,MACI,IAAInZ,IAAI,EAAE;MACX2d,QAAQ,GAAG3d,IAAI;IACnB,CAAC,MACI;MACD2d,QAAQ,GAAG,IAAI,CAAClD,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;IACjE;IACA,MAAMmD,kBAAkB,GAAG,IAAI,CAAC5G,UAAU,CAAC8F,yCAAyC,CAACa,QAAQ,CAAC;IAC9F,MAAME,kBAAkB,GAAG,IAAI,CAAC3F,UAAU,CAACxrB,aAAa,CAACqtB,qBAAqB,CAAC,CAAC,CAAC4D,QAAQ,CAAC;IAC1F,OAAOE,kBAAkB,GAAGD,kBAAkB;EAClD;EACA;EACAJ,0BAA0BA,CAAA,EAAG;IACzB,MAAMM,SAAS,GAAG,IAAI,CAACC,eAAe,CAACrxB,aAAa;IACpD,OAAO,IAAI,CAAC+tB,WAAW,KAAK,YAAY,GAAGqD,SAAS,CAACj2B,WAAW,GAAGi2B,SAAS,CAACh2B,YAAY;EAC7F;EACA;AACJ;AACA;AACA;EACIk2B,gBAAgBA,CAACjB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACN,MAAM,EAAE;MACd,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACA,MAAM,CAACuB,gBAAgB,CAACjB,KAAK,EAAE,IAAI,CAACtC,WAAW,CAAC;EAChE;EACA;EACA4B,iBAAiBA,CAAA,EAAG;IAChB;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAClG,eAAe,CAAClC,mBAAmB,CAAC,CAAC;EAC9C;EACA;EACAoI,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC7C,aAAa,GAAG,IAAI,CAACzC,UAAU,CAACwD,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC;EAC9E;EACA;EACA8B,0BAA0BA,CAAC0B,QAAQ,EAAE;IACjC,IAAIA,QAAQ,EAAE;MACV,IAAI,CAAC9B,wBAAwB,CAAC3nC,IAAI,CAACypC,QAAQ,CAAC;IAChD;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC/B,yBAAyB,EAAE;MACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;MACrC,IAAI,CAACprB,MAAM,CAAChH,iBAAiB,CAAC,MAAM3S,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC7D,IAAI,CAACwlC,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC;IACP;EACJ;EACA;EACAA,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACX,yBAAyB,GAAG,KAAK;IACtC;IACA;IACA;IACA;IACA,IAAI,CAAC6B,eAAe,CAACrxB,aAAa,CAAC3N,KAAK,CAACu+B,SAAS,GAAG,IAAI,CAACC,yBAAyB;IACnF;IACA;IACA;IACA,IAAI,CAACzsB,MAAM,CAACsG,GAAG,CAAC,MAAM,IAAI,CAACkkB,kBAAkB,CAAC4C,YAAY,CAAC,CAAC,CAAC;IAC7D,MAAMC,uBAAuB,GAAG,IAAI,CAAChC,wBAAwB;IAC7D,IAAI,CAACA,wBAAwB,GAAG,EAAE;IAClC,KAAK,MAAMxwB,EAAE,IAAIwyB,uBAAuB,EAAE;MACtCxyB,EAAE,CAAC,CAAC;IACR;EACJ;EACA;EACAwvB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACU,mBAAmB,GACpB,IAAI,CAACpB,WAAW,KAAK,YAAY,GAAG,EAAE,GAAI,GAAE,IAAI,CAACkB,iBAAkB,IAAG;IAC1E,IAAI,CAACC,kBAAkB,GACnB,IAAI,CAACnB,WAAW,KAAK,YAAY,GAAI,GAAE,IAAI,CAACkB,iBAAkB,IAAG,GAAG,EAAE;EAC9E;EACA;IAAS,IAAI,CAAC77B,IAAI,YAAAs+B,iCAAAp+B,CAAA;MAAA,YAAAA,CAAA,IAAwFi7B,wBAAwB,EAt2BlCtjC,+DAAE,CAs2BkDA,qDAAa,GAt2BjEA,+DAAE,CAs2B4EA,4DAAoB,GAt2BlGA,+DAAE,CAs2B6GA,iDAAS,GAt2BxHA,+DAAE,CAs2BmIs7B,uBAAuB,MAt2B5Jt7B,+DAAE,CAs2BuLk7B,8DAAiB,MAt2B1Ml7B,+DAAE,CAs2BqOg/B,gBAAgB,GAt2BvPh/B,+DAAE,CAs2BkQ2hC,aAAa,GAt2BjR3hC,+DAAE,CAs2B4R2iC,kBAAkB;IAAA,CAA4D;EAAE;EAC9c;IAAS,IAAI,CAACgE,IAAI,kBAv2B8E3mC,+DAAE;MAAA+I,IAAA,EAu2BJu6B,wBAAwB;MAAA3tB,SAAA;MAAAkxB,SAAA,WAAAC,+BAAAjjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAv2BtB7jB,yDAAE,CAAAo7B,GAAA;QAAA;QAAA,IAAAvX,EAAA;UAAA,IAAAmjB,EAAA;UAAFhnC,4DAAE,CAAAgnC,EAAA,GAAFhnC,yDAAE,QAAA8jB,GAAA,CAAAsiB,eAAA,GAAAY,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAA1jB,QAAA;MAAAC,YAAA,WAAA0jB,sCAAAxjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF7jB,yDAAE,8CAAA8jB,GAAA,CAAAgf,WAAA,8DAAAhf,GAAA,CAAAgf,WAAA;QAAA;MAAA;MAAAltB,MAAA;QAAAktB,WAAA;QAAAW,UAAA;MAAA;MAAAhjB,OAAA;QAAAmb,mBAAA;MAAA;MAAAgD,UAAA;MAAA9oB,QAAA,GAAF9V,gEAAE,CAu2B0c,CACpiB;QACIikB,OAAO,EAAEqc,aAAa;QACtBzB,UAAU,EAAEA,CAAC0I,iBAAiB,EAAErL,QAAQ,KAAKqL,iBAAiB,IAAIrL,QAAQ;QAC1E4C,IAAI,EAAE,CAAC,CAAC,IAAIr+B,mDAAQ,CAAC,CAAC,EAAE,IAAIL,iDAAM,CAACuiC,kBAAkB,CAAC,CAAC,EAAEW,wBAAwB;MACrF,CAAC,CACJ,GA72B2FtjC,wEAAE,EAAFA,iEAAE;MAAAynC,kBAAA,EAAApM,GAAA;MAAAqM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAjkB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF7jB,6DAAE;UAAFA,4DAAE,eA62B6Q,CAAC;UA72BhRA,0DAAE,EA62B0S,CAAC;UA72B7SA,0DAAE,CA62BkT,CAAC;UA72BrTA,uDAAE,YA62BumB,CAAC;QAAA;QAAA,IAAA6jB,EAAA;UA72B1mB7jB,uDAAE,EA62ByjB,CAAC;UA72B5jBA,yDAAE,UAAA8jB,GAAA,CAAAmgB,kBA62ByjB,CAAC,WAAAngB,GAAA,CAAAogB,mBAAD,CAAC;QAAA;MAAA;MAAAoE,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAo1D;EAAE;AACt/E;AACA;EAAA,QAAA3/B,SAAA,oBAAAA,SAAA,KA/2BoG7I,+DAAE,CA+2BXsjC,wBAAwB,EAAc,CAAC;IACtHv6B,IAAI,EAAE2xB,oDAAS;IACf37B,IAAI,EAAE,CAAC;MAAEiX,QAAQ,EAAE,6BAA6B;MAAEoO,IAAI,EAAE;QAC5C,OAAO,EAAE,6BAA6B;QACtC,mDAAmD,EAAE,8BAA8B;QACnF,iDAAiD,EAAE;MACvD,CAAC;MAAEmkB,aAAa,EAAE5N,4DAAiB,CAAC8N,IAAI;MAAED,eAAe,EAAE5N,kEAAuB,CAAC8N,MAAM;MAAE9J,UAAU,EAAE,IAAI;MAAEza,SAAS,EAAE,CACpH;QACIF,OAAO,EAAEqc,aAAa;QACtBzB,UAAU,EAAEA,CAAC0I,iBAAiB,EAAErL,QAAQ,KAAKqL,iBAAiB,IAAIrL,QAAQ;QAC1E4C,IAAI,EAAE,CAAC,CAAC,IAAIr+B,mDAAQ,CAAC,CAAC,EAAE,IAAIL,iDAAM,CAACuiC,kBAAkB,CAAC,CAAC,EAAEW,wBAAwB;MACrF,CAAC,CACJ;MAAEuE,QAAQ,EAAE,shBAAshB;MAAES,MAAM,EAAE,CAAC,srDAAsrD;IAAE,CAAC;EACnvE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEv/B,IAAI,EAAE/I,qDAAawV;IAAC,CAAC,EAAE;MAAEzM,IAAI,EAAE/I,4DAAoB0mC;IAAC,CAAC,EAAE;MAAE39B,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAE9L,SAAS;MAAE+L,UAAU,EAAE,CAAC;QAC5ID,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACu8B,uBAAuB;MAClC,CAAC;IAAE,CAAC,EAAE;MAAEvyB,IAAI,EAAEmyB,8DAAiB;MAAElyB,UAAU,EAAE,CAAC;QAC1CD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC;IAAE,CAAC,EAAE;MAAEsI,IAAI,EAAEi2B;IAAiB,CAAC,EAAE;MAAEj2B,IAAI,EAAE44B;IAAc,CAAC,EAAE;MAAE54B,IAAI,EAAE65B,oBAAoB;MAAE55B,UAAU,EAAE,CAAC;QAClGD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC,EAAE;QACCsI,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAAC4jC,kBAAkB;MAC7B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEG,WAAW,EAAE,CAAC;MAC1C/5B,IAAI,EAAExI,gDAAKA;IACf,CAAC,CAAC;IAAEkjC,UAAU,EAAE,CAAC;MACb16B,IAAI,EAAExI,gDAAKA;IACf,CAAC,CAAC;IAAEq7B,mBAAmB,EAAE,CAAC;MACtB7yB,IAAI,EAAEpI,iDAAMA;IAChB,CAAC,CAAC;IAAEylC,eAAe,EAAE,CAAC;MAClBr9B,IAAI,EAAE8xB,oDAAS;MACf97B,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAE4pC,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,SAASC,SAASA,CAAC9F,WAAW,EAAE53B,SAAS,EAAE4F,IAAI,EAAE;EAC7C,MAAMzN,EAAE,GAAGyN,IAAI;EACf,IAAI,CAACzN,EAAE,CAAC++B,qBAAqB,EAAE;IAC3B,OAAO,CAAC;EACZ;EACA,MAAMyG,IAAI,GAAGxlC,EAAE,CAAC++B,qBAAqB,CAAC,CAAC;EACvC,IAAIU,WAAW,KAAK,YAAY,EAAE;IAC9B,OAAO53B,SAAS,KAAK,OAAO,GAAG29B,IAAI,CAAChI,IAAI,GAAGgI,IAAI,CAAC/H,KAAK;EACzD;EACA,OAAO51B,SAAS,KAAK,OAAO,GAAG29B,IAAI,CAAC7H,GAAG,GAAG6H,IAAI,CAAC9H,MAAM;AACzD;AACA;AACA;AACA;AACA;AACA,MAAM+H,eAAe,CAAC;EAClB;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAAC/tC,KAAK,EAAE;IACvB,IAAI,CAACguC,gBAAgB,GAAGhuC,KAAK;IAC7B,IAAI2qB,uEAAY,CAAC3qB,KAAK,CAAC,EAAE;MACrB,IAAI,CAACiuC,kBAAkB,CAAChuC,IAAI,CAACD,KAAK,CAAC;IACvC,CAAC,MACI;MACD;MACA,IAAI,CAACiuC,kBAAkB,CAAChuC,IAAI,CAAC,IAAI4qB,sEAAe,CAACJ,mDAAY,CAACzqB,KAAK,CAAC,GAAGA,KAAK,GAAG6pB,KAAK,CAACwD,IAAI,CAACrtB,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5G;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIkuC,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoBA,CAACl1B,EAAE,EAAE;IACzB,IAAI,CAACo1B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACD,qBAAqB,GAAGn1B,EAAE,GACzB,CAAC7X,KAAK,EAAE8N,IAAI,KAAK+J,EAAE,CAAC7X,KAAK,IAAI,IAAI,CAACgoC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACjH,KAAK,GAAG,CAAC,CAAC,EAAEjzB,IAAI,CAAC,GACxFhN,SAAS;EACnB;EACA;EACA,IAAIosC,qBAAqBA,CAACruC,KAAK,EAAE;IAC7B,IAAIA,KAAK,EAAE;MACP,IAAI,CAACouC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACE,SAAS,GAAGtuC,KAAK;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIuuC,8BAA8BA,CAAA,EAAG;IACjC,OAAO,IAAI,CAACC,aAAa,CAACpiB,aAAa;EAC3C;EACA,IAAImiB,8BAA8BA,CAACxf,IAAI,EAAE;IACrC,IAAI,CAACyf,aAAa,CAACpiB,aAAa,GAAG7C,2EAAoB,CAACwF,IAAI,CAAC;EACjE;EACAxtB,WAAWA,CAAA,CACX;EACAktC,iBAAiB,EACjB;EACAH,SAAS,EACT;EACAI,QAAQ,EACR;EACAF,aAAa,EACb;EACA3N,SAAS,EAAE1iB,MAAM,EAAE;IACf,IAAI,CAACswB,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACF,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC3N,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAAC8N,UAAU,GAAG,IAAIzoC,yCAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAAC+nC,kBAAkB,GAAG,IAAI/nC,yCAAO,CAAC,CAAC;IACvC;IACA,IAAI,CAAC6jC,UAAU,GAAG,IAAI,CAACkE,kBAAkB,CAACx9B,IAAI;IAC9C;IACAomB,0DAAS,CAAC,IAAI,CAAC;IACf;IACAl2B,yDAAQ,CAAC,CAAC;IACV;IACA;IACA;IACAq/B,0DAAS,CAAC,CAAC,CAACp/B,IAAI,EAAEguC,GAAG,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACjuC,IAAI,EAAEguC,GAAG,CAAC,CAAC;IAC7D;IACA3O,4DAAW,CAAC,CAAC,CAAC,CAAC;IACf;IACA,IAAI,CAAC6O,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACV,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC3I,UAAU,GAAG,IAAIv/B,yCAAO,CAAC,CAAC;IAC/B,IAAI,CAAC6jC,UAAU,CAAC3pC,SAAS,CAAC4pC,IAAI,IAAI;MAC9B,IAAI,CAAClf,KAAK,GAAGkf,IAAI;MACjB,IAAI,CAAC+E,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAAClO,SAAS,CAACkI,mBAAmB,CAACt4B,IAAI,CAAC5I,0DAAS,CAAC,IAAI,CAAC49B,UAAU,CAAC,CAAC,CAACrlC,SAAS,CAACgqC,KAAK,IAAI;MACnF,IAAI,CAACjB,cAAc,GAAGiB,KAAK;MAC3B,IAAI,IAAI,CAACuE,UAAU,CAACnqB,SAAS,CAACpiB,MAAM,EAAE;QAClC+b,MAAM,CAACsG,GAAG,CAAC,MAAM,IAAI,CAACkqB,UAAU,CAAC1uC,IAAI,CAAC,IAAI,CAACkpC,cAAc,CAAC,CAAC;MAC/D;MACA,IAAI,CAAC4F,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAAClO,SAAS,CAACI,MAAM,CAAC,IAAI,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACIoK,gBAAgBA,CAACjB,KAAK,EAAEtC,WAAW,EAAE;IACjC,IAAIsC,KAAK,CAAClI,KAAK,IAAIkI,KAAK,CAACjI,GAAG,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,IAAI,CAACiI,KAAK,CAAClI,KAAK,GAAG,IAAI,CAACiH,cAAc,CAACjH,KAAK,IAAIkI,KAAK,CAACjI,GAAG,GAAG,IAAI,CAACgH,cAAc,CAAChH,GAAG,MAC9E,OAAOt0B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAM2C,KAAK,CAAE,0DAAyD,CAAC;IAC3E;IACA;IACA,MAAMw+B,kBAAkB,GAAG5E,KAAK,CAAClI,KAAK,GAAG,IAAI,CAACiH,cAAc,CAACjH,KAAK;IAClE;IACA,MAAM+M,QAAQ,GAAG7E,KAAK,CAACjI,GAAG,GAAGiI,KAAK,CAAClI,KAAK;IACxC;IACA;IACA,IAAIgN,SAAS;IACb,IAAIC,QAAQ;IACZ;IACA,KAAK,IAAI1jC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwjC,QAAQ,EAAExjC,CAAC,EAAE,EAAE;MAC/B,MAAMigB,IAAI,GAAG,IAAI,CAAC+iB,iBAAiB,CAACvjC,GAAG,CAACO,CAAC,GAAGujC,kBAAkB,CAAC;MAC/D,IAAItjB,IAAI,IAAIA,IAAI,CAAC0jB,SAAS,CAAChtC,MAAM,EAAE;QAC/B8sC,SAAS,GAAGC,QAAQ,GAAGzjB,IAAI,CAAC0jB,SAAS,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ;IACA;IACA,KAAK,IAAI3jC,CAAC,GAAGwjC,QAAQ,GAAG,CAAC,EAAExjC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,MAAMigB,IAAI,GAAG,IAAI,CAAC+iB,iBAAiB,CAACvjC,GAAG,CAACO,CAAC,GAAGujC,kBAAkB,CAAC;MAC/D,IAAItjB,IAAI,IAAIA,IAAI,CAAC0jB,SAAS,CAAChtC,MAAM,EAAE;QAC/B+sC,QAAQ,GAAGzjB,IAAI,CAAC0jB,SAAS,CAAC1jB,IAAI,CAAC0jB,SAAS,CAAChtC,MAAM,GAAG,CAAC,CAAC;QACpD;MACJ;IACJ;IACA,OAAO8sC,SAAS,IAAIC,QAAQ,GACtBvB,SAAS,CAAC9F,WAAW,EAAE,KAAK,EAAEqH,QAAQ,CAAC,GAAGvB,SAAS,CAAC9F,WAAW,EAAE,OAAO,EAAEoH,SAAS,CAAC,GACpF,CAAC;EACX;EACAh1B,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC40B,OAAO,IAAI,IAAI,CAACV,YAAY,EAAE;MACnC;MACA;MACA;MACA,MAAM7+B,OAAO,GAAG,IAAI,CAACu/B,OAAO,CAACO,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC;MACtD,IAAI,CAAC//B,OAAO,EAAE;QACV,IAAI,CAACggC,cAAc,CAAC,CAAC;MACzB,CAAC,MACI;QACD,IAAI,CAACC,aAAa,CAACjgC,OAAO,CAAC;MAC/B;MACA,IAAI,CAAC6+B,YAAY,GAAG,KAAK;IAC7B;EACJ;EACA9iC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACu1B,SAAS,CAAC3U,MAAM,CAAC,CAAC;IACvB,IAAI,CAAC+hB,kBAAkB,CAAChuC,IAAI,CAACgC,SAAS,CAAC;IACvC,IAAI,CAACgsC,kBAAkB,CAAC/tC,QAAQ,CAAC,CAAC;IAClC,IAAI,CAACyuC,UAAU,CAACzuC,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACulC,UAAU,CAACxlC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACwlC,UAAU,CAACvlC,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACsuC,aAAa,CAACtiB,MAAM,CAAC,CAAC;EAC/B;EACA;EACA6iB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAAC5F,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACmG,cAAc,GAAG,IAAI,CAACxkB,KAAK,CAAC2kB,KAAK,CAAC,IAAI,CAACtG,cAAc,CAACjH,KAAK,EAAE,IAAI,CAACiH,cAAc,CAAChH,GAAG,CAAC;IAC1F,IAAI,CAAC,IAAI,CAAC2M,OAAO,EAAE;MACf;MACA;MACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACJ,QAAQ,CAACgB,IAAI,CAAC,IAAI,CAACJ,cAAc,CAAC,CAACl2B,MAAM,CAAC,CAACjY,KAAK,EAAE8N,IAAI,KAAK;QAC3E,OAAO,IAAI,CAACi/B,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAAC/sC,KAAK,EAAE8N,IAAI,CAAC,GAAGA,IAAI;MACpF,CAAC,CAAC;IACN;IACA,IAAI,CAACm/B,YAAY,GAAG,IAAI;EAC5B;EACA;EACAS,iBAAiBA,CAACc,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAID,KAAK,EAAE;MACPA,KAAK,CAAC5kB,UAAU,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAACqjB,YAAY,GAAG,IAAI;IACxB,OAAOwB,KAAK,GAAGA,KAAK,CAAChlB,OAAO,CAAC,IAAI,CAAC,GAAGxkB,wCAAE,CAAC,CAAC;EAC7C;EACA;EACAmpC,cAAcA,CAAA,EAAG;IACb,MAAMtuC,KAAK,GAAG,IAAI,CAAC6pB,KAAK,CAAC1oB,MAAM;IAC/B,IAAIqJ,CAAC,GAAG,IAAI,CAACgjC,iBAAiB,CAACrsC,MAAM;IACrC,OAAOqJ,CAAC,EAAE,EAAE;MACR,MAAMigB,IAAI,GAAG,IAAI,CAAC+iB,iBAAiB,CAACvjC,GAAG,CAACO,CAAC,CAAC;MAC1CigB,IAAI,CAACM,OAAO,CAAC7qB,KAAK,GAAG,IAAI,CAACgoC,cAAc,CAACjH,KAAK,GAAGz2B,CAAC;MAClDigB,IAAI,CAACM,OAAO,CAAC/qB,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAAC4uC,gCAAgC,CAACnkB,IAAI,CAACM,OAAO,CAAC;MACnDN,IAAI,CAACokB,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACAN,aAAaA,CAACjgC,OAAO,EAAE;IACnB,IAAI,CAACi/B,aAAa,CAACvjB,YAAY,CAAC1b,OAAO,EAAE,IAAI,CAACk/B,iBAAiB,EAAE,CAACljB,MAAM,EAAEwkB,sBAAsB,EAAEtkB,YAAY,KAAK,IAAI,CAACukB,oBAAoB,CAACzkB,MAAM,EAAEE,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACtc,IAAI,CAAC;IAC1L;IACAM,OAAO,CAAC0gC,qBAAqB,CAAE1kB,MAAM,IAAK;MACtC,MAAMG,IAAI,GAAG,IAAI,CAAC+iB,iBAAiB,CAACvjC,GAAG,CAACqgB,MAAM,CAACE,YAAY,CAAC;MAC5DC,IAAI,CAACM,OAAO,CAACY,SAAS,GAAGrB,MAAM,CAACtc,IAAI;IACxC,CAAC,CAAC;IACF;IACA,MAAMhO,KAAK,GAAG,IAAI,CAAC6pB,KAAK,CAAC1oB,MAAM;IAC/B,IAAIqJ,CAAC,GAAG,IAAI,CAACgjC,iBAAiB,CAACrsC,MAAM;IACrC,OAAOqJ,CAAC,EAAE,EAAE;MACR,MAAMigB,IAAI,GAAG,IAAI,CAAC+iB,iBAAiB,CAACvjC,GAAG,CAACO,CAAC,CAAC;MAC1CigB,IAAI,CAACM,OAAO,CAAC7qB,KAAK,GAAG,IAAI,CAACgoC,cAAc,CAACjH,KAAK,GAAGz2B,CAAC;MAClDigB,IAAI,CAACM,OAAO,CAAC/qB,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAAC4uC,gCAAgC,CAACnkB,IAAI,CAACM,OAAO,CAAC;IACvD;EACJ;EACA;EACA6jB,gCAAgCA,CAAC7jB,OAAO,EAAE;IACtCA,OAAO,CAACmgB,KAAK,GAAGngB,OAAO,CAAC7qB,KAAK,KAAK,CAAC;IACnC6qB,OAAO,CAACkkB,IAAI,GAAGlkB,OAAO,CAAC7qB,KAAK,KAAK6qB,OAAO,CAAC/qB,KAAK,GAAG,CAAC;IAClD+qB,OAAO,CAACmkB,IAAI,GAAGnkB,OAAO,CAAC7qB,KAAK,GAAG,CAAC,KAAK,CAAC;IACtC6qB,OAAO,CAACokB,GAAG,GAAG,CAACpkB,OAAO,CAACmkB,IAAI;EAC/B;EACAH,oBAAoBA,CAACzkB,MAAM,EAAEpqB,KAAK,EAAE;IAChC;IACA;IACA;IACA;IACA,OAAO;MACH4qB,WAAW,EAAE,IAAI,CAACuiB,SAAS;MAC3BtiB,OAAO,EAAE;QACLY,SAAS,EAAErB,MAAM,CAACtc,IAAI;QACtB;QACA;QACA8+B,eAAe,EAAE,IAAI,CAACC,gBAAgB;QACtC7sC,KAAK,EAAE,CAAC,CAAC;QACTF,KAAK,EAAE,CAAC,CAAC;QACTkrC,KAAK,EAAE,KAAK;QACZ+D,IAAI,EAAE,KAAK;QACXE,GAAG,EAAE,KAAK;QACVD,IAAI,EAAE;MACV,CAAC;MACDhvC;IACJ,CAAC;EACL;EACA;IAAS,IAAI,CAACgM,IAAI,YAAAkjC,wBAAAhjC,CAAA;MAAA,YAAAA,CAAA,IAAwFygC,eAAe,EArpCzB9oC,+DAAE,CAqpCyCA,2DAAmB,GArpC9DA,+DAAE,CAqpCyEA,sDAAc,GArpCzFA,+DAAE,CAqpCoGA,0DAAkB,GArpCxHA,+DAAE,CAqpCmI+qB,8EAAuB,GArpC5J/qB,+DAAE,CAqpCuKsjC,wBAAwB,MArpCjMtjC,+DAAE,CAqpC4NA,iDAAS;IAAA,CAA4C;EAAE;EACrX;IAAS,IAAI,CAACyV,IAAI,kBAtpC8EzV,+DAAE;MAAA+I,IAAA,EAspCJ+/B,eAAe;MAAAnzB,SAAA;MAAAC,MAAA;QAAAmzB,eAAA;QAAAG,oBAAA;QAAAG,qBAAA;QAAAE,8BAAA;MAAA;MAAA3K,UAAA;MAAA9oB,QAAA,GAtpCb9V,gEAAE,CAspC0S,CAAC;QAAEikB,OAAO,EAAE8G,8EAAuB;QAAE0gB,QAAQ,EAAEtkB,mFAA4BA;MAAC,CAAC,CAAC;IAAA,EAAiB;EAAE;AACjf;AACA;EAAA,QAAAte,SAAA,oBAAAA,SAAA,KAxpCoG7I,+DAAE,CAwpCX8oC,eAAe,EAAc,CAAC;IAC7G//B,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,kCAAkC;MAC5CmO,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE8G,8EAAuB;QAAE0gB,QAAQ,EAAEtkB,mFAA4BA;MAAC,CAAC,CAAC;MACzFyX,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE71B,IAAI,EAAE/I,2DAAmBsrC;IAAC,CAAC,EAAE;MAAEviC,IAAI,EAAE/I,sDAAcurC;IAAC,CAAC,EAAE;MAAExiC,IAAI,EAAE/I,0DAAkBwrC;IAAC,CAAC,EAAE;MAAEziC,IAAI,EAAEoyB,mFAAiC;MAAEnyB,UAAU,EAAE,CAAC;QAC7KD,IAAI,EAAE3I,iDAAM;QACZrB,IAAI,EAAE,CAACgsB,8EAAuB;MAClC,CAAC;IAAE,CAAC,EAAE;MAAEhiB,IAAI,EAAEu6B,wBAAwB;MAAEt6B,UAAU,EAAE,CAAC;QACjDD,IAAI,EAAE+xB,mDAAQA;MAClB,CAAC;IAAE,CAAC,EAAE;MAAE/xB,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEw0B,eAAe,EAAE,CAAC;MACnEhgC,IAAI,EAAExI,gDAAKA;IACf,CAAC,CAAC;IAAE2oC,oBAAoB,EAAE,CAAC;MACvBngC,IAAI,EAAExI,gDAAKA;IACf,CAAC,CAAC;IAAE8oC,qBAAqB,EAAE,CAAC;MACxBtgC,IAAI,EAAExI,gDAAKA;IACf,CAAC,CAAC;IAAEgpC,8BAA8B,EAAE,CAAC;MACjCxgC,IAAI,EAAExI,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMmrC,2BAA2B,SAAS9I,oBAAoB,CAAC;EAC3DrmC,WAAWA,CAACgkC,UAAU,EAAEC,gBAAgB,EAAErnB,MAAM,EAAE6J,GAAG,EAAE;IACnD,KAAK,CAACud,UAAU,EAAEC,gBAAgB,EAAErnB,MAAM,EAAE6J,GAAG,CAAC;EACpD;EACAmiB,yCAAyCA,CAAC9c,IAAI,EAAE;IAC5C,OAAQ,IAAI,CAAC+X,aAAa,CAAC,CAAC,CAACrrB,aAAa,CAACqtB,qBAAqB,CAAC,CAAC,CAAC/Z,IAAI,CAAC,GACpE,IAAI,CAACmV,mBAAmB,CAACnV,IAAI,CAAC;EACtC;EACA;IAAS,IAAI,CAAClgB,IAAI,YAAAwjC,oCAAAtjC,CAAA;MAAA,YAAAA,CAAA,IAAwFqjC,2BAA2B,EAzrCrC1rC,+DAAE,CAyrCqDA,qDAAa,GAzrCpEA,+DAAE,CAyrC+Eg/B,gBAAgB,GAzrCjGh/B,+DAAE,CAyrC4GA,iDAAS,GAzrCvHA,+DAAE,CAyrCkIk7B,8DAAiB;IAAA,CAA4D;EAAE;EACnT;IAAS,IAAI,CAACzlB,IAAI,kBA1rC8EzV,+DAAE;MAAA+I,IAAA,EA0rCJ2iC,2BAA2B;MAAA/1B,SAAA;MAAAyxB,SAAA;MAAAxI,UAAA;MAAA9oB,QAAA,GA1rCzB9V,gEAAE,CA0rCsJ,CAAC;QAAEikB,OAAO,EAAE0e,kBAAkB;QAAEze,WAAW,EAAEwnB;MAA4B,CAAC,CAAC,GA1rCnO1rC,wEAAE;IAAA,EA0rCyQ;EAAE;AACjX;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KA5rCoG7I,+DAAE,CA4rCX0rC,2BAA2B,EAAc,CAAC;IACzH3iC,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,8BAA8B;MACxCmO,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE0e,kBAAkB;QAAEze,WAAW,EAAEwnB;MAA4B,CAAC,CAAC;MACtF9M,UAAU,EAAE,IAAI;MAChBxa,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErb,IAAI,EAAE/I,qDAAawV;IAAC,CAAC,EAAE;MAAEzM,IAAI,EAAEi2B;IAAiB,CAAC,EAAE;MAAEj2B,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAEmyB,8DAAiB;MAAElyB,UAAU,EAAE,CAAC;QAChJD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,MAAMmrC,0BAA0B,SAAShJ,oBAAoB,CAAC;EAC1DrmC,WAAWA,CAACikC,gBAAgB,EAAErnB,MAAM,EAAE6J,GAAG,EAAE;IACvC,KAAK,CAAC,IAAIxN,qDAAU,CAAC4D,QAAQ,CAAC8J,eAAe,CAAC,EAAEsd,gBAAgB,EAAErnB,MAAM,EAAE6J,GAAG,CAAC;IAC9E,IAAI,CAAC0d,gBAAgB,GAAG,IAAI9O,4CAAU,CAAEwC,QAAQ,IAAK,IAAI,CAACjb,MAAM,CAAChH,iBAAiB,CAAC,MAAM4oB,+CAAS,CAAC3hB,QAAQ,EAAE,QAAQ,CAAC,CAAC3N,IAAI,CAAC5I,0DAAS,CAAC,IAAI,CAAC49B,UAAU,CAAC,CAAC,CAACrlC,SAAS,CAACg5B,QAAQ,CAAC,CAAC,CAAC;EACjL;EACA+Q,yCAAyCA,CAAC9c,IAAI,EAAE;IAC5C,OAAO,IAAI,CAAC+X,aAAa,CAAC,CAAC,CAACrrB,aAAa,CAACqtB,qBAAqB,CAAC,CAAC,CAAC/Z,IAAI,CAAC;EAC3E;EACA;IAAS,IAAI,CAAClgB,IAAI,YAAA0jC,mCAAAxjC,CAAA;MAAA,YAAAA,CAAA,IAAwFujC,0BAA0B,EArtCpC5rC,+DAAE,CAqtCoDg/B,gBAAgB,GArtCtEh/B,+DAAE,CAqtCiFA,iDAAS,GArtC5FA,+DAAE,CAqtCuGk7B,8DAAiB;IAAA,CAA4D;EAAE;EACxR;IAAS,IAAI,CAACzlB,IAAI,kBAttC8EzV,+DAAE;MAAA+I,IAAA,EAstCJ6iC,0BAA0B;MAAAj2B,SAAA;MAAAipB,UAAA;MAAA9oB,QAAA,GAttCxB9V,gEAAE,CAstC8G,CAAC;QAAEikB,OAAO,EAAE0e,kBAAkB;QAAEze,WAAW,EAAE0nB;MAA2B,CAAC,CAAC,GAttC1L5rC,wEAAE;IAAA,EAstCgO;EAAE;AACxU;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KAxtCoG7I,+DAAE,CAwtCX4rC,0BAA0B,EAAc,CAAC;IACxH7iC,IAAI,EAAEzI,oDAAS;IACfvB,IAAI,EAAE,CAAC;MACCiX,QAAQ,EAAE,2CAA2C;MACrDmO,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE0e,kBAAkB;QAAEze,WAAW,EAAE0nB;MAA2B,CAAC,CAAC;MACrFhN,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE71B,IAAI,EAAEi2B;IAAiB,CAAC,EAAE;MAAEj2B,IAAI,EAAE/I,iDAASuU;IAAC,CAAC,EAAE;MAAExL,IAAI,EAAEmyB,8DAAiB;MAAElyB,UAAU,EAAE,CAAC;QACvHD,IAAI,EAAEtI,mDAAQA;MAClB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMqrC,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAAC3jC,IAAI,YAAA4jC,4BAAA1jC,CAAA;MAAA,YAAAA,CAAA,IAAwFyjC,mBAAmB;IAAA,CAAkD;EAAE;EACjL;IAAS,IAAI,CAAC/pB,IAAI,kBAruC8E/hB,8DAAE;MAAA+I,IAAA,EAquCS+iC;IAAmB,EAAuD;EAAE;EACvL;IAAS,IAAI,CAAC7pB,IAAI,kBAtuC8EjiB,8DAAE,IAsuC+B;EAAE;AACvI;AACA;EAAA,QAAA6I,SAAA,oBAAAA,SAAA,KAxuCoG7I,+DAAE,CAwuCX8rC,mBAAmB,EAAc,CAAC;IACjH/iC,IAAI,EAAEnI,mDAAQ;IACd7B,IAAI,EAAE,CAAC;MACCsjB,OAAO,EAAE,CAACie,aAAa,CAAC;MACxBne,OAAO,EAAE,CAACme,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAM0L,eAAe,CAAC;EAClB;IAAS,IAAI,CAAC7jC,IAAI,YAAA8jC,wBAAA5jC,CAAA;MAAA,YAAAA,CAAA,IAAwF2jC,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACjqB,IAAI,kBApvC8E/hB,8DAAE;MAAA+I,IAAA,EAovCSijC;IAAe,EAQnF;EAAE;EACzC;IAAS,IAAI,CAAC/pB,IAAI,kBA7vC8EjiB,8DAAE;MAAAmiB,OAAA,GA6vCoCkC,0DAAU,EACxIynB,mBAAmB,EAAEznB,0DAAU,EAAEynB,mBAAmB;IAAA,EAAI;EAAE;AACtE;AACA;EAAA,QAAAjjC,SAAA,oBAAAA,SAAA,KAhwCoG7I,+DAAE,CAgwCXgsC,eAAe,EAAc,CAAC;IAC7GjjC,IAAI,EAAEnI,mDAAQ;IACd7B,IAAI,EAAE,CAAC;MACCojB,OAAO,EAAE,CACLkC,0DAAU,EACVynB,mBAAmB,EACnBxI,wBAAwB,EACxB5E,yBAAyB,EACzBoK,eAAe,EACf8C,0BAA0B,EAC1BF,2BAA2B,CAC9B;MACDrpB,OAAO,EAAE,CACLgC,0DAAU,EACVynB,mBAAmB,EACnBpN,yBAAyB,EACzBoK,eAAe,EACfxF,wBAAwB,EACxBsI,0BAA0B,EAC1BF,2BAA2B;IAEnC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA", "sources": ["./node_modules/rxjs/dist/esm/internal/operators/audit.js", "./node_modules/rxjs/dist/esm/internal/operators/auditTime.js", "./node_modules/rxjs/dist/esm/internal/operators/pairwise.js", "./node_modules/rxjs/dist/esm/internal/operators/skip.js", "./node_modules/rxjs/dist/esm/internal/scheduler/AnimationFrameAction.js", "./node_modules/rxjs/dist/esm/internal/scheduler/AnimationFrameScheduler.js", "./node_modules/rxjs/dist/esm/internal/scheduler/AsapAction.js", "./node_modules/rxjs/dist/esm/internal/scheduler/AsapScheduler.js", "./node_modules/rxjs/dist/esm/internal/scheduler/animationFrame.js", "./node_modules/rxjs/dist/esm/internal/scheduler/animationFrameProvider.js", "./node_modules/rxjs/dist/esm/internal/scheduler/asap.js", "./node_modules/rxjs/dist/esm/internal/scheduler/immediateProvider.js", "./node_modules/rxjs/dist/esm/internal/util/Immediate.js", "./node_modules/@angular/cdk/fesm2022/a11y.mjs", "./node_modules/@angular/cdk/fesm2022/bidi.mjs", "./node_modules/@angular/cdk/fesm2022/coercion.mjs", "./node_modules/@angular/cdk/fesm2022/collections.mjs", "./node_modules/@angular/cdk/fesm2022/keycodes.mjs", "./node_modules/@angular/cdk/fesm2022/layout.mjs", "./node_modules/@angular/cdk/fesm2022/observers.mjs", "./node_modules/@angular/cdk/fesm2022/platform.mjs", "./node_modules/@angular/cdk/fesm2022/scrolling.mjs"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function audit(durationSelector) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        let lastValue = null;\n        let durationSubscriber = null;\n        let isComplete = false;\n        const endDuration = () => {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            durationSubscriber = null;\n            if (hasValue) {\n                hasValue = false;\n                const value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n            isComplete && subscriber.complete();\n        };\n        const cleanupDuration = () => {\n            durationSubscriber = null;\n            isComplete && subscriber.complete();\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            lastValue = value;\n            if (!durationSubscriber) {\n                innerFrom(durationSelector(value)).subscribe((durationSubscriber = createOperatorSubscriber(subscriber, endDuration, cleanupDuration)));\n            }\n        }, () => {\n            isComplete = true;\n            (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n        }));\n    });\n}\n", "import { asyncScheduler } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = asyncScheduler) {\n    return audit(() => timer(duration, scheduler));\n}\n", "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function pairwise() {\n    return operate((source, subscriber) => {\n        let prev;\n        let hasPrev = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const p = prev;\n            prev = value;\n            hasPrev && subscriber.next([p, value]);\n            hasPrev = true;\n        }));\n    });\n}\n", "import { filter } from './filter';\nexport function skip(count) {\n    return filter((_, index) => count <= index);\n}\n", "import { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nexport class AnimationFrameAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(() => scheduler.flush(undefined)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        var _a;\n        if (delay != null ? delay > 0 : this.delay > 0) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        const { actions } = scheduler;\n        if (id != null && id === scheduler._scheduled && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n            animationFrameProvider.cancelAnimationFrame(id);\n            scheduler._scheduled = undefined;\n        }\n        return undefined;\n    }\n}\n", "import { AsyncScheduler } from './AsyncScheduler';\nexport class AnimationFrameScheduler extends AsyncScheduler {\n    flush(action) {\n        this._active = true;\n        let flushId;\n        if (action) {\n            flushId = action.id;\n        }\n        else {\n            flushId = this._scheduled;\n            this._scheduled = undefined;\n        }\n        const { actions } = this;\n        let error;\n        action = action || actions.shift();\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions[0]) && action.id === flushId && actions.shift());\n        this._active = false;\n        if (error) {\n            while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n", "import { AsyncAction } from './AsyncAction';\nimport { immediateProvider } from './immediateProvider';\nexport class AsapAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        var _a;\n        if (delay != null ? delay > 0 : this.delay > 0) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        const { actions } = scheduler;\n        if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n            immediateProvider.clearImmediate(id);\n            if (scheduler._scheduled === id) {\n                scheduler._scheduled = undefined;\n            }\n        }\n        return undefined;\n    }\n}\n", "import { AsyncScheduler } from './AsyncScheduler';\nexport class AsapScheduler extends AsyncScheduler {\n    flush(action) {\n        this._active = true;\n        const flushId = this._scheduled;\n        this._scheduled = undefined;\n        const { actions } = this;\n        let error;\n        action = action || actions.shift();\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions[0]) && action.id === flushId && actions.shift());\n        this._active = false;\n        if (error) {\n            while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n", "import { AnimationFrameAction } from './AnimationFrameAction';\nimport { AnimationFrameScheduler } from './AnimationFrameScheduler';\nexport const animationFrameScheduler = new AnimationFrameScheduler(AnimationFrameAction);\nexport const animationFrame = animationFrameScheduler;\n", "import { Subscription } from '../Subscription';\nexport const animationFrameProvider = {\n    schedule(callback) {\n        let request = requestAnimationFrame;\n        let cancel = cancelAnimationFrame;\n        const { delegate } = animationFrameProvider;\n        if (delegate) {\n            request = delegate.requestAnimationFrame;\n            cancel = delegate.cancelAnimationFrame;\n        }\n        const handle = request((timestamp) => {\n            cancel = undefined;\n            callback(timestamp);\n        });\n        return new Subscription(() => cancel === null || cancel === void 0 ? void 0 : cancel(handle));\n    },\n    requestAnimationFrame(...args) {\n        const { delegate } = animationFrameProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.requestAnimationFrame) || requestAnimationFrame)(...args);\n    },\n    cancelAnimationFrame(...args) {\n        const { delegate } = animationFrameProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.cancelAnimationFrame) || cancelAnimationFrame)(...args);\n    },\n    delegate: undefined,\n};\n", "import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport const asapScheduler = new AsapScheduler(AsapAction);\nexport const asap = asapScheduler;\n", "import { Immediate } from '../util/Immediate';\nconst { setImmediate, clearImmediate } = Immediate;\nexport const immediateProvider = {\n    setImmediate(...args) {\n        const { delegate } = immediateProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate)(...args);\n    },\n    clearImmediate(handle) {\n        const { delegate } = immediateProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n    },\n    delegate: undefined,\n};\n", "let nextHandle = 1;\nlet resolved;\nconst activeHandles = {};\nfunction findAndClearHandle(handle) {\n    if (handle in activeHandles) {\n        delete activeHandles[handle];\n        return true;\n    }\n    return false;\n}\nexport const Immediate = {\n    setImmediate(cb) {\n        const handle = nextHandle++;\n        activeHandles[handle] = true;\n        if (!resolved) {\n            resolved = Promise.resolve();\n        }\n        resolved.then(() => findAndClearHandle(handle) && cb());\n        return handle;\n    },\n    clearImmediate(handle) {\n        findAndClearHandle(handle);\n    },\n};\nexport const TestTools = {\n    pending() {\n        return Object.keys(activeHandles).length;\n    }\n};\n", "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, QueryList, Directive, Input, InjectionToken, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, BehaviorSubject, of } from 'rxjs';\nimport { hasModifierKey, A, Z, ZERO, NINE, PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceElement } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    if (ids.some(existingId => existingId.trim() == id.trim())) {\n        return;\n    }\n    ids.push(id.trim());\n    el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    const filteredIds = ids.filter(val => val != id.trim());\n    if (filteredIds.length) {\n        el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n    }\n    else {\n        el.removeAttribute(attr);\n    }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n    // Get string array of all individual ids (whitespace delimited) in the attribute value\n    return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n    constructor(_document, \n    /**\n     * @deprecated To be turned into a required parameter.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n        this._platform = _platform;\n        /** Map of all registered message elements that have been placed into the document. */\n        this._messageRegistry = new Map();\n        /** Container for all registered messages. */\n        this._messagesContainer = null;\n        /** Unique ID for the service. */\n        this._id = `${nextId++}`;\n        this._document = _document;\n        this._id = inject(APP_ID) + '-' + nextId++;\n    }\n    describe(hostElement, message, role) {\n        if (!this._canBeDescribed(hostElement, message)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (typeof message !== 'string') {\n            // We need to ensure that the element has an ID.\n            setMessageId(message, this._id);\n            this._messageRegistry.set(key, { messageElement: message, referenceCount: 0 });\n        }\n        else if (!this._messageRegistry.has(key)) {\n            this._createMessageElement(message, role);\n        }\n        if (!this._isElementDescribedByMessage(hostElement, key)) {\n            this._addMessageReference(hostElement, key);\n        }\n    }\n    removeDescription(hostElement, message, role) {\n        if (!message || !this._isElementNode(hostElement)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (this._isElementDescribedByMessage(hostElement, key)) {\n            this._removeMessageReference(hostElement, key);\n        }\n        // If the message is a string, it means that it's one that we created for the\n        // consumer so we can remove it safely, otherwise we should leave it in place.\n        if (typeof message === 'string') {\n            const registeredMessage = this._messageRegistry.get(key);\n            if (registeredMessage && registeredMessage.referenceCount === 0) {\n                this._deleteMessageElement(key);\n            }\n        }\n        if (this._messagesContainer?.childNodes.length === 0) {\n            this._messagesContainer.remove();\n            this._messagesContainer = null;\n        }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n        const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n        for (let i = 0; i < describedElements.length; i++) {\n            this._removeCdkDescribedByReferenceIds(describedElements[i]);\n            describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n        }\n        this._messagesContainer?.remove();\n        this._messagesContainer = null;\n        this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n        const messageElement = this._document.createElement('div');\n        setMessageId(messageElement, this._id);\n        messageElement.textContent = message;\n        if (role) {\n            messageElement.setAttribute('role', role);\n        }\n        this._createMessagesContainer();\n        this._messagesContainer.appendChild(messageElement);\n        this._messageRegistry.set(getKey(message, role), { messageElement, referenceCount: 0 });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n        this._messageRegistry.get(key)?.messageElement?.remove();\n        this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n        if (this._messagesContainer) {\n            return;\n        }\n        const containerClassName = 'cdk-describedby-message-container';\n        const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n        for (let i = 0; i < serverContainers.length; i++) {\n            // When going from the server to the client, we may end up in a situation where there's\n            // already a container on the page, but we don't have a reference to it. Clear the\n            // old container so we don't get duplicates. Doing this, instead of emptying the previous\n            // container, should be slightly faster.\n            serverContainers[i].remove();\n        }\n        const messagesContainer = this._document.createElement('div');\n        // We add `visibility: hidden` in order to prevent text in this container from\n        // being searchable by the browser's Ctrl + F functionality.\n        // Screen-readers will still read the description for elements with aria-describedby even\n        // when the description element is not visible.\n        messagesContainer.style.visibility = 'hidden';\n        // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n        // the description element doesn't impact page layout.\n        messagesContainer.classList.add(containerClassName);\n        messagesContainer.classList.add('cdk-visually-hidden');\n        // @breaking-change 14.0.0 Remove null check for `_platform`.\n        if (this._platform && !this._platform.isBrowser) {\n            messagesContainer.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(messagesContainer);\n        this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n        // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n        const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n        element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        // Add the aria-describedby reference and set the\n        // describedby_host attribute to mark the element.\n        addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n        registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        registeredMessage.referenceCount--;\n        removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n        const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n        const registeredMessage = this._messageRegistry.get(key);\n        const messageId = registeredMessage && registeredMessage.messageElement.id;\n        return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n        if (!this._isElementNode(element)) {\n            return false;\n        }\n        if (message && typeof message === 'object') {\n            // We'd have to make some assumptions about the description element's text, if the consumer\n            // passed in an element. Assume that if an element is passed in, the consumer has verified\n            // that it can be used as a description.\n            return true;\n        }\n        const trimmedMessage = message == null ? '' : `${message}`.trim();\n        const ariaLabel = element.getAttribute('aria-label');\n        // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n        // element, because screen readers will end up reading out the same text twice in a row.\n        return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n        return element.nodeType === this._document.ELEMENT_NODE;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: AriaDescriber, deps: [{ token: DOCUMENT }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: AriaDescriber, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: AriaDescriber, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.Platform }]; } });\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n    return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n    if (!element.id) {\n        element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n    }\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n    constructor(_items) {\n        this._items = _items;\n        this._activeItemIndex = -1;\n        this._activeItem = null;\n        this._wrap = false;\n        this._letterKeyStream = new Subject();\n        this._typeaheadSubscription = Subscription.EMPTY;\n        this._vertical = true;\n        this._allowedModifierKeys = [];\n        this._homeAndEnd = false;\n        this._pageUpAndDown = { enabled: false, delta: 10 };\n        /**\n         * Predicate function that can be used to check whether an item should be skipped\n         * by the key manager. By default, disabled items are skipped.\n         */\n        this._skipPredicateFn = (item) => item.disabled;\n        // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n        this._pressedLetters = [];\n        /**\n         * Stream that emits any time the TAB key is pressed, so components can react\n         * when focus is shifted off of the list.\n         */\n        this.tabOut = new Subject();\n        /** Stream that emits whenever the active item of the list manager changes. */\n        this.change = new Subject();\n        // We allow for the items to be an array because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (_items instanceof QueryList) {\n            this._itemChangesSubscription = _items.changes.subscribe((newItems) => {\n                if (this._activeItem) {\n                    const itemArray = newItems.toArray();\n                    const newIndex = itemArray.indexOf(this._activeItem);\n                    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n                        this._activeItemIndex = newIndex;\n                    }\n                }\n            });\n        }\n    }\n    /**\n     * Sets the predicate function that determines which items should be skipped by the\n     * list key manager.\n     * @param predicate Function that determines whether the given item should be skipped.\n     */\n    skipPredicate(predicate) {\n        this._skipPredicateFn = predicate;\n        return this;\n    }\n    /**\n     * Configures wrapping mode, which determines whether the active item will wrap to\n     * the other end of list when there are no more items in the given direction.\n     * @param shouldWrap Whether the list should wrap when reaching the end.\n     */\n    withWrap(shouldWrap = true) {\n        this._wrap = shouldWrap;\n        return this;\n    }\n    /**\n     * Configures whether the key manager should be able to move the selection vertically.\n     * @param enabled Whether vertical selection should be enabled.\n     */\n    withVerticalOrientation(enabled = true) {\n        this._vertical = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to move the selection horizontally.\n     * Passing in `null` will disable horizontal movement.\n     * @param direction Direction in which the selection can be moved.\n     */\n    withHorizontalOrientation(direction) {\n        this._horizontal = direction;\n        return this;\n    }\n    /**\n     * Modifier keys which are allowed to be held down and whose default actions will be prevented\n     * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n     */\n    withAllowedModifierKeys(keys) {\n        this._allowedModifierKeys = keys;\n        return this;\n    }\n    /**\n     * Turns on typeahead mode which allows users to set the active item by typing.\n     * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n     */\n    withTypeAhead(debounceInterval = 200) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this._items.length &&\n            this._items.some(item => typeof item.getLabel !== 'function')) {\n            throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n        }\n        this._typeaheadSubscription.unsubscribe();\n        // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n        // and convert those letters back into a string. Afterwards find the first item that starts\n        // with that string and select it.\n        this._typeaheadSubscription = this._letterKeyStream\n            .pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(debounceInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('')))\n            .subscribe(inputString => {\n            const items = this._getItemsArray();\n            // Start at 1 because we want to start searching at the item immediately\n            // following the current active item.\n            for (let i = 1; i < items.length + 1; i++) {\n                const index = (this._activeItemIndex + i) % items.length;\n                const item = items[index];\n                if (!this._skipPredicateFn(item) &&\n                    item.getLabel().toUpperCase().trim().indexOf(inputString) === 0) {\n                    this.setActiveItem(index);\n                    break;\n                }\n            }\n            this._pressedLetters = [];\n        });\n        return this;\n    }\n    /** Cancels the current typeahead sequence. */\n    cancelTypeahead() {\n        this._pressedLetters = [];\n        return this;\n    }\n    /**\n     * Configures the key manager to activate the first and last items\n     * respectively when the Home or End key is pressed.\n     * @param enabled Whether pressing the Home or End key activates the first/last item.\n     */\n    withHomeAndEnd(enabled = true) {\n        this._homeAndEnd = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n     * respectively when the Page-Up or Page-Down key is pressed.\n     * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n     * @param delta Whether pressing the Home or End key activates the first/last item.\n     */\n    withPageUpDown(enabled = true, delta = 10) {\n        this._pageUpAndDown = { enabled, delta };\n        return this;\n    }\n    setActiveItem(item) {\n        const previousActiveItem = this._activeItem;\n        this.updateActiveItem(item);\n        if (this._activeItem !== previousActiveItem) {\n            this.change.next(this._activeItemIndex);\n        }\n    }\n    /**\n     * Sets the active item depending on the key event passed in.\n     * @param event Keyboard event to be used for determining which element should be active.\n     */\n    onKeydown(event) {\n        const keyCode = event.keyCode;\n        const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n        const isModifierAllowed = modifiers.every(modifier => {\n            return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n        });\n        switch (keyCode) {\n            case TAB:\n                this.tabOut.next();\n                return;\n            case DOWN_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case UP_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case RIGHT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case LEFT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case HOME:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setFirstItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case END:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setLastItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_UP:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n                    this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_DOWN:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n                    const itemsLength = this._getItemsArray().length;\n                    this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            default:\n                if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n                    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n                    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n                    if (event.key && event.key.length === 1) {\n                        this._letterKeyStream.next(event.key.toLocaleUpperCase());\n                    }\n                    else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n                        this._letterKeyStream.next(String.fromCharCode(keyCode));\n                    }\n                }\n                // Note that we return here, in order to avoid preventing\n                // the default action of non-navigational keys.\n                return;\n        }\n        this._pressedLetters = [];\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    get activeItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The active item. */\n    get activeItem() {\n        return this._activeItem;\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return this._pressedLetters.length > 0;\n    }\n    /** Sets the active item to the first enabled item in the list. */\n    setFirstItemActive() {\n        this._setActiveItemByIndex(0, 1);\n    }\n    /** Sets the active item to the last enabled item in the list. */\n    setLastItemActive() {\n        this._setActiveItemByIndex(this._items.length - 1, -1);\n    }\n    /** Sets the active item to the next enabled item in the list. */\n    setNextItemActive() {\n        this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n    }\n    /** Sets the active item to a previous enabled item in the list. */\n    setPreviousItemActive() {\n        this._activeItemIndex < 0 && this._wrap\n            ? this.setLastItemActive()\n            : this._setActiveItemByDelta(-1);\n    }\n    updateActiveItem(item) {\n        const itemArray = this._getItemsArray();\n        const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n        const activeItem = itemArray[index];\n        // Explicitly check for `null` and `undefined` because other falsy values are valid.\n        this._activeItem = activeItem == null ? null : activeItem;\n        this._activeItemIndex = index;\n    }\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._itemChangesSubscription?.unsubscribe();\n        this._letterKeyStream.complete();\n        this.tabOut.complete();\n        this.change.complete();\n        this._pressedLetters = [];\n    }\n    /**\n     * This method sets the active item, given a list of items and the delta between the\n     * currently active item and the new active item. It will calculate differently\n     * depending on whether wrap mode is turned on.\n     */\n    _setActiveItemByDelta(delta) {\n        this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n    }\n    /**\n     * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n     * down the list until it finds an item that is not disabled, and it will wrap if it\n     * encounters either end of the list.\n     */\n    _setActiveInWrapMode(delta) {\n        const items = this._getItemsArray();\n        for (let i = 1; i <= items.length; i++) {\n            const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n            const item = items[index];\n            if (!this._skipPredicateFn(item)) {\n                this.setActiveItem(index);\n                return;\n            }\n        }\n    }\n    /**\n     * Sets the active item properly given the default mode. In other words, it will\n     * continue to move down the list until it finds an item that is not disabled. If\n     * it encounters either end of the list, it will stop and not wrap.\n     */\n    _setActiveInDefaultMode(delta) {\n        this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n    }\n    /**\n     * Sets the active item to the first enabled item starting at the index specified. If the\n     * item is disabled, it will move in the fallbackDelta direction until it either\n     * finds an enabled item or encounters the end of the list.\n     */\n    _setActiveItemByIndex(index, fallbackDelta) {\n        const items = this._getItemsArray();\n        if (!items[index]) {\n            return;\n        }\n        while (this._skipPredicateFn(items[index])) {\n            index += fallbackDelta;\n            if (!items[index]) {\n                return;\n            }\n        }\n        this.setActiveItem(index);\n    }\n    /** Returns the items as an array. */\n    _getItemsArray() {\n        return this._items instanceof QueryList ? this._items.toArray() : this._items;\n    }\n}\n\nclass ActiveDescendantKeyManager extends ListKeyManager {\n    setActiveItem(index) {\n        if (this.activeItem) {\n            this.activeItem.setInactiveStyles();\n        }\n        super.setActiveItem(index);\n        if (this.activeItem) {\n            this.activeItem.setActiveStyles();\n        }\n    }\n}\n\nclass FocusKeyManager extends ListKeyManager {\n    constructor() {\n        super(...arguments);\n        this._origin = 'program';\n    }\n    /**\n     * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n     * @param origin Focus origin to be used when focusing items.\n     */\n    setFocusOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    setActiveItem(item) {\n        super.setActiveItem(item);\n        if (this.activeItem) {\n            this.activeItem.focus(this._origin);\n        }\n    }\n}\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n    constructor() {\n        /**\n         * Whether to count an element as focusable even if it is not currently visible.\n         */\n        this.ignoreVisibility = false;\n    }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n    constructor(_platform) {\n        this._platform = _platform;\n    }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n        // This does not capture some cases, such as a non-form control with a disabled attribute or\n        // a form control inside of a disabled form, but should capture the most common cases.\n        return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n        return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n        // Nothing is tabbable on the server 😎\n        if (!this._platform.isBrowser) {\n            return false;\n        }\n        const frameElement = getFrameElement(getWindow(element));\n        if (frameElement) {\n            // Frame elements inherit their tabindex onto all child elements.\n            if (getTabIndexValue(frameElement) === -1) {\n                return false;\n            }\n            // Browsers disable tabbing to an element inside of an invisible frame.\n            if (!this.isVisible(frameElement)) {\n                return false;\n            }\n        }\n        let nodeName = element.nodeName.toLowerCase();\n        let tabIndexValue = getTabIndexValue(element);\n        if (element.hasAttribute('contenteditable')) {\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'iframe' || nodeName === 'object') {\n            // The frame or object's content may be tabbable depending on the content, but it's\n            // not possibly to reliably detect the content of the frames. We always consider such\n            // elements as non-tabbable.\n            return false;\n        }\n        // In iOS, the browser only considers some specific elements as tabbable.\n        if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n            return false;\n        }\n        if (nodeName === 'audio') {\n            // Audio elements without controls enabled are never tabbable, regardless\n            // of the tabindex attribute explicitly being set.\n            if (!element.hasAttribute('controls')) {\n                return false;\n            }\n            // Audio elements with controls are by default tabbable unless the\n            // tabindex attribute is set to `-1` explicitly.\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'video') {\n            // For all video elements, if the tabindex attribute is set to `-1`, the video\n            // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n            // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n            // tabindex attribute is the source of truth here.\n            if (tabIndexValue === -1) {\n                return false;\n            }\n            // If the tabindex is explicitly set, and not `-1` (as per check before), the\n            // video element is always tabbable (regardless of whether it has controls or not).\n            if (tabIndexValue !== null) {\n                return true;\n            }\n            // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n            // has controls enabled. Firefox is special as videos are always tabbable regardless\n            // of whether there are controls or not.\n            return this._platform.FIREFOX || element.hasAttribute('controls');\n        }\n        return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n        // Perform checks in order of left to most expensive.\n        // Again, naive approach that does not capture many edge cases and browser quirks.\n        return (isPotentiallyFocusable(element) &&\n            !this.isDisabled(element) &&\n            (config?.ignoreVisibility || this.isVisible(element)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: InteractivityChecker, deps: [{ token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: InteractivityChecker, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: InteractivityChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }]; } });\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n    try {\n        return window.frameElement;\n    }\n    catch {\n        return null;\n    }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n    // Use logic from jQuery to check for an invisible element.\n    // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n    return !!(element.offsetWidth ||\n        element.offsetHeight ||\n        (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    return (nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'button' ||\n        nodeName === 'textarea');\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n    return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n    return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n    return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n    return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n    if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n        return false;\n    }\n    let tabIndex = element.getAttribute('tabindex');\n    return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n    if (!hasValidTabIndex(element)) {\n        return null;\n    }\n    // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n    return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    let inputType = nodeName === 'input' && element.type;\n    return (inputType === 'text' ||\n        inputType === 'password' ||\n        nodeName === 'select' ||\n        nodeName === 'textarea');\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n    // Inputs are potentially focusable *unless* they're type=\"hidden\".\n    if (isHiddenInput(element)) {\n        return false;\n    }\n    return (isNativeFormElement(element) ||\n        isAnchorWithHref(element) ||\n        element.hasAttribute('contenteditable') ||\n        hasValidTabIndex(element));\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n    // ownerDocument is null if `node` itself *is* a document.\n    return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change 11.0.0\n */\nclass FocusTrap {\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(value, this._startAnchor);\n            this._toggleAnchorTabIndex(value, this._endAnchor);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n        this._element = _element;\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._hasAttached = false;\n        // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n        this.startAnchorListener = () => this.focusLastTabbableElement();\n        this.endAnchorListener = () => this.focusFirstTabbableElement();\n        this._enabled = true;\n        if (!deferAnchors) {\n            this.attachAnchors();\n        }\n    }\n    /** Destroys the focus trap by cleaning up the anchors. */\n    destroy() {\n        const startAnchor = this._startAnchor;\n        const endAnchor = this._endAnchor;\n        if (startAnchor) {\n            startAnchor.removeEventListener('focus', this.startAnchorListener);\n            startAnchor.remove();\n        }\n        if (endAnchor) {\n            endAnchor.removeEventListener('focus', this.endAnchorListener);\n            endAnchor.remove();\n        }\n        this._startAnchor = this._endAnchor = null;\n        this._hasAttached = false;\n    }\n    /**\n     * Inserts the anchors into the DOM. This is usually done automatically\n     * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n     * @returns Whether the focus trap managed to attach successfully. This may not be the case\n     * if the target element isn't currently in the DOM.\n     */\n    attachAnchors() {\n        // If we're not on the browser, there can be no focus to trap.\n        if (this._hasAttached) {\n            return true;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            if (!this._startAnchor) {\n                this._startAnchor = this._createAnchor();\n                this._startAnchor.addEventListener('focus', this.startAnchorListener);\n            }\n            if (!this._endAnchor) {\n                this._endAnchor = this._createAnchor();\n                this._endAnchor.addEventListener('focus', this.endAnchorListener);\n            }\n        });\n        if (this._element.parentNode) {\n            this._element.parentNode.insertBefore(this._startAnchor, this._element);\n            this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n            this._hasAttached = true;\n        }\n        return this._hasAttached;\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses the first tabbable element.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusInitialElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the first tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusFirstTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the last tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusLastTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n        });\n    }\n    /**\n     * Get the specified boundary element of the trapped region.\n     * @param bound The boundary to get (start or end of trapped region).\n     * @returns The boundary element.\n     */\n    _getRegionBoundary(bound) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            for (let i = 0; i < markers.length; i++) {\n                // @breaking-change 8.0.0\n                if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n                        `attribute will be removed in 8.0.0.`, markers[i]);\n                }\n                else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n                        `will be removed in 8.0.0.`, markers[i]);\n                }\n            }\n        }\n        if (bound == 'start') {\n            return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n        }\n        return markers.length\n            ? markers[markers.length - 1]\n            : this._getLastTabbableElement(this._element);\n    }\n    /**\n     * Focuses the element that should be focused when the focus trap is initialized.\n     * @returns Whether focus was moved successfully.\n     */\n    focusInitialElement(options) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n        if (redirectToElement) {\n            // @breaking-change 8.0.0\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` +\n                    `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0`, redirectToElement);\n            }\n            // Warn the consumer if the element they've pointed to\n            // isn't focusable, when not in production mode.\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                !this._checker.isFocusable(redirectToElement)) {\n                console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n            }\n            if (!this._checker.isFocusable(redirectToElement)) {\n                const focusableChild = this._getFirstTabbableElement(redirectToElement);\n                focusableChild?.focus(options);\n                return !!focusableChild;\n            }\n            redirectToElement.focus(options);\n            return true;\n        }\n        return this.focusFirstTabbableElement(options);\n    }\n    /**\n     * Focuses the first tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusFirstTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('start');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Focuses the last tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusLastTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('end');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Checks whether the focus trap has successfully been attached.\n     */\n    hasAttached() {\n        return this._hasAttached;\n    }\n    /** Get the first tabbable element from a DOM subtree (inclusive). */\n    _getFirstTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        const children = root.children;\n        for (let i = 0; i < children.length; i++) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getFirstTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Get the last tabbable element from a DOM subtree (inclusive). */\n    _getLastTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in reverse DOM order.\n        const children = root.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getLastTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Creates an anchor element. */\n    _createAnchor() {\n        const anchor = this._document.createElement('div');\n        this._toggleAnchorTabIndex(this._enabled, anchor);\n        anchor.classList.add('cdk-visually-hidden');\n        anchor.classList.add('cdk-focus-trap-anchor');\n        anchor.setAttribute('aria-hidden', 'true');\n        return anchor;\n    }\n    /**\n     * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n     * @param isEnabled Whether the focus trap is enabled.\n     * @param anchor Anchor on which to toggle the tabindex.\n     */\n    _toggleAnchorTabIndex(isEnabled, anchor) {\n        // Remove the tabindex completely, rather than setting it to -1, because if the\n        // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n        isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n    }\n    /**\n     * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n     * @param enabled: Whether the anchors should trap Tab.\n     */\n    toggleAnchors(enabled) {\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(enabled, this._startAnchor);\n            this._toggleAnchorTabIndex(enabled, this._endAnchor);\n        }\n    }\n    /** Executes a function when the zone is stable. */\n    _executeOnStable(fn) {\n        if (this._ngZone.isStable) {\n            fn();\n        }\n        else {\n            this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n        }\n    }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change 11.0.0\n */\nclass FocusTrapFactory {\n    constructor(_checker, _ngZone, _document) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n        return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusTrapFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this.focusTrap.enabled;\n    }\n    set enabled(value) {\n        this.focusTrap.enabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n    get autoCapture() {\n        return this._autoCapture;\n    }\n    set autoCapture(value) {\n        this._autoCapture = coerceBooleanProperty(value);\n    }\n    constructor(_elementRef, _focusTrapFactory, \n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 13.0.0\n     */\n    _document) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n        this._previouslyFocusedElement = null;\n        this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n    ngOnDestroy() {\n        this.focusTrap.destroy();\n        // If we stored a previously focused element when using autoCapture, return focus to that\n        // element now that the trapped region is being destroyed.\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    ngAfterContentInit() {\n        this.focusTrap.attachAnchors();\n        if (this.autoCapture) {\n            this._captureFocus();\n        }\n    }\n    ngDoCheck() {\n        if (!this.focusTrap.hasAttached()) {\n            this.focusTrap.attachAnchors();\n        }\n    }\n    ngOnChanges(changes) {\n        const autoCaptureChange = changes['autoCapture'];\n        if (autoCaptureChange &&\n            !autoCaptureChange.firstChange &&\n            this.autoCapture &&\n            this.focusTrap.hasAttached()) {\n            this._captureFocus();\n        }\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n        this.focusTrap.focusInitialElementWhenReady();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTrapFocus, deps: [{ token: i0.ElementRef }, { token: FocusTrapFactory }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkTrapFocus, selector: \"[cdkTrapFocus]\", inputs: { enabled: [\"cdkTrapFocus\", \"enabled\"], autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"] }, exportAs: [\"cdkTrapFocus\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTrapFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTrapFocus]',\n                    exportAs: 'cdkTrapFocus',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { enabled: [{\n                type: Input,\n                args: ['cdkTrapFocus']\n            }], autoCapture: [{\n                type: Input,\n                args: ['cdkTrapFocusAutoCapture']\n            }] } });\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n    /** Whether the FocusTrap is enabled. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._enabled) {\n            this._focusTrapManager.register(this);\n        }\n        else {\n            this._focusTrapManager.deregister(this);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config) {\n        super(_element, _checker, _ngZone, _document, config.defer);\n        this._focusTrapManager = _focusTrapManager;\n        this._inertStrategy = _inertStrategy;\n        this._focusTrapManager.register(this);\n    }\n    /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n    destroy() {\n        this._focusTrapManager.deregister(this);\n        super.destroy();\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _enable() {\n        this._inertStrategy.preventFocus(this);\n        this.toggleAnchors(true);\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _disable() {\n        this._inertStrategy.allowFocus(this);\n        this.toggleAnchors(false);\n    }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n    constructor() {\n        /** Focus event handler. */\n        this._listener = null;\n    }\n    /** Adds a document event listener that keeps focus inside the FocusTrap. */\n    preventFocus(focusTrap) {\n        // Ensure there's only one listener per document\n        if (this._listener) {\n            focusTrap._document.removeEventListener('focus', this._listener, true);\n        }\n        this._listener = (e) => this._trapFocus(focusTrap, e);\n        focusTrap._ngZone.runOutsideAngular(() => {\n            focusTrap._document.addEventListener('focus', this._listener, true);\n        });\n    }\n    /** Removes the event listener added in preventFocus. */\n    allowFocus(focusTrap) {\n        if (!this._listener) {\n            return;\n        }\n        focusTrap._document.removeEventListener('focus', this._listener, true);\n        this._listener = null;\n    }\n    /**\n     * Refocuses the first element in the FocusTrap if the focus event target was outside\n     * the FocusTrap.\n     *\n     * This is an event listener callback. The event listener is added in runOutsideAngular,\n     * so all this code runs outside Angular as well.\n     */\n    _trapFocus(focusTrap, event) {\n        const target = event.target;\n        const focusTrapRoot = focusTrap._element;\n        // Don't refocus if target was in an overlay, because the overlay might be associated\n        // with an element inside the FocusTrap, ex. mat-select.\n        if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n            // Some legacy FocusTrap usages have logic that focuses some element on the page\n            // just before FocusTrap is destroyed. For backwards compatibility, wait\n            // to be sure FocusTrap is still enabled before refocusing.\n            setTimeout(() => {\n                // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n                if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n                    focusTrap.focusFirstTabbableElement();\n                }\n            });\n        }\n    }\n}\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n    constructor() {\n        // A stack of the FocusTraps on the page. Only the FocusTrap at the\n        // top of the stack is active.\n        this._focusTrapStack = [];\n    }\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n        // Dedupe focusTraps that register multiple times.\n        this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n        let stack = this._focusTrapStack;\n        if (stack.length) {\n            stack[stack.length - 1]._disable();\n        }\n        stack.push(focusTrap);\n        focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n        focusTrap._disable();\n        const stack = this._focusTrapStack;\n        const i = stack.indexOf(focusTrap);\n        if (i !== -1) {\n            stack.splice(i, 1);\n            if (stack.length) {\n                stack[stack.length - 1]._enable();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusTrapManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusTrapManager, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusTrapManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n    constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._focusTrapManager = _focusTrapManager;\n        this._document = _document;\n        // TODO split up the strategies into different modules, similar to DateAdapter.\n        this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = { defer: false }) {\n        let configObject;\n        if (typeof config === 'boolean') {\n            configObject = { defer: config };\n        }\n        else {\n            configObject = config;\n        }\n        return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ConfigurableFocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: FocusTrapManager }, { token: DOCUMENT }, { token: FOCUS_TRAP_INERT_STRATEGY, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ConfigurableFocusTrapFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ConfigurableFocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: FocusTrapManager }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_TRAP_INERT_STRATEGY]\n                }] }]; } });\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n    // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n    // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n    // `event.detail` is zero depending on the browser:\n    // - `event.buttons` works on Firefox, but fails on Chrome.\n    // - `detail` works on Chrome, but fails on Firefox.\n    return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n    const touch = (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);\n    // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n    // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n    // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n    // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n    return (!!touch &&\n        touch.identifier === -1 &&\n        (touch.radiusX == null || touch.radiusX === 1) &&\n        (touch.radiusY == null || touch.radiusY === 1));\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n    ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n        return this._modality.value;\n    }\n    constructor(_platform, ngZone, document, options) {\n        this._platform = _platform;\n        /**\n         * The most recently detected input modality event target. Is null if no input modality has been\n         * detected or if the associated event target is null for some unknown reason.\n         */\n        this._mostRecentTarget = null;\n        /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n        this._modality = new BehaviorSubject(null);\n        /**\n         * The timestamp of the last touch input modality. Used to determine whether mousedown events\n         * should be attributed to mouse or touch.\n         */\n        this._lastTouchMs = 0;\n        /**\n         * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n         * bound.\n         */\n        this._onKeydown = (event) => {\n            // If this is one of the keys we should ignore, then ignore it and don't update the input\n            // modality to keyboard.\n            if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n                return;\n            }\n            this._modality.next('keyboard');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onMousedown = (event) => {\n            // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n            // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n            // after the previous touch event.\n            if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n                return;\n            }\n            // Fake mousedown events are fired by some screen readers when controls are activated by the\n            // screen reader. Attribute them to keyboard input modality.\n            this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onTouchstart = (event) => {\n            // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n            // events are fired. Again, attribute to keyboard input modality.\n            if (isFakeTouchstartFromScreenReader(event)) {\n                this._modality.next('keyboard');\n                return;\n            }\n            // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n            // triggered via mouse vs touch.\n            this._lastTouchMs = Date.now();\n            this._modality.next('touch');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        this._options = {\n            ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n            ...options,\n        };\n        // Skip the first emission as it's null.\n        this.modalityDetected = this._modality.pipe(skip(1));\n        this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n        // If we're not in a browser, this service should do nothing, as there's no relevant input\n        // modality to detect.\n        if (_platform.isBrowser) {\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n                document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n                document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n            });\n        }\n    }\n    ngOnDestroy() {\n        this._modality.complete();\n        if (this._platform.isBrowser) {\n            document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n            document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n            document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: InputModalityDetector, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT }, { token: INPUT_MODALITY_DETECTOR_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: InputModalityDetector, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: InputModalityDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n                }] }]; } });\n\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n    return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n    constructor(elementToken, _ngZone, _document, _defaultOptions) {\n        this._ngZone = _ngZone;\n        this._defaultOptions = _defaultOptions;\n        // We inject the live element and document as `any` because the constructor signature cannot\n        // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n        // a class decorator causes TypeScript to preserve the constructor signature types.\n        this._document = _document;\n        this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n        const defaultOptions = this._defaultOptions;\n        let politeness;\n        let duration;\n        if (args.length === 1 && typeof args[0] === 'number') {\n            duration = args[0];\n        }\n        else {\n            [politeness, duration] = args;\n        }\n        this.clear();\n        clearTimeout(this._previousTimeout);\n        if (!politeness) {\n            politeness =\n                defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n        }\n        if (duration == null && defaultOptions) {\n            duration = defaultOptions.duration;\n        }\n        // TODO: ensure changing the politeness works on all environments we support.\n        this._liveElement.setAttribute('aria-live', politeness);\n        if (this._liveElement.id) {\n            this._exposeAnnouncerToModals(this._liveElement.id);\n        }\n        // This 100ms timeout is necessary for some browser + screen-reader combinations:\n        // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n        // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n        //   second time without clearing and then using a non-zero delay.\n        // (using JAWS 17 at time of this writing).\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._currentPromise) {\n                this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n            }\n            clearTimeout(this._previousTimeout);\n            this._previousTimeout = setTimeout(() => {\n                this._liveElement.textContent = message;\n                if (typeof duration === 'number') {\n                    this._previousTimeout = setTimeout(() => this.clear(), duration);\n                }\n                this._currentResolve();\n                this._currentPromise = this._currentResolve = undefined;\n            }, 100);\n            return this._currentPromise;\n        });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n        if (this._liveElement) {\n            this._liveElement.textContent = '';\n        }\n    }\n    ngOnDestroy() {\n        clearTimeout(this._previousTimeout);\n        this._liveElement?.remove();\n        this._liveElement = null;\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n        const elementClass = 'cdk-live-announcer-element';\n        const previousElements = this._document.getElementsByClassName(elementClass);\n        const liveEl = this._document.createElement('div');\n        // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n        for (let i = 0; i < previousElements.length; i++) {\n            previousElements[i].remove();\n        }\n        liveEl.classList.add(elementClass);\n        liveEl.classList.add('cdk-visually-hidden');\n        liveEl.setAttribute('aria-atomic', 'true');\n        liveEl.setAttribute('aria-live', 'polite');\n        liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n        this._document.body.appendChild(liveEl);\n        return liveEl;\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live announcer element if there is an\n     * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live announcer element.\n     */\n    _exposeAnnouncerToModals(id) {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `SnakBarContainer` and other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: LiveAnnouncer, deps: [{ token: LIVE_ANNOUNCER_ELEMENT_TOKEN, optional: true }, { token: i0.NgZone }, { token: DOCUMENT }, { token: LIVE_ANNOUNCER_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: LiveAnnouncer, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: LiveAnnouncer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n                }] }]; } });\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n        return this._politeness;\n    }\n    set politeness(value) {\n        this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n        if (this._politeness === 'off') {\n            if (this._subscription) {\n                this._subscription.unsubscribe();\n                this._subscription = null;\n            }\n        }\n        else if (!this._subscription) {\n            this._subscription = this._ngZone.runOutsideAngular(() => {\n                return this._contentObserver.observe(this._elementRef).subscribe(() => {\n                    // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n                    const elementText = this._elementRef.nativeElement.textContent;\n                    // The `MutationObserver` fires also for attribute\n                    // changes which we don't want to announce.\n                    if (elementText !== this._previousAnnouncedText) {\n                        this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n                        this._previousAnnouncedText = elementText;\n                    }\n                });\n            });\n        }\n    }\n    constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n        this._elementRef = _elementRef;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._contentObserver = _contentObserver;\n        this._ngZone = _ngZone;\n        this._politeness = 'polite';\n    }\n    ngOnDestroy() {\n        if (this._subscription) {\n            this._subscription.unsubscribe();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAriaLive, deps: [{ token: i0.ElementRef }, { token: LiveAnnouncer }, { token: i1$1.ContentObserver }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkAriaLive, selector: \"[cdkAriaLive]\", inputs: { politeness: [\"cdkAriaLive\", \"politeness\"], duration: [\"cdkAriaLiveDuration\", \"duration\"] }, exportAs: [\"cdkAriaLive\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAriaLive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAriaLive]',\n                    exportAs: 'cdkAriaLive',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: LiveAnnouncer }, { type: i1$1.ContentObserver }, { type: i0.NgZone }]; }, propDecorators: { politeness: [{\n                type: Input,\n                args: ['cdkAriaLive']\n            }], duration: [{\n                type: Input,\n                args: ['cdkAriaLiveDuration']\n            }] } });\n\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n    constructor(_ngZone, _platform, _inputModalityDetector, \n    /** @breaking-change 11.0.0 make document required */\n    document, options) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._inputModalityDetector = _inputModalityDetector;\n        /** The focus origin that the next focus event is a result of. */\n        this._origin = null;\n        /** Whether the window has just been focused. */\n        this._windowFocused = false;\n        /**\n         * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n         * focus events to touch interactions requires special logic.\n         */\n        this._originFromTouchInteraction = false;\n        /** Map of elements being monitored to their info. */\n        this._elementInfo = new Map();\n        /** The number of elements currently being monitored. */\n        this._monitoredElementCount = 0;\n        /**\n         * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n         * as well as the number of monitored elements that they contain. We have to treat focus/blur\n         * handlers differently from the rest of the events, because the browser won't emit events\n         * to the document when focus moves inside of a shadow root.\n         */\n        this._rootNodeFocusListenerCount = new Map();\n        /**\n         * Event listener for `focus` events on the window.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._windowFocusListener = () => {\n            // Make a note of when the window regains focus, so we can\n            // restore the origin info for the focused element.\n            this._windowFocused = true;\n            this._windowFocusTimeoutId = window.setTimeout(() => (this._windowFocused = false));\n        };\n        /** Subject for stopping our InputModalityDetector subscription. */\n        this._stopInputModalityDetector = new Subject();\n        /**\n         * Event listener for `focus` and 'blur' events on the document.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._rootNodeFocusAndBlurListener = (event) => {\n            const target = _getEventTarget(event);\n            // We need to walk up the ancestor chain in order to support `checkChildren`.\n            for (let element = target; element; element = element.parentElement) {\n                if (event.type === 'focus') {\n                    this._onFocus(event, element);\n                }\n                else {\n                    this._onBlur(event, element);\n                }\n            }\n        };\n        this._document = document;\n        this._detectionMode = options?.detectionMode || 0 /* FocusMonitorDetectionMode.IMMEDIATE */;\n    }\n    monitor(element, checkChildren = false) {\n        const nativeElement = coerceElement(element);\n        // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n        if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n            // Note: we don't want the observable to emit at all so we don't pass any parameters.\n            return of();\n        }\n        // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n        // the shadow root, rather than the `document`, because the browser won't emit focus events\n        // to the `document`, if focus is moving within the same shadow root.\n        const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n        const cachedInfo = this._elementInfo.get(nativeElement);\n        // Check if we're already monitoring this element.\n        if (cachedInfo) {\n            if (checkChildren) {\n                // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n                // observers into ones that behave as if `checkChildren` was turned on. We need a more\n                // robust solution.\n                cachedInfo.checkChildren = true;\n            }\n            return cachedInfo.subject;\n        }\n        // Create monitored element info.\n        const info = {\n            checkChildren: checkChildren,\n            subject: new Subject(),\n            rootNode,\n        };\n        this._elementInfo.set(nativeElement, info);\n        this._registerGlobalListeners(info);\n        return info.subject;\n    }\n    stopMonitoring(element) {\n        const nativeElement = coerceElement(element);\n        const elementInfo = this._elementInfo.get(nativeElement);\n        if (elementInfo) {\n            elementInfo.subject.complete();\n            this._setClasses(nativeElement);\n            this._elementInfo.delete(nativeElement);\n            this._removeGlobalListeners(elementInfo);\n        }\n    }\n    focusVia(element, origin, options) {\n        const nativeElement = coerceElement(element);\n        const focusedElement = this._getDocument().activeElement;\n        // If the element is focused already, calling `focus` again won't trigger the event listener\n        // which means that the focus classes won't be updated. If that's the case, update the classes\n        // directly without waiting for an event.\n        if (nativeElement === focusedElement) {\n            this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n        }\n        else {\n            this._setOrigin(origin);\n            // `focus` isn't available on the server\n            if (typeof nativeElement.focus === 'function') {\n                nativeElement.focus(options);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n        if (this._origin) {\n            // If the origin was realized via a touch interaction, we need to perform additional checks\n            // to determine whether the focus origin should be attributed to touch or program.\n            if (this._originFromTouchInteraction) {\n                return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n            }\n            else {\n                return this._origin;\n            }\n        }\n        // If the window has just regained focus, we can restore the most recent origin from before the\n        // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n        // focus. This typically means one of two things happened:\n        //\n        // 1) The element was programmatically focused, or\n        // 2) The element was focused via screen reader navigation (which generally doesn't fire\n        //    events).\n        //\n        // Because we can't distinguish between these two cases, we default to setting `program`.\n        if (this._windowFocused && this._lastFocusOrigin) {\n            return this._lastFocusOrigin;\n        }\n        // If the interaction is coming from an input label, we consider it a mouse interactions.\n        // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n        // our detection, because all our assumptions are for `mousedown`. We need to handle this\n        // special case, because it's very common for checkboxes and radio buttons.\n        if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n            return 'mouse';\n        }\n        return 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n        // Please note that this check is not perfect. Consider the following edge case:\n        //\n        // <div #parent tabindex=\"0\">\n        //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n        // </div>\n        //\n        // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n        // #child, #parent is programmatically focused. This code will attribute the focus to touch\n        // instead of program. This is a relatively minor edge-case that can be worked around by using\n        // focusVia(parent, 'program') to focus #parent.\n        return (this._detectionMode === 1 /* FocusMonitorDetectionMode.EVENTUAL */ ||\n            !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget));\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n        element.classList.toggle('cdk-focused', !!origin);\n        element.classList.toggle('cdk-touch-focused', origin === 'touch');\n        element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n        element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n        element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n        this._ngZone.runOutsideAngular(() => {\n            this._origin = origin;\n            this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n            // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n            // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n            // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n            // a touch event because when a touch event is fired, the associated focus event isn't yet in\n            // the event queue. Before doing so, clear any pending timeouts.\n            if (this._detectionMode === 0 /* FocusMonitorDetectionMode.IMMEDIATE */) {\n                clearTimeout(this._originTimeoutId);\n                const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n                this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n            }\n        });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n        // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n        // focus event affecting the monitored element. If we want to use the origin of the first event\n        // instead we should check for the cdk-focused class here and return if the element already has\n        // it. (This only matters for elements that have includesChildren = true).\n        // If we are not counting child-element-focus as focused, make sure that the event target is the\n        // monitored element itself.\n        const elementInfo = this._elementInfo.get(element);\n        const focusEventTarget = _getEventTarget(event);\n        if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n            return;\n        }\n        this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n        // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n        // order to focus another child of the monitored element.\n        const elementInfo = this._elementInfo.get(element);\n        if (!elementInfo ||\n            (elementInfo.checkChildren &&\n                event.relatedTarget instanceof Node &&\n                element.contains(event.relatedTarget))) {\n            return;\n        }\n        this._setClasses(element);\n        this._emitOrigin(elementInfo, null);\n    }\n    _emitOrigin(info, origin) {\n        if (info.subject.observers.length) {\n            this._ngZone.run(() => info.subject.next(origin));\n        }\n    }\n    _registerGlobalListeners(elementInfo) {\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        const rootNode = elementInfo.rootNode;\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n        if (!rootNodeFocusListeners) {\n            this._ngZone.runOutsideAngular(() => {\n                rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n            });\n        }\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n        // Register global listeners when first element is monitored.\n        if (++this._monitoredElementCount === 1) {\n            // Note: we listen to events in the capture phase so we\n            // can detect them even if the user stops propagation.\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                window.addEventListener('focus', this._windowFocusListener);\n            });\n            // The InputModalityDetector is also just a collection of global listeners.\n            this._inputModalityDetector.modalityDetected\n                .pipe(takeUntil(this._stopInputModalityDetector))\n                .subscribe(modality => {\n                this._setOrigin(modality, true /* isFromInteraction */);\n            });\n        }\n    }\n    _removeGlobalListeners(elementInfo) {\n        const rootNode = elementInfo.rootNode;\n        if (this._rootNodeFocusListenerCount.has(rootNode)) {\n            const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n            if (rootNodeFocusListeners > 1) {\n                this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n            }\n            else {\n                rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                this._rootNodeFocusListenerCount.delete(rootNode);\n            }\n        }\n        // Unregister global listeners when last element is unmonitored.\n        if (!--this._monitoredElementCount) {\n            const window = this._getWindow();\n            window.removeEventListener('focus', this._windowFocusListener);\n            // Equivalently, stop our InputModalityDetector subscription.\n            this._stopInputModalityDetector.next();\n            // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n            clearTimeout(this._windowFocusTimeoutId);\n            clearTimeout(this._originTimeoutId);\n        }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n        this._setClasses(element, origin);\n        this._emitOrigin(elementInfo, origin);\n        this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n        const results = [];\n        this._elementInfo.forEach((info, currentElement) => {\n            if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n                results.push([currentElement, info]);\n            }\n        });\n        return results;\n    }\n    /**\n     * Returns whether an interaction is likely to have come from the user clicking the `label` of\n     * an `input` or `textarea` in order to focus it.\n     * @param focusEventTarget Target currently receiving focus.\n     */\n    _isLastInteractionFromInputLabel(focusEventTarget) {\n        const { _mostRecentTarget: mostRecentTarget, mostRecentModality } = this._inputModalityDetector;\n        // If the last interaction used the mouse on an element contained by one of the labels\n        // of an `input`/`textarea` that is currently focused, it is very likely that the\n        // user redirected focus using the label.\n        if (mostRecentModality !== 'mouse' ||\n            !mostRecentTarget ||\n            mostRecentTarget === focusEventTarget ||\n            (focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA') ||\n            focusEventTarget.disabled) {\n            return false;\n        }\n        const labels = focusEventTarget.labels;\n        if (labels) {\n            for (let i = 0; i < labels.length; i++) {\n                if (labels[i].contains(mostRecentTarget)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusMonitor, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: InputModalityDetector }, { token: DOCUMENT, optional: true }, { token: FOCUS_MONITOR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusMonitor, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FocusMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i1.Platform }, { type: InputModalityDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n                }] }]; } });\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n    constructor(_elementRef, _focusMonitor) {\n        this._elementRef = _elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._focusOrigin = null;\n        this.cdkFocusChange = new EventEmitter();\n    }\n    get focusOrigin() {\n        return this._focusOrigin;\n    }\n    ngAfterViewInit() {\n        const element = this._elementRef.nativeElement;\n        this._monitorSubscription = this._focusMonitor\n            .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n            .subscribe(origin => {\n            this._focusOrigin = origin;\n            this.cdkFocusChange.emit(origin);\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        if (this._monitorSubscription) {\n            this._monitorSubscription.unsubscribe();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkMonitorFocus, deps: [{ token: i0.ElementRef }, { token: FocusMonitor }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: { cdkFocusChange: \"cdkFocusChange\" }, exportAs: [\"cdkMonitorFocus\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkMonitorFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n                    exportAs: 'cdkMonitorFocus',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: FocusMonitor }]; }, propDecorators: { cdkFocusChange: [{\n                type: Output\n            }] } });\n\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n    constructor(_platform, document) {\n        this._platform = _platform;\n        this._document = document;\n        this._breakpointSubscription = inject(BreakpointObserver)\n            .observe('(forced-colors: active)')\n            .subscribe(() => {\n            if (this._hasCheckedHighContrastMode) {\n                this._hasCheckedHighContrastMode = false;\n                this._applyBodyHighContrastModeCssClasses();\n            }\n        });\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n        if (!this._platform.isBrowser) {\n            return 0 /* HighContrastMode.NONE */;\n        }\n        // Create a test element with an arbitrary background-color that is neither black nor\n        // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n        // appending the test element to the DOM does not affect layout by absolutely positioning it\n        const testElement = this._document.createElement('div');\n        testElement.style.backgroundColor = 'rgb(1,2,3)';\n        testElement.style.position = 'absolute';\n        this._document.body.appendChild(testElement);\n        // Get the computed style for the background color, collapsing spaces to normalize between\n        // browsers. Once we get this color, we no longer need the test element. Access the `window`\n        // via the document so we can fake it in tests. Note that we have extra null checks, because\n        // this logic will likely run during app bootstrap and throwing can break the entire app.\n        const documentWindow = this._document.defaultView || window;\n        const computedStyle = documentWindow && documentWindow.getComputedStyle\n            ? documentWindow.getComputedStyle(testElement)\n            : null;\n        const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(/ /g, '');\n        testElement.remove();\n        switch (computedColor) {\n            // Pre Windows 11 dark theme.\n            case 'rgb(0,0,0)':\n            // Windows 11 dark themes.\n            case 'rgb(45,50,54)':\n            case 'rgb(32,32,32)':\n                return 2 /* HighContrastMode.WHITE_ON_BLACK */;\n            // Pre Windows 11 light theme.\n            case 'rgb(255,255,255)':\n            // Windows 11 light theme.\n            case 'rgb(255,250,239)':\n                return 1 /* HighContrastMode.BLACK_ON_WHITE */;\n        }\n        return 0 /* HighContrastMode.NONE */;\n    }\n    ngOnDestroy() {\n        this._breakpointSubscription.unsubscribe();\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n        if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n            const bodyClasses = this._document.body.classList;\n            bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            this._hasCheckedHighContrastMode = true;\n            const mode = this.getHighContrastMode();\n            if (mode === 1 /* HighContrastMode.BLACK_ON_WHITE */) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n            }\n            else if (mode === 2 /* HighContrastMode.WHITE_ON_BLACK */) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: HighContrastModeDetector, deps: [{ token: i1.Platform }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: HighContrastModeDetector, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: HighContrastModeDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nclass A11yModule {\n    constructor(highContrastModeDetector) {\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: A11yModule, deps: [{ token: HighContrastModeDetector }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: A11yModule, declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus], imports: [ObserversModule], exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: A11yModule, imports: [ObserversModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: A11yModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ObserversModule],\n                    declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                }]\n        }], ctorParameters: function () { return [{ type: HighContrastModeDetector }]; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusTrap, FocusTrapFactory, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, addAriaReferencedId, getAriaReferenceIds, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, removeAriaReferencedId };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable, Optional, Inject, Directive, Output, Input, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n    providedIn: 'root',\n    factory: DIR_DOCUMENT_FACTORY,\n});\n/** @docs-private */\nfunction DIR_DOCUMENT_FACTORY() {\n    return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n    const value = rawValue?.toLowerCase() || '';\n    if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n        return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n    }\n    return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n    constructor(_document) {\n        /** The current 'ltr' or 'rtl' value. */\n        this.value = 'ltr';\n        /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n        this.change = new EventEmitter();\n        if (_document) {\n            const bodyDir = _document.body ? _document.body.dir : null;\n            const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n            this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n        }\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Directionality, deps: [{ token: DIR_DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Directionality, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Directionality, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DIR_DOCUMENT]\n                }] }]; } });\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n    constructor() {\n        /** Normalized direction that accounts for invalid/unsupported values. */\n        this._dir = 'ltr';\n        /** Whether the `value` has been set to its initial value. */\n        this._isInitialized = false;\n        /** Event emitted when the direction changes. */\n        this.change = new EventEmitter();\n    }\n    /** @docs-private */\n    get dir() {\n        return this._dir;\n    }\n    set dir(value) {\n        const previousValue = this._dir;\n        // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n        // whereas the browser does it based on the content of the element. Since doing so based\n        // on the content can be expensive, for now we're doing the simpler matching.\n        this._dir = _resolveDirectionality(value);\n        this._rawDir = value;\n        if (previousValue !== this._dir && this._isInitialized) {\n            this.change.emit(this._dir);\n        }\n    }\n    /** Current layout direction of the element. */\n    get value() {\n        return this.dir;\n    }\n    /** Initialize once default value has been set. */\n    ngAfterContentInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Dir, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: Dir, selector: \"[dir]\", inputs: { dir: \"dir\" }, outputs: { change: \"dirChange\" }, host: { properties: { \"attr.dir\": \"_rawDir\" } }, providers: [{ provide: Directionality, useExisting: Dir }], exportAs: [\"dir\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Dir, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[dir]',\n                    providers: [{ provide: Directionality, useExisting: Dir }],\n                    host: { '[attr.dir]': '_rawDir' },\n                    exportAs: 'dir',\n                }]\n        }], propDecorators: { change: [{\n                type: Output,\n                args: ['dirChange']\n            }], dir: [{\n                type: Input\n            }] } });\n\nclass BidiModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BidiModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: BidiModule, declarations: [Dir], exports: [Dir] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BidiModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BidiModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [Dir],\n                    declarations: [Dir],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BidiModule, DIR_DOCUMENT, Dir, Directionality };\n", "import { ElementRef } from '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n    return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n    // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n    // and other non-number values as NaN, where Number just uses 0) but it considers the string\n    // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n    return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\nfunction coerceArray(value) {\n    return Array.isArray(value) ? value : [value];\n}\n\n/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n    if (value == null) {\n        return '';\n    }\n    return typeof value === 'string' ? value : `${value}px`;\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n    return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n    const result = [];\n    if (value != null) {\n        const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n        for (const sourceValue of sourceValues) {\n            const trimmedString = `${sourceValue}`.trim();\n            if (trimmedString) {\n                result.push(trimmedString);\n            }\n        }\n    }\n    return result;\n}\n\nexport { _isNumberValue, coerceArray, coerceBooleanProperty, coerceCssPixelValue, coerceElement, coerceNumberProperty, coerceStringArray };\n", "import { ConnectableObservable, isObservable, of, Subject } from 'rxjs';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken } from '@angular/core';\n\nclass DataSource {\n}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n    // Check if the value is a DataSource by observing if it has a connect function. Cannot\n    // be checked as an `instanceof DataSource` since people could create their own sources\n    // that match the interface, but don't extend DataSource. We also can't use `isObservable`\n    // here, because of some internal apps.\n    return value && typeof value.connect === 'function' && !(value instanceof ConnectableObservable);\n}\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n    constructor(_data) {\n        super();\n        this._data = _data;\n    }\n    connect() {\n        return isObservable(this._data) ? this._data : of(this._data);\n    }\n    disconnect() { }\n}\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n                operation = 1 /* _ViewRepeaterOperation.INSERTED */;\n            }\n            else if (currentIndex == null) {\n                viewContainerRef.remove(adjustedPreviousIndex);\n                operation = 3 /* _ViewRepeaterOperation.REMOVED */;\n            }\n            else {\n                view = viewContainerRef.get(adjustedPreviousIndex);\n                viewContainerRef.move(view, currentIndex);\n                operation = 2 /* _ViewRepeaterOperation.MOVED */;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() { }\n}\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n    constructor() {\n        /**\n         * The size of the cache used to store unused views.\n         * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n         */\n        this.viewCacheSize = 20;\n        /**\n         * View cache that stores embedded view instances that have been previously stamped out,\n         * but don't are not currently rendered. The view repeater will reuse these views rather than\n         * creating brand new ones.\n         *\n         * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n         */\n        this._viewCache = [];\n    }\n    /** Apply changes to the DOM. */\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        // Rearrange the views to put them in the right location.\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                // Item added.\n                const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = view ? 1 /* _ViewRepeaterOperation.INSERTED */ : 0 /* _ViewRepeaterOperation.REPLACED */;\n            }\n            else if (currentIndex == null) {\n                // Item removed.\n                this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n                operation = 3 /* _ViewRepeaterOperation.REMOVED */;\n            }\n            else {\n                // Item moved.\n                view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = 2 /* _ViewRepeaterOperation.MOVED */;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() {\n        for (const view of this._viewCache) {\n            view.destroy();\n        }\n        this._viewCache = [];\n    }\n    /**\n     * Inserts a view for a new item, either from the cache or by creating a new\n     * one. Returns `undefined` if the item was inserted into a cached view.\n     */\n    _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n        const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n        if (cachedView) {\n            cachedView.context.$implicit = value;\n            return undefined;\n        }\n        const viewArgs = viewArgsFactory();\n        return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n    }\n    /** Detaches the view at the given index and inserts into the view cache. */\n    _detachAndCacheView(index, viewContainerRef) {\n        const detachedView = viewContainerRef.detach(index);\n        this._maybeCacheView(detachedView, viewContainerRef);\n    }\n    /** Moves view at the previous index to the current index. */\n    _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n        const view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        view.context.$implicit = value;\n        return view;\n    }\n    /**\n     * Cache the given detached view. If the cache is full, the view will be\n     * destroyed.\n     */\n    _maybeCacheView(view, viewContainerRef) {\n        if (this._viewCache.length < this.viewCacheSize) {\n            this._viewCache.push(view);\n        }\n        else {\n            const index = viewContainerRef.indexOf(view);\n            // The host component could remove views from the container outside of\n            // the view repeater. It's unlikely this will occur, but just in case,\n            // destroy the view on its own, otherwise destroy it through the\n            // container to ensure that all the references are removed.\n            if (index === -1) {\n                view.destroy();\n            }\n            else {\n                viewContainerRef.remove(index);\n            }\n        }\n    }\n    /** Inserts a recycled view from the cache at the given index. */\n    _insertViewFromCache(index, viewContainerRef) {\n        const cachedView = this._viewCache.pop();\n        if (cachedView) {\n            viewContainerRef.insert(cachedView, index);\n        }\n        return cachedView || null;\n    }\n}\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        this.compareWith = compareWith;\n        /** Currently-selected values. */\n        this._selection = new Set();\n        /** Keeps track of the deselected options that haven't been emitted by the change event. */\n        this._deselectedToEmit = [];\n        /** Keeps track of the selected options that haven't been emitted by the change event. */\n        this._selectedToEmit = [];\n        /** Event emitted when the value has changed. */\n        this.changed = new Subject();\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /**\n     * Selects a value or an array of values.\n     * @param values The values to select\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Deselects a value or an array of values.\n     * @param values The values to deselect\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Sets the selected values\n     * @param values The new selected values\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    setSelection(...values) {\n        this._verifyValueAssignment(values);\n        const oldValues = this.selected;\n        const newSelectedSet = new Set(values);\n        values.forEach(value => this._markSelected(value));\n        oldValues\n            .filter(value => !newSelectedSet.has(value))\n            .forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     * @param value The value to toggle\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    toggle(value) {\n        return this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     * @param flushEvent Whether to flush the changes in an event.\n     *   If false, the changes to the selection will be flushed along with the next event.\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    clear(flushEvent = true) {\n        this._unmarkAll();\n        const changed = this._hasQueuedChanges();\n        if (flushEvent) {\n            this._emitChangeEvent();\n        }\n        return changed;\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(this._getConcreteValue(value));\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        value = this._getConcreteValue(value);\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            if (!this.isSelected(value)) {\n                this._selection.add(value);\n            }\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        value = this._getConcreteValue(value);\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n    /** Whether there are queued up change to be emitted. */\n    _hasQueuedChanges() {\n        return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n    }\n    /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n    _getConcreteValue(inputValue) {\n        if (!this.compareWith) {\n            return inputValue;\n        }\n        else {\n            for (let selectedValue of this._selection) {\n                if (this.compareWith(inputValue, selectedValue)) {\n                    return selectedValue;\n                }\n            }\n            return inputValue;\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n    constructor() {\n        this._listeners = [];\n    }\n    /**\n     * Notify other items that selection for the given name has been set.\n     * @param id ID of the item.\n     * @param name Name of the item.\n     */\n    notify(id, name) {\n        for (let listener of this._listeners) {\n            listener(id, name);\n        }\n    }\n    /**\n     * Listen for future changes to item selection.\n     * @return Function used to deregister listener\n     */\n    listen(listener) {\n        this._listeners.push(listener);\n        return () => {\n            this._listeners = this._listeners.filter((registered) => {\n                return listener !== registered;\n            });\n        };\n    }\n    ngOnDestroy() {\n        this._listeners = [];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: UniqueSelectionDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: UniqueSelectionDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: UniqueSelectionDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ArrayDataSource, DataSource, SelectionModel, UniqueSelectionDispatcher, _DisposeViewRepeaterStrategy, _RecycleViewRepeaterStrategy, _VIEW_REPEATER_STRATEGY, getMultipleValuesInSingleSelectionError, isDataSource };\n", "const MAC_ENTER = 3;\nconst BACKSPACE = 8;\nconst TAB = 9;\nconst NUM_CENTER = 12;\nconst ENTER = 13;\nconst SHIFT = 16;\nconst CONTROL = 17;\nconst ALT = 18;\nconst PAUSE = 19;\nconst CAPS_LOCK = 20;\nconst ESCAPE = 27;\nconst SPACE = 32;\nconst PAGE_UP = 33;\nconst PAGE_DOWN = 34;\nconst END = 35;\nconst HOME = 36;\nconst LEFT_ARROW = 37;\nconst UP_ARROW = 38;\nconst RIGHT_ARROW = 39;\nconst DOWN_ARROW = 40;\nconst PLUS_SIGN = 43;\nconst PRINT_SCREEN = 44;\nconst INSERT = 45;\nconst DELETE = 46;\nconst ZERO = 48;\nconst ONE = 49;\nconst TWO = 50;\nconst THREE = 51;\nconst FOUR = 52;\nconst FIVE = 53;\nconst SIX = 54;\nconst SEVEN = 55;\nconst EIGHT = 56;\nconst NINE = 57;\nconst FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186\nconst FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187\nconst QUESTION_MARK = 63;\nconst AT_SIGN = 64;\nconst A = 65;\nconst B = 66;\nconst C = 67;\nconst D = 68;\nconst E = 69;\nconst F = 70;\nconst G = 71;\nconst H = 72;\nconst I = 73;\nconst J = 74;\nconst K = 75;\nconst L = 76;\nconst M = 77;\nconst N = 78;\nconst O = 79;\nconst P = 80;\nconst Q = 81;\nconst R = 82;\nconst S = 83;\nconst T = 84;\nconst U = 85;\nconst V = 86;\nconst W = 87;\nconst X = 88;\nconst Y = 89;\nconst Z = 90;\nconst META = 91; // WIN_KEY_LEFT\nconst MAC_WK_CMD_LEFT = 91;\nconst MAC_WK_CMD_RIGHT = 93;\nconst CONTEXT_MENU = 93;\nconst NUMPAD_ZERO = 96;\nconst NUMPAD_ONE = 97;\nconst NUMPAD_TWO = 98;\nconst NUMPAD_THREE = 99;\nconst NUMPAD_FOUR = 100;\nconst NUMPAD_FIVE = 101;\nconst NUMPAD_SIX = 102;\nconst NUMPAD_SEVEN = 103;\nconst NUMPAD_EIGHT = 104;\nconst NUMPAD_NINE = 105;\nconst NUMPAD_MULTIPLY = 106;\nconst NUMPAD_PLUS = 107;\nconst NUMPAD_MINUS = 109;\nconst NUMPAD_PERIOD = 110;\nconst NUMPAD_DIVIDE = 111;\nconst F1 = 112;\nconst F2 = 113;\nconst F3 = 114;\nconst F4 = 115;\nconst F5 = 116;\nconst F6 = 117;\nconst F7 = 118;\nconst F8 = 119;\nconst F9 = 120;\nconst F10 = 121;\nconst F11 = 122;\nconst F12 = 123;\nconst NUM_LOCK = 144;\nconst SCROLL_LOCK = 145;\nconst FIRST_MEDIA = 166;\nconst FF_MINUS = 173;\nconst MUTE = 173; // Firefox (Gecko) fires 181 for MUTE\nconst VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN\nconst VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP\nconst FF_MUTE = 181;\nconst FF_VOLUME_DOWN = 182;\nconst LAST_MEDIA = 183;\nconst FF_VOLUME_UP = 183;\nconst SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON\nconst EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS\nconst COMMA = 188;\nconst DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS\nconst PERIOD = 190;\nconst SLASH = 191;\nconst APOSTROPHE = 192;\nconst TILDE = 192;\nconst OPEN_SQUARE_BRACKET = 219;\nconst BACKSLASH = 220;\nconst CLOSE_SQUARE_BRACKET = 221;\nconst SINGLE_QUOTE = 222;\nconst MAC_META = 224;\n\n/**\n * Checks whether a modifier key is pressed.\n * @param event Event to be checked.\n */\nfunction hasModifierKey(event, ...modifiers) {\n    if (modifiers.length) {\n        return modifiers.some(modifier => event[modifier]);\n    }\n    return event.altKey || event.shiftKey || event.ctrlKey || event.metaKey;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A, ALT, APOSTROPHE, AT_SIGN, B, BACKSLASH, BACKSPACE, C, CAPS_LOCK, CLOSE_SQUARE_BRACKET, COMMA, CONTEXT_MENU, CONTROL, D, DASH, DELETE, DOWN_ARROW, E, EIGHT, END, ENTER, EQUALS, ESCAPE, F, F1, F10, F11, F12, F2, F3, F4, F5, F6, F7, F8, F9, FF_EQUALS, FF_MINUS, FF_MUTE, FF_SEMICOLON, FF_VOLUME_DOWN, FF_VOLUME_UP, FIRST_MEDIA, FIVE, FOUR, G, H, HOME, I, INSERT, J, K, L, LAST_MEDIA, LEFT_ARROW, M, MAC_ENTER, MAC_META, MAC_WK_CMD_LEFT, MAC_WK_CMD_RIGHT, META, MUTE, N, NINE, NUMPAD_DIVIDE, NUMPAD_EIGHT, NUMPAD_FIVE, NUMPAD_FOUR, NUMPAD_MINUS, NUMPAD_MULTIPLY, NUMPAD_NINE, NUMPAD_ONE, NUMPAD_PERIOD, NUMPAD_PLUS, NUMPAD_SEVEN, NUMPAD_SIX, NUMPAD_THREE, NUMPAD_TWO, NUMPAD_ZERO, NUM_CENTER, NUM_LOCK, O, ONE, OPEN_SQUARE_BRACKET, P, PAGE_DOWN, PAGE_UP, PAUSE, PERIOD, PLUS_SIGN, PRINT_SCREEN, Q, QUESTION_MARK, R, RIGHT_ARROW, S, SCROLL_LOCK, SEMICOLON, SEVEN, SHIFT, SINGLE_QUOTE, SIX, SLASH, SPACE, T, TAB, THREE, TILDE, TWO, U, UP_ARROW, V, VOLUME_DOWN, VOLUME_UP, W, X, Y, Z, ZERO, hasModifierKey };\n", "import * as i0 from '@angular/core';\nimport { NgModule, CSP_NONCE, Injectable, Optional, Inject } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\n\nclass LayoutModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: LayoutModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: LayoutModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: LayoutModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: LayoutModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n    constructor(_platform, _nonce) {\n        this._platform = _platform;\n        this._nonce = _nonce;\n        this._matchMedia =\n            this._platform.isBrowser && window.matchMedia\n                ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n                    // call it from a different scope.\n                    window.matchMedia.bind(window)\n                : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n        if (this._platform.WEBKIT || this._platform.BLINK) {\n            createEmptyStyleRule(query, this._nonce);\n        }\n        return this._matchMedia(query);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MediaMatcher, deps: [{ token: i1.Platform }, { token: CSP_NONCE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MediaMatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MediaMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }]; } });\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n    if (mediaQueriesForWebkitCompatibility.has(query)) {\n        return;\n    }\n    try {\n        if (!mediaQueryStyleNode) {\n            mediaQueryStyleNode = document.createElement('style');\n            if (nonce) {\n                mediaQueryStyleNode.nonce = nonce;\n            }\n            mediaQueryStyleNode.setAttribute('type', 'text/css');\n            document.head.appendChild(mediaQueryStyleNode);\n        }\n        if (mediaQueryStyleNode.sheet) {\n            mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n            mediaQueriesForWebkitCompatibility.add(query);\n        }\n    }\n    catch (e) {\n        console.error(e);\n    }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n    // Use `as any` here to avoid adding additional necessary properties for\n    // the noop matcher.\n    return {\n        matches: query === 'all' || query === '',\n        media: query,\n        addListener: () => { },\n        removeListener: () => { },\n    };\n}\n\n/** Utility for checking the matching state of @media queries. */\nclass BreakpointObserver {\n    constructor(_mediaMatcher, _zone) {\n        this._mediaMatcher = _mediaMatcher;\n        this._zone = _zone;\n        /**  A map of all media queries currently being listened for. */\n        this._queries = new Map();\n        /** A subject for all other observables to takeUntil based on. */\n        this._destroySubject = new Subject();\n    }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n        this._destroySubject.next();\n        this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n        const queries = splitQueries(coerceArray(value));\n        return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n        const queries = splitQueries(coerceArray(value));\n        const observables = queries.map(query => this._registerQuery(query).observable);\n        let stateObservable = combineLatest(observables);\n        // Emit the first state immediately, and then debounce the subsequent emissions.\n        stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n        return stateObservable.pipe(map(breakpointStates => {\n            const response = {\n                matches: false,\n                breakpoints: {},\n            };\n            breakpointStates.forEach(({ matches, query }) => {\n                response.matches = response.matches || matches;\n                response.breakpoints[query] = matches;\n            });\n            return response;\n        }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n        // Only set up a new MediaQueryList if it is not already being listened for.\n        if (this._queries.has(query)) {\n            return this._queries.get(query);\n        }\n        const mql = this._mediaMatcher.matchMedia(query);\n        // Create callback for match changes and add it is as a listener.\n        const queryObservable = new Observable((observer) => {\n            // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n            // back into the zone because matchMedia is only included in Zone.js by loading the\n            // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n            // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n            // patches it.\n            const handler = (e) => this._zone.run(() => observer.next(e));\n            mql.addListener(handler);\n            return () => {\n                mql.removeListener(handler);\n            };\n        }).pipe(startWith(mql), map(({ matches }) => ({ query, matches })), takeUntil(this._destroySubject));\n        // Add the MediaQueryList to the set of queries.\n        const output = { observable: queryObservable, mql };\n        this._queries.set(query, output);\n        return output;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BreakpointObserver, deps: [{ token: MediaMatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BreakpointObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BreakpointObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: MediaMatcher }, { type: i0.NgZone }]; } });\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n    return queries\n        .map(query => query.split(','))\n        .reduce((a1, a2) => a1.concat(a2))\n        .map(query => query.trim());\n}\n\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n    XSmall: '(max-width: 599.98px)',\n    Small: '(min-width: 600px) and (max-width: 959.98px)',\n    Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n    Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n    XLarge: '(min-width: 1920px)',\n    Handset: '(max-width: 599.98px) and (orientation: portrait), ' +\n        '(max-width: 959.98px) and (orientation: landscape)',\n    Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' +\n        '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    Web: '(min-width: 840px) and (orientation: portrait), ' +\n        '(min-width: 1280px) and (orientation: landscape)',\n    HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n    TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n    WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n    HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n    TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    WebLandscape: '(min-width: 1280px) and (orientation: landscape)',\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };\n", "import { coerceElement, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    constructor(_mutationObserverFactory) {\n        this._mutationObserverFactory = _mutationObserverFactory;\n        /** Keeps track of the existing MutationObservers so they can be reused. */\n        this._observedElements = new Map();\n    }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream.subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        if (!this._observedElements.has(element)) {\n            const stream = new Subject();\n            const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n            if (observer) {\n                observer.observe(element, {\n                    characterData: true,\n                    childList: true,\n                    subtree: true,\n                });\n            }\n            this._observedElements.set(element, { observer, stream, count: 1 });\n        }\n        else {\n            this._observedElements.get(element).count++;\n        }\n        return this._observedElements.get(element).stream;\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ContentObserver, deps: [{ token: MutationObserverFactory }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ContentObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: MutationObserverFactory }]; } });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    constructor(_contentObserver, _elementRef, _ngZone) {\n        this._contentObserver = _contentObserver;\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        /** Event emitted for each change in the element's content. */\n        this.event = new EventEmitter();\n        this._disabled = false;\n        this._currentSubscription = null;\n    }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n        // Consider brining it back inside the zone next time we're making breaking changes.\n        // Bringing it back inside can cause things like infinite change detection loops and changed\n        // after checked errors if people's code isn't handling it properly.\n        this._ngZone.runOutsideAngular(() => {\n            this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n        });\n    }\n    _unsubscribe() {\n        this._currentSubscription?.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkObserveContent, deps: [{ token: ContentObserver }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\"], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                }]\n        }], ctorParameters: function () { return [{ type: ContentObserver }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkObserveContentDisabled']\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: ObserversModule, declarations: [CdkObserveContent], exports: [CdkObserveContent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkObserveContent],\n                    declarations: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n", "import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n    hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n}\ncatch {\n    hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n    constructor(_platformId) {\n        this._platformId = _platformId;\n        // We want to use the Angular platform check because if the Document is shimmed\n        // without the navigator, the following checks will fail. This is preferred because\n        // sometimes the Document may be shimmed without the user's knowledge or intention\n        /** Whether the Angular application is being rendered in the browser. */\n        this.isBrowser = this._platformId\n            ? isPlatformBrowser(this._platformId)\n            : typeof document === 'object' && !!document;\n        /** Whether the current browser is Microsoft Edge. */\n        this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n        /** Whether the current rendering engine is Microsoft Trident. */\n        this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n        // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n        /** Whether the current rendering engine is Blink. */\n        this.BLINK = this.isBrowser &&\n            !!(window.chrome || hasV8BreakIterator) &&\n            typeof CSS !== 'undefined' &&\n            !this.EDGE &&\n            !this.TRIDENT;\n        // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n        // ensure that Webkit runs standalone and is not used as another engine's base.\n        /** Whether the current rendering engine is WebKit. */\n        this.WEBKIT = this.isBrowser &&\n            /AppleWebKit/i.test(navigator.userAgent) &&\n            !this.BLINK &&\n            !this.EDGE &&\n            !this.TRIDENT;\n        /** Whether the current platform is Apple iOS. */\n        this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n        // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n        // them self as Gecko-like browsers and modify the userAgent's according to that.\n        // Since we only cover one explicit Firefox case, we can simply check for Firefox\n        // instead of having an unstable check for Gecko.\n        /** Whether the current browser is Firefox. */\n        this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n        /** Whether the current platform is Android. */\n        // Trident on mobile adds the android platform to the userAgent to trick detections.\n        this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n        // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n        // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n        // Safari browser should also use Webkit as its layout engine.\n        /** Whether the current browser is Safari. */\n        this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Platform, deps: [{ token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Platform, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\n\nclass PlatformModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: PlatformModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: PlatformModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: PlatformModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: PlatformModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n    // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n    // first changing it to something else:\n    // The specified value \"\" does not conform to the required format.\n    // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n    'color',\n    'button',\n    'checkbox',\n    'date',\n    'datetime-local',\n    'email',\n    'file',\n    'hidden',\n    'image',\n    'month',\n    'number',\n    'password',\n    'radio',\n    'range',\n    'reset',\n    'search',\n    'submit',\n    'tel',\n    'text',\n    'time',\n    'url',\n    'week',\n];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n    // Result is cached.\n    if (supportedInputTypes) {\n        return supportedInputTypes;\n    }\n    // We can't check if an input type is not supported until we're on the browser, so say that\n    // everything is supported when not on the browser. We don't use `Platform` here since it's\n    // just a helper function and can't inject it.\n    if (typeof document !== 'object' || !document) {\n        supportedInputTypes = new Set(candidateInputTypes);\n        return supportedInputTypes;\n    }\n    let featureTestInput = document.createElement('input');\n    supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n        featureTestInput.setAttribute('type', value);\n        return featureTestInput.type === value;\n    }));\n    return supportedInputTypes;\n}\n\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n    if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n        try {\n            window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n                get: () => (supportsPassiveEvents = true),\n            }));\n        }\n        finally {\n            supportsPassiveEvents = supportsPassiveEvents || false;\n        }\n    }\n    return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n    return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n    if (scrollBehaviorSupported == null) {\n        // If we're not in the browser, it can't be supported. Also check for `Element`, because\n        // some projects stub out the global `document` during SSR which can throw us off.\n        if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n            scrollBehaviorSupported = false;\n            return scrollBehaviorSupported;\n        }\n        // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n        if ('scrollBehavior' in document.documentElement.style) {\n            scrollBehaviorSupported = true;\n        }\n        else {\n            // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n            // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n            const scrollToFunction = Element.prototype.scrollTo;\n            if (scrollToFunction) {\n                // We can detect if the function has been polyfilled by calling `toString` on it. Native\n                // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n                // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n                // polyfilled functions as supporting scroll behavior.\n                scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n            }\n            else {\n                scrollBehaviorSupported = false;\n            }\n        }\n    }\n    return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n    // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n    if (typeof document !== 'object' || !document) {\n        return 0 /* RtlScrollAxisType.NORMAL */;\n    }\n    if (rtlScrollAxisType == null) {\n        // Create a 1px wide scrolling container and a 2px wide content element.\n        const scrollContainer = document.createElement('div');\n        const containerStyle = scrollContainer.style;\n        scrollContainer.dir = 'rtl';\n        containerStyle.width = '1px';\n        containerStyle.overflow = 'auto';\n        containerStyle.visibility = 'hidden';\n        containerStyle.pointerEvents = 'none';\n        containerStyle.position = 'absolute';\n        const content = document.createElement('div');\n        const contentStyle = content.style;\n        contentStyle.width = '2px';\n        contentStyle.height = '1px';\n        scrollContainer.appendChild(content);\n        document.body.appendChild(scrollContainer);\n        rtlScrollAxisType = 0 /* RtlScrollAxisType.NORMAL */;\n        // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n        // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n        // dealing with one of the other two types of browsers.\n        if (scrollContainer.scrollLeft === 0) {\n            // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n            // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n            // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n            // return 0 when we read it again.\n            scrollContainer.scrollLeft = 1;\n            rtlScrollAxisType =\n                scrollContainer.scrollLeft === 0 ? 1 /* RtlScrollAxisType.NEGATED */ : 2 /* RtlScrollAxisType.INVERTED */;\n        }\n        scrollContainer.remove();\n    }\n    return rtlScrollAxisType;\n}\n\nlet shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n    if (shadowDomIsSupported == null) {\n        const head = typeof document !== 'undefined' ? document.head : null;\n        shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n    }\n    return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n    if (_supportsShadowDom()) {\n        const rootNode = element.getRootNode ? element.getRootNode() : null;\n        // Note that this should be caught by `_supportsShadowDom`, but some\n        // teams have been able to hit this code path on unsupported browsers.\n        if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n            return rootNode;\n        }\n    }\n    return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n    let activeElement = typeof document !== 'undefined' && document\n        ? document.activeElement\n        : null;\n    while (activeElement && activeElement.shadowRoot) {\n        const newActiveElement = activeElement.shadowRoot.activeElement;\n        if (newActiveElement === activeElement) {\n            break;\n        }\n        else {\n            activeElement = newActiveElement;\n        }\n    }\n    return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n    // If an event is bound outside the Shadow DOM, the `event.target` will\n    // point to the shadow root so we have to use `composedPath` instead.\n    return (event.composedPath ? event.composedPath()[0] : event.target);\n}\n\n/** Gets whether the code is currently running in a test environment. */\nfunction _isTestEnvironment() {\n    // We can't use `declare const` because it causes conflicts inside Google with the real typings\n    // for these symbols and we can't read them off the global object, because they don't appear to\n    // be attached there for some runners like Jest.\n    // (see: https://github.com/angular/components/issues/23365#issuecomment-*********)\n    return (\n    // @ts-ignore\n    (typeof __karma__ !== 'undefined' && !!__karma__) ||\n        // @ts-ignore\n        (typeof jasmine !== 'undefined' && !!jasmine) ||\n        // @ts-ignore\n        (typeof jest !== 'undefined' && !!jest) ||\n        // @ts-ignore\n        (typeof Mocha !== 'undefined' && !!Mocha));\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Platform, PlatformModule, _getEventTarget, _getFocusedElementPierceShadowDom, _getShadowRoot, _isTestEnvironment, _supportsShadowDom, getRtlScrollAxisType, getSupportedInputTypes, normalizePassiveListenerOptions, supportsPassiveEventListeners, supportsScrollBehavior };\n", "import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, ElementRef, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior, Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._scrolledIndexChange = new Subject();\n        /** @docs-private Implemented as part of VirtualScrollStrategy. */\n        this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n        /** The attached viewport. */\n        this._viewport = null;\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    constructor() {\n        this._itemSize = 20;\n        this._minBufferPx = 100;\n        this._maxBufferPx = 200;\n        /** The scroll strategy used by this directive. */\n        this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkFixedSizeVirtualScroll, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n            {\n                provide: VIRTUAL_SCROLL_STRATEGY,\n                useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n            },\n        ], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    standalone: true,\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    constructor(_ngZone, _platform, document) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n        this._scrolled = new Subject();\n        /** Keeps track of the global `scroll` and `resize` subscriptions. */\n        this._globalSubscription = null;\n        /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n        this._scrolledCount = 0;\n        /**\n         * Map of all the scrollable references that are registered with the service and their\n         * scroll event subscriptions.\n         */\n        this.scrollContainers = new Map();\n        this._document = document;\n    }\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._globalSubscription) {\n                this._addGlobalListener();\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._removeGlobalListener();\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._removeGlobalListener();\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => {\n            return !target || ancestors.indexOf(target) > -1;\n        }));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    /** Sets up the global scroll listeners. */\n    _addGlobalListener() {\n        this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n            const window = this._getWindow();\n            return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n        });\n    }\n    /** Cleans up the global scroll listener. */\n    _removeGlobalListener() {\n        if (this._globalSubscription) {\n            this._globalSubscription.unsubscribe();\n            this._globalSubscription = null;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollDispatcher, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        this.elementRef = elementRef;\n        this.scrollDispatcher = scrollDispatcher;\n        this.ngZone = ngZone;\n        this.dir = dir;\n        this._destroyed = new Subject();\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll')\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(observer)));\n    }\n    ngOnInit() {\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != 0 /* RtlScrollAxisType.NORMAL */) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkScrollable, isStandalone: true, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    constructor(_platform, ngZone, document) {\n        this._platform = _platform;\n        /** Stream of viewport change events. */\n        this._change = new Subject();\n        /** Event listener that will be used to handle the viewport change events. */\n        this._changeListener = (event) => {\n            this._change.next(event);\n        };\n        this._document = document;\n        ngZone.runOutsideAngular(() => {\n            if (_platform.isBrowser) {\n                const window = this._getWindow();\n                // Note that bind the events ourselves, rather than going through something like RxJS's\n                // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n                window.addEventListener('resize', this._changeListener);\n                window.addEventListener('orientationchange', this._changeListener);\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        if (this._platform.isBrowser) {\n            const window = this._getWindow();\n            window.removeEventListener('resize', this._changeListener);\n            window.removeEventListener('orientationchange', this._changeListener);\n        }\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a ClientRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ViewportRuler, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ViewportRuler, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n        const viewportEl = this.elementRef.nativeElement;\n        return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualScrollable, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollable, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    get appendOnly() {\n        return this._appendOnly;\n    }\n    set appendOnly(value) {\n        this._appendOnly = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler, scrollable) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n        this.elementRef = elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollStrategy = _scrollStrategy;\n        this.scrollable = scrollable;\n        this._platform = inject(Platform);\n        /** Emits when the viewport is detached from a CdkVirtualForOf. */\n        this._detachedSubject = new Subject();\n        /** Emits when the rendered range changes. */\n        this._renderedRangeSubject = new Subject();\n        this._orientation = 'vertical';\n        this._appendOnly = false;\n        // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n        // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n        // depending on how the strategy calculates the scrolled index, it may come at a cost to\n        // performance.\n        /** Emits when the index of the first element visible in the viewport changes. */\n        this.scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n        /** A stream that emits whenever the rendered range changes. */\n        this.renderedRangeStream = this._renderedRangeSubject;\n        /**\n         * The total size of all content (in pixels), including content that is not currently rendered.\n         */\n        this._totalContentSize = 0;\n        /** A string representing the `style.width` property value to be used for the spacer element. */\n        this._totalContentWidth = '';\n        /** A string representing the `style.height` property value to be used for the spacer element. */\n        this._totalContentHeight = '';\n        /** The currently rendered range of indices. */\n        this._renderedRange = { start: 0, end: 0 };\n        /** The length of the data bound to this viewport (in number of items). */\n        this._dataLength = 0;\n        /** The size of the viewport (in pixels). */\n        this._viewportSize = 0;\n        /** The last rendered content offset that was set. */\n        this._renderedContentOffset = 0;\n        /**\n         * Whether the last rendered content offset was to the end of the content (and therefore needs to\n         * be rewritten as an offset to the start of the content).\n         */\n        this._renderedContentOffsetNeedsRewrite = false;\n        /** Whether there is a pending change detection cycle. */\n        this._isChangeDetectionPending = false;\n        /** A list of functions to run after the next change detection cycle. */\n        this._runAfterChangeDetection = [];\n        /** Subscription to changes in the viewport size. */\n        this._viewportChanges = Subscription.EMPTY;\n        if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n        if (!this.scrollable) {\n            // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n            this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n            this.scrollable = this;\n        }\n    }\n    ngOnInit() {\n        // Scrolling depends on the element dimensions which we can't get during SSR.\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        if (this.scrollable === this) {\n            super.ngOnInit();\n        }\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.scrollable\n                .elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER), \n            // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n            // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n            // to unsubscribe here just in case.\n            takeUntil(this._destroyed))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // In appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        // This is to break the call cycle\n        let measureScrollOffset;\n        if (this.scrollable == this) {\n            measureScrollOffset = (_from) => super.measureScrollOffset(_from);\n        }\n        else {\n            measureScrollOffset = (_from) => this.scrollable.measureScrollOffset(_from);\n        }\n        return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) -\n            this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n        let fromRect;\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const isRtl = this.dir?.value == 'rtl';\n        if (from == 'start') {\n            fromRect = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            fromRect = isRtl ? LEFT : RIGHT;\n        }\n        else if (from) {\n            fromRect = from;\n        }\n        else {\n            fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n        }\n        const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n        const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n        return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        this._isChangeDetectionPending = false;\n        // Apply the content transform. The transform can't be set via an Angular binding because\n        // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n        // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n        // the `Number` function first to coerce it to a numeric value.\n        this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n        // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n        // from the root, since the repeated items are content projected in. Calling `detectChanges`\n        // instead does not properly check the projected content.\n        this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n        const runAfterChangeDetection = this._runAfterChangeDetection;\n        this._runAfterChangeDetection = [];\n        for (const fn of runAfterChangeDetection) {\n            fn();\n        }\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight =\n            this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n        this._totalContentWidth =\n            this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: VIRTUAL_SCROLL_STRATEGY, optional: true }, { token: i2.Directionality, optional: true }, { token: ScrollDispatcher }, { token: ViewportRuler }, { token: VIRTUAL_SCROLLABLE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualScrollViewport, isStandalone: true, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: \"appendOnly\" }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n            },\n        ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                            deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLL_STRATEGY]\n                }] }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: ScrollDispatcher }, { type: ViewportRuler }, { type: CdkVirtualScrollable, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLLABLE]\n                }] }]; }, propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    constructor(\n    /** The view container to add items to. */\n    _viewContainerRef, \n    /** The template to use when stamping out new items. */\n    _template, \n    /** The set of available differs. */\n    _differs, \n    /** The strategy used to render items in the virtual scroll viewport. */\n    _viewRepeater, \n    /** The virtual scrolling viewport that these items are being rendered in. */\n    _viewport, ngZone) {\n        this._viewContainerRef = _viewContainerRef;\n        this._template = _template;\n        this._differs = _differs;\n        this._viewRepeater = _viewRepeater;\n        this._viewport = _viewport;\n        /** Emits when the rendered view of the data changes. */\n        this.viewChange = new Subject();\n        /** Subject that emits when a new DataSource instance is given. */\n        this._dataSourceChanges = new Subject();\n        /** Emits whenever the data in the current DataSource changes. */\n        this.dataStream = this._dataSourceChanges.pipe(\n        // Start off with null `DataSource`.\n        startWith(null), \n        // Bundle up the previous and current data sources so we can work with both.\n        pairwise(), \n        // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n        // new one, passing back a stream of data changes which we run through `switchMap` to give\n        // us a data stream that emits the latest data from whatever the current `DataSource` is.\n        switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n        // Replay the last emitted data when someone subscribes.\n        shareReplay(1));\n        /** The differ used to calculate changes to the data. */\n        this._differ = null;\n        /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n        this._needsUpdate = false;\n        this._destroyed = new Subject();\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualForOf, deps: [{ token: i0.ViewContainerRef }, { token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: _VIEW_REPEATER_STRATEGY }, { token: CdkVirtualScrollViewport, skipSelf: true }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualForOf, isStandalone: true, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: i2$1._RecycleViewRepeaterStrategy, decorators: [{\n                    type: Inject,\n                    args: [_VIEW_REPEATER_STRATEGY]\n                }] }, { type: CdkVirtualScrollViewport, decorators: [{\n                    type: SkipSelf\n                }] }, { type: i0.NgZone }]; }, propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return (this.getElementRef().nativeElement.getBoundingClientRect()[from] -\n            this.measureScrollOffset(from));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollableElement, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualScrollableElement, isStandalone: true, selector: \"[cdkVirtualScrollingElement]\", host: { classAttribute: \"cdk-virtual-scrollable\" }, providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollableElement, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualScrollingElement]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }],\n                    standalone: true,\n                    host: {\n                        'class': 'cdk-virtual-scrollable',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor(scrollDispatcher, ngZone, dir) {\n        super(new ElementRef(document.documentElement), scrollDispatcher, ngZone, dir);\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(document, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollableWindow, deps: [{ token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualScrollableWindow, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[scrollWindow]\", providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollableWindow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }],\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\nclass CdkScrollableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollableModule, imports: [CdkScrollable], exports: [CdkScrollable] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollableModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    imports: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollingModule, imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport,\n            CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollViewport,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollingModule, imports: [BidiModule,\n            CdkScrollableModule, BidiModule, CdkScrollableModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkVirtualScrollViewport,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n"], "names": ["operate", "innerFrom", "createOperatorSubscriber", "audit", "durationSelector", "source", "subscriber", "hasValue", "lastValue", "durationSubscriber", "isComplete", "endDuration", "unsubscribe", "value", "next", "complete", "cleanupDuration", "subscribe", "closed", "asyncScheduler", "timer", "auditTime", "duration", "scheduler", "pairwise", "prev", "has<PERSON>rev", "p", "filter", "skip", "count", "_", "index", "AsyncAction", "animationFrameProvider", "AnimationFrameAction", "constructor", "work", "requestAsyncId", "id", "delay", "actions", "push", "_scheduled", "requestAnimationFrame", "flush", "undefined", "recycleAsyncId", "_a", "length", "cancelAnimationFrame", "AsyncScheduler", "AnimationFrameScheduler", "action", "_active", "flushId", "error", "shift", "execute", "state", "immediate<PERSON>rovider", "AsapAction", "setImmediate", "bind", "clearImmediate", "AsapScheduler", "animationFrameScheduler", "animationFrame", "Subscription", "schedule", "callback", "request", "cancel", "delegate", "handle", "timestamp", "args", "asapScheduler", "asap", "Immediate", "nextH<PERSON>le", "resolved", "active<PERSON><PERSON><PERSON>", "findAndClearHandle", "cb", "Promise", "resolve", "then", "TestTools", "pending", "Object", "keys", "DOCUMENT", "i0", "inject", "APP_ID", "Injectable", "Inject", "QueryList", "Directive", "Input", "InjectionToken", "Optional", "EventEmitter", "Output", "NgModule", "i1", "_getFocusedElementPierceShadowDom", "normalizePassiveListenerOptions", "_getEventTarget", "_getShadowRoot", "Subject", "BehaviorSubject", "of", "hasModifierKey", "A", "Z", "ZERO", "NINE", "PAGE_DOWN", "PAGE_UP", "END", "HOME", "LEFT_ARROW", "RIGHT_ARROW", "UP_ARROW", "DOWN_ARROW", "TAB", "ALT", "CONTROL", "MAC_META", "META", "SHIFT", "tap", "debounceTime", "map", "take", "distinctUntilChanged", "takeUntil", "coerceBooleanProperty", "coerceElement", "i1$1", "ObserversModule", "BreakpointObserver", "ID_DELIMITER", "addAriaReferencedId", "el", "attr", "ids", "getAriaReferenceIds", "some", "existingId", "trim", "setAttribute", "join", "removeAriaReferencedId", "filteredIds", "val", "removeAttribute", "getAttribute", "match", "MESSAGES_CONTAINER_ID", "CDK_DESCRIBEDBY_ID_PREFIX", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE", "nextId", "AriaDescriber", "_document", "_platform", "_messageRegistry", "Map", "_messagesContainer", "_id", "describe", "hostElement", "message", "role", "_canBeDescribed", "key", "<PERSON><PERSON><PERSON>", "setMessageId", "set", "messageElement", "referenceCount", "has", "_createMessageElement", "_isElementDescribedByMessage", "_addMessageReference", "removeDescription", "_isElementNode", "_removeMessageReference", "registeredMessage", "get", "_deleteMessageElement", "childNodes", "remove", "ngOnDestroy", "describedE<PERSON>s", "querySelectorAll", "i", "_removeCdkDescribedByReferenceIds", "clear", "createElement", "textContent", "_createMessagesContainer", "append<PERSON><PERSON><PERSON>", "delete", "containerClassName", "serverContainers", "messagesContainer", "style", "visibility", "classList", "add", "<PERSON><PERSON><PERSON><PERSON>", "body", "element", "originalReferenceIds", "indexOf", "referenceIds", "messageId", "trimmedMessage", "aria<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "ɵfac", "AriaDescriber_Factory", "t", "ɵɵinject", "Platform", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "decorators", "serviceId", "ListKeyManager", "_items", "_activeItemIndex", "_activeItem", "_wrap", "_letterKeyStream", "_typeaheadSubscription", "EMPTY", "_vertical", "_allowedModifierKeys", "_homeAndEnd", "_pageUpAndDown", "enabled", "delta", "_skipPredicateFn", "item", "disabled", "_pressedLetters", "tabOut", "change", "_itemChangesSubscription", "changes", "newItems", "itemArray", "toArray", "newIndex", "skipPredicate", "predicate", "withWrap", "shouldWrap", "withVerticalOrientation", "withHorizontalOrientation", "direction", "_horizontal", "withAllowedModifierKeys", "withTypeAhead", "debounceInterval", "get<PERSON><PERSON><PERSON>", "Error", "pipe", "letter", "inputString", "items", "_getItemsArray", "toUpperCase", "setActiveItem", "cancelTypeahead", "withHomeAndEnd", "withPageUpDown", "previousActiveItem", "updateActiveItem", "onKeydown", "event", "keyCode", "modifiers", "isModifierAllowed", "every", "modifier", "setNextItemActive", "setPreviousItemActive", "setFirstItemActive", "setLastItemActive", "targetIndex", "_setActiveItemByIndex", "itemsLength", "toLocaleUpperCase", "String", "fromCharCode", "preventDefault", "activeItemIndex", "activeItem", "isTyping", "_setActiveItemByDelta", "destroy", "_setActiveInWrapMode", "_setActiveInDefaultMode", "fallback<PERSON><PERSON><PERSON>", "ActiveDescendantKeyManager", "setInactiveStyles", "setActiveStyles", "FocusKeyManager", "arguments", "_origin", "setFocusOrigin", "origin", "focus", "IsFocusableConfig", "ignoreVisibility", "InteractivityChecker", "isDisabled", "hasAttribute", "isVisible", "hasGeometry", "getComputedStyle", "isTabbable", "frameElement", "getFrameElement", "getWindow", "getTabIndexValue", "nodeName", "toLowerCase", "tabIndexValue", "WEBKIT", "IOS", "isPotentiallyTabbableIOS", "FIREFOX", "tabIndex", "isFocusable", "config", "isPotentiallyFocusable", "InteractivityChecker_Factory", "window", "offsetWidth", "offsetHeight", "getClientRects", "isNativeFormElement", "isHiddenInput", "isInputElement", "isAnchorWithHref", "isAnchorElement", "hasValidTabIndex", "isNaN", "parseInt", "inputType", "node", "ownerDocument", "defaultView", "FocusTrap", "_enabled", "_startAnchor", "_endAnchor", "_toggleAnchorTabIndex", "_element", "_checker", "_ngZone", "deferAnchors", "_hasAttached", "startAnchorListener", "focusLastTabbableElement", "endAnchorListener", "focusFirstTabbableElement", "attachAnchors", "startAnchor", "endAnchor", "removeEventListener", "runOutsideAngular", "_createAnchor", "addEventListener", "parentNode", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "options", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus<PERSON><PERSON><PERSON><PERSON>", "has<PERSON>tta<PERSON>", "root", "children", "tabbable<PERSON><PERSON><PERSON>", "anchor", "isEnabled", "toggleAnchors", "fn", "isStable", "onStable", "FocusTrapFactory", "create", "deferCaptureElements", "FocusTrapFactory_Factory", "NgZone", "CdkTrapFocus", "focusTrap", "autoCapture", "_autoCapture", "_elementRef", "_focusTrapFactory", "_previouslyFocusedElement", "nativeElement", "ngAfterContentInit", "_captureFocus", "ngDoCheck", "ngOnChanges", "autoCaptureChange", "firstChange", "CdkTrapFocus_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "selector", "ConfigurableFocusTrap", "_focusTrapManager", "register", "deregister", "_inertStrategy", "defer", "_enable", "preventFocus", "_disable", "allowFocus", "FOCUS_TRAP_INERT_STRATEGY", "EventListenerFocusTrapInertStrategy", "_listener", "e", "_trapFocus", "target", "focusTrapRoot", "contains", "closest", "setTimeout", "activeElement", "FocusTrapManager", "_focusTrapStack", "ft", "stack", "splice", "FocusTrapManager_Factory", "ConfigurableFocusTrapFactory", "configObject", "ConfigurableFocusTrapFactory_Factory", "isFakeMousedownFromScreenReader", "buttons", "detail", "isFakeTouchstartFromScreenReader", "touch", "touches", "changedTouches", "identifier", "radiusX", "radiusY", "INPUT_MODALITY_DETECTOR_OPTIONS", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "TOUCH_BUFFER_MS", "modalityEventListenerOptions", "passive", "capture", "InputModalityDetector", "mostRecentModality", "_modality", "ngZone", "document", "_mostRecentTarget", "_lastTouchMs", "_onKeydown", "_options", "_onMousedown", "Date", "now", "_onTouchstart", "modalityDetected", "modalityChanged", "InputModalityDetector_Factory", "Document", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "uniqueIds", "LiveAnnouncer", "elementToken", "_defaultOptions", "_liveElement", "_createLiveElement", "announce", "defaultOptions", "politeness", "clearTimeout", "_previousTimeout", "_exposeAnnouncerToModals", "_currentPromise", "_currentResolve", "elementClass", "previousElements", "getElementsByClassName", "liveEl", "modals", "modal", "ariaOwns", "LiveAnnouncer_Factory", "CdkAriaLive", "_politeness", "_subscription", "_contentObserver", "observe", "elementText", "_previousAnnouncedText", "_liveAnnouncer", "CdkAriaLive_Factory", "ContentObserver", "FOCUS_MONITOR_DEFAULT_OPTIONS", "captureEventListenerOptions", "FocusMonitor", "_inputModalityDetector", "_windowFocused", "_originFromTouchInteraction", "_elementInfo", "_monitoredElementCount", "_rootNodeFocusListenerCount", "_windowFocusListener", "_windowFocusTimeoutId", "_stopInputModalityDetector", "_rootNodeFocusAndBlurListener", "parentElement", "_onFocus", "_onBlur", "_detectionMode", "detectionMode", "monitor", "check<PERSON><PERSON><PERSON><PERSON>", "rootNode", "_getDocument", "cachedInfo", "subject", "info", "_registerGlobalListeners", "stopMonitoring", "elementInfo", "_setClasses", "_removeGlobalListeners", "focusVia", "focusedElement", "_getClosestElementsInfo", "for<PERSON>ach", "currentElement", "_originChanged", "_set<PERSON><PERSON><PERSON>", "_info", "_getWindow", "doc", "_getFocus<PERSON><PERSON>in", "focusEventTarget", "_shouldBeAttributedToTouch", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_isLastInteractionFromInputLabel", "toggle", "isFromInteraction", "_originTimeoutId", "ms", "relatedTarget", "Node", "_emit<PERSON><PERSON>in", "observers", "run", "rootNodeFocusListeners", "modality", "results", "mostRecentTarget", "labels", "FocusMonitor_Factory", "CdkMonitorFocus", "_focusMonitor", "_focus<PERSON><PERSON>in", "cdkFocusChange", "<PERSON><PERSON><PERSON><PERSON>", "ngAfterViewInit", "_monitorSubscription", "emit", "CdkMonitorFocus_Factory", "outputs", "BLACK_ON_WHITE_CSS_CLASS", "WHITE_ON_BLACK_CSS_CLASS", "HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS", "HighContrastModeDetector", "_breakpointSubscription", "_hasCheckedHighContrastMode", "_applyBodyHighContrastModeCssClasses", "getHighContrastMode", "testElement", "backgroundColor", "position", "documentWindow", "computedStyle", "computedColor", "replace", "bodyClasses", "mode", "HighContrastModeDetector_Factory", "A11yModule", "highContrastModeDetector", "A11yModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports", "DIR_DOCUMENT", "DIR_DOCUMENT_FACTORY", "RTL_LOCALE_PATTERN", "_resolveDirectionality", "rawValue", "navigator", "language", "test", "Directionality", "bodyDir", "dir", "htmlDir", "documentElement", "Directionality_Factory", "<PERSON><PERSON>", "_dir", "_isInitialized", "previousValue", "_rawDir", "Dir_Factory", "hostVars", "hostBindings", "Dir_<PERSON><PERSON><PERSON><PERSON>", "rf", "ctx", "ɵɵattribute", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "host", "BidiModule", "BidiModule_Factory", "coerceNumberProperty", "fallback<PERSON><PERSON><PERSON>", "_isNumberValue", "Number", "parseFloat", "coerce<PERSON><PERSON><PERSON>", "Array", "isArray", "coerceCssPixelValue", "elementOrRef", "coerce<PERSON><PERSON><PERSON><PERSON><PERSON>", "separator", "result", "sourceValues", "split", "sourceValue", "trimmedString", "ConnectableObservable", "isObservable", "DataSource", "isDataSource", "connect", "ArrayDataSource", "_data", "disconnect", "_DisposeViewRepeaterStrategy", "applyChanges", "viewContainerRef", "itemContextFactory", "itemValueResolver", "itemViewChanged", "forEachOperation", "record", "adjustedPreviousIndex", "currentIndex", "view", "operation", "previousIndex", "insertContext", "createEmbeddedView", "templateRef", "context", "move", "detach", "_RecycleViewRepeaterStrategy", "viewCacheSize", "_viewCache", "viewArgsFactory", "_insertView", "_detachAndCacheView", "_moveView", "cachedView", "_insertViewFromCache", "$implicit", "viewArgs", "detached<PERSON>iew", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pop", "insert", "SelectionModel", "selected", "_selected", "from", "_selection", "values", "_multiple", "initiallySelectedValues", "_emitChanges", "compareWith", "Set", "_deselectedToEmit", "_selectedToEmit", "changed", "_markSelected", "select", "_verifyValueAssignment", "_hasQueuedChanges", "_emitChangeEvent", "deselect", "_unmarkSelected", "setSelection", "oldValues", "newSelectedSet", "isSelected", "flushEvent", "_unmarkAll", "_getConcreteValue", "isEmpty", "size", "sort", "isMultipleSelection", "added", "removed", "getMultipleValuesInSingleSelectionError", "inputValue", "selected<PERSON><PERSON><PERSON>", "UniqueSelectionDispatcher", "_listeners", "notify", "name", "listener", "listen", "registered", "UniqueSelectionDispatcher_Factory", "_VIEW_REPEATER_STRATEGY", "MAC_ENTER", "BACKSPACE", "NUM_CENTER", "ENTER", "PAUSE", "CAPS_LOCK", "ESCAPE", "SPACE", "PLUS_SIGN", "PRINT_SCREEN", "INSERT", "DELETE", "ONE", "TWO", "THREE", "FOUR", "FIVE", "SIX", "SEVEN", "EIGHT", "FF_SEMICOLON", "FF_EQUALS", "QUESTION_MARK", "AT_SIGN", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "MAC_WK_CMD_LEFT", "MAC_WK_CMD_RIGHT", "CONTEXT_MENU", "NUMPAD_ZERO", "NUMPAD_ONE", "NUMPAD_TWO", "NUMPAD_THREE", "NUMPAD_FOUR", "NUMPAD_FIVE", "NUMPAD_SIX", "NUMPAD_SEVEN", "NUMPAD_EIGHT", "NUMPAD_NINE", "NUMPAD_MULTIPLY", "NUMPAD_PLUS", "NUMPAD_MINUS", "NUMPAD_PERIOD", "NUMPAD_DIVIDE", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "NUM_LOCK", "SCROLL_LOCK", "FIRST_MEDIA", "FF_MINUS", "MUTE", "VOLUME_DOWN", "VOLUME_UP", "FF_MUTE", "FF_VOLUME_DOWN", "LAST_MEDIA", "FF_VOLUME_UP", "SEMICOLON", "EQUALS", "COMMA", "DASH", "PERIOD", "SLASH", "APOSTROPHE", "TILDE", "OPEN_SQUARE_BRACKET", "BACKSLASH", "CLOSE_SQUARE_BRACKET", "SINGLE_QUOTE", "altKey", "shift<PERSON>ey", "ctrl<PERSON>ey", "metaKey", "CSP_NONCE", "combineLatest", "concat", "Observable", "startWith", "LayoutModule", "LayoutModule_Factory", "mediaQueriesForWebkitCompatibility", "mediaQueryStyleNode", "MediaMatcher", "_nonce", "_matchMedia", "matchMedia", "noopMatchMedia", "query", "BLINK", "createEmptyStyleRule", "MediaMatcher_Factory", "nonce", "head", "sheet", "insertRule", "matches", "media", "addListener", "removeListener", "_mediaMatcher", "_zone", "_queries", "_destroySubject", "isMatched", "queries", "splitQueries", "mediaQuery", "_registerQuery", "mql", "observables", "observable", "stateObservable", "breakpointStates", "response", "breakpoints", "queryObservable", "observer", "handler", "output", "BreakpointObserver_Factory", "reduce", "a1", "a2", "Breakpoints", "XSmall", "Small", "Medium", "Large", "<PERSON>L<PERSON>ge", "Handset", "Tablet", "Web", "HandsetPortrait", "TabletPortrait", "WebPortrait", "HandsetLandscape", "TabletLandscape", "WebLandscape", "MutationObserverFactory", "MutationObserver", "MutationObserverFactory_Factory", "_mutationObserverFactory", "_observedElements", "_cleanupObserver", "stream", "_observeElement", "subscription", "_unobserveElement", "mutations", "characterData", "childList", "subtree", "ContentObserver_Factory", "CdkObserveContent", "_disabled", "_unsubscribe", "_subscribe", "debounce", "_debounce", "_currentSubscription", "CdkObserveContent_Factory", "ObserversModule_Factory", "PLATFORM_ID", "isPlatformBrowser", "hasV8BreakIterator", "Intl", "v8BreakIterator", "_platformId", "EDGE", "userAgent", "TRIDENT", "chrome", "CSS", "ANDROID", "SAFARI", "Platform_Factory", "PlatformModule", "PlatformModule_Factory", "supportedInputTypes", "candidateInputTypes", "getSupportedInputTypes", "featureTestInput", "supportsPassiveEvents", "supportsPassiveEventListeners", "defineProperty", "rtlScrollAxisType", "scrollBehaviorSupported", "supportsScrollBehavior", "Element", "scrollToFunction", "prototype", "scrollTo", "toString", "getRtlScrollAxisType", "scrollContainer", "containerStyle", "width", "overflow", "pointerEvents", "content", "contentStyle", "height", "scrollLeft", "shadowDomIsSupported", "_supportsShadowDom", "createShadowRoot", "attachShadow", "getRootNode", "ShadowRoot", "shadowRoot", "newActiveElement", "<PERSON><PERSON><PERSON>", "_isTestEnvironment", "__karma__", "jasmine", "jest", "<PERSON><PERSON>", "forwardRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "SkipSelf", "fromEvent", "switchMap", "shareReplay", "i2", "i2$1", "_c0", "_c1", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "itemSize", "minBufferPx", "maxBufferPx", "_scrolledIndexChange", "scrolledIndexChange", "_viewport", "_itemSize", "_minBufferPx", "_maxBufferPx", "attach", "viewport", "_updateTotalContentSize", "_updateRenderedRange", "updateItemAndBufferSize", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "CdkFixedSizeVirtualScroll_Factory", "standalone", "useFactory", "deps", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_scrolled", "_globalSubscription", "_scrolledCount", "scrollContainers", "scrollable", "elementScrolled", "scrollableReference", "scrolled", "auditTimeInMs", "_addGlobalListener", "_removeGlobalListener", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "scrollingContainers", "_scrollableContainsElement", "scrollableElement", "getElementRef", "ScrollDispatcher_Factory", "CdkScrollable", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "_destroyed", "_elementScrolled", "ngOnInit", "isRtl", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "_applyScrollToOptions", "scrollTop", "LEFT", "RIGHT", "CdkScrollable_Factory", "DEFAULT_RESIZE_TIME", "ViewportRuler", "_change", "_changeListener", "_viewportSize", "_updateViewportSize", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "documentRect", "getBoundingClientRect", "scrollY", "scrollX", "throttleTime", "innerWidth", "innerHeight", "ViewportRuler_Factory", "VIRTUAL_SCROLLABLE", "CdkVirtualScrollable", "measureViewportSize", "orientation", "viewportEl", "CdkVirtualScrollable_Factory", "ɵɵInheritDefinitionFeature", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "CdkVirtualScrollViewport", "_orientation", "_calculateSpacerSize", "appendOnly", "_appendOnly", "_changeDetectorRef", "viewportRuler", "_detachedSubject", "_renderedRangeSubject", "renderedRangeStream", "_totalContentSize", "_totalContentWidth", "_totalContentHeight", "_rendered<PERSON><PERSON>e", "_dataLength", "_renderedContentOffset", "_renderedContentOffsetNeedsRewrite", "_isChangeDetectionPending", "_runAfterChangeDetection", "_viewportChanges", "checkViewportSize", "_measureViewportSize", "_markChangeDetectionNeeded", "forOf", "_forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "_doChangeDetection", "measureBoundingClientRectWithScrollOffset", "range", "getOffsetToRenderedContentStart", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "_renderedContentTransform", "measureRenderedContentSize", "_from", "measureViewportOffset", "fromRect", "scrollerClientRect", "viewportClientRect", "contentEl", "_contentWrapper", "measureRangeSize", "runAfter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runAfterChangeDetection", "CdkVirtualScrollViewport_Factory", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "CdkVirtualScrollViewport_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "CdkVirtualScrollViewport_HostBindings", "ɵɵclassProp", "virtualScrollable", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "CdkVirtualScrollViewport_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵstyleProp", "styles", "encapsulation", "changeDetection", "None", "OnPush", "static", "getOffset", "rect", "CdkVirtualForOf", "cdkVirtualForOf", "_cdkVirtualForOf", "_dataSourceChanges", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "_needsUpdate", "cdkVirtualForTemplate", "_template", "cdkVirtualForTemplateCacheSize", "_view<PERSON><PERSON><PERSON>er", "_viewContainerRef", "_differs", "viewChange", "cur", "_changeDataSource", "_differ", "_onRenderedDataChange", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "rootNodes", "diff", "_renderedItems", "_updateContext", "_applyChanges", "slice", "find", "oldDs", "newDs", "_updateComputedContextProperties", "detectChanges", "_adjustedPreviousIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "last", "even", "odd", "CdkVirtualForOf_Factory", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "useClass", "CdkVirtualScrollableElement", "CdkVirtualScrollableElement_Factory", "CdkVirtualScrollableWindow", "CdkVirtualScrollableWindow_Factory", "CdkScrollableModule", "CdkScrollableModule_Factory", "ScrollingModule", "ScrollingModule_Factory"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]}