"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["default-src_app_pipes_pipes_module_ts-src_app_services_planning_service_ts-src_app_services_r-e0264d"],{

/***/ 38448:
/*!**************************************************!*\
  !*** ./src/app/pipes/highlight-presence.pipe.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HighlightPresencePipe: () => (/* binding */ HighlightPresencePipe)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/platform-browser */ 80436);


class HighlightPresencePipe {
  constructor(sanitizer) {
    this.sanitizer = sanitizer;
  }
  transform(value) {
    if (!value) {
      return this.sanitizer.bypassSecurityTrustHtml('');
    }
    // Recherche la chaîne "(presence obligatoire)" (insensible à la casse) et la remplace par une version en rouge
    const formattedText = value.replace(/\(presence obligatoire\)/gi, '<span class="text-red-600 font-semibold">(presence obligatoire)</span>');
    // Sanitize le HTML pour éviter les problèmes de sécurité
    return this.sanitizer.bypassSecurityTrustHtml(formattedText);
  }
  static {
    this.ɵfac = function HighlightPresencePipe_Factory(t) {
      return new (t || HighlightPresencePipe)(_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdirectiveInject"](_angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__.DomSanitizer, 16));
    };
  }
  static {
    this.ɵpipe = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefinePipe"]({
      name: "highlightPresence",
      type: HighlightPresencePipe,
      pure: true
    });
  }
}

/***/ }),

/***/ 41683:
/*!***************************************!*\
  !*** ./src/app/pipes/pipes.module.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PipesModule: () => (/* binding */ PipesModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _highlight_presence_pipe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./highlight-presence.pipe */ 38448);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);



class PipesModule {
  static {
    this.ɵfac = function PipesModule_Factory(t) {
      return new (t || PipesModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: PipesModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](PipesModule, {
    declarations: [_highlight_presence_pipe__WEBPACK_IMPORTED_MODULE_0__.HighlightPresencePipe],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule],
    exports: [_highlight_presence_pipe__WEBPACK_IMPORTED_MODULE_0__.HighlightPresencePipe]
  });
})();

/***/ }),

/***/ 26543:
/*!**********************************************!*\
  !*** ./src/app/services/planning.service.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PlanningService: () => (/* binding */ PlanningService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common/http */ 46443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 77919);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 98764);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs/operators */ 61318);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @auth0/angular-jwt */ 92389);







class PlanningService {
  constructor(http, jwtHelper) {
    this.http = http;
    this.jwtHelper = jwtHelper;
  }
  getUserHeaders() {
    const token = localStorage.getItem('token');
    if (!token || this.jwtHelper.isTokenExpired(token)) {
      throw new Error('Token invalide ou expiré');
    }
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json'
    });
  }
  getAdminHeaders() {
    const token = localStorage.getItem('token');
    if (!token || this.jwtHelper.isTokenExpired(token)) {
      throw new Error('Token invalide ou expiré');
    }
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpHeaders({
      Authorization: `Bearer ${token}`,
      role: 'admin',
      'Content-Type': 'application/json'
    });
  }
  getAllPlannings() {
    console.log('Service - Récupération de tous les plannings');
    // Utiliser l'endpoint getall qui existe dans l'API
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/getall`, {
      headers: this.getUserHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.tap)(response => {
      console.log('Service - Plannings récupérés:', response);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
      console.error('Service - Erreur lors de la récupération des plannings:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error);
    }));
  }
  getPlanningById(id) {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/getone/${id}`);
  }
  createPlanning(planning) {
    return this.http.post(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/add`, planning, {
      headers: this.getUserHeaders()
    });
  }
  updatePlanning(id, planning) {
    console.log('Service - Mise à jour du planning:', id);
    console.log('Service - Données envoyées:', planning);
    console.log('Service - URL:', `${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/update/${id}`);
    // Vérifier le token avant d'envoyer la requête
    try {
      const headers = this.getUserHeaders();
      console.log('Service - Headers:', headers);
      return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/update/${id}`, planning, {
        headers: headers
      }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.tap)(response => {
        console.log('Service - Réponse du serveur:', response);
      }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
        console.error('Service - Erreur lors de la mise à jour:', error);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error);
      }));
    } catch (error) {
      console.error('Service - Erreur lors de la préparation de la requête:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => new Error('Erreur d\'authentification: ' + (error instanceof Error ? error.message : String(error))));
    }
  }
  deletePlanning(id) {
    return this.http.delete(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/delete/${id}`, {
      headers: this.getUserHeaders()
    });
  }
  getPlanningsByUser(userId) {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/user/${userId}`, {
      headers: this.getUserHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.tap)(response => {
      console.log('Service - Plannings par utilisateur récupérés:', response);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
      console.error('Service - Erreur lors de la récupération des plannings par utilisateur:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error);
    }));
  }
  // Cette méthode est remplacée par getAllPlannings qui inclut maintenant les réunions
  getPlanningWithReunions(id) {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/with-reunions/${id}`, {
      headers: this.getUserHeaders()
    });
  }
  // Méthode pour récupérer tous les plannings (admin seulement)
  getAllPlanningsAdmin() {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}plannings/admin/all`, {
      headers: this.getAdminHeaders()
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.tap)(response => {
      console.log('Service - Tous les plannings (admin) récupérés:', response);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
      console.error('Service - Erreur lors de la récupération des plannings admin:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(() => error);
    }));
  }
  static {
    this.ɵfac = function PlanningService_Factory(t) {
      return new (t || PlanningService)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpClient), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵinject"](_auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_6__.JwtHelperService));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineInjectable"]({
      token: PlanningService,
      factory: PlanningService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 20078:
/*!*********************************************!*\
  !*** ./src/app/services/reunion.service.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReunionService: () => (/* binding */ ReunionService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common/http */ 46443);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @auth0/angular-jwt */ 92389);





class ReunionService {
  constructor(http, jwtHelper) {
    this.http = http;
    this.jwtHelper = jwtHelper;
  }
  getUserHeaders() {
    const token = localStorage.getItem('token');
    if (!token || this.jwtHelper.isTokenExpired(token)) {
      throw new Error('Token invalide ou expiré');
    }
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json'
    });
  }
  getAllReunions() {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/getall`);
  }
  getReunionById(id) {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/getone/${id}`);
  }
  createReunion(reunion) {
    return this.http.post(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/add`, reunion, {
      headers: this.getUserHeaders()
    });
  }
  updateReunion(id, reunion) {
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/update/${id}`, reunion, {
      headers: this.getUserHeaders()
    });
  }
  deleteReunion(id) {
    return this.http.delete(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/delete/${id}`, {
      headers: this.getUserHeaders()
    });
  }
  /**
   * Vérifie l'unicité d'un lien de visioconférence
   * @param lienVisio Le lien à vérifier
   * @param excludeReunionId ID de la réunion à exclure (pour la modification)
   */
  checkLienVisioUniqueness(lienVisio, excludeReunionId) {
    const body = {
      lienVisio,
      excludeReunionId
    };
    return this.http.post(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/check-lien-visio`, body, {
      headers: this.getUserHeaders()
    });
  }
  getReunionsByPlanning(planningId) {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/planning/${planningId}`);
  }
  getProchainesReunions(userId) {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/user/${userId}`);
  }
  // Méthode pour les admins - récupère toutes les réunions
  getAllReunionsAdmin() {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/admin/all`, {
      headers: this.getUserHeaders()
    });
  }
  // Méthode pour les admins - suppression forcée
  forceDeleteReunion(id) {
    return this.http.delete(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}reunions/admin/force-delete/${id}`, {
      headers: this.getUserHeaders()
    });
  }
  static {
    this.ɵfac = function ReunionService_Factory(t) {
      return new (t || ReunionService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpClient), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_3__.JwtHelperService));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
      token: ReunionService,
      factory: ReunionService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 68397:
/*!*******************************************!*\
  !*** ./src/app/services/toast.service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastService: () => (/* binding */ ToastService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 75797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);


class ToastService {
  constructor() {
    this.toastsSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject([]);
    this.toasts$ = this.toastsSubject.asObservable();
    this.currentId = 0;
  }
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }
  addToast(toast) {
    const newToast = {
      ...toast,
      id: this.generateId(),
      duration: toast.duration || 5000
    };
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, newToast]);
    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        this.removeToast(newToast.id);
      }, newToast.duration);
    }
  }
  show(message, type = 'info', duration = 5000) {
    const id = this.generateId();
    const toast = {
      id,
      type,
      title: '',
      message,
      duration
    };
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, toast]);
    if (duration > 0) {
      setTimeout(() => this.dismiss(id), duration);
    }
  }
  showSuccess(message, duration = 3000) {
    this.show(message, 'success', duration);
  }
  showError(message, duration = 5000) {
    this.show(message, 'error', duration);
  }
  showWarning(message, duration = 4000) {
    this.show(message, 'warning', duration);
  }
  showInfo(message, duration = 3000) {
    this.show(message, 'info', duration);
  }
  dismiss(id) {
    const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);
    this.toastsSubject.next(currentToasts);
  }
  success(title, message, duration) {
    this.addToast({
      type: 'success',
      title,
      message,
      duration,
      icon: 'check-circle'
    });
  }
  error(title, message, duration, action) {
    this.addToast({
      type: 'error',
      title,
      message,
      duration: duration || 8000,
      icon: 'x-circle',
      action
    });
  }
  warning(title, message, duration) {
    this.addToast({
      type: 'warning',
      title,
      message,
      duration,
      icon: 'exclamation-triangle'
    });
  }
  // Méthodes spécifiques pour les erreurs d'autorisation
  accessDenied(action = 'effectuer cette action', code) {
    const codeText = code ? ` (Code: ${code})` : '';
    this.error('Accès refusé', `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`, 8000, {
      label: 'Comprendre les rôles',
      handler: () => {
        // Optionnel: rediriger vers une page d'aide
        console.log("Redirection vers l'aide sur les rôles");
      }
    });
  }
  ownershipRequired(resource = 'cette ressource') {
    this.error('Propriétaire requis', `Seul le propriétaire ou un administrateur peut modifier ${resource}`, 8000);
  }
  removeToast(id) {
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next(currentToasts.filter(toast => toast.id !== id));
  }
  clear() {
    this.toastsSubject.next([]);
  }
  static {
    this.ɵfac = function ToastService_Factory(t) {
      return new (t || ToastService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: ToastService,
      factory: ToastService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=default-src_app_pipes_pipes_module_ts-src_app_services_planning_service_ts-src_app_services_r-e0264d.js.map