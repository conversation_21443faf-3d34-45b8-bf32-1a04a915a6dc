{"version": 3, "file": "default-src_app_pipes_pipes_module_ts-src_app_services_planning_service_ts-src_app_services_r-e0264d.js", "mappings": ";;;;;;;;;;;;;;;;;AAMM,MAAOA,qBAAqB;EAEhCC,YAAoBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;EAAiB;EAE9CC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI,CAACF,SAAS,CAACG,uBAAuB,CAAC,EAAE,CAAC;;IAGnD;IACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,OAAO,CACjC,4BAA4B,EAC5B,wEAAwE,CACzE;IAED;IACA,OAAO,IAAI,CAACL,SAAS,CAACG,uBAAuB,CAACC,aAAa,CAAC;EAC9D;;;uBAjBWN,qBAAqB,EAAAQ,+DAAA,CAAAE,mEAAA;IAAA;EAAA;;;;YAArBV,qBAAqB;MAAAY,IAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;ACLa;AACmB;;AAa5D,MAAOE,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBANpBD,yDAAY;IAAA;EAAA;;;sHAMHC,WAAW;IAAAC,YAAA,GATpBf,2EAAqB;IAAAgB,OAAA,GAGrBH,yDAAY;IAAAI,OAAA,GAGZjB,2EAAqB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;ACXoD;AAC/B;AACG;AACU;;;;AAOrD,MAAOuB,eAAe;EAE1BtB,YAAoBuB,IAAgB,EAAUC,SAA2B;IAArD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,SAAS,GAATA,SAAS;EACtD;EACQC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACF,SAAS,CAACK,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIb,6DAAW,CAAC;MACrBc,aAAa,EAAE,UAAUL,KAAK,IAAI,EAAE,EAAE;MACtC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEQM,eAAeA,CAAA;IACrB,MAAMN,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACF,SAAS,CAACK,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIb,6DAAW,CAAC;MACrBc,aAAa,EAAE,UAAUL,KAAK,EAAE;MAChCO,IAAI,EAAE,OAAO;MACb,cAAc,EAAE;KACjB,CAAC;EACJ;EAEDC,eAAeA,CAAA;IACbC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC3D;IACA,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAClB,GAAGhB,qEAAW,CAACiB,UAAU,kBAAkB,EAC3C;MAAEC,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAE,CACnC,CAACe,IAAI,CACJrB,mDAAG,CAACsB,QAAQ,IAAG;MACbN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEK,QAAQ,CAAC;IACzD,CAAC,CAAC,EACFrB,0DAAU,CAACsB,KAAK,IAAG;MACjBP,OAAO,CAACO,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/E,OAAOxB,gDAAU,CAAC,MAAMwB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAC,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAACrB,IAAI,CAACc,GAAG,CAAW,GAAGhB,qEAAW,CAACiB,UAAU,oBAAoBM,EAAE,EAAE,CAAC;EACnF;EAEAC,cAAcA,CAACC,QAA+B;IAC5C,OAAO,IAAI,CAACvB,IAAI,CAACwB,IAAI,CAAW,GAAG1B,qEAAW,CAACiB,UAAU,eAAe,EAACQ,QAAQ,EAAC;MAACP,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EACrH;EAEAuB,cAAcA,CAACJ,EAAU,EAAEE,QAAa;IACtCX,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEQ,EAAE,CAAC;IACrDT,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEU,QAAQ,CAAC;IACpDX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,GAAGf,qEAAW,CAACiB,UAAU,oBAAoBM,EAAE,EAAE,CAAC;IAEhF;IACA,IAAI;MACF,MAAML,OAAO,GAAG,IAAI,CAACd,cAAc,EAAE;MACrCU,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEG,OAAO,CAAC;MAE1C,OAAO,IAAI,CAAChB,IAAI,CAAC0B,GAAG,CAClB,GAAG5B,qEAAW,CAACiB,UAAU,oBAAoBM,EAAE,EAAE,EACjDE,QAAQ,EACR;QAACP,OAAO,EAAEA;MAAO,CAAC,CACnB,CAACC,IAAI,CACJrB,mDAAG,CAACsB,QAAQ,IAAG;QACbN,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEK,QAAQ,CAAC;MACxD,CAAC,CAAC,EACFrB,0DAAU,CAACsB,KAAK,IAAG;QACjBP,OAAO,CAACO,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,OAAOxB,gDAAU,CAAC,MAAMwB,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;KACF,CAAC,OAAOA,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,wDAAwD,EAAEA,KAAK,CAAC;MAC9E,OAAOxB,gDAAU,CAAC,MAAM,IAAIY,KAAK,CAAC,8BAA8B,IAAIY,KAAK,YAAYZ,KAAK,GAAGY,KAAK,CAACQ,OAAO,GAAGC,MAAM,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEjI;EAEAU,cAAcA,CAACR,EAAU;IACvB,OAAO,IAAI,CAACrB,IAAI,CAAC8B,MAAM,CAAO,GAAGhC,qEAAW,CAACiB,UAAU,oBAAoBM,EAAE,EAAE,EAAC;MAACL,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EACnH;EAGF6B,kBAAkBA,CAACC,MAAc;IAC/B,OAAO,IAAI,CAAChC,IAAI,CAACc,GAAG,CAClB,GAAGhB,qEAAW,CAACiB,UAAU,kBAAkBiB,MAAM,EAAE,EACnD;MACEhB,OAAO,EAAE,IAAI,CAACd,cAAc;KAC7B,CACF,CAACe,IAAI,CACJrB,mDAAG,CAACsB,QAAQ,IAAG;MACbN,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEK,QAAQ,CAAC;IACzE,CAAC,CAAC,EACFrB,0DAAU,CAACsB,KAAK,IAAG;MACjBP,OAAO,CAACO,KAAK,CAAC,yEAAyE,EAAEA,KAAK,CAAC;MAC/F,OAAOxB,gDAAU,CAAC,MAAMwB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EACA;EAEAc,uBAAuBA,CAACZ,EAAU;IAChC,OAAO,IAAI,CAACrB,IAAI,CAACc,GAAG,CAClB,GAAGhB,qEAAW,CAACiB,UAAU,2BAA2BM,EAAE,EAAE,EACxD;MAAEL,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAE,CACnC;EACH;EAEA;EACAgC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAClC,IAAI,CAACc,GAAG,CAClB,GAAGhB,qEAAW,CAACiB,UAAU,qBAAqB,EAC9C;MAAEC,OAAO,EAAE,IAAI,CAACP,eAAe;IAAE,CAAE,CACpC,CAACQ,IAAI,CACJrB,mDAAG,CAACsB,QAAQ,IAAG;MACbN,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEK,QAAQ,CAAC;IAC1E,CAAC,CAAC,EACFrB,0DAAU,CAACsB,KAAK,IAAG;MACjBP,OAAO,CAACO,KAAK,CAAC,+DAA+D,EAAEA,KAAK,CAAC;MACrF,OAAOxB,gDAAU,CAAC,MAAMwB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;;;uBA7HapB,eAAe,EAAAf,sDAAA,CAAAE,4DAAA,GAAAF,sDAAA,CAAAqD,gEAAA;IAAA;EAAA;;;aAAftC,eAAe;MAAAwC,OAAA,EAAfxC,eAAe,CAAAyC,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA;;;;;;;;;;;;;;;;;;;ACRyC;AAEF;;;;AAOrD,MAAOC,cAAc;EACzBjE,YAAoBuB,IAAgB,EAAUC,SAA2B;IAArD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,SAAS,GAATA,SAAS;EACtD;EACQC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACF,SAAS,CAACK,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIb,6DAAW,CAAC;MACrBc,aAAa,EAAE,UAAUL,KAAK,IAAI,EAAE,EAAE;MACtC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEFwC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC3C,IAAI,CAACc,GAAG,CAAY,GAAGhB,qEAAW,CAACiB,UAAU,iBAAiB,CAAC;EAC7E;EACA6B,cAAcA,CAACvB,EAAU;IACvB,OAAO,IAAI,CAACrB,IAAI,CAACc,GAAG,CAAU,GAAGhB,qEAAW,CAACiB,UAAU,mBAAmBM,EAAE,EAAE,CAAC;EACjF;EAEAwB,aAAaA,CAACC,OAA6B;IACzC,OAAO,IAAI,CAAC9C,IAAI,CAACwB,IAAI,CAAU,GAAG1B,qEAAW,CAACiB,UAAU,cAAc,EAAC+B,OAAO,EAAC;MAAC9B,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EAClH;EAEA6C,aAAaA,CAAC1B,EAAU,EAAEyB,OAAgB;IACxC,OAAO,IAAI,CAAC9C,IAAI,CAAC0B,GAAG,CAAU,GAAG5B,qEAAW,CAACiB,UAAU,mBAAmBM,EAAE,EAAE,EAACyB,OAAO,EAAC;MAAC9B,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EAC1H;EACA8C,aAAaA,CAAC3B,EAAU;IACtB,OAAO,IAAI,CAACrB,IAAI,CAAC8B,MAAM,CAAO,GAAGhC,qEAAW,CAACiB,UAAU,mBAAmBM,EAAE,EAAE,EAAC;MAACL,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EAClH;EAEA;;;;;EAKA+C,wBAAwBA,CAACC,SAAiB,EAAEC,gBAAyB;IACnE,MAAMC,IAAI,GAAG;MAAEF,SAAS;MAAEC;IAAgB,CAAE;IAC5C,OAAO,IAAI,CAACnD,IAAI,CAACwB,IAAI,CAAC,GAAG1B,qEAAW,CAACiB,UAAU,2BAA2B,EAAEqC,IAAI,EAAE;MAACpC,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EACrH;EAEAmD,qBAAqBA,CAACC,UAAkB;IACtC,OAAO,IAAI,CAACtD,IAAI,CAACc,GAAG,CAAY,GAAGhB,qEAAW,CAACiB,UAAU,qBAAqBuC,UAAU,EAAE,CAAC;EAC7F;EAEAC,qBAAqBA,CAACvB,MAAc;IAClC,OAAO,IAAI,CAAChC,IAAI,CAACc,GAAG,CAAY,GAAGhB,qEAAW,CAACiB,UAAU,iBAAiBiB,MAAM,EAAE,CAAC;EACrF;EAEA;EACAwB,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACxD,IAAI,CAACc,GAAG,CAAM,GAAGhB,qEAAW,CAACiB,UAAU,oBAAoB,EAAE;MAACC,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EAC5G;EAEA;EACAuD,kBAAkBA,CAACpC,EAAU;IAC3B,OAAO,IAAI,CAACrB,IAAI,CAAC8B,MAAM,CAAM,GAAGhC,qEAAW,CAACiB,UAAU,+BAA+BM,EAAE,EAAE,EAAE;MAACL,OAAO,EAAE,IAAI,CAACd,cAAc;IAAE,CAAC,CAAC;EAC9H;;;uBA1DYwC,cAAc,EAAA1D,sDAAA,CAAAE,4DAAA,GAAAF,sDAAA,CAAAqD,gEAAA;IAAA;EAAA;;;aAAdK,cAAc;MAAAH,OAAA,EAAdG,cAAc,CAAAF,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA;;;;;;;;;;;;;;;;;ACPmB;;AAMjC,MAAOkB,YAAY;EAKvBlF,YAAA;IAJQ,KAAAmF,aAAa,GAAG,IAAIF,iDAAe,CAAU,EAAE,CAAC;IACxD,KAAAG,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IACnC,KAAAC,SAAS,GAAG,CAAC;EAEN;EACPC,UAAUA,CAAA;IAChB,OAAOC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD;EAEQC,QAAQA,CAACC,KAAwB;IACvC,MAAMC,QAAQ,GAAU;MACtB,GAAGD,KAAK;MACRjD,EAAE,EAAE,IAAI,CAAC2C,UAAU,EAAE;MACrBQ,QAAQ,EAAEF,KAAK,CAACE,QAAQ,IAAI;KAC7B;IAED,MAAMC,aAAa,GAAG,IAAI,CAACb,aAAa,CAAChF,KAAK;IAC9C,IAAI,CAACgF,aAAa,CAACc,IAAI,CAAC,CAAC,GAAGD,aAAa,EAAEF,QAAQ,CAAC,CAAC;IAErD;IACA,IAAIA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAACC,QAAQ,GAAG,CAAC,EAAE;MAC9CG,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,WAAW,CAACL,QAAQ,CAAClD,EAAE,CAAC;MAC/B,CAAC,EAAEkD,QAAQ,CAACC,QAAQ,CAAC;;EAEzB;EACAK,IAAIA,CACFlD,OAAe,EACfmD,IAAA,GAAiD,MAAM,EACvDN,QAAQ,GAAG,IAAI;IAEf,MAAMnD,EAAE,GAAG,IAAI,CAAC2C,UAAU,EAAE;IAC5B,MAAMM,KAAK,GAAU;MAAEjD,EAAE;MAAEyD,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEpD,OAAO;MAAE6C;IAAQ,CAAE;IAC/D,MAAMC,aAAa,GAAG,IAAI,CAACb,aAAa,CAAChF,KAAK;IAC9C,IAAI,CAACgF,aAAa,CAACc,IAAI,CAAC,CAAC,GAAGD,aAAa,EAAEH,KAAK,CAAC,CAAC;IAElD,IAAIE,QAAQ,GAAG,CAAC,EAAE;MAChBG,UAAU,CAAC,MAAM,IAAI,CAACK,OAAO,CAAC3D,EAAE,CAAC,EAAEmD,QAAQ,CAAC;;EAEhD;EAEAS,WAAWA,CAACtD,OAAe,EAAE6C,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACK,IAAI,CAAClD,OAAO,EAAE,SAAS,EAAE6C,QAAQ,CAAC;EACzC;EAEAU,SAASA,CAACvD,OAAe,EAAE6C,QAAQ,GAAG,IAAI;IACxC,IAAI,CAACK,IAAI,CAAClD,OAAO,EAAE,OAAO,EAAE6C,QAAQ,CAAC;EACvC;EAEAW,WAAWA,CAACxD,OAAe,EAAE6C,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACK,IAAI,CAAClD,OAAO,EAAE,SAAS,EAAE6C,QAAQ,CAAC;EACzC;EAEAY,QAAQA,CAACzD,OAAe,EAAE6C,QAAQ,GAAG,IAAI;IACvC,IAAI,CAACK,IAAI,CAAClD,OAAO,EAAE,MAAM,EAAE6C,QAAQ,CAAC;EACtC;EAEAQ,OAAOA,CAAC3D,EAAU;IAChB,MAAMoD,aAAa,GAAG,IAAI,CAACb,aAAa,CAAChF,KAAK,CAACyG,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjE,EAAE,KAAKA,EAAE,CAAC;IACzE,IAAI,CAACuC,aAAa,CAACc,IAAI,CAACD,aAAa,CAAC;EACxC;EACAc,OAAOA,CAACR,KAAa,EAAEpD,OAAe,EAAE6C,QAAiB;IACvD,IAAI,CAACH,QAAQ,CAAC;MACZS,IAAI,EAAE,SAAS;MACfC,KAAK;MACLpD,OAAO;MACP6C,QAAQ;MACRgB,IAAI,EAAE;KACP,CAAC;EACJ;EACArE,KAAKA,CACH4D,KAAa,EACbpD,OAAe,EACf6C,QAAiB,EACjBiB,MAAwB;IAExB,IAAI,CAACpB,QAAQ,CAAC;MACZS,IAAI,EAAE,OAAO;MACbC,KAAK;MACLpD,OAAO;MACP6C,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BgB,IAAI,EAAE,UAAU;MAChBC;KACD,CAAC;EACJ;EAEAC,OAAOA,CAACX,KAAa,EAAEpD,OAAe,EAAE6C,QAAiB;IACvD,IAAI,CAACH,QAAQ,CAAC;MACZS,IAAI,EAAE,SAAS;MACfC,KAAK;MACLpD,OAAO;MACP6C,QAAQ;MACRgB,IAAI,EAAE;KACP,CAAC;EACJ;EACA;EACAG,YAAYA,CAACF,MAAA,GAAiB,wBAAwB,EAAEG,IAAa;IACnE,MAAMC,QAAQ,GAAGD,IAAI,GAAG,WAAWA,IAAI,GAAG,GAAG,EAAE;IAC/C,IAAI,CAACzE,KAAK,CACR,cAAc,EACd,oDAAoDsE,MAAM,GAAGI,QAAQ,EAAE,EACvE,IAAI,EACJ;MACEC,KAAK,EAAE,sBAAsB;MAC7BC,OAAO,EAAEA,CAAA,KAAK;QACZ;QACAnF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;KACD,CACF;EACH;EAEAmF,iBAAiBA,CAACC,QAAA,GAAmB,iBAAiB;IACpD,IAAI,CAAC9E,KAAK,CACR,qBAAqB,EACrB,2DAA2D8E,QAAQ,EAAE,EACrE,IAAI,CACL;EACH;EAEArB,WAAWA,CAACvD,EAAU;IACpB,MAAMoD,aAAa,GAAG,IAAI,CAACb,aAAa,CAAChF,KAAK;IAC9C,IAAI,CAACgF,aAAa,CAACc,IAAI,CAACD,aAAa,CAACY,MAAM,CAAEf,KAAK,IAAKA,KAAK,CAACjD,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC3E;EACA6E,KAAKA,CAAA;IACH,IAAI,CAACtC,aAAa,CAACc,IAAI,CAAC,EAAE,CAAC;EAC7B;;;uBA/HWf,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAApB,OAAA,EAAZoB,YAAY,CAAAnB,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "sources": ["./src/app/pipes/highlight-presence.pipe.ts", "./src/app/pipes/pipes.module.ts", "./src/app/services/planning.service.ts", "./src/app/services/reunion.service.ts", "./src/app/services/toast.service.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\n\n@Pipe({\n  name: 'highlightPresence'\n})\nexport class HighlightPresencePipe implements PipeTransform {\n\n  constructor(private sanitizer: DomSanitizer) {}\n\n  transform(value: string): SafeHtml {\n    if (!value) {\n      return this.sanitizer.bypassSecurityTrustHtml('');\n    }\n\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = value.replace(\n      /\\(presence obligatoire\\)/gi,\n      '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>'\n    );\n\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HighlightPresencePipe } from './highlight-presence.pipe';\n\n@NgModule({\n  declarations: [\n    HighlightPresencePipe\n  ],\n  imports: [\n    CommonModule\n  ],\n  exports: [\n    HighlightPresencePipe\n  ]\n})\nexport class PipesModule { }\n", "import { Injectable } from '@angular/core';\nimport { HttpHeaders, HttpClient, HttpResponse } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { Planning, CreatePlanningRequest } from '../models/planning.model';\nimport { JwtHelperService } from '@auth0/angular-jwt';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PlanningService {\n\n  constructor(private http: HttpClient, private jwtHelper: JwtHelperService)\n  {}\n   private getUserHeaders(): HttpHeaders {\n     const token = localStorage.getItem('token');\n     if (!token || this.jwtHelper.isTokenExpired(token)) {\n       throw new Error('Token invalide ou expiré');\n     }\n     return new HttpHeaders({\n       Authorization: `Bearer ${token || ''}`,\n       'Content-Type': 'application/json',\n     });\n   }\n\n   private getAdminHeaders(): HttpHeaders {\n     const token = localStorage.getItem('token');\n     if (!token || this.jwtHelper.isTokenExpired(token)) {\n       throw new Error('Token invalide ou expiré');\n     }\n     return new HttpHeaders({\n       Authorization: `Bearer ${token}`,\n       role: 'admin',\n       'Content-Type': 'application/json',\n     });\n   }\n\n  getAllPlannings(): Observable<any> {\n    console.log('Service - Récupération de tous les plannings');\n    // Utiliser l'endpoint getall qui existe dans l'API\n    return this.http.get<any>(\n      `${environment.urlBackend}plannings/getall`,\n      { headers: this.getUserHeaders() }\n    ).pipe(\n      tap(response => {\n        console.log('Service - Plannings récupérés:', response);\n      }),\n      catchError(error => {\n        console.error('Service - Erreur lors de la récupération des plannings:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  getPlanningById(id: string): Observable<Planning> {\n    return this.http.get<Planning>(`${environment.urlBackend}plannings/getone/${id}`);\n  }\n\n  createPlanning(planning: CreatePlanningRequest): Observable<Planning> {\n    return this.http.post<Planning>(`${environment.urlBackend}plannings/add`,planning,{headers: this.getUserHeaders()});\n  }\n\n  updatePlanning(id: string, planning: any): Observable<any> {\n    console.log('Service - Mise à jour du planning:', id);\n    console.log('Service - Données envoyées:', planning);\n    console.log('Service - URL:', `${environment.urlBackend}plannings/update/${id}`);\n\n    // Vérifier le token avant d'envoyer la requête\n    try {\n      const headers = this.getUserHeaders();\n      console.log('Service - Headers:', headers);\n\n      return this.http.put<any>(\n        `${environment.urlBackend}plannings/update/${id}`,\n        planning,\n        {headers: headers}\n      ).pipe(\n        tap(response => {\n          console.log('Service - Réponse du serveur:', response);\n        }),\n        catchError(error => {\n          console.error('Service - Erreur lors de la mise à jour:', error);\n          return throwError(() => error);\n        })\n      );\n    } catch (error) {\n      console.error('Service - Erreur lors de la préparation de la requête:', error);\n      return throwError(() => new Error('Erreur d\\'authentification: ' + (error instanceof Error ? error.message : String(error))));\n    }\n  }\n\n  deletePlanning(id: string): Observable<void> {\n    return this.http.delete<void>(`${environment.urlBackend}plannings/delete/${id}`,{headers: this.getUserHeaders()});\n  }\n\n\ngetPlanningsByUser(userId: string): Observable<any> {\n  return this.http.get<any>(\n    `${environment.urlBackend}plannings/user/${userId}`,\n    {\n      headers: this.getUserHeaders()\n    }\n  ).pipe(\n    tap(response => {\n      console.log('Service - Plannings par utilisateur récupérés:', response);\n    }),\n    catchError(error => {\n      console.error('Service - Erreur lors de la récupération des plannings par utilisateur:', error);\n      return throwError(() => error);\n    })\n  );\n}\n// Cette méthode est remplacée par getAllPlannings qui inclut maintenant les réunions\n\ngetPlanningWithReunions(id: string): Observable<Planning> {\n  return this.http.get<Planning>(\n    `${environment.urlBackend}plannings/with-reunions/${id}`,\n    { headers: this.getUserHeaders() }\n  );\n}\n\n// Méthode pour récupérer tous les plannings (admin seulement)\ngetAllPlanningsAdmin(): Observable<any> {\n  return this.http.get<any>(\n    `${environment.urlBackend}plannings/admin/all`,\n    { headers: this.getAdminHeaders() }\n  ).pipe(\n    tap(response => {\n      console.log('Service - Tous les plannings (admin) récupérés:', response);\n    }),\n    catchError(error => {\n      console.error('Service - Erreur lors de la récupération des plannings admin:', error);\n      return throwError(() => error);\n    })\n  );\n}\n}", "import { Injectable } from '@angular/core';\nimport {HttpHeaders,HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { Reunion, CreateReunionRequest } from '../models/reunion.model';\nimport { JwtHelperService } from '@auth0/angular-jwt';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ReunionService {\n  constructor(private http: HttpClient, private jwtHelper: JwtHelperService)\n  {}\n   private getUserHeaders(): HttpHeaders {\n     const token = localStorage.getItem('token');\n     if (!token || this.jwtHelper.isTokenExpired(token)) {\n       throw new Error('Token invalide ou expiré');\n     }\n     return new HttpHeaders({\n       Authorization: `Bearer ${token || ''}`,\n       'Content-Type': 'application/json',\n     });\n   }\n\n getAllReunions(): Observable<Reunion[]> {\n   return this.http.get<Reunion[]>(`${environment.urlBackend}reunions/getall`);\n }\n getReunionById(id: string): Observable<Reunion> {\n   return this.http.get<Reunion>(`${environment.urlBackend}reunions/getone/${id}`);\n }\n\n createReunion(reunion: CreateReunionRequest): Observable<Reunion> {\n   return this.http.post<Reunion>(`${environment.urlBackend}reunions/add`,reunion,{headers: this.getUserHeaders()});\n }\n\n updateReunion(id: string, reunion: Reunion): Observable<Reunion> {\n   return this.http.put<Reunion>(`${environment.urlBackend}reunions/update/${id}`,reunion,{headers: this.getUserHeaders()});\n }\n deleteReunion(id: string): Observable<void> {\n   return this.http.delete<void>(`${environment.urlBackend}reunions/delete/${id}`,{headers: this.getUserHeaders()});\n }\n\n /**\n  * Vérifie l'unicité d'un lien de visioconférence\n  * @param lienVisio Le lien à vérifier\n  * @param excludeReunionId ID de la réunion à exclure (pour la modification)\n  */\n checkLienVisioUniqueness(lienVisio: string, excludeReunionId?: string): Observable<any> {\n   const body = { lienVisio, excludeReunionId };\n   return this.http.post(`${environment.urlBackend}reunions/check-lien-visio`, body, {headers: this.getUserHeaders()});\n }\n\n getReunionsByPlanning(planningId: string): Observable<Reunion[]> {\n   return this.http.get<Reunion[]>(`${environment.urlBackend}reunions/planning/${planningId}`);\n }\n\n getProchainesReunions(userId: string): Observable<Reunion[]> {\n   return this.http.get<Reunion[]>(`${environment.urlBackend}reunions/user/${userId}`);\n }\n\n // Méthode pour les admins - récupère toutes les réunions\n getAllReunionsAdmin(): Observable<any> {\n   return this.http.get<any>(`${environment.urlBackend}reunions/admin/all`, {headers: this.getUserHeaders()});\n }\n\n // Méthode pour les admins - suppression forcée\n forceDeleteReunion(id: string): Observable<any> {\n   return this.http.delete<any>(`${environment.urlBackend}reunions/admin/force-delete/${id}`, {headers: this.getUserHeaders()});\n }\n\n}", "import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { Toast } from 'src/app/models/message.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ToastService {\n  private toastsSubject = new BehaviorSubject<Toast[]>([]);\n  toasts$ = this.toastsSubject.asObservable();\n  private currentId = 0;\n\n  constructor() {}\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private addToast(toast: Omit<Toast, 'id'>): void {\n    const newToast: Toast = {\n      ...toast,\n      id: this.generateId(),\n      duration: toast.duration || 5000,\n    };\n\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, newToast]);\n\n    // Auto-remove toast after duration\n    if (newToast.duration && newToast.duration > 0) {\n      setTimeout(() => {\n        this.removeToast(newToast.id);\n      }, newToast.duration);\n    }\n  }\n  show(\n    message: string,\n    type: 'success' | 'error' | 'warning' | 'info' = 'info',\n    duration = 5000\n  ) {\n    const id = this.generateId();\n    const toast: Toast = { id, type, title: '', message, duration };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, toast]);\n\n    if (duration > 0) {\n      setTimeout(() => this.dismiss(id), duration);\n    }\n  }\n\n  showSuccess(message: string, duration = 3000) {\n    this.show(message, 'success', duration);\n  }\n\n  showError(message: string, duration = 5000) {\n    this.show(message, 'error', duration);\n  }\n\n  showWarning(message: string, duration = 4000) {\n    this.show(message, 'warning', duration);\n  }\n\n  showInfo(message: string, duration = 3000) {\n    this.show(message, 'info', duration);\n  }\n\n  dismiss(id: string) {\n    const currentToasts = this.toastsSubject.value.filter((t) => t.id !== id);\n    this.toastsSubject.next(currentToasts);\n  }\n  success(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'success',\n      title,\n      message,\n      duration,\n      icon: 'check-circle',\n    });\n  }\n  error(\n    title: string,\n    message: string,\n    duration?: number,\n    action?: Toast['action']\n  ): void {\n    this.addToast({\n      type: 'error',\n      title,\n      message,\n      duration: duration || 8000, // Longer duration for errors\n      icon: 'x-circle',\n      action,\n    });\n  }\n\n  warning(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'warning',\n      title,\n      message,\n      duration,\n      icon: 'exclamation-triangle',\n    });\n  }\n  // Méthodes spécifiques pour les erreurs d'autorisation\n  accessDenied(action: string = 'effectuer cette action', code?: number): void {\n    const codeText = code ? ` (Code: ${code})` : '';\n    this.error(\n      'Accès refusé',\n      `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`,\n      8000,\n      {\n        label: 'Comprendre les rôles',\n        handler: () => {\n          // Optionnel: rediriger vers une page d'aide\n          console.log(\"Redirection vers l'aide sur les rôles\");\n        },\n      }\n    );\n  }\n\n  ownershipRequired(resource: string = 'cette ressource'): void {\n    this.error(\n      'Propriétaire requis',\n      `Seul le propriétaire ou un administrateur peut modifier ${resource}`,\n      8000\n    );\n  }\n\n  removeToast(id: string): void {\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next(currentToasts.filter((toast) => toast.id !== id));\n  }\n  clear() {\n    this.toastsSubject.next([]);\n  }\n}\n"], "names": ["HighlightPresencePipe", "constructor", "sanitizer", "transform", "value", "bypassSecurityTrustHtml", "formattedText", "replace", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "pure", "CommonModule", "PipesModule", "declarations", "imports", "exports", "HttpHeaders", "throwError", "tap", "catchError", "environment", "PlanningService", "http", "jwtHelper", "getUserHeaders", "token", "localStorage", "getItem", "isTokenExpired", "Error", "Authorization", "getAdminHeaders", "role", "getAllPlannings", "console", "log", "get", "urlBackend", "headers", "pipe", "response", "error", "getPlanningById", "id", "createPlanning", "planning", "post", "updatePlanning", "put", "message", "String", "deletePlanning", "delete", "getPlanningsByUser", "userId", "getPlanningWithReunions", "getAllPlanningsAdmin", "ɵɵinject", "HttpClient", "i2", "JwtHelperService", "factory", "ɵfac", "providedIn", "ReunionService", "getAllReunions", "getReunionById", "createReunion", "reunion", "updateReunion", "deleteReunion", "checkLienVisioUniqueness", "lienVisio", "excludeReunionId", "body", "getReunionsByPlanning", "planningId", "getProchainesReunions", "getAllReunionsAdmin", "forceDeleteReunion", "BehaviorSubject", "ToastService", "toastsSubject", "toasts$", "asObservable", "currentId", "generateId", "Math", "random", "toString", "substr", "addToast", "toast", "newToast", "duration", "currentToasts", "next", "setTimeout", "removeToast", "show", "type", "title", "dismiss", "showSuccess", "showError", "showWarning", "showInfo", "filter", "t", "success", "icon", "action", "warning", "accessDenied", "code", "codeText", "label", "handler", "ownershipRequired", "resource", "clear"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}