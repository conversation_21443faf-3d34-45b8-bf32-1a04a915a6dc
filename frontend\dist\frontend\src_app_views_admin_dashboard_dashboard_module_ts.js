"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_admin_dashboard_dashboard_module_ts"],{

/***/ 68397:
/*!*******************************************!*\
  !*** ./src/app/services/toast.service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastService: () => (/* binding */ ToastService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 75797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);


class ToastService {
  constructor() {
    this.toastsSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject([]);
    this.toasts$ = this.toastsSubject.asObservable();
    this.currentId = 0;
  }
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }
  addToast(toast) {
    const newToast = {
      ...toast,
      id: this.generateId(),
      duration: toast.duration || 5000
    };
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, newToast]);
    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        this.removeToast(newToast.id);
      }, newToast.duration);
    }
  }
  show(message, type = 'info', duration = 5000) {
    const id = this.generateId();
    const toast = {
      id,
      type,
      title: '',
      message,
      duration
    };
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, toast]);
    if (duration > 0) {
      setTimeout(() => this.dismiss(id), duration);
    }
  }
  showSuccess(message, duration = 3000) {
    this.show(message, 'success', duration);
  }
  showError(message, duration = 5000) {
    this.show(message, 'error', duration);
  }
  showWarning(message, duration = 4000) {
    this.show(message, 'warning', duration);
  }
  showInfo(message, duration = 3000) {
    this.show(message, 'info', duration);
  }
  dismiss(id) {
    const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);
    this.toastsSubject.next(currentToasts);
  }
  success(title, message, duration) {
    this.addToast({
      type: 'success',
      title,
      message,
      duration,
      icon: 'check-circle'
    });
  }
  error(title, message, duration, action) {
    this.addToast({
      type: 'error',
      title,
      message,
      duration: duration || 8000,
      icon: 'x-circle',
      action
    });
  }
  warning(title, message, duration) {
    this.addToast({
      type: 'warning',
      title,
      message,
      duration,
      icon: 'exclamation-triangle'
    });
  }
  // Méthodes spécifiques pour les erreurs d'autorisation
  accessDenied(action = 'effectuer cette action', code) {
    const codeText = code ? ` (Code: ${code})` : '';
    this.error('Accès refusé', `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`, 8000, {
      label: 'Comprendre les rôles',
      handler: () => {
        // Optionnel: rediriger vers une page d'aide
        console.log("Redirection vers l'aide sur les rôles");
      }
    });
  }
  ownershipRequired(resource = 'cette ressource') {
    this.error('Propriétaire requis', `Seul le propriétaire ou un administrateur peut modifier ${resource}`, 8000);
  }
  removeToast(id) {
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next(currentToasts.filter(toast => toast.id !== id));
  }
  clear() {
    this.toastsSubject.next([]);
  }
  static {
    this.ɵfac = function ToastService_Factory(t) {
      return new (t || ToastService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: ToastService,
      factory: ToastService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 67525:
/*!*******************************************************************!*\
  !*** ./src/app/views/admin/dashboard/dashboard-routing.module.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardRoutingModule: () => (/* binding */ DashboardRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _dashboard_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dashboard.component */ 69017);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);




const routes = [{
  path: '',
  component: _dashboard_component__WEBPACK_IMPORTED_MODULE_0__.DashboardComponent
}];
class DashboardRoutingModule {
  static {
    this.ɵfac = function DashboardRoutingModule_Factory(t) {
      return new (t || DashboardRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: DashboardRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](DashboardRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 69017:
/*!**************************************************************!*\
  !*** ./src/app/views/admin/dashboard/dashboard.component.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardComponent: () => (/* binding */ DashboardComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_services_auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/auth.service */ 44796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/toast.service */ 68397);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);





function DashboardComponent_button_30_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function DashboardComponent_button_30_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r8);
      const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r7.clearSearch());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "i", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function DashboardComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 25)(1, "div", 26)(2, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](3, "i", 28)(4, "div", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 30)(6, "h3", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, " Success ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "p", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r1.message, " ");
  }
}
function DashboardComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 33)(1, "div", 26)(2, "div", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](3, "i", 35)(4, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 30)(6, "h3", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, " Error ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "p", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r2.error, " ");
  }
}
function DashboardComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 39)(1, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "div", 41)(3, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
function DashboardComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 43)(1, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "div", 45)(3, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "div", 47)(5, "div", 48)(6, "div", 30)(7, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8, " Total Users ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](12, "div", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "div", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](14, "i", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "div", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](16, "div", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](18, "div", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](19, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](20, "div", 59)(21, "div", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "div", 47)(23, "div", 48)(24, "div", 30)(25, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](26, " User Status ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](27, "div", 61)(28, "div", 48)(29, "div", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](30, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](31, "span", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](32);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](33, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](34, "div", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](35, "span", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](36);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](37, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](38, "div", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](39, "div", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](40, "i", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](41, "div", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](42, "div", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](43, "div", 70)(44, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](45, "div", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](46, "div", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](47, "div", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](48, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](49, "div", 75)(50, "div", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](51, "div", 47)(52, "div", 48)(53, "div", 30)(54, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](55, " User Roles ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](56, "div", 77)(57, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](58, "div", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](59, "span", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](60);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](61, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](62, "div", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](63, "span", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](64);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](65, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](66, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](67, "span", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](68);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](69, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](70, "div", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](71, "div", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](72, "i", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](73, "div", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](74, "div", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](75, "div", 70)(76, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](77, "div", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](78, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](79, "div", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](80, "div", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](81, "div", 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](82, "div", 93)(83, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](84, "Students");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](85, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](86, "Teachers");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](87, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](88, "Admins");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r4.users.length, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](22);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx_r4.getActiveCount(), " Active");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx_r4.getInactiveCount(), " Inactive");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵstyleProp"]("width", ctx_r4.users.length ? ctx_r4.getActiveCount() / ctx_r4.users.length * 100 : 0, "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵstyleProp"]("width", ctx_r4.users.length ? ctx_r4.getInactiveCount() / ctx_r4.users.length * 100 : 0, "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx_r4.getStudentCount(), " Students");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx_r4.getTeacherCount(), " Teachers");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", ctx_r4.getAdminCount(), " Admins");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵstyleProp"]("width", ctx_r4.users.length ? ctx_r4.getStudentCount() / ctx_r4.users.length * 100 : 0, "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵstyleProp"]("width", ctx_r4.users.length ? ctx_r4.getTeacherCount() / ctx_r4.users.length * 100 : 0, "%");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵstyleProp"]("width", ctx_r4.users.length ? ctx_r4.getAdminCount() / ctx_r4.users.length * 100 : 0, "%");
  }
}
function DashboardComponent_div_35_tr_27_option_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "option", 132);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](2, "titlecase");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const role_r12 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", role_r12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind1"](2, 2, role_r12), " ");
  }
}
function DashboardComponent_div_35_tr_27_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "tr", 109)(1, "td", 110)(2, "div", 48)(3, "div", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](4, "div", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "span", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "div", 113)(8, "div", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "td", 115)(11, "div", 116);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "td", 117)(14, "span", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](15, "i");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](16);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "td", 117)(18, "span", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](19, "i");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](20);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "td", 117)(22, "div", 120)(23, "select", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("change", function DashboardComponent_div_35_tr_27_Template_select_change_23_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r14);
      const user_r10 = restoredCtx.$implicit;
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r13.onRoleChange(user_r10._id, $event.target.value));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](24, DashboardComponent_div_35_tr_27_option_24_Template, 3, 4, "option", 122);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](25, "div", 123);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](26, "i", 124);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](27, "td", 117)(28, "button", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function DashboardComponent_div_35_tr_27_Template_button_click_28_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r14);
      const user_r10 = restoredCtx.$implicit;
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r15.toggleUserActivation(user_r10._id, user_r10.isActive !== false));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](29, "div", 126)(30, "i", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](31, "span", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](32);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](33, "td", 117)(34, "button", 128);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function DashboardComponent_div_35_tr_27_Template_button_click_34_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r14);
      const user_r10 = restoredCtx.$implicit;
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r16.onDeleteUser(user_r10._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](35, "i", 129);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](36, " Delete ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](37, "td", 117)(38, "button", 130);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function DashboardComponent_div_35_tr_27_Template_button_click_38_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r14);
      const user_r10 = restoredCtx.$implicit;
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r17.showUserDetails(user_r10._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](39, "i", 131);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](40, " Details ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const user_r10 = ctx.$implicit;
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](user_r10.fullName.charAt(0));
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", user_r10.fullName, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", user_r10.email, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", user_r10.verified ? "bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75]" : "bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785]");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassMap"](user_r10.verified ? "fas fa-check-circle mr-1" : "fas fa-times-circle mr-1");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", user_r10.verified ? "Yes" : "No", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", user_r10.isActive !== false ? "bg-[#afcf75]/15 dark:bg-[#afcf75]/10 text-[#2a5a03] dark:text-[#afcf75] border border-[#afcf75]/30 shadow-sm" : "bg-[#ff6b69]/15 dark:bg-[#ff6b69]/10 text-[#ff6b69] dark:text-[#ff8785] border border-[#ff6b69]/30 shadow-sm");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassMap"](user_r10.isActive !== false ? "fas fa-check-circle mr-1.5 text-[10px]" : "fas fa-times-circle mr-1.5 text-[10px]");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", user_r10.isActive !== false ? "Active" : "Deactivated", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", user_r10.role);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r9.roles);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", user_r10.isActive !== false ? "bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785] hover:bg-[#ff6b69]/20 dark:hover:bg-[#ff6b69]/10 border-[#ff6b69]/30 hover:border-[#ff6b69]/50" : "bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75] hover:bg-[#afcf75]/20 dark:hover:bg-[#afcf75]/10 border-[#afcf75]/30 hover:border-[#afcf75]/50")("title", user_r10.isActive !== false ? "Click to deactivate this user account" : "Click to activate this user account");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", user_r10.isActive !== false ? "bg-gradient-to-r from-[#ff6b69]/5 to-[#ff6b69]/10" : "bg-gradient-to-r from-[#afcf75]/5 to-[#afcf75]/10");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassMap"](user_r10.isActive !== false ? "fas fa-user-slash" : "fas fa-user-check");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", user_r10.isActive !== false ? "Deactivate" : "Activate", " ");
  }
}
function DashboardComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "div", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "div", 96)(3, "h6", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](4, "i", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5, " User Management ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 99)(7, "table", 100)(8, "thead", 101)(9, "tr")(10, "th", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11, " Name ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "th", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](13, " Email ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](14, "th", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](15, " Verified ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](16, "th", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](17, " Status ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "th", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](19, " Role ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "th", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](21, " Activate ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "th", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](23, " Delete ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](24, "th", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](25, " Details ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](26, "tbody", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](27, DashboardComponent_div_35_tr_27_Template, 41, 19, "tr", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](27);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r5.filteredUsers);
  }
}
function DashboardComponent_div_36_div_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 143)(1, "button", 144);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function DashboardComponent_div_36_div_13_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r20);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r19.clearSearch());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "div", 145);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 146);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](4, "i", 147);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5, " Clear search ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
}
function DashboardComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "div", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "div", 133)(3, "div", 134);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](4, "div", 135);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 136);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](6, "i", 137);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](7, "div", 138)(8, "div", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "h3", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10, " No users found ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "p", 141);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](13, DashboardComponent_div_36_div_13_Template, 6, 0, "div", 142);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r6.searchTerm ? "No users match your search criteria." : "There are no users in the system yet.", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r6.searchTerm);
  }
}
class DashboardComponent {
  constructor(authService, router, toastService) {
    this.authService = authService;
    this.router = router;
    this.toastService = toastService;
    this.users = [];
    this.error = '';
    this.message = '';
    this.roles = ['student', 'teacher', 'admin'];
    this.loading = true;
    this.currentUser = null;
    this.searchTerm = '';
    this.filteredUsers = [];
  }
  ngOnInit() {
    this.loadUserData();
  }
  loadUserData() {
    this.loading = true;
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    if (!token || !userStr) {
      this.router.navigate(['/admin/login']);
      return;
    }
    this.currentUser = JSON.parse(userStr);
    // Check if user is admin
    if (this.currentUser.role !== 'admin') {
      this.router.navigate(['/']);
      return;
    }
    this.authService.getAllUsers(token).subscribe({
      next: res => {
        this.users = res;
        this.filteredUsers = [...this.users];
        this.loading = false;
      },
      error: err => {
        this.error = err.error?.message || 'Failed to fetch users';
        this.loading = false;
      }
    });
  }
  searchUsers() {
    if (!this.searchTerm.trim()) {
      this.filteredUsers = [...this.users];
      return;
    }
    const term = this.searchTerm.toLowerCase().trim();
    this.filteredUsers = this.users.filter(user => user.fullName.toLowerCase().includes(term) || user.email.toLowerCase().includes(term) || user.role.toLowerCase().includes(term));
  }
  clearSearch() {
    this.searchTerm = '';
    this.filteredUsers = [...this.users];
  }
  applyFilters() {
    this.searchUsers();
  }
  onRoleChange(userId, newRole) {
    const token = localStorage.getItem('token');
    this.authService.updateUserRole(userId, newRole, token).subscribe({
      next: res => {
        this.message = res.message;
        this.error = '';
        // Update the user in the local arrays
        const userIndex = this.users.findIndex(u => u._id === userId);
        if (userIndex !== -1) {
          this.users[userIndex].role = newRole;
        }
        const filteredIndex = this.filteredUsers.findIndex(u => u._id === userId);
        if (filteredIndex !== -1) {
          this.filteredUsers[filteredIndex].role = newRole;
        }
        // Auto-hide message after 3 seconds
        setTimeout(() => {
          this.message = '';
        }, 3000);
      },
      error: err => {
        this.error = err.error?.message || 'Failed to update role';
        this.message = '';
        // Auto-hide error after 3 seconds
        setTimeout(() => {
          this.error = '';
        }, 3000);
      }
    });
  }
  onDeleteUser(userId) {
    const confirmDelete = confirm('Are you sure you want to delete this user?');
    if (!confirmDelete) return;
    const token = localStorage.getItem('token');
    this.authService.deleteUser(userId, token).subscribe({
      next: res => {
        this.message = res.message;
        this.error = '';
        // Remove user from both arrays
        this.users = this.users.filter(u => u._id !== userId);
        this.filteredUsers = this.filteredUsers.filter(u => u._id !== userId);
        // Auto-hide message after 3 seconds
        setTimeout(() => {
          this.message = '';
        }, 3000);
      },
      error: err => {
        this.error = err.error?.message || 'Failed to delete user';
        this.message = '';
        // Auto-hide error after 3 seconds
        setTimeout(() => {
          this.error = '';
        }, 3000);
      }
    });
  }
  toggleUserActivation(userId, currentStatus) {
    const newStatus = !currentStatus;
    const action = newStatus ? 'activate' : 'deactivate';
    // Find the user to get their name for better messaging
    const user = this.users.find(u => u._id === userId);
    const userName = user?.fullName || user?.firstName || 'User';
    const confirmAction = confirm(`Are you sure you want to ${action} ${userName}?`);
    if (!confirmAction) return;
    const token = localStorage.getItem('token');
    this.authService.toggleUserActivation(userId, newStatus, token).subscribe({
      next: res => {
        const statusText = newStatus ? 'activated' : 'deactivated';
        const successMessage = `${userName} has been ${statusText} successfully`;
        // Show success toast
        this.toastService.showSuccess(successMessage);
        // Clear any existing messages
        this.message = '';
        this.error = '';
        // Update user in both arrays
        const userIndex = this.users.findIndex(u => u._id === userId);
        if (userIndex !== -1) {
          this.users[userIndex].isActive = newStatus;
        }
        const filteredIndex = this.filteredUsers.findIndex(u => u._id === userId);
        if (filteredIndex !== -1) {
          this.filteredUsers[filteredIndex].isActive = newStatus;
        }
        // Apply filters to refresh the view
        this.applyFilters();
      },
      error: err => {
        const statusText = newStatus ? 'activate' : 'deactivate';
        const errorMessage = err.error?.message || `Failed to ${statusText} ${userName}`;
        // Show error toast
        this.toastService.showError(errorMessage);
        // Clear any existing messages
        this.message = '';
        this.error = '';
      }
    });
  }
  getStudentCount() {
    return this.users.filter(u => u.role === 'student').length;
  }
  getTeacherCount() {
    return this.users.filter(u => u.role === 'teacher').length;
  }
  getAdminCount() {
    return this.users.filter(u => u.role === 'admin').length;
  }
  getActiveCount() {
    return this.users.filter(u => u.isActive !== false).length;
  }
  getInactiveCount() {
    return this.users.filter(u => u.isActive === false).length;
  }
  logout() {
    this.authService.logout();
    this.router.navigate(['/admin/login']);
  }
  showUserDetails(userId) {
    this.router.navigate(['/admin/userdetails', userId]);
  }
  static {
    this.ɵfac = function DashboardComponent_Factory(t) {
      return new (t || DashboardComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_services_auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_1__.ToastService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: DashboardComponent,
      selectors: [["app-dashboard"]],
      decls: 37,
      vars: 8,
      consts: [[1, "container-fluid", "p-4", "md:p-6", "bg-[#edf1f4]", "dark:bg-[#121212]", "min-h-screen", "relative"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#6d78c9]"], [1, "relative", "z-10"], [1, "flex", "flex-col", "md:flex-row", "md:items-center", "md:justify-between", "mb-8"], [1, "text-2xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent", "mb-2"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]"], [1, "relative", "w-full", "md:w-72", "mt-4", "md:mt-0", "group"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none"], [1, "fas", "fa-search", "text-[#bdc6cc]", "dark:text-[#6d6870]", "group-focus-within:text-[#4f5fad]", "dark:group-focus-within:text-[#6d78c9]", "transition-colors"], ["type", "text", "placeholder", "Search users...", 1, "w-full", "pl-10", "pr-10", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all", 3, "value", "input"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none", "opacity-0", "group-focus-within:opacity-100", "transition-opacity"], [1, "w-0.5", "h-4", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "rounded-full"], ["class", "absolute inset-y-0 right-0 pr-3 flex items-center text-[#bdc6cc] dark:text-[#6d6870] hover:text-[#ff6b69] dark:hover:text-[#ff8785] transition-colors", 3, "click", 4, "ngIf"], ["class", "bg-[#afcf75]/10 dark:bg-[#afcf75]/5 border border-[#afcf75] dark:border-[#afcf75]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm", 4, "ngIf"], ["class", "bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm", 4, "ngIf"], ["class", "flex justify-center items-center py-20", 4, "ngIf"], ["class", "grid grid-cols-1 md:grid-cols-3 gap-6 mb-8", 4, "ngIf"], ["class", "bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden mb-8 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative", 4, "ngIf"], [1, "absolute", "inset-y-0", "right-0", "pr-3", "flex", "items-center", "text-[#bdc6cc]", "dark:text-[#6d6870]", "hover:text-[#ff6b69]", "dark:hover:text-[#ff8785]", "transition-colors", 3, "click"], [1, "fas", "fa-times-circle"], [1, "bg-[#afcf75]/10", "dark:bg-[#afcf75]/5", "border", "border-[#afcf75]", "dark:border-[#afcf75]/30", "rounded-lg", "p-4", "mx-auto", "max-w-3xl", "mb-6", "backdrop-blur-sm"], [1, "flex", "items-start"], [1, "text-[#2a5a03]", "dark:text-[#afcf75]", "mr-3", "text-xl", "relative"], [1, "fas", "fa-check-circle"], [1, "absolute", "inset-0", "bg-[#afcf75]/20", "dark:bg-[#afcf75]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "flex-1"], [1, "font-medium", "text-[#2a5a03]", "dark:text-[#afcf75]", "mb-1"], [1, "text-sm", "text-[#2a5a03]/80", "dark:text-[#afcf75]/80"], [1, "bg-[#ff6b69]/10", "dark:bg-[#ff6b69]/5", "border", "border-[#ff6b69]", "dark:border-[#ff6b69]/30", "rounded-lg", "p-4", "mx-auto", "max-w-3xl", "mb-6", "backdrop-blur-sm"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "mr-3", "text-xl", "relative"], [1, "fas", "fa-exclamation-triangle"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "dark:bg-[#ff8785]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "font-medium", "text-[#ff6b69]", "dark:text-[#ff8785]", "mb-1"], [1, "text-sm", "text-[#ff6b69]/80", "dark:text-[#ff8785]/80"], [1, "flex", "justify-center", "items-center", "py-20"], [1, "relative"], [1, "w-14", "h-14", "border-4", "border-[#4f5fad]/20", "dark:border-[#6d78c9]/20", "border-t-[#4f5fad]", "dark:border-t-[#6d78c9]", "rounded-full", "animate-spin"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "grid", "grid-cols-1", "md:grid-cols-3", "gap-6", "mb-8"], [1, "bg-white", "dark:bg-[#1e1e1e]", "rounded-xl", "shadow-md", "dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]", "overflow-hidden", "backdrop-blur-sm", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "relative", "group", "hover:shadow-lg", "dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "transition-all", "duration-300", "hover:-translate-y-1"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "opacity-0", "group-hover:opacity-100", "blur-md", "transition-opacity", "duration-300"], [1, "p-6"], [1, "flex", "items-center"], [1, "text-sm", "font-medium", "text-[#6d6870]", "dark:text-[#a0a0a0]", "uppercase", "tracking-wider"], [1, "mt-1", "text-2xl", "font-semibold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "ml-4", "relative"], [1, "absolute", "inset-0", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/10", "rounded-full", "blur-xl", "opacity-0", "group-hover:opacity-100", "transition-opacity", "duration-300", "scale-150"], [1, "relative", "z-10", "bg-[#edf1f4]/80", "dark:bg-[#2a2a2a]/80", "rounded-full", "p-2.5", "backdrop-blur-sm", "group-hover:scale-110", "transition-transform", "duration-300"], [1, "fas", "fa-users", "text-[#4f5fad]", "dark:text-[#6d78c9]", "text-xl"], [1, "mt-4", "w-full", "bg-[#edf1f4]", "dark:bg-[#2a2a2a]", "rounded-full", "h-2.5", "overflow-hidden", "relative"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]/10", "to-[#4f5fad]/10", "dark:from-[#6d78c9]/10", "dark:to-[#4f5fad]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "duration-300", "blur-md"], [1, "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "h-2.5", "rounded-full", "relative", "z-10", 2, "width", "100%"], [1, "absolute", "inset-0", "bg-[#00f7ff]/20", "rounded-full", "animate-pulse"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#2a5a03]", "to-[#afcf75]", "dark:from-[#2a5a03]", "dark:to-[#afcf75]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#2a5a03]", "to-[#afcf75]", "dark:from-[#2a5a03]", "dark:to-[#afcf75]", "opacity-0", "group-hover:opacity-100", "blur-md", "transition-opacity", "duration-300"], [1, "mt-1", "flex", "space-x-4", "text-sm"], [1, "w-2", "h-2", "rounded-full", "bg-[#afcf75]", "dark:bg-[#afcf75]", "mr-1.5", "relative"], [1, "absolute", "inset-0", "bg-[#afcf75]", "rounded-full", "animate-ping", "opacity-75"], [1, "text-[#2a5a03]", "dark:text-[#afcf75]", "font-medium"], [1, "w-2", "h-2", "rounded-full", "bg-[#ff6b69]", "dark:bg-[#ff8785]", "mr-1.5"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "font-medium"], [1, "absolute", "inset-0", "bg-[#afcf75]/10", "dark:bg-[#afcf75]/10", "rounded-full", "blur-xl", "opacity-0", "group-hover:opacity-100", "transition-opacity", "duration-300", "scale-150"], [1, "fas", "fa-check-circle", "text-[#afcf75]", "dark:text-[#afcf75]", "text-xl"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#2a5a03]/10", "to-[#afcf75]/10", "dark:from-[#2a5a03]/10", "dark:to-[#afcf75]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "duration-300", "blur-md"], [1, "flex", "h-full", "relative", "z-10"], [1, "bg-gradient-to-r", "from-[#2a5a03]", "to-[#afcf75]", "dark:from-[#2a5a03]", "dark:to-[#afcf75]", "h-2.5", "relative"], [1, "absolute", "inset-0", "bg-[#afcf75]/20", "rounded-full", "animate-pulse"], [1, "bg-gradient-to-r", "from-[#ff6b69]", "to-[#ff8785]", "dark:from-[#ff6b69]", "dark:to-[#ff8785]", "h-2.5", "relative"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "rounded-full", "animate-pulse"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4a89ce]", "to-[#7826b5]", "dark:from-[#4a89ce]", "dark:to-[#7826b5]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4a89ce]", "to-[#7826b5]", "dark:from-[#4a89ce]", "dark:to-[#7826b5]", "opacity-0", "group-hover:opacity-100", "blur-md", "transition-opacity", "duration-300"], [1, "mt-1", "flex", "flex-wrap", "gap-4", "text-sm"], [1, "w-2", "h-2", "rounded-full", "bg-[#4a89ce]", "dark:bg-[#4a89ce]", "mr-1.5"], [1, "text-[#4a89ce]", "dark:text-[#4a89ce]", "font-medium"], [1, "w-2", "h-2", "rounded-full", "bg-[#7826b5]", "dark:bg-[#7826b5]", "mr-1.5"], [1, "text-[#7826b5]", "dark:text-[#7826b5]", "font-medium"], [1, "w-2", "h-2", "rounded-full", "bg-[#4f5fad]", "dark:bg-[#6d78c9]", "mr-1.5"], [1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "font-medium"], [1, "absolute", "inset-0", "bg-[#4a89ce]/10", "dark:bg-[#4a89ce]/10", "rounded-full", "blur-xl", "opacity-0", "group-hover:opacity-100", "transition-opacity", "duration-300", "scale-150"], [1, "fas", "fa-user-tag", "text-[#4a89ce]", "dark:text-[#4a89ce]", "text-xl"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#4a89ce]/10", "to-[#7826b5]/10", "dark:from-[#4a89ce]/10", "dark:to-[#7826b5]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "duration-300", "blur-md"], [1, "bg-gradient-to-r", "from-[#4a89ce]", "to-[#4a89ce]", "dark:from-[#4a89ce]", "dark:to-[#4a89ce]", "h-2.5", "relative"], [1, "absolute", "inset-0", "bg-[#4a89ce]/20", "rounded-full", "animate-pulse"], [1, "bg-gradient-to-r", "from-[#7826b5]", "to-[#7826b5]", "dark:from-[#7826b5]", "dark:to-[#7826b5]", "h-2.5", "relative"], [1, "absolute", "inset-0", "bg-[#7826b5]/20", "rounded-full", "animate-pulse"], [1, "bg-gradient-to-r", "from-[#4f5fad]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#6d78c9]", "h-2.5", "relative"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "rounded-full", "animate-pulse"], [1, "flex", "justify-between", "mt-2", "text-xs", "text-[#6d6870]", "dark:text-[#a0a0a0]"], [1, "bg-white", "dark:bg-[#1e1e1e]", "rounded-xl", "shadow-md", "dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]", "overflow-hidden", "mb-8", "backdrop-blur-sm", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "relative"], [1, "absolute", "top-0", "left-0", "right-0", "h-0.5", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]"], [1, "p-5", "border-b", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "flex", "items-center", "justify-between"], [1, "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent", "flex", "items-center"], [1, "fas", "fa-users-cog", "mr-2"], [1, "overflow-x-auto"], [1, "w-full", "divide-y", "divide-[#edf1f4]", "dark:divide-[#2a2a2a]", "table-fixed"], [1, "bg-[#f8fafc]", "dark:bg-[#1a1a1a]"], ["scope", "col", 1, "px-3", "py-3", "text-left", "text-xs", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "uppercase", "tracking-wider", "w-[15%]"], ["scope", "col", 1, "px-3", "py-3", "text-left", "text-xs", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "uppercase", "tracking-wider", "w-[20%]"], ["scope", "col", 1, "px-2", "py-3", "text-center", "text-xs", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "uppercase", "tracking-wider", "w-[8%]"], ["scope", "col", 1, "px-2", "py-3", "text-center", "text-xs", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "uppercase", "tracking-wider", "w-[10%]"], ["scope", "col", 1, "px-2", "py-3", "text-center", "text-xs", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "uppercase", "tracking-wider", "w-[13%]"], [1, "bg-white", "dark:bg-[#1e1e1e]", "divide-y", "divide-[#edf1f4]", "dark:divide-[#2a2a2a]"], ["class", "hover:bg-[#f8fafc] dark:hover:bg-[#1a1a1a] transition-colors", 4, "ngFor", "ngForOf"], [1, "hover:bg-[#f8fafc]", "dark:hover:bg-[#1a1a1a]", "transition-colors"], [1, "px-3", "py-3", "whitespace-nowrap", "truncate"], [1, "flex-shrink-0", "h-8", "w-8", "rounded-full", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "text-white", "flex", "items-center", "justify-center", "text-xs", "shadow-md", "relative", "group"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "rounded-full", "blur-md", "opacity-0", "group-hover:opacity-100", "transition-opacity"], [1, "ml-3"], [1, "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "truncate", "max-w-[120px]"], [1, "px-3", "py-3", "whitespace-nowrap"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "truncate", "max-w-[150px]"], [1, "px-2", "py-3", "whitespace-nowrap", "text-center"], [1, "px-2", "py-1", "text-xs", "rounded-lg", "inline-flex", "items-center", "justify-center", 3, "ngClass"], [1, "px-3", "py-1.5", "text-xs", "rounded-full", "inline-flex", "items-center", "justify-center", "font-semibold", "transition-all", "duration-300", 3, "ngClass"], [1, "relative", "group"], [1, "w-full", "px-2", "py-1.5", "text-xs", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1a1a1a]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-1", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all", "appearance-none", "pr-7", 3, "value", "change"], [3, "value", 4, "ngFor", "ngForOf"], [1, "absolute", "inset-y-0", "right-0", "flex", "items-center", "pr-2", "pointer-events-none", "text-[#6d6870]", "dark:text-[#a0a0a0]"], [1, "fas", "fa-chevron-down", "text-xs"], [1, "px-3", "py-2", "text-xs", "rounded-lg", "font-semibold", "flex", "items-center", "justify-center", "mx-auto", "transition-all", "duration-300", "w-full", "relative", "overflow-hidden", "group", "border", "shadow-sm", "hover:shadow-md", "transform", "hover:scale-105", 3, "ngClass", "title", "click"], [1, "absolute", "inset-0", "opacity-0", "group-hover:opacity-100", "transition-opacity", "duration-300", 3, "ngClass"], [1, "relative", "z-10", "mr-2", "transition-transform", "duration-300", "group-hover:scale-110"], ["title", "Supprimer d\u00E9finitivement cet utilisateur", 1, "px-2", "py-1.5", "text-xs", "rounded-lg", "bg-[#ff6b69]/10", "dark:bg-[#ff6b69]/5", "text-[#ff6b69]", "dark:text-[#ff8785]", "hover:bg-[#ff6b69]/20", "dark:hover:bg-[#ff6b69]/10", "font-medium", "flex", "items-center", "justify-center", "mx-auto", "transition-all", "w-full", 3, "click"], [1, "fas", "fa-trash-alt", "mr-1.5"], [1, "px-2", "py-1.5", "text-xs", "rounded-lg", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/5", "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:bg-[#4f5fad]/20", "dark:hover:bg-[#6d78c9]/10", "font-medium", "flex", "items-center", "justify-center", "mx-auto", "transition-all", "w-full", 3, "click"], [1, "fas", "fa-eye", "mr-1.5"], [3, "value"], [1, "p-10", "text-center"], [1, "relative", "mx-auto", "w-20", "h-20", "mb-6"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "rounded-full", "blur-xl"], [1, "relative", "z-10", "w-20", "h-20", "rounded-full", "bg-gradient-to-br", "from-[#edf1f4]", "to-white", "dark:from-[#1a1a1a]", "dark:to-[#2a2a2a]", "flex", "items-center", "justify-center", "shadow-lg"], [1, "fas", "fa-users", "text-3xl", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "absolute", "inset-0", "border-2", "border-[#4f5fad]/20", "dark:border-[#6d78c9]/20", "rounded-full", "animate-ping", "opacity-75"], [1, "absolute", "inset-0", "border", "border-[#4f5fad]/40", "dark:border-[#6d78c9]/40", "rounded-full", "animate-pulse"], [1, "text-xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent", "mb-3"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "max-w-md", "mx-auto"], ["class", "mt-8", 4, "ngIf"], [1, "mt-8"], [1, "inline-flex", "items-center", "px-4", "py-2.5", "text-sm", "relative", "overflow-hidden", "group", 3, "click"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "opacity-10", "dark:opacity-20", "group-hover:opacity-20", "dark:group-hover:opacity-30", "transition-opacity"], [1, "relative", "z-10", "flex", "items-center", "text-[#4f5fad]", "dark:text-[#6d78c9]", "font-medium"], [1, "fas", "fa-times-circle", "mr-2"]],
      template: function DashboardComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "div", 4)(5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](6, "div", 6)(7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "div", 7)(18, "div", 8)(19, "div")(20, "h1", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](21, " Admin Dashboard ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "p", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](23, " Manage users and system settings ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](24, "div", 11)(25, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](26, "i", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](27, "input", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("input", function DashboardComponent_Template_input_input_27_listener($event) {
            ctx.searchTerm = $event.target.value;
            return ctx.searchUsers();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](29, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](30, DashboardComponent_button_30_Template, 2, 0, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](31, DashboardComponent_div_31_Template, 10, 1, "div", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](32, DashboardComponent_div_32_Template, 10, 1, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](33, DashboardComponent_div_33_Template, 4, 0, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](34, DashboardComponent_div_34_Template, 89, 16, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](35, DashboardComponent_div_35_Template, 28, 1, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](36, DashboardComponent_div_36_Template, 14, 2, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", ctx.searchTerm);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.searchTerm);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.message);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.filteredUsers.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.filteredUsers.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.filteredUsers.length === 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.TitleCasePipe],
      styles: ["[_nghost-%COMP%] {\n  display: block;\n}\n[_nghost-%COMP%]   .min-h-screen[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, var(--dark-bg), var(--medium-bg));\n  background-image: linear-gradient(rgba(0, 247, 255, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 247, 255, 0.03) 1px, transparent 1px);\n  background-size: 20px 20px;\n}\n\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%] {\n  background: rgba(31, 41, 55, 0.7);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border: 1px solid rgba(0, 247, 255, 0.1);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), inset 0 0 0 1px rgba(255, 255, 255, 0.05);\n  transition: all 0.3s ease;\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), inset 0 0 0 1px rgba(255, 255, 255, 0.1), 0 0 15px rgba(0, 247, 255, 0.2);\n  border-color: rgba(0, 247, 255, 0.3);\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%] {\n  height: 4px;\n  background: rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n  border-radius: 4px;\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5 div[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5 div[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-primary-light[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-primary-dark[_ngcontent-%COMP%] {\n  background: linear-gradient(90deg, var(--accent-color-dark), var(--accent-color));\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-green-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-green-600[_ngcontent-%COMP%] {\n  background: linear-gradient(90deg, #059669, #10B981);\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-red-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-red-600[_ngcontent-%COMP%] {\n  background: linear-gradient(90deg, #DC2626, #EF4444);\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-blue-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-blue-600[_ngcontent-%COMP%] {\n  background: linear-gradient(90deg, #3B82F6, #60A5FA);\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-purple-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-purple-600[_ngcontent-%COMP%] {\n  background: linear-gradient(90deg, #8B5CF6, #A78BFA);\n}\n.grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-yellow-500[_ngcontent-%COMP%], .grid[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]   .h-1\\.5[_ngcontent-%COMP%]   .bg-yellow-600[_ngcontent-%COMP%] {\n  background: linear-gradient(90deg, #F59E0B, #FBBF24);\n}\n\ntable[_ngcontent-%COMP%] {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 0;\n}\ntable[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  background: rgba(17, 24, 39, 0.7);\n  -webkit-backdrop-filter: blur(4px);\n          backdrop-filter: blur(4px);\n  color: var(--accent-color);\n  font-weight: 500;\n  letter-spacing: 1px;\n  text-transform: uppercase;\n  padding: 12px 16px;\n  position: relative;\n}\ntable[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\n  border-top-left-radius: var(--border-radius-md);\n}\ntable[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:last-child {\n  border-top-right-radius: var(--border-radius-md);\n}\ntable[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  width: 100%;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, rgba(0, 247, 255, 0.5), transparent);\n}\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\n  transition: all 0.2s ease;\n}\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\n  background: rgba(0, 247, 255, 0.05) !important;\n}\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%] {\n  color: white;\n}\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  padding: 12px 16px;\n  transition: all 0.2s ease;\n}\ntable[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));\n  box-shadow: var(--glow-effect);\n}\n\n.badge[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.5rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n.badge.verified[_ngcontent-%COMP%] {\n  background-color: rgba(5, 150, 105, 0.1);\n  color: #10B981;\n  border: 1px solid rgba(16, 185, 129, 0.2);\n}\n.badge.not-verified[_ngcontent-%COMP%] {\n  background-color: rgba(239, 68, 68, 0.1);\n  color: #EF4444;\n  border: 1px solid rgba(239, 68, 68, 0.2);\n}\n.badge.active[_ngcontent-%COMP%] {\n  background-color: rgba(5, 150, 105, 0.1);\n  color: #10B981;\n  border: 1px solid rgba(16, 185, 129, 0.2);\n}\n.badge.inactive[_ngcontent-%COMP%] {\n  background-color: rgba(156, 163, 175, 0.1);\n  color: #9CA3AF;\n  border: 1px solid rgba(156, 163, 175, 0.2);\n}\n\nbutton[_ngcontent-%COMP%] {\n  transition: all 0.2s ease;\n}\nbutton[_ngcontent-%COMP%]:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--glow-effect);\n}\nbutton[_ngcontent-%COMP%]:active {\n  transform: translateY(0);\n}\n\ninput[type=text][_ngcontent-%COMP%] {\n  background: rgba(31, 41, 55, 0.7);\n  border: 1px solid rgba(0, 247, 255, 0.1);\n  color: white;\n  transition: all 0.3s ease;\n}\ninput[type=text][_ngcontent-%COMP%]:focus {\n  border-color: var(--accent-color);\n  box-shadow: 0 0 0 1px rgba(0, 247, 255, 0.2), var(--glow-effect);\n  outline: none;\n}\ninput[type=text][_ngcontent-%COMP%]::placeholder {\n  color: rgba(156, 163, 175, 0.7);\n}\n\nselect[_ngcontent-%COMP%] {\n  background: rgba(31, 41, 55, 0.7);\n  border: 1px solid rgba(0, 247, 255, 0.1);\n  color: white;\n  transition: all 0.3s ease;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2300f7ff'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E\");\n  background-repeat: no-repeat;\n  background-position: right 0.5rem center;\n  background-size: 1rem;\n  padding-right: 2rem;\n}\nselect[_ngcontent-%COMP%]:focus {\n  border-color: var(--accent-color);\n  box-shadow: 0 0 0 1px rgba(0, 247, 255, 0.2), var(--glow-effect);\n  outline: none;\n}\nselect[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\n  background-color: #1F2937;\n}\n\n@keyframes _ngcontent-%COMP%_shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n.animate-spin[_ngcontent-%COMP%] {\n  border-color: var(--accent-color);\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZGFzaGJvYXJkL2Rhc2hib2FyZC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsY0FBYztBQUNoQjtBQUNBO0VBQ0UscUVBQXFFO0VBQ3JFLHFKQUFxSjtFQUNySiwwQkFBMEI7QUFDNUI7O0FBRUE7RUFDRSxpQ0FBaUM7RUFDakMsbUNBQTJCO1VBQTNCLDJCQUEyQjtFQUMzQix3Q0FBd0M7RUFDeEMsb0ZBQW9GO0VBQ3BGLHlCQUF5QjtBQUMzQjtBQUNBO0VBQ0UsMkJBQTJCO0VBQzNCLG9IQUFvSDtFQUNwSCxvQ0FBb0M7QUFDdEM7QUFDQTtFQUNFLFdBQVc7RUFDWCw4QkFBOEI7RUFDOUIsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtBQUNwQjtBQUNBO0VBQ0Usa0JBQWtCO0VBQ2xCLGdCQUFnQjtBQUNsQjtBQUNBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sT0FBTztFQUNQLFFBQVE7RUFDUixTQUFTO0VBQ1Qsc0ZBQXNGO0VBQ3RGLDhCQUE4QjtBQUNoQztBQUNBO0VBQ0UsaUZBQWlGO0FBQ25GO0FBQ0E7RUFDRSxvREFBb0Q7QUFDdEQ7QUFDQTtFQUNFLG9EQUFvRDtBQUN0RDtBQUNBO0VBQ0Usb0RBQW9EO0FBQ3REO0FBQ0E7RUFDRSxvREFBb0Q7QUFDdEQ7QUFDQTtFQUNFLG9EQUFvRDtBQUN0RDs7QUFFQTtFQUNFLFdBQVc7RUFDWCx5QkFBeUI7RUFDekIsaUJBQWlCO0FBQ25CO0FBQ0E7RUFDRSxpQ0FBaUM7RUFDakMsa0NBQTBCO1VBQTFCLDBCQUEwQjtFQUMxQiwwQkFBMEI7RUFDMUIsZ0JBQWdCO0VBQ2hCLG1CQUFtQjtFQUNuQix5QkFBeUI7RUFDekIsa0JBQWtCO0VBQ2xCLGtCQUFrQjtBQUNwQjtBQUNBO0VBQ0UsK0NBQStDO0FBQ2pEO0FBQ0E7RUFDRSxnREFBZ0Q7QUFDbEQ7QUFDQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsT0FBTztFQUNQLFNBQVM7RUFDVCxXQUFXO0VBQ1gsV0FBVztFQUNYLG9GQUFvRjtBQUN0RjtBQUNBO0VBQ0UseUJBQXlCO0FBQzNCO0FBQ0E7RUFDRSw4Q0FBOEM7QUFDaEQ7QUFDQTtFQUNFLFlBQVk7QUFDZDtBQUNBO0VBQ0Usa0JBQWtCO0VBQ2xCLHlCQUF5QjtBQUMzQjtBQUNBO0VBQ0UsZ0ZBQWdGO0VBQ2hGLDhCQUE4QjtBQUNoQzs7QUFFQTtFQUNFLG9CQUFvQjtFQUNwQixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLHFCQUFxQjtFQUNyQixrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCO0FBQ0E7RUFDRSx3Q0FBd0M7RUFDeEMsY0FBYztFQUNkLHlDQUF5QztBQUMzQztBQUNBO0VBQ0Usd0NBQXdDO0VBQ3hDLGNBQWM7RUFDZCx3Q0FBd0M7QUFDMUM7QUFDQTtFQUNFLHdDQUF3QztFQUN4QyxjQUFjO0VBQ2QseUNBQXlDO0FBQzNDO0FBQ0E7RUFDRSwwQ0FBMEM7RUFDMUMsY0FBYztFQUNkLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFLHlCQUF5QjtBQUMzQjtBQUNBO0VBQ0UsMkJBQTJCO0VBQzNCLDhCQUE4QjtBQUNoQztBQUNBO0VBQ0Usd0JBQXdCO0FBQzFCOztBQUVBO0VBQ0UsaUNBQWlDO0VBQ2pDLHdDQUF3QztFQUN4QyxZQUFZO0VBQ1oseUJBQXlCO0FBQzNCO0FBQ0E7RUFDRSxpQ0FBaUM7RUFDakMsZ0VBQWdFO0VBQ2hFLGFBQWE7QUFDZjtBQUNBO0VBQ0UsK0JBQStCO0FBQ2pDOztBQUVBO0VBQ0UsaUNBQWlDO0VBQ2pDLHdDQUF3QztFQUN4QyxZQUFZO0VBQ1oseUJBQXlCO0VBQ3pCLGdCQUFnQjtFQUNoQiw2UEFBNlA7RUFDN1AsNEJBQTRCO0VBQzVCLHdDQUF3QztFQUN4QyxxQkFBcUI7RUFDckIsbUJBQW1CO0FBQ3JCO0FBQ0E7RUFDRSxpQ0FBaUM7RUFDakMsZ0VBQWdFO0VBQ2hFLGFBQWE7QUFDZjtBQUNBO0VBQ0UseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0U7SUFDRSw0QkFBNEI7RUFDOUI7RUFDQTtJQUNFLDJCQUEyQjtFQUM3QjtBQUNGO0FBQ0E7RUFDRSxpQ0FBaUM7RUFDakMsMkNBQTJDO0FBQzdDOztBQUlBLGdzVEFBZ3NUIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xuICBkaXNwbGF5OiBibG9jaztcbn1cbjpob3N0IC5taW4taC1zY3JlZW4ge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCB2YXIoLS1kYXJrLWJnKSwgdmFyKC0tbWVkaXVtLWJnKSk7XG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudChyZ2JhKDAsIDI0NywgMjU1LCAwLjAzKSAxcHgsIHRyYW5zcGFyZW50IDFweCksIGxpbmVhci1ncmFkaWVudCg5MGRlZywgcmdiYSgwLCAyNDcsIDI1NSwgMC4wMykgMXB4LCB0cmFuc3BhcmVudCAxcHgpO1xuICBiYWNrZ3JvdW5kLXNpemU6IDIwcHggMjBweDtcbn1cblxuLmdyaWQgLmJnLXdoaXRlIHtcbiAgYmFja2dyb3VuZDogcmdiYSgzMSwgNDEsIDU1LCAwLjcpO1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjQ3LCAyNTUsIDAuMSk7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjIpLCBpbnNldCAwIDAgMCAxcHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cbi5ncmlkIC5iZy13aGl0ZTpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgYm94LXNoYWRvdzogMCA2cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMyksIGluc2V0IDAgMCAwIDFweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSksIDAgMCAxNXB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuMik7XG4gIGJvcmRlci1jb2xvcjogcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcbn1cbi5ncmlkIC5iZy13aGl0ZSAuaC0xXFwuNSB7XG4gIGhlaWdodDogNHB4O1xuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMyk7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGJvcmRlci1yYWRpdXM6IDRweDtcbn1cbi5ncmlkIC5iZy13aGl0ZSAuaC0xXFwuNSBkaXYge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG4uZ3JpZCAuYmctd2hpdGUgLmgtMVxcLjUgZGl2OjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHRyYW5zcGFyZW50LCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMiksIHRyYW5zcGFyZW50KTtcbiAgYW5pbWF0aW9uOiBzaGltbWVyIDJzIGluZmluaXRlO1xufVxuLmdyaWQgLmJnLXdoaXRlIC5oLTFcXC41IC5iZy1wcmltYXJ5LWxpZ2h0LCAuZ3JpZCAuYmctd2hpdGUgLmgtMVxcLjUgLmJnLXByaW1hcnktZGFyayB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdmFyKC0tYWNjZW50LWNvbG9yLWRhcmspLCB2YXIoLS1hY2NlbnQtY29sb3IpKTtcbn1cbi5ncmlkIC5iZy13aGl0ZSAuaC0xXFwuNSAuYmctZ3JlZW4tNTAwLCAuZ3JpZCAuYmctd2hpdGUgLmgtMVxcLjUgLmJnLWdyZWVuLTYwMCB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzA1OTY2OSwgIzEwQjk4MSk7XG59XG4uZ3JpZCAuYmctd2hpdGUgLmgtMVxcLjUgLmJnLXJlZC01MDAsIC5ncmlkIC5iZy13aGl0ZSAuaC0xXFwuNSAuYmctcmVkLTYwMCB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgI0RDMjYyNiwgI0VGNDQ0NCk7XG59XG4uZ3JpZCAuYmctd2hpdGUgLmgtMVxcLjUgLmJnLWJsdWUtNTAwLCAuZ3JpZCAuYmctd2hpdGUgLmgtMVxcLjUgLmJnLWJsdWUtNjAwIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjM0I4MkY2LCAjNjBBNUZBKTtcbn1cbi5ncmlkIC5iZy13aGl0ZSAuaC0xXFwuNSAuYmctcHVycGxlLTUwMCwgLmdyaWQgLmJnLXdoaXRlIC5oLTFcXC41IC5iZy1wdXJwbGUtNjAwIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjOEI1Q0Y2LCAjQTc4QkZBKTtcbn1cbi5ncmlkIC5iZy13aGl0ZSAuaC0xXFwuNSAuYmcteWVsbG93LTUwMCwgLmdyaWQgLmJnLXdoaXRlIC5oLTFcXC41IC5iZy15ZWxsb3ctNjAwIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjRjU5RTBCLCAjRkJCRjI0KTtcbn1cblxudGFibGUge1xuICB3aWR0aDogMTAwJTtcbiAgYm9yZGVyLWNvbGxhcHNlOiBzZXBhcmF0ZTtcbiAgYm9yZGVyLXNwYWNpbmc6IDA7XG59XG50YWJsZSB0aGVhZCB0aCB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMTcsIDI0LCAzOSwgMC43KTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDRweCk7XG4gIGNvbG9yOiB2YXIoLS1hY2NlbnQtY29sb3IpO1xuICBmb250LXdlaWdodDogNTAwO1xuICBsZXR0ZXItc3BhY2luZzogMXB4O1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICBwYWRkaW5nOiAxMnB4IDE2cHg7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cbnRhYmxlIHRoZWFkIHRoOmZpcnN0LWNoaWxkIHtcbiAgYm9yZGVyLXRvcC1sZWZ0LXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cy1tZCk7XG59XG50YWJsZSB0aGVhZCB0aDpsYXN0LWNoaWxkIHtcbiAgYm9yZGVyLXRvcC1yaWdodC1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMtbWQpO1xufVxudGFibGUgdGhlYWQgdGg6OmFmdGVyIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBsZWZ0OiAwO1xuICBib3R0b206IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDFweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgcmdiYSgwLCAyNDcsIDI1NSwgMC41KSwgdHJhbnNwYXJlbnQpO1xufVxudGFibGUgdGJvZHkgdHIge1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xufVxudGFibGUgdGJvZHkgdHI6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDI0NywgMjU1LCAwLjA1KSAhaW1wb3J0YW50O1xufVxudGFibGUgdGJvZHkgdHI6aG92ZXIgdGQge1xuICBjb2xvcjogd2hpdGU7XG59XG50YWJsZSB0Ym9keSB0ciB0ZCB7XG4gIHBhZGRpbmc6IDEycHggMTZweDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbn1cbnRhYmxlIHRib2R5IHRyIHRkIC5mbGV4LXNocmluay0wIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgdmFyKC0tYWNjZW50LWNvbG9yKSwgdmFyKC0tc2Vjb25kYXJ5LWNvbG9yKSk7XG4gIGJveC1zaGFkb3c6IHZhcigtLWdsb3ctZWZmZWN0KTtcbn1cblxuLmJhZGdlIHtcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbn1cbi5iYWRnZS52ZXJpZmllZCB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNSwgMTUwLCAxMDUsIDAuMSk7XG4gIGNvbG9yOiAjMTBCOTgxO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDE2LCAxODUsIDEyOSwgMC4yKTtcbn1cbi5iYWRnZS5ub3QtdmVyaWZpZWQge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDIzOSwgNjgsIDY4LCAwLjEpO1xuICBjb2xvcjogI0VGNDQ0NDtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyMzksIDY4LCA2OCwgMC4yKTtcbn1cbi5iYWRnZS5hY3RpdmUge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDUsIDE1MCwgMTA1LCAwLjEpO1xuICBjb2xvcjogIzEwQjk4MTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxNiwgMTg1LCAxMjksIDAuMik7XG59XG4uYmFkZ2UuaW5hY3RpdmUge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDE1NiwgMTYzLCAxNzUsIDAuMSk7XG4gIGNvbG9yOiAjOUNBM0FGO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDE1NiwgMTYzLCAxNzUsIDAuMik7XG59XG5cbmJ1dHRvbiB7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG59XG5idXR0b246aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG4gIGJveC1zaGFkb3c6IHZhcigtLWdsb3ctZWZmZWN0KTtcbn1cbmJ1dHRvbjphY3RpdmUge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XG59XG5cbmlucHV0W3R5cGU9dGV4dF0ge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDMxLCA0MSwgNTUsIDAuNyk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjQ3LCAyNTUsIDAuMSk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cbmlucHV0W3R5cGU9dGV4dF06Zm9jdXMge1xuICBib3JkZXItY29sb3I6IHZhcigtLWFjY2VudC1jb2xvcik7XG4gIGJveC1zaGFkb3c6IDAgMCAwIDFweCByZ2JhKDAsIDI0NywgMjU1LCAwLjIpLCB2YXIoLS1nbG93LWVmZmVjdCk7XG4gIG91dGxpbmU6IG5vbmU7XG59XG5pbnB1dFt0eXBlPXRleHRdOjpwbGFjZWhvbGRlciB7XG4gIGNvbG9yOiByZ2JhKDE1NiwgMTYzLCAxNzUsIDAuNyk7XG59XG5cbnNlbGVjdCB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMzEsIDQxLCA1NSwgMC43KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbiAgY29sb3I6IHdoaXRlO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBhcHBlYXJhbmNlOiBub25lO1xuICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNDc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nbm9uZScgdmlld0JveD0nMCAwIDI0IDI0JyBzdHJva2U9JyUyMzAwZjdmZiclM0UlM0NwYXRoIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgc3Ryb2tlLXdpZHRoPScyJyBkPSdNMTkgOWwtNyA3LTctNyclM0UlM0MvcGF0aCUzRSUzQy9zdmclM0VcIik7XG4gIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XG4gIGJhY2tncm91bmQtcG9zaXRpb246IHJpZ2h0IDAuNXJlbSBjZW50ZXI7XG4gIGJhY2tncm91bmQtc2l6ZTogMXJlbTtcbiAgcGFkZGluZy1yaWdodDogMnJlbTtcbn1cbnNlbGVjdDpmb2N1cyB7XG4gIGJvcmRlci1jb2xvcjogdmFyKC0tYWNjZW50LWNvbG9yKTtcbiAgYm94LXNoYWRvdzogMCAwIDAgMXB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuMiksIHZhcigtLWdsb3ctZWZmZWN0KTtcbiAgb3V0bGluZTogbm9uZTtcbn1cbnNlbGVjdCBvcHRpb24ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMUYyOTM3O1xufVxuXG5Aa2V5ZnJhbWVzIHNoaW1tZXIge1xuICAwJSB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC0xMDAlKTtcbiAgfVxuICAxMDAlIHtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMTAwJSk7XG4gIH1cbn1cbi5hbmltYXRlLXNwaW4ge1xuICBib3JkZXItY29sb3I6IHZhcigtLWFjY2VudC1jb2xvcik7XG4gIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuNSk7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 73804:
/*!***********************************************************!*\
  !*** ./src/app/views/admin/dashboard/dashboard.module.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardModule: () => (/* binding */ DashboardModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _dashboard_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dashboard-routing.module */ 67525);
/* harmony import */ var _dashboard_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dashboard.component */ 69017);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);




class DashboardModule {
  static {
    this.ɵfac = function DashboardModule_Factory(t) {
      return new (t || DashboardModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: DashboardModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _dashboard_routing_module__WEBPACK_IMPORTED_MODULE_0__.DashboardRoutingModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](DashboardModule, {
    declarations: [_dashboard_component__WEBPACK_IMPORTED_MODULE_1__.DashboardComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _dashboard_routing_module__WEBPACK_IMPORTED_MODULE_0__.DashboardRoutingModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_views_admin_dashboard_dashboard_module_ts.js.map