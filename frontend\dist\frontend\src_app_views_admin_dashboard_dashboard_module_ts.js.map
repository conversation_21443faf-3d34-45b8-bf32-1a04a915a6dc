{"version": 3, "file": "src_app_views_admin_dashboard_dashboard_module_ts.js", "mappings": ";;;;;;;;;;;;;;;AACuC;;AAMjC,MAAOC,YAAY;EAKvBC,YAAA;IAJQ,KAAAC,aAAa,GAAG,IAAIH,iDAAe,CAAU,EAAE,CAAC;IACxD,KAAAI,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IACnC,KAAAC,SAAS,GAAG,CAAC;EAEN;EACPC,UAAUA,CAAA;IAChB,OAAOC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD;EAEQC,QAAQA,CAACC,KAAwB;IACvC,MAAMC,QAAQ,GAAU;MACtB,GAAGD,KAAK;MACRE,EAAE,EAAE,IAAI,CAACR,UAAU,EAAE;MACrBS,QAAQ,EAAEH,KAAK,CAACG,QAAQ,IAAI;KAC7B;IAED,MAAMC,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAEH,QAAQ,CAAC,CAAC;IAErD;IACA,IAAIA,QAAQ,CAACE,QAAQ,IAAIF,QAAQ,CAACE,QAAQ,GAAG,CAAC,EAAE;MAC9CI,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,WAAW,CAACP,QAAQ,CAACC,EAAE,CAAC;MAC/B,CAAC,EAAED,QAAQ,CAACE,QAAQ,CAAC;;EAEzB;EACAM,IAAIA,CACFC,OAAe,EACfC,IAAA,GAAiD,MAAM,EACvDR,QAAQ,GAAG,IAAI;IAEf,MAAMD,EAAE,GAAG,IAAI,CAACR,UAAU,EAAE;IAC5B,MAAMM,KAAK,GAAU;MAAEE,EAAE;MAAES,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEF,OAAO;MAAEP;IAAQ,CAAE;IAC/D,MAAMC,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAEJ,KAAK,CAAC,CAAC;IAElD,IAAIG,QAAQ,GAAG,CAAC,EAAE;MAChBI,UAAU,CAAC,MAAM,IAAI,CAACM,OAAO,CAACX,EAAE,CAAC,EAAEC,QAAQ,CAAC;;EAEhD;EAEAW,WAAWA,CAACJ,OAAe,EAAEP,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEP,QAAQ,CAAC;EACzC;EAEAY,SAASA,CAACL,OAAe,EAAEP,QAAQ,GAAG,IAAI;IACxC,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,OAAO,EAAEP,QAAQ,CAAC;EACvC;EAEAa,WAAWA,CAACN,OAAe,EAAEP,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEP,QAAQ,CAAC;EACzC;EAEAc,QAAQA,CAACP,OAAe,EAAEP,QAAQ,GAAG,IAAI;IACvC,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,MAAM,EAAEP,QAAQ,CAAC;EACtC;EAEAU,OAAOA,CAACX,EAAU;IAChB,MAAME,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK,CAACa,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjB,EAAE,KAAKA,EAAE,CAAC;IACzE,IAAI,CAACZ,aAAa,CAACgB,IAAI,CAACF,aAAa,CAAC;EACxC;EACAgB,OAAOA,CAACR,KAAa,EAAEF,OAAe,EAAEP,QAAiB;IACvD,IAAI,CAACJ,QAAQ,CAAC;MACZY,IAAI,EAAE,SAAS;MACfC,KAAK;MACLF,OAAO;MACPP,QAAQ;MACRkB,IAAI,EAAE;KACP,CAAC;EACJ;EACAC,KAAKA,CACHV,KAAa,EACbF,OAAe,EACfP,QAAiB,EACjBoB,MAAwB;IAExB,IAAI,CAACxB,QAAQ,CAAC;MACZY,IAAI,EAAE,OAAO;MACbC,KAAK;MACLF,OAAO;MACPP,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BkB,IAAI,EAAE,UAAU;MAChBE;KACD,CAAC;EACJ;EAEAC,OAAOA,CAACZ,KAAa,EAAEF,OAAe,EAAEP,QAAiB;IACvD,IAAI,CAACJ,QAAQ,CAAC;MACZY,IAAI,EAAE,SAAS;MACfC,KAAK;MACLF,OAAO;MACPP,QAAQ;MACRkB,IAAI,EAAE;KACP,CAAC;EACJ;EACA;EACAI,YAAYA,CAACF,MAAA,GAAiB,wBAAwB,EAAEG,IAAa;IACnE,MAAMC,QAAQ,GAAGD,IAAI,GAAG,WAAWA,IAAI,GAAG,GAAG,EAAE;IAC/C,IAAI,CAACJ,KAAK,CACR,cAAc,EACd,oDAAoDC,MAAM,GAAGI,QAAQ,EAAE,EACvE,IAAI,EACJ;MACEC,KAAK,EAAE,sBAAsB;MAC7BC,OAAO,EAAEA,CAAA,KAAK;QACZ;QACAC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;KACD,CACF;EACH;EAEAC,iBAAiBA,CAACC,QAAA,GAAmB,iBAAiB;IACpD,IAAI,CAACX,KAAK,CACR,qBAAqB,EACrB,2DAA2DW,QAAQ,EAAE,EACrE,IAAI,CACL;EACH;EAEAzB,WAAWA,CAACN,EAAU;IACpB,MAAME,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAACF,aAAa,CAACc,MAAM,CAAElB,KAAK,IAAKA,KAAK,CAACE,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC3E;EACAgC,KAAKA,CAAA;IACH,IAAI,CAAC5C,aAAa,CAACgB,IAAI,CAAC,EAAE,CAAC;EAC7B;;;uBA/HWlB,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAA+C,OAAA,EAAZ/C,YAAY,CAAAgD,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA;;;;;;;;;;;;;;;;;;ACJmC;AACI;;;AAE3D,MAAMG,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH,oEAAkBA;AAAA,CAAE,CAAC;AAM9D,MAAOI,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXK,sBAAsB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFvBT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IE+DhBU,4DAAA,iBAIC;IAFCA,wDAAA,mBAAAG,8DAAA;MAAAH,2DAAA,CAAAK,GAAA;MAAA,MAAAC,MAAA,GAAAN,2DAAA;MAAA,OAASA,yDAAA,CAAAM,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAGvBT,uDAAA,YAAmC;IACrCA,0DAAA,EAAS;;;;;IAKbA,4DAAA,cAGC;IAGKA,uDAAA,YAAmC;IAKrCA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,gBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA4D;IAC1DA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,MAAAe,MAAA,CAAArD,OAAA,MACF;;;;;IAKNsC,4DAAA,cAGC;IAGKA,uDAAA,YAA2C;IAK7CA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,cACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA4D;IAC1DA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,MAAAgB,MAAA,CAAA1C,KAAA,MACF;;;;;IAMN0B,4DAAA,cAAoE;IAEhEA,uDAAA,cAEO;IAKTA,0DAAA,EAAM;;;;;IAIRA,4DAAA,cAGC;IAMGA,uDAAA,cAEO;IAOPA,4DAAA,cAAiB;IAMTA,oDAAA,oBACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,cAEC;IACCA,oDAAA,IACF;IAAAA,0DAAA,EAAM;IAERA,4DAAA,eAA2B;IACzBA,uDAAA,eAEO;IACPA,4DAAA,eAEC;IACCA,uDAAA,aAEK;IACPA,0DAAA,EAAM;IAGVA,4DAAA,eAEC;IACCA,uDAAA,eAEO;IACPA,4DAAA,eAGC;IACCA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IAMZA,4DAAA,eAEC;IAECA,uDAAA,eAEO;IAOPA,4DAAA,eAAiB;IAMTA,oDAAA,qBACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAyC;IAKnCA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IACNA,4DAAA,gBACG;IAAAA,oDAAA,IAA6B;IAAAA,0DAAA,EAC/B;IAEHA,4DAAA,eAA+B;IAC7BA,uDAAA,eAEO;IACPA,4DAAA,gBACG;IAAAA,oDAAA,IAAiC;IAAAA,0DAAA,EACnC;IAIPA,4DAAA,eAA2B;IACzBA,uDAAA,eAEO;IACPA,4DAAA,eAEC;IACCA,uDAAA,aAEK;IACPA,0DAAA,EAAM;IAGVA,4DAAA,eAEC;IACCA,uDAAA,eAEO;IACPA,4DAAA,eAAuC;IAOnCA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IACNA,4DAAA,eAKC;IACCA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IAOdA,4DAAA,eAEC;IAECA,uDAAA,eAEO;IAOPA,4DAAA,eAAiB;IAMTA,oDAAA,oBACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAA+C;IAE3CA,uDAAA,eAEO;IACPA,4DAAA,gBACG;IAAAA,oDAAA,IAAgC;IAAAA,0DAAA,EAClC;IAEHA,4DAAA,eAA+B;IAC7BA,uDAAA,eAEO;IACPA,4DAAA,gBACG;IAAAA,oDAAA,IAAgC;IAAAA,0DAAA,EAClC;IAEHA,4DAAA,eAA+B;IAC7BA,uDAAA,eAEO;IACPA,4DAAA,gBACG;IAAAA,oDAAA,IAA4B;IAAAA,0DAAA,EAC9B;IAIPA,4DAAA,eAA2B;IACzBA,uDAAA,eAEO;IACPA,4DAAA,eAEC;IACCA,uDAAA,aAEK;IACPA,0DAAA,EAAM;IAGVA,4DAAA,eAEC;IACCA,uDAAA,eAEO;IACPA,4DAAA,eAAuC;IAOnCA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IACNA,4DAAA,eAKC;IACCA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IACNA,4DAAA,eAKC;IACCA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IAGVA,4DAAA,eAEC;IACOA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAO;IACrBA,4DAAA,YAAM;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAO;IACrBA,4DAAA,YAAM;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAO;;;;IAtOfA,uDAAA,IACF;IADEA,gEAAA,MAAAiB,MAAA,CAAAC,KAAA,CAAAC,MAAA,MACF;IAiEOnB,uDAAA,IAA6B;IAA7BA,gEAAA,KAAAiB,MAAA,CAAAG,cAAA,cAA6B;IAQ7BpB,uDAAA,GAAiC;IAAjCA,gEAAA,KAAAiB,MAAA,CAAAI,gBAAA,gBAAiC;IA2BtCrB,uDAAA,GAEC;IAFDA,yDAAA,UAAAiB,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAG,cAAA,KAAAH,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;IAQDnB,uDAAA,GAEC;IAFDA,yDAAA,UAAAiB,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAI,gBAAA,KAAAJ,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;IAuCInB,uDAAA,IAAgC;IAAhCA,gEAAA,KAAAiB,MAAA,CAAAM,eAAA,gBAAgC;IAQhCvB,uDAAA,GAAgC;IAAhCA,gEAAA,KAAAiB,MAAA,CAAAO,eAAA,gBAAgC;IAQhCxB,uDAAA,GAA4B;IAA5BA,gEAAA,KAAAiB,MAAA,CAAAQ,aAAA,cAA4B;IA2BjCzB,uDAAA,GAEC;IAFDA,yDAAA,UAAAiB,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAM,eAAA,KAAAN,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;IAQDnB,uDAAA,GAEC;IAFDA,yDAAA,UAAAiB,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAO,eAAA,KAAAP,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;IAQDnB,uDAAA,GAEC;IAFDA,yDAAA,UAAAiB,MAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAF,MAAA,CAAAQ,aAAA,KAAAR,MAAA,CAAAC,KAAA,CAAAC,MAAA,gBAEC;;;;;IA+KGnB,4DAAA,kBAAkD;IAChDA,oDAAA,GACF;;IAAAA,0DAAA,EAAS;;;;IAF0BA,wDAAA,UAAA2B,QAAA,CAAc;IAC/C3B,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,OAAA2B,QAAA,OACF;;;;;;IA/ER3B,4DAAA,cAGC;IAMOA,uDAAA,eAEO;IACPA,4DAAA,cAA4B;IAAAA,oDAAA,GAE1B;IAAAA,0DAAA,EAAO;IAEXA,4DAAA,eAAkB;IAIdA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IAIZA,4DAAA,eAAwC;IAIpCA,oDAAA,IACF;IAAAA,0DAAA,EAAM;IAERA,4DAAA,eAAoD;IAShDA,uDAAA,SAMK;IACLA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAETA,4DAAA,eAAoD;IAShDA,uDAAA,SAMK;IACLA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAETA,4DAAA,eAAoD;IAK9CA,wDAAA,oBAAA6B,mEAAAC,MAAA;MAAA,MAAAC,WAAA,GAAA/B,2DAAA,CAAAgC,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAnC,2DAAA;MAAA,OAAUA,yDAAA,CAAAmC,OAAA,CAAAC,YAAA,CAAAH,QAAA,CAAAI,GAAA,EAAAP,MAAA,CAAAQ,MAAA,CAAAjF,KAAA,CAAiD;IAAA,EAAC;IAE5D2C,wDAAA,KAAAwC,kDAAA,sBAES;IACXxC,0DAAA,EAAS;IACTA,4DAAA,gBAEC;IACCA,uDAAA,cAA2C;IAC7CA,0DAAA,EAAM;IAGVA,4DAAA,eAAoD;IAQhDA,wDAAA,mBAAAyC,kEAAA;MAAA,MAAAV,WAAA,GAAA/B,2DAAA,CAAAgC,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAQ,OAAA,GAAA1C,2DAAA;MAAA,OACuBA,yDAAA,CAAA0C,OAAA,CAAAC,oBAAA,CAAAV,QAAA,CAAAI,GAAA,EAAAJ,QAAA,CAAAW,QAAA,KAC3B,KAAK,CACnB;IAAA,EADmB;IAQD5C,uDAAA,gBAMM;IAUNA,4DAAA,eAA4B;IAC1BA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAGXA,4DAAA,eAAoD;IAGhDA,wDAAA,mBAAA6C,kEAAA;MAAA,MAAAd,WAAA,GAAA/B,2DAAA,CAAAgC,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAY,OAAA,GAAA9C,2DAAA;MAAA,OAASA,yDAAA,CAAA8C,OAAA,CAAAC,YAAA,CAAAd,QAAA,CAAAI,GAAA,CAAsB;IAAA,EAAC;IAGhCrC,uDAAA,cAAuC;IACvCA,oDAAA,gBACF;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,eAAoD;IAGhDA,wDAAA,mBAAAgD,kEAAA;MAAA,MAAAjB,WAAA,GAAA/B,2DAAA,CAAAgC,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAe,OAAA,GAAAjD,2DAAA;MAAA,OAASA,yDAAA,CAAAiD,OAAA,CAAAC,eAAA,CAAAjB,QAAA,CAAAI,GAAA,CAAyB;IAAA,EAAC;IAEnCrC,uDAAA,cAAiC;IACjCA,oDAAA,iBACF;IAAAA,0DAAA,EAAS;;;;;IApIuBA,uDAAA,GAE1B;IAF0BA,+DAAA,CAAAiC,QAAA,CAAAmB,QAAA,CAAAC,MAAA,IAE1B;IAMArD,uDAAA,GACF;IADEA,gEAAA,MAAAiC,QAAA,CAAAmB,QAAA,MACF;IAQFpD,uDAAA,GACF;IADEA,gEAAA,MAAAiC,QAAA,CAAAqB,KAAA,MACF;IAKEtD,uDAAA,GAIC;IAJDA,wDAAA,YAAAiC,QAAA,CAAAsB,QAAA,uJAIC;IAGCvD,uDAAA,GAIC;IAJDA,wDAAA,CAAAiC,QAAA,CAAAsB,QAAA,2DAIC;IAEHvD,uDAAA,GACF;IADEA,gEAAA,MAAAiC,QAAA,CAAAsB,QAAA,qBACF;IAKEvD,uDAAA,GAIC;IAJDA,wDAAA,YAAAiC,QAAA,CAAAW,QAAA,6OAIC;IAGC5C,uDAAA,GAIC;IAJDA,wDAAA,CAAAiC,QAAA,CAAAW,QAAA,iGAIC;IAEH5C,uDAAA,GACF;IADEA,gEAAA,MAAAiC,QAAA,CAAAW,QAAA,2CACF;IAMI5C,uDAAA,GAAmB;IAAnBA,wDAAA,UAAAiC,QAAA,CAAAwB,IAAA,CAAmB;IAGMzD,uDAAA,GAAQ;IAARA,wDAAA,YAAA0D,MAAA,CAAAC,KAAA,CAAQ;IAcnC3D,uDAAA,GAIC;IAJDA,wDAAA,YAAAiC,QAAA,CAAAW,QAAA,+VAIC,UAAAX,QAAA,CAAAW,QAAA;IAYI5C,uDAAA,GAIC;IAJDA,wDAAA,YAAAiC,QAAA,CAAAW,QAAA,uHAIC;IAKJ5C,uDAAA,GAIC;IAJDA,wDAAA,CAAAiC,QAAA,CAAAW,QAAA,uDAIC;IAGD5C,uDAAA,GACF;IADEA,gEAAA,MAAAiC,QAAA,CAAAW,QAAA,4CACF;;;;;IA1Md5C,4DAAA,cAGC;IAECA,uDAAA,cAEO;IAEPA,4DAAA,cAEC;IAIGA,uDAAA,YAAqC;IACrCA,oDAAA,wBACF;IAAAA,0DAAA,EAAK;IAEPA,4DAAA,cAA6B;IAUnBA,oDAAA,cACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAGC;IACCA,oDAAA,eACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAGC;IACCA,oDAAA,kBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAGC;IACCA,oDAAA,gBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAGC;IACCA,oDAAA,cACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAGC;IACCA,oDAAA,kBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAGC;IACCA,oDAAA,gBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAGC;IACCA,oDAAA,iBACF;IAAAA,0DAAA,EAAK;IAGTA,4DAAA,kBAEC;IACCA,wDAAA,KAAA4D,wCAAA,oBAkJK;IACP5D,0DAAA,EAAQ;;;;IAlJaA,uDAAA,IAAgB;IAAhBA,wDAAA,YAAA6D,MAAA,CAAAC,aAAA,CAAgB;;;;;;IAwMvC9D,4DAAA,eAAqC;IAEjCA,wDAAA,mBAAA+D,kEAAA;MAAA/D,2DAAA,CAAAgE,IAAA;MAAA,MAAAC,OAAA,GAAAjE,2DAAA;MAAA,OAASA,yDAAA,CAAAiE,OAAA,CAAAxD,WAAA,EAAa;IAAA,EAAC;IAGvBT,uDAAA,eAEO;IACPA,4DAAA,eAEC;IACCA,uDAAA,aAAwC;IACxCA,oDAAA,qBACF;IAAAA,0DAAA,EAAM;;;;;IA7DdA,4DAAA,cAGC;IAECA,uDAAA,cAEO;IAEPA,4DAAA,eAA8B;IAG1BA,uDAAA,eAEO;IAGPA,4DAAA,eAEC;IACCA,uDAAA,aAEK;IACPA,0DAAA,EAAM;IAGNA,uDAAA,eAEO;IAITA,0DAAA,EAAM;IAENA,4DAAA,cAEC;IACCA,oDAAA,wBACF;IAAAA,0DAAA,EAAK;IAELA,4DAAA,cAAuE;IACrEA,oDAAA,IAKF;IAAAA,0DAAA,EAAI;IAEJA,wDAAA,KAAAkE,yCAAA,mBAeM;IACRlE,0DAAA,EAAM;;;;IAvBFA,uDAAA,IAKF;IALEA,gEAAA,MAAAmE,MAAA,CAAAC,UAAA,yFAKF;IAEmBpE,uDAAA,GAAgB;IAAhBA,wDAAA,SAAAmE,MAAA,CAAAC,UAAA,CAAgB;;;ADrqBrC,MAAO7E,kBAAkB;EAU7BlD,YACUgI,WAAwB,EACxBC,MAAc,EACdC,YAA0B;IAF1B,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAZtB,KAAArD,KAAK,GAAU,EAAE;IACjB,KAAA5C,KAAK,GAAG,EAAE;IACV,KAAAZ,OAAO,GAAG,EAAE;IACZ,KAAAiG,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IACvC,KAAAa,OAAO,GAAG,IAAI;IACd,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAL,UAAU,GAAG,EAAE;IACf,KAAAN,aAAa,GAAU,EAAE;EAMtB;EAEHY,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE5C,IAAI,CAACF,KAAK,IAAI,CAACG,OAAO,EAAE;MACtB,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACtC;;IAGF,IAAI,CAACP,WAAW,GAAGQ,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;IAEtC;IACA,IAAI,IAAI,CAACN,WAAW,CAAChB,IAAI,KAAK,OAAO,EAAE;MACrC,IAAI,CAACa,MAAM,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3B;;IAGF,IAAI,CAACX,WAAW,CAACc,WAAW,CAACP,KAAK,CAAC,CAACQ,SAAS,CAAC;MAC5C9H,IAAI,EAAG+H,GAAQ,IAAI;QACjB,IAAI,CAACnE,KAAK,GAAGmE,GAAG;QAChB,IAAI,CAACvB,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC5C,KAAK,CAAC;QACpC,IAAI,CAACsD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDlG,KAAK,EAAGgH,GAAG,IAAI;QACb,IAAI,CAAChH,KAAK,GAAGgH,GAAG,CAAChH,KAAK,EAAEZ,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAAC8G,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EACAe,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACnB,UAAU,CAACoB,IAAI,EAAE,EAAE;MAC3B,IAAI,CAAC1B,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC5C,KAAK,CAAC;MACpC;;IAGF,MAAMuE,IAAI,GAAG,IAAI,CAACrB,UAAU,CAACsB,WAAW,EAAE,CAACF,IAAI,EAAE;IACjD,IAAI,CAAC1B,aAAa,GAAG,IAAI,CAAC5C,KAAK,CAAChD,MAAM,CACnCyH,IAAI,IACHA,IAAI,CAACvC,QAAQ,CAACsC,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAC,IAC1CE,IAAI,CAACrC,KAAK,CAACoC,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAC,IACvCE,IAAI,CAAClC,IAAI,CAACiC,WAAW,EAAE,CAACE,QAAQ,CAACH,IAAI,CAAC,CACzC;EACH;EACAhF,WAAWA,CAAA;IACT,IAAI,CAAC2D,UAAU,GAAG,EAAE;IACpB,IAAI,CAACN,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC5C,KAAK,CAAC;EACtC;EAEA2E,YAAYA,CAAA;IACV,IAAI,CAACN,WAAW,EAAE;EACpB;EAEAnD,YAAYA,CAAC0D,MAAc,EAAEC,OAAe;IAC1C,MAAMnB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACT,WAAW,CAAC2B,cAAc,CAACF,MAAM,EAAEC,OAAO,EAAEnB,KAAM,CAAC,CAACQ,SAAS,CAAC;MACjE9H,IAAI,EAAG+H,GAAQ,IAAI;QACjB,IAAI,CAAC3H,OAAO,GAAG2H,GAAG,CAAC3H,OAAO;QAC1B,IAAI,CAACY,KAAK,GAAG,EAAE;QAEf;QACA,MAAM2H,SAAS,GAAG,IAAI,CAAC/E,KAAK,CAACgF,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC9D,GAAG,KAAKyD,MAAM,CAAC;QAC/D,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;UACpB,IAAI,CAAC/E,KAAK,CAAC+E,SAAS,CAAC,CAACxC,IAAI,GAAGsC,OAAO;;QAGtC,MAAMK,aAAa,GAAG,IAAI,CAACtC,aAAa,CAACoC,SAAS,CAC/CC,CAAC,IAAKA,CAAC,CAAC9D,GAAG,KAAKyD,MAAM,CACxB;QACD,IAAIM,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAI,CAACtC,aAAa,CAACsC,aAAa,CAAC,CAAC3C,IAAI,GAAGsC,OAAO;;QAGlD;QACAxI,UAAU,CAAC,MAAK;UACd,IAAI,CAACG,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDY,KAAK,EAAGgH,GAAG,IAAI;QACb,IAAI,CAAChH,KAAK,GAAGgH,GAAG,CAAChH,KAAK,EAAEZ,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACA,OAAO,GAAG,EAAE;QAEjB;QACAH,UAAU,CAAC,MAAK;UACd,IAAI,CAACe,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EACAyE,YAAYA,CAAC+C,MAAc;IACzB,MAAMO,aAAa,GAAGC,OAAO,CAAC,4CAA4C,CAAC;IAC3E,IAAI,CAACD,aAAa,EAAE;IAEpB,MAAMzB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACT,WAAW,CAACkC,UAAU,CAACT,MAAM,EAAElB,KAAM,CAAC,CAACQ,SAAS,CAAC;MACpD9H,IAAI,EAAG+H,GAAQ,IAAI;QACjB,IAAI,CAAC3H,OAAO,GAAG2H,GAAG,CAAC3H,OAAO;QAC1B,IAAI,CAACY,KAAK,GAAG,EAAE;QAEf;QACA,IAAI,CAAC4C,KAAK,GAAG,IAAI,CAACA,KAAK,CAAChD,MAAM,CAAEiI,CAAC,IAAKA,CAAC,CAAC9D,GAAG,KAAKyD,MAAM,CAAC;QACvD,IAAI,CAAChC,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC5F,MAAM,CAAEiI,CAAC,IAAKA,CAAC,CAAC9D,GAAG,KAAKyD,MAAM,CAAC;QAEvE;QACAvI,UAAU,CAAC,MAAK;UACd,IAAI,CAACG,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDY,KAAK,EAAGgH,GAAG,IAAI;QACb,IAAI,CAAChH,KAAK,GAAGgH,GAAG,CAAChH,KAAK,EAAEZ,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACA,OAAO,GAAG,EAAE;QAEjB;QACAH,UAAU,CAAC,MAAK;UACd,IAAI,CAACe,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EACAqE,oBAAoBA,CAACmD,MAAc,EAAEU,aAAsB;IACzD,MAAMC,SAAS,GAAG,CAACD,aAAa;IAChC,MAAMjI,MAAM,GAAGkI,SAAS,GAAG,UAAU,GAAG,YAAY;IAEpD;IACA,MAAMd,IAAI,GAAG,IAAI,CAACzE,KAAK,CAACwF,IAAI,CAACP,CAAC,IAAIA,CAAC,CAAC9D,GAAG,KAAKyD,MAAM,CAAC;IACnD,MAAMa,QAAQ,GAAGhB,IAAI,EAAEvC,QAAQ,IAAIuC,IAAI,EAAEiB,SAAS,IAAI,MAAM;IAE5D,MAAMC,aAAa,GAAGP,OAAO,CAC3B,4BAA4B/H,MAAM,IAAIoI,QAAQ,GAAG,CAClD;IACD,IAAI,CAACE,aAAa,EAAE;IAEpB,MAAMjC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACT,WAAW,CAAC1B,oBAAoB,CAACmD,MAAM,EAAEW,SAAS,EAAE7B,KAAM,CAAC,CAACQ,SAAS,CAAC;MACzE9H,IAAI,EAAG+H,GAAQ,IAAI;QACjB,MAAMyB,UAAU,GAAGL,SAAS,GAAG,WAAW,GAAG,aAAa;QAC1D,MAAMM,cAAc,GAAG,GAAGJ,QAAQ,aAAaG,UAAU,eAAe;QAExE;QACA,IAAI,CAACvC,YAAY,CAACzG,WAAW,CAACiJ,cAAc,CAAC;QAE7C;QACA,IAAI,CAACrJ,OAAO,GAAG,EAAE;QACjB,IAAI,CAACY,KAAK,GAAG,EAAE;QAEf;QACA,MAAM2H,SAAS,GAAG,IAAI,CAAC/E,KAAK,CAACgF,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC9D,GAAG,KAAKyD,MAAM,CAAC;QAC/D,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;UACpB,IAAI,CAAC/E,KAAK,CAAC+E,SAAS,CAAC,CAACrD,QAAQ,GAAG6D,SAAS;;QAG5C,MAAML,aAAa,GAAG,IAAI,CAACtC,aAAa,CAACoC,SAAS,CAC/CC,CAAC,IAAKA,CAAC,CAAC9D,GAAG,KAAKyD,MAAM,CACxB;QACD,IAAIM,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAI,CAACtC,aAAa,CAACsC,aAAa,CAAC,CAACxD,QAAQ,GAAG6D,SAAS;;QAGxD;QACA,IAAI,CAACZ,YAAY,EAAE;MACrB,CAAC;MACDvH,KAAK,EAAGgH,GAAG,IAAI;QACb,MAAMwB,UAAU,GAAGL,SAAS,GAAG,UAAU,GAAG,YAAY;QACxD,MAAMO,YAAY,GAAG1B,GAAG,CAAChH,KAAK,EAAEZ,OAAO,IAAI,aAAaoJ,UAAU,IAAIH,QAAQ,EAAE;QAEhF;QACA,IAAI,CAACpC,YAAY,CAACxG,SAAS,CAACiJ,YAAY,CAAC;QAEzC;QACA,IAAI,CAACtJ,OAAO,GAAG,EAAE;QACjB,IAAI,CAACY,KAAK,GAAG,EAAE;MACjB;KACD,CAAC;EACJ;EACAiD,eAAeA,CAAA;IACb,OAAO,IAAI,CAACL,KAAK,CAAChD,MAAM,CAAEiI,CAAC,IAAKA,CAAC,CAAC1C,IAAI,KAAK,SAAS,CAAC,CAACtC,MAAM;EAC9D;EACAK,eAAeA,CAAA;IACb,OAAO,IAAI,CAACN,KAAK,CAAChD,MAAM,CAAEiI,CAAC,IAAKA,CAAC,CAAC1C,IAAI,KAAK,SAAS,CAAC,CAACtC,MAAM;EAC9D;EACAM,aAAaA,CAAA;IACX,OAAO,IAAI,CAACP,KAAK,CAAChD,MAAM,CAAEiI,CAAC,IAAKA,CAAC,CAAC1C,IAAI,KAAK,OAAO,CAAC,CAACtC,MAAM;EAC5D;EACAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACF,KAAK,CAAChD,MAAM,CAAEiI,CAAC,IAAKA,CAAC,CAACvD,QAAQ,KAAK,KAAK,CAAC,CAACzB,MAAM;EAC9D;EACAE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACH,KAAK,CAAChD,MAAM,CAAEiI,CAAC,IAAKA,CAAC,CAACvD,QAAQ,KAAK,KAAK,CAAC,CAACzB,MAAM;EAC9D;EAEA8F,MAAMA,CAAA;IACJ,IAAI,CAAC5C,WAAW,CAAC4C,MAAM,EAAE;IACzB,IAAI,CAAC3C,MAAM,CAACU,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAEA9B,eAAeA,CAAC4C,MAAc;IAC5B,IAAI,CAACxB,MAAM,CAACU,QAAQ,CAAC,CAAC,oBAAoB,EAAEc,MAAM,CAAC,CAAC;EACtD;;;uBAzNWvG,kBAAkB,EAAAS,+DAAA,CAAAF,sEAAA,GAAAE,+DAAA,CAAAoH,mDAAA,GAAApH,+DAAA,CAAAsH,wEAAA;IAAA;EAAA;;;YAAlB/H,kBAAkB;MAAAgI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/B7H,4DAAA,aAEC;UAGGA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAKVA,4DAAA,cAA2B;UASnBA,oDAAA,yBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAAsD;UACpDA,oDAAA,0CACF;UAAAA,0DAAA,EAAI;UAINA,4DAAA,eAAwD;UAIpDA,uDAAA,aAEK;UACPA,0DAAA,EAAM;UACNA,4DAAA,iBAME;UAHAA,wDAAA,mBAAA+H,oDAAAjG,MAAA;YAAAgG,GAAA,CAAA1D,UAAA,GAAAtC,MAAA,CAAAQ,MAAA,CAAAjF,KAAA;YAAA,OAAiDyK,GAAA,CAAAvC,WAAA,EAAa;UAAA,EAAC;UAHjEvF,0DAAA,EAME;UACFA,4DAAA,eAEC;UACCA,uDAAA,eAEO;UACTA,0DAAA,EAAM;UACNA,wDAAA,KAAAgI,qCAAA,qBAMS;UACXhI,0DAAA,EAAM;UAIRA,wDAAA,KAAAiI,kCAAA,mBAqBM;UAENjI,wDAAA,KAAAkI,kCAAA,mBAqBM;UAGNlI,wDAAA,KAAAmI,kCAAA,kBAUM;UAGNnI,wDAAA,KAAAoI,kCAAA,oBAuQM;UAGNpI,wDAAA,KAAAqI,kCAAA,mBAoOM;UAGNrI,wDAAA,KAAAsI,kCAAA,mBAiEM;UACRtI,0DAAA,EAAM;;;UAtoBEA,uDAAA,IAAoB;UAApBA,wDAAA,UAAA8H,GAAA,CAAA1D,UAAA,CAAoB;UAanBpE,uDAAA,GAAgB;UAAhBA,wDAAA,SAAA8H,GAAA,CAAA1D,UAAA,CAAgB;UAWpBpE,uDAAA,GAAa;UAAbA,wDAAA,SAAA8H,GAAA,CAAApK,OAAA,CAAa;UAuBbsC,uDAAA,GAAW;UAAXA,wDAAA,SAAA8H,GAAA,CAAAxJ,KAAA,CAAW;UAuBR0B,uDAAA,GAAa;UAAbA,wDAAA,SAAA8H,GAAA,CAAAtD,OAAA,CAAa;UAchBxE,uDAAA,GAA0C;UAA1CA,wDAAA,UAAA8H,GAAA,CAAAtD,OAAA,IAAAsD,GAAA,CAAAhE,aAAA,CAAA3C,MAAA,KAA0C;UA0Q1CnB,uDAAA,GAA0C;UAA1CA,wDAAA,UAAA8H,GAAA,CAAAtD,OAAA,IAAAsD,GAAA,CAAAhE,aAAA,CAAA3C,MAAA,KAA0C;UAuO1CnB,uDAAA,GAA4C;UAA5CA,wDAAA,UAAA8H,GAAA,CAAAtD,OAAA,IAAAsD,GAAA,CAAAhE,aAAA,CAAA3C,MAAA,OAA4C;;;;;;;;;;;;;;;;;;;;;;;;;AC/nBJ;AAEqB;AACT;;AAYrD,MAAOqH,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAJxBD,yDAAY,EACZ5I,6EAAsB;IAAA;EAAA;;;sHAGb6I,eAAe;IAAAC,YAAA,GAPxBlJ,oEAAkB;IAAAM,OAAA,GAGlB0I,yDAAY,EACZ5I,6EAAsB;EAAA;AAAA", "sources": ["./src/app/services/toast.service.ts", "./src/app/views/admin/dashboard/dashboard-routing.module.ts", "./src/app/views/admin/dashboard/dashboard.component.ts", "./src/app/views/admin/dashboard/dashboard.component.html", "./src/app/views/admin/dashboard/dashboard.module.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { Toast } from 'src/app/models/message.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ToastService {\n  private toastsSubject = new BehaviorSubject<Toast[]>([]);\n  toasts$ = this.toastsSubject.asObservable();\n  private currentId = 0;\n\n  constructor() {}\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private addToast(toast: Omit<Toast, 'id'>): void {\n    const newToast: Toast = {\n      ...toast,\n      id: this.generateId(),\n      duration: toast.duration || 5000,\n    };\n\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, newToast]);\n\n    // Auto-remove toast after duration\n    if (newToast.duration && newToast.duration > 0) {\n      setTimeout(() => {\n        this.removeToast(newToast.id);\n      }, newToast.duration);\n    }\n  }\n  show(\n    message: string,\n    type: 'success' | 'error' | 'warning' | 'info' = 'info',\n    duration = 5000\n  ) {\n    const id = this.generateId();\n    const toast: Toast = { id, type, title: '', message, duration };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, toast]);\n\n    if (duration > 0) {\n      setTimeout(() => this.dismiss(id), duration);\n    }\n  }\n\n  showSuccess(message: string, duration = 3000) {\n    this.show(message, 'success', duration);\n  }\n\n  showError(message: string, duration = 5000) {\n    this.show(message, 'error', duration);\n  }\n\n  showWarning(message: string, duration = 4000) {\n    this.show(message, 'warning', duration);\n  }\n\n  showInfo(message: string, duration = 3000) {\n    this.show(message, 'info', duration);\n  }\n\n  dismiss(id: string) {\n    const currentToasts = this.toastsSubject.value.filter((t) => t.id !== id);\n    this.toastsSubject.next(currentToasts);\n  }\n  success(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'success',\n      title,\n      message,\n      duration,\n      icon: 'check-circle',\n    });\n  }\n  error(\n    title: string,\n    message: string,\n    duration?: number,\n    action?: Toast['action']\n  ): void {\n    this.addToast({\n      type: 'error',\n      title,\n      message,\n      duration: duration || 8000, // Longer duration for errors\n      icon: 'x-circle',\n      action,\n    });\n  }\n\n  warning(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'warning',\n      title,\n      message,\n      duration,\n      icon: 'exclamation-triangle',\n    });\n  }\n  // Méthodes spécifiques pour les erreurs d'autorisation\n  accessDenied(action: string = 'effectuer cette action', code?: number): void {\n    const codeText = code ? ` (Code: ${code})` : '';\n    this.error(\n      'Accès refusé',\n      `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`,\n      8000,\n      {\n        label: 'Comprendre les rôles',\n        handler: () => {\n          // Optionnel: rediriger vers une page d'aide\n          console.log(\"Redirection vers l'aide sur les rôles\");\n        },\n      }\n    );\n  }\n\n  ownershipRequired(resource: string = 'cette ressource'): void {\n    this.error(\n      'Propriétaire requis',\n      `Seul le propriétaire ou un administrateur peut modifier ${resource}`,\n      8000\n    );\n  }\n\n  removeToast(id: string): void {\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next(currentToasts.filter((toast) => toast.id !== id));\n  }\n  clear() {\n    this.toastsSubject.next([]);\n  }\n}\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { DashboardComponent } from './dashboard.component';\n\nconst routes: Routes = [{ path: '', component: DashboardComponent }];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class DashboardRoutingModule {}\n", "import { Component, OnInit } from '@angular/core';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css'],\n})\nexport class DashboardComponent implements OnInit {\n  users: any[] = [];\n  error = '';\n  message = '';\n  roles = ['student', 'teacher', 'admin'];\n  loading = true;\n  currentUser: any = null;\n  searchTerm = '';\n  filteredUsers: any[] = [];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n  }\n\n  loadUserData(): void {\n    this.loading = true;\n    const token = localStorage.getItem('token');\n    const userStr = localStorage.getItem('user');\n\n    if (!token || !userStr) {\n      this.router.navigate(['/admin/login']);\n      return;\n    }\n\n    this.currentUser = JSON.parse(userStr);\n\n    // Check if user is admin\n    if (this.currentUser.role !== 'admin') {\n      this.router.navigate(['/']);\n      return;\n    }\n\n    this.authService.getAllUsers(token).subscribe({\n      next: (res: any) => {\n        this.users = res;\n        this.filteredUsers = [...this.users];\n        this.loading = false;\n      },\n      error: (err) => {\n        this.error = err.error?.message || 'Failed to fetch users';\n        this.loading = false;\n      },\n    });\n  }\n  searchUsers(): void {\n    if (!this.searchTerm.trim()) {\n      this.filteredUsers = [...this.users];\n      return;\n    }\n\n    const term = this.searchTerm.toLowerCase().trim();\n    this.filteredUsers = this.users.filter(\n      (user) =>\n        user.fullName.toLowerCase().includes(term) ||\n        user.email.toLowerCase().includes(term) ||\n        user.role.toLowerCase().includes(term)\n    );\n  }\n  clearSearch(): void {\n    this.searchTerm = '';\n    this.filteredUsers = [...this.users];\n  }\n\n  applyFilters(): void {\n    this.searchUsers();\n  }\n\n  onRoleChange(userId: string, newRole: string) {\n    const token = localStorage.getItem('token');\n    this.authService.updateUserRole(userId, newRole, token!).subscribe({\n      next: (res: any) => {\n        this.message = res.message;\n        this.error = '';\n\n        // Update the user in the local arrays\n        const userIndex = this.users.findIndex((u) => u._id === userId);\n        if (userIndex !== -1) {\n          this.users[userIndex].role = newRole;\n        }\n\n        const filteredIndex = this.filteredUsers.findIndex(\n          (u) => u._id === userId\n        );\n        if (filteredIndex !== -1) {\n          this.filteredUsers[filteredIndex].role = newRole;\n        }\n\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: (err) => {\n        this.error = err.error?.message || 'Failed to update role';\n        this.message = '';\n\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      },\n    });\n  }\n  onDeleteUser(userId: string) {\n    const confirmDelete = confirm('Are you sure you want to delete this user?');\n    if (!confirmDelete) return;\n\n    const token = localStorage.getItem('token');\n    this.authService.deleteUser(userId, token!).subscribe({\n      next: (res: any) => {\n        this.message = res.message;\n        this.error = '';\n\n        // Remove user from both arrays\n        this.users = this.users.filter((u) => u._id !== userId);\n        this.filteredUsers = this.filteredUsers.filter((u) => u._id !== userId);\n\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: (err) => {\n        this.error = err.error?.message || 'Failed to delete user';\n        this.message = '';\n\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      },\n    });\n  }\n  toggleUserActivation(userId: string, currentStatus: boolean) {\n    const newStatus = !currentStatus;\n    const action = newStatus ? 'activate' : 'deactivate';\n\n    // Find the user to get their name for better messaging\n    const user = this.users.find(u => u._id === userId);\n    const userName = user?.fullName || user?.firstName || 'User';\n\n    const confirmAction = confirm(\n      `Are you sure you want to ${action} ${userName}?`\n    );\n    if (!confirmAction) return;\n\n    const token = localStorage.getItem('token');\n    this.authService.toggleUserActivation(userId, newStatus, token!).subscribe({\n      next: (res: any) => {\n        const statusText = newStatus ? 'activated' : 'deactivated';\n        const successMessage = `${userName} has been ${statusText} successfully`;\n\n        // Show success toast\n        this.toastService.showSuccess(successMessage);\n\n        // Clear any existing messages\n        this.message = '';\n        this.error = '';\n\n        // Update user in both arrays\n        const userIndex = this.users.findIndex((u) => u._id === userId);\n        if (userIndex !== -1) {\n          this.users[userIndex].isActive = newStatus;\n        }\n\n        const filteredIndex = this.filteredUsers.findIndex(\n          (u) => u._id === userId\n        );\n        if (filteredIndex !== -1) {\n          this.filteredUsers[filteredIndex].isActive = newStatus;\n        }\n\n        // Apply filters to refresh the view\n        this.applyFilters();\n      },\n      error: (err) => {\n        const statusText = newStatus ? 'activate' : 'deactivate';\n        const errorMessage = err.error?.message || `Failed to ${statusText} ${userName}`;\n\n        // Show error toast\n        this.toastService.showError(errorMessage);\n\n        // Clear any existing messages\n        this.message = '';\n        this.error = '';\n      },\n    });\n  }\n  getStudentCount(): number {\n    return this.users.filter((u) => u.role === 'student').length;\n  }\n  getTeacherCount(): number {\n    return this.users.filter((u) => u.role === 'teacher').length;\n  }\n  getAdminCount(): number {\n    return this.users.filter((u) => u.role === 'admin').length;\n  }\n  getActiveCount(): number {\n    return this.users.filter((u) => u.isActive !== false).length;\n  }\n  getInactiveCount(): number {\n    return this.users.filter((u) => u.isActive === false).length;\n  }\n\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/admin/login']);\n  }\n\n  showUserDetails(userId: string) {\n    this.router.navigate(['/admin/userdetails', userId]);\n  }\n}\n", "<!-- Begin Page Content -->\n<div\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen relative\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Page Content -->\n  <div class=\"relative z-10\">\n    <!-- Page Heading -->\n    <div\n      class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\"\n    >\n      <div>\n        <h1\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\"\n        >\n          Admin Dashboard\n        </h1>\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n          Manage users and system settings\n        </p>\n      </div>\n\n      <!-- Search Box -->\n      <div class=\"relative w-full md:w-72 mt-4 md:mt-0 group\">\n        <div\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n        >\n          <i\n            class=\"fas fa-search text-[#bdc6cc] dark:text-[#6d6870] group-focus-within:text-[#4f5fad] dark:group-focus-within:text-[#6d78c9] transition-colors\"\n          ></i>\n        </div>\n        <input\n          type=\"text\"\n          [value]=\"searchTerm\"\n          (input)=\"searchTerm = $any($event.target).value; searchUsers()\"\n          placeholder=\"Search users...\"\n          class=\"w-full pl-10 pr-10 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n        />\n        <div\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n        >\n          <div\n            class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n          ></div>\n        </div>\n        <button\n          *ngIf=\"searchTerm\"\n          (click)=\"clearSearch()\"\n          class=\"absolute inset-y-0 right-0 pr-3 flex items-center text-[#bdc6cc] dark:text-[#6d6870] hover:text-[#ff6b69] dark:hover:text-[#ff8785] transition-colors\"\n        >\n          <i class=\"fas fa-times-circle\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Alerts -->\n    <div\n      *ngIf=\"message\"\n      class=\"bg-[#afcf75]/10 dark:bg-[#afcf75]/5 border border-[#afcf75] dark:border-[#afcf75]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm\"\n    >\n      <div class=\"flex items-start\">\n        <div class=\"text-[#2a5a03] dark:text-[#afcf75] mr-3 text-xl relative\">\n          <i class=\"fas fa-check-circle\"></i>\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#afcf75]/20 dark:bg-[#afcf75]/20 blur-xl rounded-full transform scale-150 -z-10\"\n          ></div>\n        </div>\n        <div class=\"flex-1\">\n          <h3 class=\"font-medium text-[#2a5a03] dark:text-[#afcf75] mb-1\">\n            Success\n          </h3>\n          <p class=\"text-sm text-[#2a5a03]/80 dark:text-[#afcf75]/80\">\n            {{ message }}\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <div\n      *ngIf=\"error\"\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl mb-6 backdrop-blur-sm\"\n    >\n      <div class=\"flex items-start\">\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\n          ></div>\n        </div>\n        <div class=\"flex-1\">\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\n            Error\n          </h3>\n          <p class=\"text-sm text-[#ff6b69]/80 dark:text-[#ff8785]/80\">\n            {{ error }}\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div *ngIf=\"loading\" class=\"flex justify-center items-center py-20\">\n      <div class=\"relative\">\n        <div\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\n        ></div>\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div\n      *ngIf=\"!loading && filteredUsers.length > 0\"\n      class=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\"\n    >\n      <!-- Total Users Card -->\n      <div\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative group hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1\"\n      >\n        <!-- Decorative gradient top border -->\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n        ></div>\n\n        <!-- Glow effect on hover -->\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n        ></div>\n\n        <div class=\"p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-1\">\n              <div\n                class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\"\n              >\n                Total Users\n              </div>\n              <div\n                class=\"mt-1 text-2xl font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n              >\n                {{ users.length }}\n              </div>\n            </div>\n            <div class=\"ml-4 relative\">\n              <div\n                class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-150\"\n              ></div>\n              <div\n                class=\"relative z-10 bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 rounded-full p-2.5 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300\"\n              >\n                <i\n                  class=\"fas fa-users text-[#4f5fad] dark:text-[#6d78c9] text-xl\"\n                ></i>\n              </div>\n            </div>\n          </div>\n          <div\n            class=\"mt-4 w-full bg-[#edf1f4] dark:bg-[#2a2a2a] rounded-full h-2.5 overflow-hidden relative\"\n          >\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md\"\n            ></div>\n            <div\n              class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] h-2.5 rounded-full relative z-10\"\n              style=\"width: 100%\"\n            >\n              <div\n                class=\"absolute inset-0 bg-[#00f7ff]/20 rounded-full animate-pulse\"\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- User Status Card -->\n      <div\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative group hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1\"\n      >\n        <!-- Decorative gradient top border -->\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75]\"\n        ></div>\n\n        <!-- Glow effect on hover -->\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n        ></div>\n\n        <div class=\"p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-1\">\n              <div\n                class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\"\n              >\n                User Status\n              </div>\n              <div class=\"mt-1 flex space-x-4 text-sm\">\n                <div class=\"flex items-center\">\n                  <div\n                    class=\"w-2 h-2 rounded-full bg-[#afcf75] dark:bg-[#afcf75] mr-1.5 relative\"\n                  >\n                    <div\n                      class=\"absolute inset-0 bg-[#afcf75] rounded-full animate-ping opacity-75\"\n                    ></div>\n                  </div>\n                  <span class=\"text-[#2a5a03] dark:text-[#afcf75] font-medium\"\n                    >{{ getActiveCount() }} Active</span\n                  >\n                </div>\n                <div class=\"flex items-center\">\n                  <div\n                    class=\"w-2 h-2 rounded-full bg-[#ff6b69] dark:bg-[#ff8785] mr-1.5\"\n                  ></div>\n                  <span class=\"text-[#ff6b69] dark:text-[#ff8785] font-medium\"\n                    >{{ getInactiveCount() }} Inactive</span\n                  >\n                </div>\n              </div>\n            </div>\n            <div class=\"ml-4 relative\">\n              <div\n                class=\"absolute inset-0 bg-[#afcf75]/10 dark:bg-[#afcf75]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-150\"\n              ></div>\n              <div\n                class=\"relative z-10 bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 rounded-full p-2.5 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300\"\n              >\n                <i\n                  class=\"fas fa-check-circle text-[#afcf75] dark:text-[#afcf75] text-xl\"\n                ></i>\n              </div>\n            </div>\n          </div>\n          <div\n            class=\"mt-4 w-full bg-[#edf1f4] dark:bg-[#2a2a2a] rounded-full h-2.5 overflow-hidden relative\"\n          >\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#2a5a03]/10 to-[#afcf75]/10 dark:from-[#2a5a03]/10 dark:to-[#afcf75]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md\"\n            ></div>\n            <div class=\"flex h-full relative z-10\">\n              <div\n                class=\"bg-gradient-to-r from-[#2a5a03] to-[#afcf75] dark:from-[#2a5a03] dark:to-[#afcf75] h-2.5 relative\"\n                [style.width.%]=\"\n                  users.length ? (getActiveCount() / users.length) * 100 : 0\n                \"\n              >\n                <div\n                  class=\"absolute inset-0 bg-[#afcf75]/20 rounded-full animate-pulse\"\n                ></div>\n              </div>\n              <div\n                class=\"bg-gradient-to-r from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] h-2.5 relative\"\n                [style.width.%]=\"\n                  users.length ? (getInactiveCount() / users.length) * 100 : 0\n                \"\n              >\n                <div\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 rounded-full animate-pulse\"\n                ></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- User Roles Card -->\n      <div\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative group hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1\"\n      >\n        <!-- Decorative gradient top border -->\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4a89ce] to-[#7826b5] dark:from-[#4a89ce] dark:to-[#7826b5]\"\n        ></div>\n\n        <!-- Glow effect on hover -->\n        <div\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4a89ce] to-[#7826b5] dark:from-[#4a89ce] dark:to-[#7826b5] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n        ></div>\n\n        <div class=\"p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-1\">\n              <div\n                class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\"\n              >\n                User Roles\n              </div>\n              <div class=\"mt-1 flex flex-wrap gap-4 text-sm\">\n                <div class=\"flex items-center\">\n                  <div\n                    class=\"w-2 h-2 rounded-full bg-[#4a89ce] dark:bg-[#4a89ce] mr-1.5\"\n                  ></div>\n                  <span class=\"text-[#4a89ce] dark:text-[#4a89ce] font-medium\"\n                    >{{ getStudentCount() }} Students</span\n                  >\n                </div>\n                <div class=\"flex items-center\">\n                  <div\n                    class=\"w-2 h-2 rounded-full bg-[#7826b5] dark:bg-[#7826b5] mr-1.5\"\n                  ></div>\n                  <span class=\"text-[#7826b5] dark:text-[#7826b5] font-medium\"\n                    >{{ getTeacherCount() }} Teachers</span\n                  >\n                </div>\n                <div class=\"flex items-center\">\n                  <div\n                    class=\"w-2 h-2 rounded-full bg-[#4f5fad] dark:bg-[#6d78c9] mr-1.5\"\n                  ></div>\n                  <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n                    >{{ getAdminCount() }} Admins</span\n                  >\n                </div>\n              </div>\n            </div>\n            <div class=\"ml-4 relative\">\n              <div\n                class=\"absolute inset-0 bg-[#4a89ce]/10 dark:bg-[#4a89ce]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-150\"\n              ></div>\n              <div\n                class=\"relative z-10 bg-[#edf1f4]/80 dark:bg-[#2a2a2a]/80 rounded-full p-2.5 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300\"\n              >\n                <i\n                  class=\"fas fa-user-tag text-[#4a89ce] dark:text-[#4a89ce] text-xl\"\n                ></i>\n              </div>\n            </div>\n          </div>\n          <div\n            class=\"mt-4 w-full bg-[#edf1f4] dark:bg-[#2a2a2a] rounded-full h-2.5 overflow-hidden relative\"\n          >\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#4a89ce]/10 to-[#7826b5]/10 dark:from-[#4a89ce]/10 dark:to-[#7826b5]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md\"\n            ></div>\n            <div class=\"flex h-full relative z-10\">\n              <div\n                class=\"bg-gradient-to-r from-[#4a89ce] to-[#4a89ce] dark:from-[#4a89ce] dark:to-[#4a89ce] h-2.5 relative\"\n                [style.width.%]=\"\n                  users.length ? (getStudentCount() / users.length) * 100 : 0\n                \"\n              >\n                <div\n                  class=\"absolute inset-0 bg-[#4a89ce]/20 rounded-full animate-pulse\"\n                ></div>\n              </div>\n              <div\n                class=\"bg-gradient-to-r from-[#7826b5] to-[#7826b5] dark:from-[#7826b5] dark:to-[#7826b5] h-2.5 relative\"\n                [style.width.%]=\"\n                  users.length ? (getTeacherCount() / users.length) * 100 : 0\n                \"\n              >\n                <div\n                  class=\"absolute inset-0 bg-[#7826b5]/20 rounded-full animate-pulse\"\n                ></div>\n              </div>\n              <div\n                class=\"bg-gradient-to-r from-[#4f5fad] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#6d78c9] h-2.5 relative\"\n                [style.width.%]=\"\n                  users.length ? (getAdminCount() / users.length) * 100 : 0\n                \"\n              >\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 rounded-full animate-pulse\"\n                ></div>\n              </div>\n            </div>\n          </div>\n          <div\n            class=\"flex justify-between mt-2 text-xs text-[#6d6870] dark:text-[#a0a0a0]\"\n          >\n            <span>Students</span>\n            <span>Teachers</span>\n            <span>Admins</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Users Table -->\n    <div\n      *ngIf=\"!loading && filteredUsers.length > 0\"\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden mb-8 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\n    >\n      <!-- Decorative gradient top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n      ></div>\n\n      <div\n        class=\"p-5 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] flex items-center justify-between\"\n      >\n        <h6\n          class=\"font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center\"\n        >\n          <i class=\"fas fa-users-cog mr-2\"></i>\n          User Management\n        </h6>\n      </div>\n      <div class=\"overflow-x-auto\">\n        <table\n          class=\"w-full divide-y divide-[#edf1f4] dark:divide-[#2a2a2a] table-fixed\"\n        >\n          <thead class=\"bg-[#f8fafc] dark:bg-[#1a1a1a]\">\n            <tr>\n              <th\n                scope=\"col\"\n                class=\"px-3 py-3 text-left text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[15%]\"\n              >\n                Name\n              </th>\n              <th\n                scope=\"col\"\n                class=\"px-3 py-3 text-left text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[20%]\"\n              >\n                Email\n              </th>\n              <th\n                scope=\"col\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[8%]\"\n              >\n                Verified\n              </th>\n              <th\n                scope=\"col\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[8%]\"\n              >\n                Status\n              </th>\n              <th\n                scope=\"col\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[10%]\"\n              >\n                Role\n              </th>\n              <th\n                scope=\"col\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[13%]\"\n              >\n                Activate\n              </th>\n              <th\n                scope=\"col\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[13%]\"\n              >\n                Delete\n              </th>\n              <th\n                scope=\"col\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider w-[13%]\"\n              >\n                Details\n              </th>\n            </tr>\n          </thead>\n          <tbody\n            class=\"bg-white dark:bg-[#1e1e1e] divide-y divide-[#edf1f4] dark:divide-[#2a2a2a]\"\n          >\n            <tr\n              *ngFor=\"let user of filteredUsers\"\n              class=\"hover:bg-[#f8fafc] dark:hover:bg-[#1a1a1a] transition-colors\"\n            >\n              <td class=\"px-3 py-3 whitespace-nowrap truncate\">\n                <div class=\"flex items-center\">\n                  <div\n                    class=\"flex-shrink-0 h-8 w-8 rounded-full bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white flex items-center justify-center text-xs shadow-md relative group\"\n                  >\n                    <div\n                      class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity\"\n                    ></div>\n                    <span class=\"relative z-10\">{{\n                      user.fullName.charAt(0)\n                    }}</span>\n                  </div>\n                  <div class=\"ml-3\">\n                    <div\n                      class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] truncate max-w-[120px]\"\n                    >\n                      {{ user.fullName }}\n                    </div>\n                  </div>\n                </div>\n              </td>\n              <td class=\"px-3 py-3 whitespace-nowrap\">\n                <div\n                  class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] truncate max-w-[150px]\"\n                >\n                  {{ user.email }}\n                </div>\n              </td>\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\n                <span\n                  class=\"px-2 py-1 text-xs rounded-lg inline-flex items-center justify-center\"\n                  [ngClass]=\"\n                    user.verified\n                      ? 'bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75]'\n                      : 'bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785]'\n                  \"\n                >\n                  <i\n                    [class]=\"\n                      user.verified\n                        ? 'fas fa-check-circle mr-1'\n                        : 'fas fa-times-circle mr-1'\n                    \"\n                  ></i>\n                  {{ user.verified ? \"Yes\" : \"No\" }}\n                </span>\n              </td>\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\n                <span\n                  class=\"px-3 py-1.5 text-xs rounded-full inline-flex items-center justify-center font-semibold transition-all duration-300\"\n                  [ngClass]=\"\n                    user.isActive !== false\n                      ? 'bg-[#afcf75]/15 dark:bg-[#afcf75]/10 text-[#2a5a03] dark:text-[#afcf75] border border-[#afcf75]/30 shadow-sm'\n                      : 'bg-[#ff6b69]/15 dark:bg-[#ff6b69]/10 text-[#ff6b69] dark:text-[#ff8785] border border-[#ff6b69]/30 shadow-sm'\n                  \"\n                >\n                  <i\n                    [class]=\"\n                      user.isActive !== false\n                        ? 'fas fa-check-circle mr-1.5 text-[10px]'\n                        : 'fas fa-times-circle mr-1.5 text-[10px]'\n                    \"\n                  ></i>\n                  {{ user.isActive !== false ? \"Active\" : \"Deactivated\" }}\n                </span>\n              </td>\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\n                <div class=\"relative group\">\n                  <select\n                    class=\"w-full px-2 py-1.5 text-xs rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-1 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all appearance-none pr-7\"\n                    [value]=\"user.role\"\n                    (change)=\"onRoleChange(user._id, $any($event.target).value)\"\n                  >\n                    <option *ngFor=\"let role of roles\" [value]=\"role\">\n                      {{ role | titlecase }}\n                    </option>\n                  </select>\n                  <div\n                    class=\"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none text-[#6d6870] dark:text-[#a0a0a0]\"\n                  >\n                    <i class=\"fas fa-chevron-down text-xs\"></i>\n                  </div>\n                </div>\n              </td>\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\n                <button\n                  class=\"px-3 py-2 text-xs rounded-lg font-semibold flex items-center justify-center mx-auto transition-all duration-300 w-full relative overflow-hidden group border shadow-sm hover:shadow-md transform hover:scale-105\"\n                  [ngClass]=\"\n                    user.isActive !== false\n                      ? 'bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785] hover:bg-[#ff6b69]/20 dark:hover:bg-[#ff6b69]/10 border-[#ff6b69]/30 hover:border-[#ff6b69]/50'\n                      : 'bg-[#afcf75]/10 dark:bg-[#afcf75]/5 text-[#2a5a03] dark:text-[#afcf75] hover:bg-[#afcf75]/20 dark:hover:bg-[#afcf75]/10 border-[#afcf75]/30 hover:border-[#afcf75]/50'\n                  \"\n                  (click)=\"\n                    toggleUserActivation(user._id, user.isActive !== false)\n                  \"\n                  [title]=\"\n                    user.isActive !== false\n                      ? 'Click to deactivate this user account'\n                      : 'Click to activate this user account'\n                  \"\n                >\n                  <!-- Background animation -->\n                  <div class=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                       [ngClass]=\"\n                         user.isActive !== false\n                           ? 'bg-gradient-to-r from-[#ff6b69]/5 to-[#ff6b69]/10'\n                           : 'bg-gradient-to-r from-[#afcf75]/5 to-[#afcf75]/10'\n                       \">\n                  </div>\n\n                  <i\n                    class=\"relative z-10 mr-2 transition-transform duration-300 group-hover:scale-110\"\n                    [class]=\"\n                      user.isActive !== false\n                        ? 'fas fa-user-slash'\n                        : 'fas fa-user-check'\n                    \"\n                  ></i>\n                  <span class=\"relative z-10\">\n                    {{ user.isActive !== false ? \"Deactivate\" : \"Activate\" }}\n                  </span>\n                </button>\n              </td>\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\n                <button\n                  class=\"px-2 py-1.5 text-xs rounded-lg bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 text-[#ff6b69] dark:text-[#ff8785] hover:bg-[#ff6b69]/20 dark:hover:bg-[#ff6b69]/10 font-medium flex items-center justify-center mx-auto transition-all w-full\"\n                  (click)=\"onDeleteUser(user._id)\"\n                  title=\"Supprimer définitivement cet utilisateur\"\n                >\n                  <i class=\"fas fa-trash-alt mr-1.5\"></i>\n                  Delete\n                </button>\n              </td>\n              <td class=\"px-2 py-3 whitespace-nowrap text-center\">\n                <button\n                  class=\"px-2 py-1.5 text-xs rounded-lg bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/10 font-medium flex items-center justify-center mx-auto transition-all w-full\"\n                  (click)=\"showUserDetails(user._id)\"\n                >\n                  <i class=\"fas fa-eye mr-1.5\"></i>\n                  Details\n                </button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- No Users State -->\n    <div\n      *ngIf=\"!loading && filteredUsers.length === 0\"\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden mb-8 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\n    >\n      <!-- Decorative gradient top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n      ></div>\n\n      <div class=\"p-10 text-center\">\n        <div class=\"relative mx-auto w-20 h-20 mb-6\">\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 rounded-full blur-xl\"\n          ></div>\n\n          <!-- Icon container with gradient background -->\n          <div\n            class=\"relative z-10 w-20 h-20 rounded-full bg-gradient-to-br from-[#edf1f4] to-white dark:from-[#1a1a1a] dark:to-[#2a2a2a] flex items-center justify-center shadow-lg\"\n          >\n            <i\n              class=\"fas fa-users text-3xl bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n            ></i>\n          </div>\n\n          <!-- Animated rings -->\n          <div\n            class=\"absolute inset-0 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 rounded-full animate-ping opacity-75\"\n          ></div>\n          <div\n            class=\"absolute inset-0 border border-[#4f5fad]/40 dark:border-[#6d78c9]/40 rounded-full animate-pulse\"\n          ></div>\n        </div>\n\n        <h3\n          class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-3\"\n        >\n          No users found\n        </h3>\n\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] max-w-md mx-auto\">\n          {{\n            searchTerm\n              ? \"No users match your search criteria.\"\n              : \"There are no users in the system yet.\"\n          }}\n        </p>\n\n        <div class=\"mt-8\" *ngIf=\"searchTerm\">\n          <button\n            (click)=\"clearSearch()\"\n            class=\"inline-flex items-center px-4 py-2.5 text-sm relative overflow-hidden group\"\n          >\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-10 dark:opacity-20 group-hover:opacity-20 dark:group-hover:opacity-30 transition-opacity\"\n            ></div>\n            <div\n              class=\"relative z-10 flex items-center text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            >\n              <i class=\"fas fa-times-circle mr-2\"></i>\n              Clear search\n            </div>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n  <!-- /.container-fluid -->\n</div>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { DashboardRoutingModule } from './dashboard-routing.module';\nimport { DashboardComponent } from './dashboard.component';\n\n\n@NgModule({\n  declarations: [\n    DashboardComponent\n  ],\n  imports: [\n    CommonModule,\n    DashboardRoutingModule\n  ]\n})\nexport class DashboardModule { }\n"], "names": ["BehaviorSubject", "ToastService", "constructor", "toastsSubject", "toasts$", "asObservable", "currentId", "generateId", "Math", "random", "toString", "substr", "addToast", "toast", "newToast", "id", "duration", "currentToasts", "value", "next", "setTimeout", "removeToast", "show", "message", "type", "title", "dismiss", "showSuccess", "showError", "showWarning", "showInfo", "filter", "t", "success", "icon", "error", "action", "warning", "accessDenied", "code", "codeText", "label", "handler", "console", "log", "ownershipRequired", "resource", "clear", "factory", "ɵfac", "providedIn", "RouterModule", "DashboardComponent", "routes", "path", "component", "DashboardRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "i0", "ɵɵelementStart", "ɵɵlistener", "DashboardComponent_button_30_Template_button_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "ctx_r2", "ctx_r4", "users", "length", "getActiveCount", "getInactiveCount", "ɵɵstyleProp", "getStudentCount", "getTeacher<PERSON>ount", "getAdminCount", "ɵɵproperty", "role_r12", "ɵɵpipeBind1", "DashboardComponent_div_35_tr_27_Template_select_change_23_listener", "$event", "restoredCtx", "_r14", "user_r10", "$implicit", "ctx_r13", "onRoleChange", "_id", "target", "ɵɵtemplate", "DashboardComponent_div_35_tr_27_option_24_Template", "DashboardComponent_div_35_tr_27_Template_button_click_28_listener", "ctx_r15", "toggleUserActivation", "isActive", "DashboardComponent_div_35_tr_27_Template_button_click_34_listener", "ctx_r16", "onDeleteUser", "DashboardComponent_div_35_tr_27_Template_button_click_38_listener", "ctx_r17", "showUserDetails", "ɵɵtextInterpolate", "fullName", "char<PERSON>t", "email", "verified", "ɵɵclassMap", "role", "ctx_r9", "roles", "DashboardComponent_div_35_tr_27_Template", "ctx_r5", "filteredUsers", "DashboardComponent_div_36_div_13_Template_button_click_1_listener", "_r20", "ctx_r19", "DashboardComponent_div_36_div_13_Template", "ctx_r6", "searchTerm", "authService", "router", "toastService", "loading", "currentUser", "ngOnInit", "loadUserData", "token", "localStorage", "getItem", "userStr", "navigate", "JSON", "parse", "getAllUsers", "subscribe", "res", "err", "searchUsers", "trim", "term", "toLowerCase", "user", "includes", "applyFilters", "userId", "newRole", "updateUserRole", "userIndex", "findIndex", "u", "filteredIndex", "confirmDelete", "confirm", "deleteUser", "currentStatus", "newStatus", "find", "userName", "firstName", "confirmAction", "statusText", "successMessage", "errorMessage", "logout", "ɵɵdirectiveInject", "AuthService", "i2", "Router", "i3", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_Template_input_input_27_listener", "DashboardComponent_button_30_Template", "DashboardComponent_div_31_Template", "DashboardComponent_div_32_Template", "DashboardComponent_div_33_Template", "DashboardComponent_div_34_Template", "DashboardComponent_div_35_Template", "DashboardComponent_div_36_Template", "CommonModule", "DashboardModule", "declarations"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}