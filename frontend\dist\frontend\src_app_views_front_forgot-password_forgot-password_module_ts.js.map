{"version": 3, "file": "src_app_views_front_forgot-password_forgot-password_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AACuD;AACe;;;AAEtE,MAAME,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH,+EAAuBA;AAAA,CAAE,CAAC;AAMnE,MAAOI,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXK,2BAA2B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAF5BT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;ACP4C;;;;;;;;ICsFxDW,4DAAA,cAMC;IACCA,uDAAA,YAA8C;IAC9CA,oDAAA,0BACF;IAAAA,0DAAA,EAAM;;;;;IAIRA,4DAAA,cAGC;IAKKA,uDAAA,YAA2C;IAK7CA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,MAAAO,MAAA,CAAAC,KAAA,MACF;;;;;IAMNR,4DAAA,cAGC;IAKKA,uDAAA,YAAmC;IAKrCA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,MAAAS,MAAA,CAAAC,OAAA,MACF;;;ADlIV,MAAOpB,uBAAuB;EAKlCqB,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAJ,OAAO,GAAG,EAAE;IACZ,KAAAF,KAAK,GAAG,EAAE;IAOR,IAAI,CAACO,UAAU,GAAG,IAAI,CAACH,EAAE,CAACI,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClB,sDAAU,CAACmB,QAAQ,EAAEnB,sDAAU,CAACkB,KAAK,CAAC;KACpD,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,UAAU,CAACK,OAAO,EAAE;IAE7B,MAAMH,KAAK,GAAG,IAAI,CAACF,UAAU,CAACM,KAAK,CAACJ,KAAK;IAEzC,IAAI,CAACJ,WAAW,CAACS,cAAc,CAACL,KAAK,CAAC,CAACM,SAAS,CAAC;MAC/CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACf,OAAO,GAAGe,GAAG,CAACf,OAAO;QAC1B,IAAI,CAACF,KAAK,GAAG,EAAE;QACfkB,UAAU,CAAC,MAAM,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;UACzDC,WAAW,EAAE;YAAEX,KAAK,EAAEA;UAAK;SAC5B,CAAC,EAAE,IAAI,CAAC;MACX,CAAC;MACDT,KAAK,EAAGqB,GAAG,IAAI;QACb,IAAI,CAACrB,KAAK,GAAGqB,GAAG,CAACrB,KAAK,CAACE,OAAO,IAAI,uBAAuB;QACzD,IAAI,CAACA,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACJ;;;uBAjCWpB,uBAAuB,EAAAU,+DAAA,CAAAH,uDAAA,GAAAG,+DAAA,CAAAgC,+DAAA,GAAAhC,+DAAA,CAAAkC,mDAAA;IAAA;EAAA;;;YAAvB5C,uBAAuB;MAAA8C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVpC1C,4DAAA,aAEC;UAGGA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAIVA,4DAAA,cAA2C;UAKvCA,uDAAA,cAEO;UAMPA,4DAAA,eAA6B;UAIzBA,oDAAA,yBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAA2D;UACzDA,oDAAA,kDACF;UAAAA,0DAAA,EAAI;UAINA,4DAAA,eAAiB;UAGbA,wDAAA,sBAAA6C,2DAAA;YAAA,OAAYF,GAAA,CAAAxB,QAAA,EAAU;UAAA,EAAC;UAIvBnB,4DAAA,eAAmB;UAKfA,uDAAA,aAA8C;UAC9CA,oDAAA,eACF;UAAAA,0DAAA,EAAQ;UACRA,4DAAA,eAAsB;UACpBA,uDAAA,iBAOE;UACFA,4DAAA,eAEC;UACCA,uDAAA,eAEO;UACTA,0DAAA,EAAM;UAERA,wDAAA,KAAA+C,uCAAA,kBASM;UACR/C,0DAAA,EAAM;UAGNA,wDAAA,KAAAgD,uCAAA,kBAoBM;UAGNhD,wDAAA,KAAAiD,uCAAA,kBAoBM;UAGNjD,4DAAA,kBAIC;UACCA,uDAAA,eAEO;UAIPA,4DAAA,gBAEC;UACCA,uDAAA,aAAuC;UACvCA,oDAAA,yBACF;UAAAA,0DAAA,EAAO;UAITA,4DAAA,eAEC;UAEGA,oDAAA,iCACA;UAAAA,4DAAA,aAGC;UACCA,oDAAA,iBACF;UAAAA,0DAAA,EAAI;;;;UAvHRA,uDAAA,IAAwB;UAAxBA,wDAAA,cAAA2C,GAAA,CAAA5B,UAAA,CAAwB;UA+BnBf,uDAAA,GAIf;UAJeA,wDAAA,WAAAmD,OAAA,GAAAR,GAAA,CAAA5B,UAAA,CAAAqC,GAAA,4BAAAD,OAAA,CAAA/B,OAAA,OAAA+B,OAAA,GAAAR,GAAA,CAAA5B,UAAA,CAAAqC,GAAA,4BAAAD,OAAA,CAAAE,OAAA,EAIf;UASarD,uDAAA,GAAW;UAAXA,wDAAA,SAAA2C,GAAA,CAAAnC,KAAA,CAAW;UAuBXR,uDAAA,GAAa;UAAbA,wDAAA,SAAA2C,GAAA,CAAAjC,OAAA,CAAa;UAyBdV,uDAAA,GAA+B;UAA/BA,wDAAA,aAAA2C,GAAA,CAAA5B,UAAA,CAAAK,OAAA,CAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;ACpJI;AACmB;AAEa;AACT;;AAWhE,MAAOqC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAN7BH,yDAAY,EACZC,uDAAW,EACXC,+DAAmB,EACnB9D,wFAA2B;IAAA;EAAA;;;sHAGlB+D,oBAAoB;IAAAC,YAAA,GARhBpE,+EAAuB;IAAAM,OAAA,GAEpC0D,yDAAY,EACZC,uDAAW,EACXC,+DAAmB,EACnB9D,wFAA2B;EAAA;AAAA", "sources": ["./src/app/views/front/forgot-password/forgot-password-routing.module.ts", "./src/app/views/front/forgot-password/forgot-password.component.ts", "./src/app/views/front/forgot-password/forgot-password.component.html", "./src/app/views/front/forgot-password/forgot-password.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ForgotPasswordComponent } from './forgot-password.component';\n\nconst routes: Routes = [{ path: '', component: ForgotPasswordComponent }];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class ForgotPasswordRoutingModule {}\n", "import { Component } from '@angular/core';\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\nimport { AuthService } from '../../../services/auth.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-forgot-password',\n  templateUrl: './forgot-password.component.html',\n  styleUrls: ['./forgot-password.component.css'],\n})\nexport class ForgotPasswordComponent {\n  forgotForm: FormGroup;\n  message = '';\n  error = '';\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.forgotForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n    });\n  }\n\n  onSubmit() {\n    if (this.forgotForm.invalid) return;\n\n    const email = this.forgotForm.value.email;\n\n    this.authService.forgotPassword(email).subscribe({\n      next: (res: any) => {\n        this.message = res.message;\n        this.error = '';\n        setTimeout(() => this.router.navigate(['/reset-password'], {\n          queryParams: { email: email }\n        }), 1500);\n      },\n      error: (err) => {\n        this.error = err.error.message || 'Something went wrong.';\n        this.message = '';\n      },\n    });\n  }\n}\n", "<div\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen flex items-center justify-center relative futuristic-layout\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"w-full max-w-md relative z-10\">\n    <div\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\n    >\n      <!-- Decorative top border with gradient and glow -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\n      ></div>\n\n      <!-- Header -->\n      <div class=\"p-6 text-center\">\n        <h1\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n        >\n          Forgot Password\n        </h1>\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\n          Enter your email to receive a reset code\n        </p>\n      </div>\n\n      <!-- Form Section -->\n      <div class=\"p-6\">\n        <form\n          [formGroup]=\"forgotForm\"\n          (ngSubmit)=\"onSubmit()\"\n          class=\"space-y-5\"\n        >\n          <!-- Email -->\n          <div class=\"group\">\n            <label\n              for=\"email\"\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\n            >\n              <i class=\"fas fa-envelope mr-1.5 text-xs\"></i>\n              Email\n            </label>\n            <div class=\"relative\">\n              <input\n                id=\"email\"\n                type=\"email\"\n                formControlName=\"email\"\n                placeholder=\"<EMAIL>\"\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                required\n              />\n              <div\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n              >\n                <div\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n                ></div>\n              </div>\n            </div>\n            <div\n              *ngIf=\"\n                forgotForm.get('email')?.invalid &&\n                forgotForm.get('email')?.touched\n              \"\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\"\n            >\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              Email is required\n            </div>\n          </div>\n\n          <!-- Error Message -->\n          <div\n            *ngIf=\"error\"\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\"\n          >\n            <div class=\"flex items-start\">\n              <div\n                class=\"text-[#ff6b69] dark:text-[#ff8785] mr-2 text-base relative\"\n              >\n                <i class=\"fas fa-exclamation-triangle\"></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\n                ></div>\n              </div>\n              <div class=\"flex-1\">\n                <p class=\"text-xs text-[#ff6b69] dark:text-[#ff8785]\">\n                  {{ error }}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Success Message -->\n          <div\n            *ngIf=\"message\"\n            class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\"\n          >\n            <div class=\"flex items-start\">\n              <div\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-2 text-base relative\"\n              >\n                <i class=\"fas fa-check-circle\"></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n                ></div>\n              </div>\n              <div class=\"flex-1\">\n                <p class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\n                  {{ message }}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Submit Button -->\n          <button\n            type=\"submit\"\n            class=\"w-full relative overflow-hidden group mt-6\"\n            [disabled]=\"forgotForm.invalid\"\n          >\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105 disabled:opacity-50\"\n            ></div>\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300 disabled:opacity-0\"\n            ></div>\n            <span\n              class=\"relative flex items-center justify-center text-white font-medium py-2.5 px-4 rounded-lg transition-all z-10\"\n            >\n              <i class=\"fas fa-paper-plane mr-2\"></i>\n              Send Reset Code\n            </span>\n          </button>\n\n          <!-- Back Link -->\n          <div\n            class=\"text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] space-y-2 pt-4\"\n          >\n            <div>\n              Remember your password?\n              <a\n                routerLink=\"/login\"\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium\"\n              >\n                Sign in\n              </a>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\nimport { ForgotPasswordRoutingModule } from './forgot-password-routing.module';\nimport { ForgotPasswordComponent } from './forgot-password.component';\n\n@NgModule({\n  declarations: [ForgotPasswordComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ForgotPasswordRoutingModule,\n  ],\n})\nexport class ForgotPasswordModule {}\n"], "names": ["RouterModule", "ForgotPasswordComponent", "routes", "path", "component", "ForgotPasswordRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ctx_r2", "message", "constructor", "fb", "authService", "router", "forgotForm", "group", "email", "required", "onSubmit", "invalid", "value", "forgotPassword", "subscribe", "next", "res", "setTimeout", "navigate", "queryParams", "err", "ɵɵdirectiveInject", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ForgotPasswordComponent_Template_form_ngSubmit_27_listener", "ɵɵtemplate", "ForgotPasswordComponent_div_36_Template", "ForgotPasswordComponent_div_37_Template", "ForgotPasswordComponent_div_38_Template", "ɵɵproperty", "tmp_1_0", "get", "touched", "CommonModule", "FormsModule", "ReactiveFormsModule", "ForgotPasswordModule", "declarations"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}