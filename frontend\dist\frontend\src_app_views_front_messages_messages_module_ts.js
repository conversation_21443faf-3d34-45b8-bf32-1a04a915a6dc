"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_messages_messages_module_ts"],{

/***/ 68397:
/*!*******************************************!*\
  !*** ./src/app/services/toast.service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastService: () => (/* binding */ ToastService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 75797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);


class ToastService {
  constructor() {
    this.toastsSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject([]);
    this.toasts$ = this.toastsSubject.asObservable();
    this.currentId = 0;
  }
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }
  addToast(toast) {
    const newToast = {
      ...toast,
      id: this.generateId(),
      duration: toast.duration || 5000
    };
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, newToast]);
    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        this.removeToast(newToast.id);
      }, newToast.duration);
    }
  }
  show(message, type = 'info', duration = 5000) {
    const id = this.generateId();
    const toast = {
      id,
      type,
      title: '',
      message,
      duration
    };
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, toast]);
    if (duration > 0) {
      setTimeout(() => this.dismiss(id), duration);
    }
  }
  showSuccess(message, duration = 3000) {
    this.show(message, 'success', duration);
  }
  showError(message, duration = 5000) {
    this.show(message, 'error', duration);
  }
  showWarning(message, duration = 4000) {
    this.show(message, 'warning', duration);
  }
  showInfo(message, duration = 3000) {
    this.show(message, 'info', duration);
  }
  dismiss(id) {
    const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);
    this.toastsSubject.next(currentToasts);
  }
  success(title, message, duration) {
    this.addToast({
      type: 'success',
      title,
      message,
      duration,
      icon: 'check-circle'
    });
  }
  error(title, message, duration, action) {
    this.addToast({
      type: 'error',
      title,
      message,
      duration: duration || 8000,
      icon: 'x-circle',
      action
    });
  }
  warning(title, message, duration) {
    this.addToast({
      type: 'warning',
      title,
      message,
      duration,
      icon: 'exclamation-triangle'
    });
  }
  // Méthodes spécifiques pour les erreurs d'autorisation
  accessDenied(action = 'effectuer cette action', code) {
    const codeText = code ? ` (Code: ${code})` : '';
    this.error('Accès refusé', `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`, 8000, {
      label: 'Comprendre les rôles',
      handler: () => {
        // Optionnel: rediriger vers une page d'aide
        console.log("Redirection vers l'aide sur les rôles");
      }
    });
  }
  ownershipRequired(resource = 'cette ressource') {
    this.error('Propriétaire requis', `Seul le propriétaire ou un administrateur peut modifier ${resource}`, 8000);
  }
  removeToast(id) {
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next(currentToasts.filter(toast => toast.id !== id));
  }
  clear() {
    this.toastsSubject.next([]);
  }
  static {
    this.ɵfac = function ToastService_Factory(t) {
      return new (t || ToastService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: ToastService,
      factory: ToastService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 38096:
/*!*****************************************************************************!*\
  !*** ./src/app/views/front/messages/message-chat/message-chat.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessageChatComponent: () => (/* binding */ MessageChatComponent)
/* harmony export */ });
/* harmony import */ var C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 2510);
/* harmony import */ var _models_message_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../models/message.model */ 15293);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../../environments/environment */ 45312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _services_message_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../services/message.service */ 54537);
/* harmony import */ var _services_call_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../services/call.service */ 49454);
/* harmony import */ var _services_toast_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../services/toast.service */ 68397);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 60316);












const _c0 = ["messagesContainer"];
const _c1 = ["fileInput"];
const _c2 = ["localVideo"];
const _c3 = ["remoteVideo"];
const _c4 = ["localVideoHidden"];
const _c5 = ["remoteVideoHidden"];
function MessageChatComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 55);
  }
}
function MessageChatComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 56)(1, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, "En train d'\u00E9crire");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "div", 58)(5, "div", 59)(6, "div", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
}
function MessageChatComponent_span_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", (ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.isOnline) ? "En ligne" : ctx_r4.formatLastActive(ctx_r4.otherParticipant == null ? null : ctx_r4.otherParticipant.lastActive), " ");
  }
}
function MessageChatComponent_div_29_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 61)(1, "div", 62)(2, "button", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_29_Template_button_click_2_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r20);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      ctx_r19.toggleSearch();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r19.showMainMenu = false);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](3, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](5, "Rechercher");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "button", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](7, "i", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](9, "Voir le profil");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](10, "hr", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "button", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](12, "i", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](13, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](14, "Param\u00E8tres");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
  }
}
function MessageChatComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 70)(1, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "p", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " D\u00E9posez vos fichiers ici ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "p", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6, " Images, vid\u00E9os, documents... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function MessageChatComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "div", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "span", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, "Chargement des messages...");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
}
function MessageChatComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 78)(1, "div", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "h3", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " Aucun message ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "p", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" Commencez votre conversation avec ", ctx_r9.otherParticipant == null ? null : ctx_r9.otherParticipant.username, " ");
  }
}
function MessageChatComponent_div_35_ng_container_1_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 96)(1, "div", 97)(2, "span", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    const ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r25.formatDateSeparator(message_r23.timestamp), " ");
  }
}
function MessageChatComponent_div_35_ng_container_1_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r35 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 99)(1, "img", 100);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_35_ng_container_1_div_3_Template_img_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r35);
      const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r33 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r33.openUserProfile(message_r23.sender == null ? null : message_r23.sender.id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", (message_r23.sender == null ? null : message_r23.sender.image) || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"])("alt", message_r23.sender == null ? null : message_r23.sender.username);
  }
}
function MessageChatComponent_div_35_ng_container_1_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    const ctx_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("color", ctx_r27.getUserColor(message_r23.sender == null ? null : message_r23.sender.id));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", message_r23.sender == null ? null : message_r23.sender.username, " ");
  }
}
function MessageChatComponent_div_35_ng_container_1_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "div", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    const ctx_r28 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("innerHTML", ctx_r28.formatMessageContent(message_r23.content), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeHtml"]);
  }
}
function MessageChatComponent_div_35_ng_container_1_div_7_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 107);
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2).$implicit;
    const ctx_r39 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("color", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r39.currentUserId ? "#ffffff" : "#111827");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("innerHTML", ctx_r39.formatMessageContent(message_r23.content), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeHtml"]);
  }
}
function MessageChatComponent_div_35_ng_container_1_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r43 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 104)(1, "img", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r43);
      const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r41 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r41.openImageViewer(message_r23));
    })("load", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_load_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r43);
      const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r44 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r44.onImageLoad($event, message_r23));
    })("error", function MessageChatComponent_div_35_ng_container_1_div_7_Template_img_error_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r43);
      const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r46 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r46.onImageError($event, message_r23));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, MessageChatComponent_div_35_ng_container_1_div_7_div_2_Template, 1, 3, "div", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", ctx_r29.getImageUrl(message_r23), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"])("alt", message_r23.content || "Image");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r23.content);
  }
}
function MessageChatComponent_div_35_ng_container_1_div_8_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 116);
  }
  if (rf & 2) {
    const wave_r51 = ctx.$implicit;
    const i_r52 = ctx.index;
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2).$implicit;
    const ctx_r49 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("height", ctx_r49.isVoicePlaying(message_r23.id) ? wave_r51 : 8, "px")("animation", ctx_r49.isVoicePlaying(message_r23.id) ? "pulse 1s infinite" : "none")("animation-delay", i_r52 * 0.1 + "s");
  }
}
function MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r56 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "button", 117);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r56);
      const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2).$implicit;
      const ctx_r54 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r54.changeVoiceSpeed(message_r23));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2).$implicit;
    const ctx_r50 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r50.getVoiceSpeed(message_r23), "x ");
  }
}
function MessageChatComponent_div_35_ng_container_1_div_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r60 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 108)(1, "button", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_35_ng_container_1_div_8_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r60);
      const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r58 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r58.toggleVoicePlayback(message_r23));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, MessageChatComponent_div_35_ng_container_1_div_8_div_4_Template, 1, 6, "div", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "div", 113)(6, "div", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template, 2, 1, "button", 115);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    const ctx_r30 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassMap"](ctx_r30.isVoicePlaying(message_r23.id) ? "fas fa-pause" : "fas fa-play");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r30.voiceWaves);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r30.getVoiceDuration(message_r23), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r30.isVoicePlaying(message_r23.id));
  }
}
function MessageChatComponent_div_35_ng_container_1_div_12_i_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 123);
  }
}
function MessageChatComponent_div_35_ng_container_1_div_12_i_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 124);
  }
}
function MessageChatComponent_div_35_ng_container_1_div_12_i_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 125);
  }
}
function MessageChatComponent_div_35_ng_container_1_div_12_i_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 126);
  }
}
function MessageChatComponent_div_35_ng_container_1_div_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, MessageChatComponent_div_35_ng_container_1_div_12_i_1_Template, 1, 0, "i", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, MessageChatComponent_div_35_ng_container_1_div_12_i_2_Template, 1, 0, "i", 120);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, MessageChatComponent_div_35_ng_container_1_div_12_i_3_Template, 1, 0, "i", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, MessageChatComponent_div_35_ng_container_1_div_12_i_4_Template, 1, 0, "i", 122);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r23.status === "SENDING");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r23.status === "SENT");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r23.status === "DELIVERED");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r23.status === "READ");
  }
}
function MessageChatComponent_div_35_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r68 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, MessageChatComponent_div_35_ng_container_1_div_1_Template, 4, 1, "div", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_35_ng_container_1_Template_div_click_2_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r68);
      const message_r23 = restoredCtx.$implicit;
      const ctx_r67 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r67.onMessageClick(message_r23, $event));
    })("contextmenu", function MessageChatComponent_div_35_ng_container_1_Template_div_contextmenu_2_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r68);
      const message_r23 = restoredCtx.$implicit;
      const ctx_r69 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r69.onMessageContextMenu(message_r23, $event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, MessageChatComponent_div_35_ng_container_1_div_3_Template, 2, 2, "div", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](5, MessageChatComponent_div_35_ng_container_1_div_5_Template, 2, 3, "div", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](6, MessageChatComponent_div_35_ng_container_1_div_6_Template, 2, 1, "div", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](7, MessageChatComponent_div_35_ng_container_1_div_7_Template, 3, 3, "div", 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, MessageChatComponent_div_35_ng_container_1_div_8_Template, 9, 5, "div", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](9, "div", 94)(10, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](12, MessageChatComponent_div_35_ng_container_1_div_12_Template, 5, 4, "div", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const message_r23 = ctx.$implicit;
    const i_r24 = ctx.index;
    const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r21.shouldShowDateSeparator(i_r24));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("justify-content", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? "flex-end" : "flex-start");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("id", "message-" + message_r23.id);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", (message_r23.sender == null ? null : message_r23.sender.id) !== ctx_r21.currentUserId && ctx_r21.shouldShowAvatar(i_r24));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("background-color", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? "#3b82f6" : "#ffffff")("color", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId ? "#ffffff" : "#111827");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r21.isGroupConversation() && (message_r23.sender == null ? null : message_r23.sender.id) !== ctx_r21.currentUserId && ctx_r21.shouldShowSenderName(i_r24));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r21.getMessageType(message_r23) === "text");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r21.hasImage(message_r23));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r21.getMessageType(message_r23) === "audio");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r21.formatMessageTime(message_r23.timestamp));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", (message_r23.sender == null ? null : message_r23.sender.id) === ctx_r21.currentUserId);
  }
}
function MessageChatComponent_div_35_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "img", 128);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 129)(3, "div", 130);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "div", 131)(5, "div", 132)(6, "div", 133);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", (ctx_r22.otherParticipant == null ? null : ctx_r22.otherParticipant.image) || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"])("alt", ctx_r22.otherParticipant == null ? null : ctx_r22.otherParticipant.username);
  }
}
function MessageChatComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, MessageChatComponent_div_35_ng_container_1_Template, 13, 15, "ng-container", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, MessageChatComponent_div_35_div_2_Template, 7, 2, "div", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r10.messages)("ngForTrackBy", ctx_r10.trackByMessageId);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r10.otherUserIsTyping);
  }
}
function MessageChatComponent_div_45_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 134);
  }
}
function MessageChatComponent_i_49_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 135);
  }
}
function MessageChatComponent_div_50_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 136);
  }
}
function MessageChatComponent_div_51_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r73 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "button", 142);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_51_button_5_Template_button_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r73);
      const emoji_r71 = restoredCtx.$implicit;
      const ctx_r72 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r72.insertEmoji(emoji_r71));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const emoji_r71 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("title", emoji_r71.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", emoji_r71.emoji, " ");
  }
}
function MessageChatComponent_div_51_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 137)(1, "div", 138)(2, "h4", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, " \u00C9mojis ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "div", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](5, MessageChatComponent_div_51_button_5_Template, 2, 2, "button", 141);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r14.getEmojisForCategory(ctx_r14.selectedEmojiCategory));
  }
}
function MessageChatComponent_div_52_Template(rf, ctx) {
  if (rf & 1) {
    const _r75 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 143)(1, "div", 138)(2, "h4", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, " Pi\u00E8ces jointes ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "div", 144)(5, "button", 145);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_52_Template_button_click_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r75);
      const ctx_r74 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r74.triggerFileInput("image"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "div", 146);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](7, "i", 147);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "span", 148);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](9, "Images");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](10, "button", 145);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_52_Template_button_click_10_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r75);
      const ctx_r76 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r76.triggerFileInput("document"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "div", 149);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](12, "i", 150);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](13, "span", 148);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](14, "Documents");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](15, "button", 145);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_52_Template_button_click_15_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r75);
      const ctx_r77 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r77.openCamera());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "div", 151);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](17, "i", 152);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](18, "span", 148);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](19, "Cam\u00E9ra");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()()();
  }
}
function MessageChatComponent_div_55_Template(rf, ctx) {
  if (rf & 1) {
    const _r79 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 153);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_55_Template_div_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r79);
      const ctx_r78 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r78.closeAllMenus());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function MessageChatComponent_div_56_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 166);
  }
  if (rf & 2) {
    const wave_r81 = ctx.$implicit;
    const i_r82 = ctx.index;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("height", wave_r81, "px")("animation", "bounce 1s infinite")("animation-delay", i_r82 * 0.1 + "s");
  }
}
function MessageChatComponent_div_56_Template(rf, ctx) {
  if (rf & 1) {
    const _r84 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 154)(1, "div", 155);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 156);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 157)(4, "div", 158);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "div", 159);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](7, MessageChatComponent_div_56_div_7_Template, 1, 6, "div", 160);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "div", 161);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](10, "div", 37)(11, "button", 162);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_56_Template_button_click_11_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r84);
      const ctx_r83 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r83.onRecordCancel($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](12, "i", 163);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](13, "button", 164);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_56_Template_button_click_13_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r84);
      const ctx_r85 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r85.onRecordEnd($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](14, "i", 165);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r18.formatRecordingDuration(ctx_r18.voiceRecordingDuration), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r18.voiceWaves);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" Format: ", ctx_r18.getRecordingFormat(), " ");
  }
}
class MessageChatComponent {
  constructor(fb, route, router, MessageService, callService, toastService, cdr) {
    this.fb = fb;
    this.route = route;
    this.router = router;
    this.MessageService = MessageService;
    this.callService = callService;
    this.toastService = toastService;
    this.cdr = cdr;
    // === DONNÉES PRINCIPALES ===
    this.conversation = null;
    this.messages = [];
    this.currentUserId = null;
    this.currentUsername = 'You';
    this.otherParticipant = null;
    // === ÉTATS DE L'INTERFACE ===
    this.isLoading = false;
    this.isLoadingMore = false;
    this.hasMoreMessages = true;
    this.showEmojiPicker = false;
    this.showAttachmentMenu = false;
    this.showSearch = false;
    this.searchQuery = '';
    this.searchResults = [];
    this.searchMode = false;
    this.isSendingMessage = false;
    this.otherUserIsTyping = false;
    this.showMainMenu = false;
    this.showMessageContextMenu = false;
    this.selectedMessage = null;
    this.contextMenuPosition = {
      x: 0,
      y: 0
    };
    this.showReactionPicker = false;
    this.reactionPickerMessage = null;
    this.showImageViewer = false;
    this.selectedImage = null;
    this.uploadProgress = 0;
    this.isUploading = false;
    this.isDragOver = false;
    // === GESTION VOCALE OPTIMISÉE ===
    this.isRecordingVoice = false;
    this.voiceRecordingDuration = 0;
    this.voiceRecordingState = 'idle';
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.recordingTimer = null;
    this.voiceWaves = [4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8];
    // Lecture des messages vocaux
    this.currentAudio = null;
    this.playingMessageId = null;
    this.voicePlayback = {};
    // === APPELS WEBRTC ===
    this.isInCall = false;
    this.callType = null;
    this.callDuration = 0;
    this.callTimer = null;
    // État de l'appel WebRTC - Géré globalement par les composants d'appel
    // activeCall, isMuted, isVideoEnabled sont maintenant dans ActiveCallComponent
    // === ÉMOJIS ===
    this.emojiCategories = [{
      id: 'smileys',
      name: 'Smileys',
      icon: '😀',
      emojis: [{
        emoji: '😀',
        name: 'grinning face'
      }, {
        emoji: '😃',
        name: 'grinning face with big eyes'
      }, {
        emoji: '😄',
        name: 'grinning face with smiling eyes'
      }, {
        emoji: '😁',
        name: 'beaming face with smiling eyes'
      }, {
        emoji: '😆',
        name: 'grinning squinting face'
      }, {
        emoji: '😅',
        name: 'grinning face with sweat'
      }, {
        emoji: '😂',
        name: 'face with tears of joy'
      }, {
        emoji: '🤣',
        name: 'rolling on the floor laughing'
      }, {
        emoji: '😊',
        name: 'smiling face with smiling eyes'
      }, {
        emoji: '😇',
        name: 'smiling face with halo'
      }]
    }, {
      id: 'people',
      name: 'People',
      icon: '👤',
      emojis: [{
        emoji: '👶',
        name: 'baby'
      }, {
        emoji: '🧒',
        name: 'child'
      }, {
        emoji: '👦',
        name: 'boy'
      }, {
        emoji: '👧',
        name: 'girl'
      }, {
        emoji: '🧑',
        name: 'person'
      }, {
        emoji: '👨',
        name: 'man'
      }, {
        emoji: '👩',
        name: 'woman'
      }, {
        emoji: '👴',
        name: 'old man'
      }, {
        emoji: '👵',
        name: 'old woman'
      }]
    }, {
      id: 'nature',
      name: 'Nature',
      icon: '🌿',
      emojis: [{
        emoji: '🐶',
        name: 'dog face'
      }, {
        emoji: '🐱',
        name: 'cat face'
      }, {
        emoji: '🐭',
        name: 'mouse face'
      }, {
        emoji: '🐹',
        name: 'hamster'
      }, {
        emoji: '🐰',
        name: 'rabbit face'
      }, {
        emoji: '🦊',
        name: 'fox'
      }, {
        emoji: '🐻',
        name: 'bear'
      }, {
        emoji: '🐼',
        name: 'panda'
      }]
    }];
    this.selectedEmojiCategory = this.emojiCategories[0];
    // === PAGINATION ===
    this.MAX_MESSAGES_TO_LOAD = 25; // ✅ Increased batch size for better performance
    this.currentPage = 1;
    // === AUTRES ÉTATS ===
    this.isTyping = false;
    this.isUserTyping = false;
    this.typingTimeout = null;
    this.subscriptions = new rxjs__WEBPACK_IMPORTED_MODULE_7__.Subscription();
    this.messageForm = this.fb.group({
      content: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.minLength(1)]]
    });
  }
  // Méthode pour vérifier si le champ de saisie doit être désactivé
  isInputDisabled() {
    return !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage;
  }
  // Méthode pour gérer l'état du contrôle de saisie
  updateInputState() {
    const contentControl = this.messageForm.get('content');
    if (this.isInputDisabled()) {
      contentControl?.disable();
    } else {
      contentControl?.enable();
    }
  }
  ngOnInit() {
    this.initializeComponent();
    // Activer les sons après interaction utilisateur
    this.enableSoundsOnFirstInteraction();
  }
  ngAfterViewInit() {
    // Configurer les éléments vidéo pour WebRTC après que la vue soit initialisée
    setTimeout(() => {
      this.setupVideoElements();
    }, 100);
    // Réessayer plusieurs fois pour s'assurer que les éléments sont configurés
    setTimeout(() => {
      this.setupVideoElements();
    }, 500);
    setTimeout(() => {
      this.setupVideoElements();
    }, 1000);
    setTimeout(() => {
      this.setupVideoElements();
    }, 2000);
  }
  /**
   * Configure les éléments vidéo pour WebRTC
   */
  setupVideoElements() {
    // Essayer d'abord les éléments visibles (pour appels vidéo)
    if (this.localVideo && this.remoteVideo) {
      this.callService.setVideoElements(this.localVideo.nativeElement, this.remoteVideo.nativeElement);
    }
    // Sinon utiliser les éléments cachés (pour appels audio)
    else if (this.localVideoHidden && this.remoteVideoHidden) {
      this.callService.setVideoElements(this.localVideoHidden.nativeElement, this.remoteVideoHidden.nativeElement);
    } else {
      this.createVideoElementsManually();
      // Réessayer après un délai
      setTimeout(() => {
        this.setupVideoElements();
      }, 500);
      // Réessayer encore plus tard
      setTimeout(() => {
        this.setupVideoElements();
      }, 1500);
    }
  }
  /**
   * Crée les éléments vidéo manuellement si les ViewChild ne fonctionnent pas
   */
  createVideoElementsManually() {
    // Chercher les éléments dans le DOM
    const localVideoEl = document.getElementById('localVideo');
    const remoteVideoEl = document.getElementById('remoteVideo');
    if (localVideoEl && remoteVideoEl) {
      console.log('✅ [MessageChat] Found video elements in DOM, configuring...');
      this.callService.setVideoElements(localVideoEl, remoteVideoEl);
    } else {
      console.warn('⚠️ [MessageChat] Video elements not found in DOM either');
      // Créer les éléments dynamiquement
      const localVideo = document.createElement('video');
      localVideo.id = 'localVideo';
      localVideo.autoplay = true;
      localVideo.muted = true;
      localVideo.playsInline = true;
      localVideo.style.cssText = 'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';
      const remoteVideo = document.createElement('video');
      remoteVideo.id = 'remoteVideo';
      remoteVideo.autoplay = true;
      remoteVideo.playsInline = true;
      remoteVideo.style.cssText = 'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';
      document.body.appendChild(localVideo);
      document.body.appendChild(remoteVideo);
      this.callService.setVideoElements(localVideo, remoteVideo);
    }
  }
  enableSoundsOnFirstInteraction() {
    const enableSounds = () => {
      this.callService.enableSounds();
      document.removeEventListener('click', enableSounds);
      document.removeEventListener('keydown', enableSounds);
      document.removeEventListener('touchstart', enableSounds);
    };
    document.addEventListener('click', enableSounds, {
      once: true
    });
    document.addEventListener('keydown', enableSounds, {
      once: true
    });
    document.addEventListener('touchstart', enableSounds, {
      once: true
    });
  }
  initializeComponent() {
    this.loadCurrentUser();
    this.loadConversation();
    this.setupCallSubscriptions();
  }
  setupCallSubscriptions() {
    // Les appels sont maintenant gérés globalement par app-incoming-call et app-active-call
    // Plus besoin de subscriptions locales ici
    console.log('📞 [MessageChat] Call subscriptions handled globally');
  }
  handleIncomingCall(incomingCall) {
    // Afficher une notification ou modal d'appel entrant
    // Pour l'instant, on log juste
    console.log('🔔 Handling incoming call from:', incomingCall.caller.username);
    // Jouer la sonnerie
    this.MessageService.play('ringtone');
    // Ici on pourrait afficher une modal ou notification
    // Pour l'instant, on accepte automatiquement pour tester
    // this.acceptCall(incomingCall);
  }

  loadCurrentUser() {
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString === 'null' || userString === 'undefined') {
        console.error('❌ No user data in localStorage');
        this.currentUserId = null;
        this.currentUsername = 'You';
        return;
      }
      const user = JSON.parse(userString);
      // Essayer différentes propriétés pour l'ID utilisateur
      const userId = user._id || user.id || user.userId;
      if (userId) {
        this.currentUserId = userId;
        this.currentUsername = user.username || user.name || 'You';
      } else {
        console.error('❌ No valid user ID found in user object:', user);
        this.currentUserId = null;
        this.currentUsername = 'You';
      }
    } catch (error) {
      console.error('❌ Error parsing user from localStorage:', error);
      this.currentUserId = null;
      this.currentUsername = 'You';
    }
  }
  loadConversation() {
    const conversationId = this.route.snapshot.paramMap.get('id');
    if (!conversationId) {
      this.toastService.showError('ID de conversation manquant');
      return;
    }
    this.isLoading = true;
    // Nettoyer les subscriptions existantes avant de recharger
    this.cleanupSubscriptions();
    this.MessageService.getConversation(conversationId).subscribe({
      next: conversation => {
        this.conversation = conversation;
        this.setOtherParticipant();
        this.loadMessages();
        // Configurer les subscriptions temps réel après le chargement de la conversation
        this.setupSubscriptions();
        this.isLoading = false;
      },
      error: error => {
        console.error('Erreur lors du chargement de la conversation:', error);
        this.toastService.showError('Erreur lors du chargement de la conversation');
        this.isLoading = false;
        // Réessayer après 2 secondes en cas d'erreur
        setTimeout(() => {
          this.loadConversation();
        }, 2000);
      }
    });
  }
  setOtherParticipant() {
    if (!this.conversation?.participants || this.conversation.participants.length === 0) {
      console.warn('No participants found in conversation');
      this.otherParticipant = null;
      return;
    }
    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)
    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant
    if (this.conversation.isGroup) {
      // Pour les groupes, on pourrait afficher le nom du groupe
      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel
      this.otherParticipant = this.conversation.participants.find(p => {
        const participantId = p.id || p._id;
        return String(participantId) !== String(this.currentUserId);
      });
    } else {
      // Pour les conversations 1-à-1, on prend l'autre participant
      this.otherParticipant = this.conversation.participants.find(p => {
        const participantId = p.id || p._id;
        console.log('Comparing participant ID:', participantId, 'with current user ID:', this.currentUserId);
        return String(participantId) !== String(this.currentUserId);
      });
    }
    // Fallback si aucun autre participant n'est trouvé
    if (!this.otherParticipant && this.conversation.participants.length > 0) {
      this.otherParticipant = this.conversation.participants[0];
      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres
      if (this.conversation.participants.length > 1) {
        const firstParticipantId = this.otherParticipant.id || this.otherParticipant._id;
        if (String(firstParticipantId) === String(this.currentUserId)) {
          console.log('First participant is current user, using second participant');
          this.otherParticipant = this.conversation.participants[1];
        }
      }
    }
    // Vérification finale et logs
    if (this.otherParticipant) {
      // Log très visible pour debug
      console.log('🎯 FINAL RESULT: otherParticipant =', this.otherParticipant.username);
      console.log('🎯 Should display in sidebar:', this.otherParticipant.username);
    } else {
      console.error('❌ No other participant found! This should not happen.');
      // Log très visible pour debug
    }
    // Mettre à jour l'état du champ de saisie
    this.updateInputState();
  }
  loadMessages() {
    if (!this.conversation?.id) return;
    // Les messages sont déjà chargés avec la conversation
    let messages = this.conversation.messages || [];
    // Trier les messages par timestamp (plus anciens en premier)
    this.messages = messages.sort((a, b) => {
      const dateA = new Date(a.timestamp || a.createdAt).getTime();
      const dateB = new Date(b.timestamp || b.createdAt).getTime();
      return dateA - dateB; // Ordre croissant (plus anciens en premier)
    });

    console.log('📋 Messages loaded and sorted:', {
      total: this.messages.length,
      first: this.messages[0]?.content,
      last: this.messages[this.messages.length - 1]?.content
    });
    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;
    this.isLoading = false;
    this.scrollToBottom();
  }
  loadMoreMessages() {
    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id) return;
    this.isLoadingMore = true;
    this.currentPage++;
    // Calculer l'offset basé sur les messages déjà chargés
    const offset = this.messages.length;
    this.MessageService.getMessages(this.currentUserId,
    // senderId
    this.otherParticipant?.id || this.otherParticipant?._id,
    // receiverId
    this.conversation.id, this.currentPage, this.MAX_MESSAGES_TO_LOAD).subscribe({
      next: newMessages => {
        if (newMessages && newMessages.length > 0) {
          // Ajouter les nouveaux messages au début de la liste
          this.messages = [...newMessages.reverse(), ...this.messages];
          this.hasMoreMessages = newMessages.length === this.MAX_MESSAGES_TO_LOAD;
        } else {
          this.hasMoreMessages = false;
        }
        this.isLoadingMore = false;
      },
      error: error => {
        console.error('Erreur lors du chargement des messages:', error);
        this.toastService.showError('Erreur lors du chargement des messages');
        this.isLoadingMore = false;
        this.currentPage--; // Revenir à la page précédente en cas d'erreur
      }
    });
  }
  /**
   * ✅ Optimized subscription cleanup with memory management
   */
  cleanupSubscriptions() {
    this.subscriptions.unsubscribe();
    this.subscriptions = new rxjs__WEBPACK_IMPORTED_MODULE_7__.Subscription();
    // ✅ Clear message cache to free memory when switching conversations
    if (this.messages.length > 100) {
      this.messages = this.messages.slice(-50); // Keep only last 50 messages
    }
  }
  /**
   * Recharge la conversation actuelle
   */
  reloadConversation() {
    if (this.conversation?.id) {
      // Réinitialiser l'état
      this.messages = [];
      this.currentPage = 1;
      this.hasMoreMessages = true;
      // Recharger
      this.loadConversation();
    }
  }
  setupSubscriptions() {
    if (!this.conversation?.id) {
      console.warn('❌ Cannot setup subscriptions: no conversation ID');
      return;
    }
    console.log('🔄 Setting up real-time subscriptions for conversation:', this.conversation.id);
    // Subscription pour les nouveaux messages
    this.subscriptions.add(this.MessageService.subscribeToNewMessages(this.conversation.id).subscribe({
      next: newMessage => {
        // ✅ Reduced logging for better performance
        if (_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.production === false) {
          console.log('📨 New message received:', newMessage.id);
        }
        // Ajouter le message à la liste s'il n'existe pas déjà
        const messageExists = this.messages.some(msg => msg.id === newMessage.id);
        if (!messageExists) {
          // Ajouter le nouveau message à la fin (en bas)
          this.messages.push(newMessage);
          console.log('✅ Message added to list, total messages:', this.messages.length);
          // Forcer la détection de changements
          this.cdr.detectChanges();
          // Scroll vers le bas après un court délai
          setTimeout(() => {
            this.scrollToBottom();
          }, 50);
          // Marquer comme lu si ce n'est pas notre message
          const senderId = newMessage.sender?.id || newMessage.senderId;
          console.log('📨 Checking if message should be marked as read:', {
            senderId,
            currentUserId: this.currentUserId,
            shouldMarkAsRead: senderId !== this.currentUserId
          });
          if (senderId && senderId !== this.currentUserId) {
            this.markMessageAsRead(newMessage.id);
          }
        }
      },
      error: error => {
        console.error('❌ Error in message subscription:', error);
        this.toastService.showError('Connexion temps réel interrompue');
        // Réessayer la connexion après 5 secondes
        setTimeout(() => {
          this.setupSubscriptions();
        }, 5000);
      }
    }));
    // Subscription pour les indicateurs de frappe
    this.subscriptions.add(this.MessageService.subscribeToTypingIndicator(this.conversation.id).subscribe({
      next: typingData => {
        // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape
        if (typingData.userId !== this.currentUserId) {
          this.otherUserIsTyping = typingData.isTyping;
          this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template
          console.log('📝 Other user typing status updated:', this.otherUserIsTyping);
          this.cdr.detectChanges();
        }
      },
      error: error => {
        console.error('❌ Error in typing subscription:', error);
      }
    }));
    // Subscription pour les mises à jour de conversation
    this.subscriptions.add(this.MessageService.subscribeToConversationUpdates(this.conversation.id).subscribe({
      next: conversationUpdate => {
        // Mettre à jour la conversation si nécessaire
        if (conversationUpdate.id === this.conversation.id) {
          this.conversation = {
            ...this.conversation,
            ...conversationUpdate
          };
          this.cdr.detectChanges();
        }
      },
      error: error => {
        console.error('❌ Error in conversation subscription:', error);
      }
    }));
  }
  markMessageAsRead(messageId) {
    this.MessageService.markMessageAsRead(messageId).subscribe({
      next: () => {},
      error: error => {
        console.error('❌ Error marking message as read:', error);
      }
    });
  }
  // === ENVOI DE MESSAGES ===
  sendMessage() {
    if (!this.messageForm.valid || !this.conversation?.id) return;
    const content = this.messageForm.get('content')?.value?.trim();
    if (!content) return;
    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;
    if (!receiverId) {
      this.toastService.showError('Destinataire introuvable');
      return;
    }
    // Désactiver le bouton d'envoi
    this.isSendingMessage = true;
    this.updateInputState();
    console.log('📤 Sending message:', {
      content,
      receiverId,
      conversationId: this.conversation.id
    });
    this.MessageService.sendMessage(receiverId, content, undefined, 'TEXT', this.conversation.id).subscribe({
      next: message => {
        // Ajouter le message à la liste s'il n'y est pas déjà
        const messageExists = this.messages.some(msg => msg.id === message.id);
        if (!messageExists) {
          this.messages.push(message);
          console.log('📋 Message added to local list, total:', this.messages.length);
        }
        // Réinitialiser le formulaire
        this.messageForm.reset();
        this.isSendingMessage = false;
        this.updateInputState();
        // Forcer la détection de changements et scroll
        this.cdr.detectChanges();
        setTimeout(() => {
          this.scrollToBottom();
        }, 50);
      },
      error: error => {
        console.error("❌ Erreur lors de l'envoi du message:", error);
        this.toastService.showError("Erreur lors de l'envoi du message");
        this.isSendingMessage = false;
        this.updateInputState();
      }
    });
  }
  scrollToBottom() {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }
  // === MÉTHODES UTILITAIRES OPTIMISÉES ===
  formatLastActive(lastActive) {
    if (!lastActive) return 'Hors ligne';
    const diffMins = Math.floor((Date.now() - new Date(lastActive).getTime()) / 60000);
    if (diffMins < 1) return "À l'instant";
    if (diffMins < 60) return `Il y a ${diffMins} min`;
    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;
    return `Il y a ${Math.floor(diffMins / 1440)}j`;
  }
  // Méthodes utilitaires pour les messages vocaux
  getVoicePlaybackData(messageId) {
    return this.voicePlayback[messageId] || {
      progress: 0,
      duration: 0,
      currentTime: 0,
      speed: 1
    };
  }
  setVoicePlaybackData(messageId, data) {
    this.voicePlayback[messageId] = {
      ...this.getVoicePlaybackData(messageId),
      ...data
    };
  }
  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===
  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===
  startVideoCall() {
    if (!this.otherParticipant?.id) {
      this.toastService.showError("Impossible de démarrer l'appel");
      return;
    }
    this.initiateCall(_models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType.VIDEO);
  }
  startVoiceCall() {
    if (!this.otherParticipant?.id) {
      this.toastService.showError("Impossible de démarrer l'appel");
      return;
    }
    // Forcer la configuration des éléments vidéo avant l'appel
    this.setupVideoElements();
    this.initiateCall(_models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType.AUDIO);
  }
  // Méthodes d'appel supprimées - Gérées par ActiveCallComponent et IncomingCallComponent
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // onCallAccepted, onCallRejected - définies plus loin
  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===
  formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';
    return Math.round(bytes / 1048576) + ' MB';
  }
  downloadFile(message) {
    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));
    if (fileAttachment?.url) {
      const link = document.createElement('a');
      link.href = fileAttachment.url;
      link.download = fileAttachment.name || 'file';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.toastService.showSuccess('Téléchargement démarré');
    }
  }
  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===
  toggleSearch() {
    this.searchMode = !this.searchMode;
    this.showSearch = this.searchMode;
  }
  toggleMainMenu() {
    this.showMainMenu = !this.showMainMenu;
  }
  goBackToConversations() {
    // Naviguer vers la liste des conversations
    this.router.navigate(['/front/messages/conversations']).then(() => {}).catch(error => {
      console.error('❌ Navigation error:', error);
      // Fallback: essayer la route parent
      this.router.navigate(['/front/messages']).catch(() => {
        // Dernier recours: recharger la page
        window.location.href = '/front/messages/conversations';
      });
    });
  }
  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===
  closeAllMenus() {
    this.showEmojiPicker = false;
    this.showAttachmentMenu = false;
    this.showMainMenu = false;
    this.showMessageContextMenu = false;
    this.showReactionPicker = false;
  }
  onMessageContextMenu(message, event) {
    event.preventDefault();
    this.selectedMessage = message;
    this.contextMenuPosition = {
      x: event.clientX,
      y: event.clientY
    };
    this.showMessageContextMenu = true;
  }
  showQuickReactions(message, event) {
    event.stopPropagation();
    this.reactionPickerMessage = message;
    this.contextMenuPosition = {
      x: event.clientX,
      y: event.clientY
    };
    this.showReactionPicker = true;
  }
  quickReact(emoji) {
    if (this.reactionPickerMessage) {
      this.toggleReaction(this.reactionPickerMessage.id, emoji);
    }
    this.showReactionPicker = false;
  }
  toggleReaction(messageId, emoji) {
    if (!messageId || !emoji) {
      console.error('❌ Missing messageId or emoji for reaction');
      return;
    }
    // Appeler le service pour ajouter/supprimer la réaction
    this.MessageService.reactToMessage(messageId, emoji).subscribe({
      next: result => {
        // Mettre à jour le message local avec les nouvelles réactions
        const messageIndex = this.messages.findIndex(msg => msg.id === messageId);
        if (messageIndex !== -1) {
          this.messages[messageIndex] = result;
          this.cdr.detectChanges();
        }
        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);
      },
      error: error => {
        console.error('❌ Error toggling reaction:', error);
        this.toastService.showError("Erreur lors de l'ajout de la réaction");
      }
    });
  }
  hasUserReacted(reaction, userId) {
    return reaction.userId === userId;
  }
  replyToMessage(message) {
    this.closeAllMenus();
  }
  forwardMessage(message) {
    this.closeAllMenus();
  }
  deleteMessage(message) {
    if (!message.id) {
      console.error('❌ No message ID provided for deletion');
      this.toastService.showError('Erreur: ID du message manquant');
      return;
    }
    // Vérifier si l'utilisateur peut supprimer ce message
    const canDelete = message.sender?.id === this.currentUserId || message.senderId === this.currentUserId;
    if (!canDelete) {
      this.toastService.showError('Vous ne pouvez supprimer que vos propres messages');
      this.closeAllMenus();
      return;
    }
    // Demander confirmation
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {
      this.closeAllMenus();
      return;
    }
    // Appeler le service pour supprimer le message
    this.MessageService.deleteMessage(message.id).subscribe({
      next: result => {
        // Supprimer le message de la liste locale
        this.messages = this.messages.filter(msg => msg.id !== message.id);
        this.toastService.showSuccess('Message supprimé');
        this.cdr.detectChanges();
      },
      error: error => {
        console.error('❌ Error deleting message:', error);
        this.toastService.showError('Erreur lors de la suppression du message');
      }
    });
    this.closeAllMenus();
  }
  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===
  toggleEmojiPicker() {
    this.showEmojiPicker = !this.showEmojiPicker;
  }
  selectEmojiCategory(category) {
    this.selectedEmojiCategory = category;
  }
  getEmojisForCategory(category) {
    return category?.emojis || [];
  }
  insertEmoji(emoji) {
    const currentContent = this.messageForm.get('content')?.value || '';
    const newContent = currentContent + emoji.emoji;
    this.messageForm.patchValue({
      content: newContent
    });
    this.showEmojiPicker = false;
  }
  toggleAttachmentMenu() {
    this.showAttachmentMenu = !this.showAttachmentMenu;
  }
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // Ces méthodes sont déjà définies plus loin dans le fichier
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // handleTypingIndicator - définie plus loin
  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===
  trackByMessageId(index, message) {
    return message.id || message._id || index.toString();
  }
  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===
  testAddMessage() {
    const testMessage = {
      id: `test-${Date.now()}`,
      content: `Message de test ${new Date().toLocaleTimeString()}`,
      timestamp: new Date().toISOString(),
      sender: {
        id: this.otherParticipant?.id || 'test-user',
        username: this.otherParticipant?.username || 'Test User',
        image: this.otherParticipant?.image || 'assets/images/default-avatar.png'
      },
      type: 'TEXT',
      isRead: false
    };
    this.messages.push(testMessage);
    this.cdr.detectChanges();
    setTimeout(() => this.scrollToBottom(), 50);
  }
  isGroupConversation() {
    return this.conversation?.isGroup || this.conversation?.participants?.length > 2 || false;
  }
  openCamera() {
    this.showAttachmentMenu = false;
    // TODO: Implémenter l'ouverture de la caméra
  }

  zoomImage(factor) {
    const imageElement = document.querySelector('.image-viewer-zoom');
    if (imageElement) {
      const currentTransform = imageElement.style.transform || 'scale(1)';
      const currentScale = parseFloat(currentTransform.match(/scale\(([^)]+)\)/)?.[1] || '1');
      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));
      imageElement.style.transform = `scale(${newScale})`;
      if (newScale > 1) {
        imageElement.classList.add('zoomed');
      } else {
        imageElement.classList.remove('zoomed');
      }
    }
  }
  resetZoom() {
    const imageElement = document.querySelector('.image-viewer-zoom');
    if (imageElement) {
      imageElement.style.transform = 'scale(1)';
      imageElement.classList.remove('zoomed');
    }
  }
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions
  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji
  // Ces méthodes sont déjà définies plus loin dans le fichier
  triggerFileInput(type) {
    const input = this.fileInput?.nativeElement;
    if (!input) {
      console.error('File input element not found');
      return;
    }
    // Configurer le type de fichier accepté
    if (type === 'image') {
      input.accept = 'image/*';
    } else if (type === 'video') {
      input.accept = 'video/*';
    } else if (type === 'document') {
      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';
    } else {
      input.accept = '*/*';
    }
    // Réinitialiser la valeur pour permettre la sélection du même fichier
    input.value = '';
    // Déclencher la sélection de fichier
    input.click();
    this.showAttachmentMenu = false;
  }
  formatMessageTime(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  formatDateSeparator(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.toDateString() === today.toDateString()) {
      return "Aujourd'hui";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hier';
    } else {
      return date.toLocaleDateString('fr-FR');
    }
  }
  formatMessageContent(content) {
    if (!content) return '';
    // Remplacer les URLs par des liens
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return content.replace(urlRegex, '<a href="$1" target="_blank" class="text-blue-500 underline">$1</a>');
  }
  shouldShowDateSeparator(index) {
    if (index === 0) return true;
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];
    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;
    const currentDate = new Date(currentMessage.timestamp).toDateString();
    const previousDate = new Date(previousMessage.timestamp).toDateString();
    return currentDate !== previousDate;
  }
  shouldShowAvatar(index) {
    const currentMessage = this.messages[index];
    const nextMessage = this.messages[index + 1];
    if (!nextMessage) return true;
    return currentMessage.sender?.id !== nextMessage.sender?.id;
  }
  shouldShowSenderName(index) {
    const currentMessage = this.messages[index];
    const previousMessage = this.messages[index - 1];
    if (!previousMessage) return true;
    return currentMessage.sender?.id !== previousMessage.sender?.id;
  }
  getMessageType(message) {
    // Vérifier d'abord le type de message explicite
    if (message.type) {
      if (message.type === 'IMAGE' || message.type === 'image') return 'image';
      if (message.type === 'VIDEO' || message.type === 'video') return 'video';
      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';
      if (message.type === 'VOICE_MESSAGE') return 'audio';
      if (message.type === 'FILE' || message.type === 'file') return 'file';
    }
    // Ensuite vérifier les attachments
    if (message.attachments && message.attachments.length > 0) {
      const attachment = message.attachments[0];
      if (attachment.type?.startsWith('image/')) return 'image';
      if (attachment.type?.startsWith('video/')) return 'video';
      if (attachment.type?.startsWith('audio/')) return 'audio';
      return 'file';
    }
    // Vérifier si c'est un message vocal basé sur les propriétés
    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';
    return 'text';
  }
  hasImage(message) {
    // Vérifier le type de message
    if (message.type === 'IMAGE' || message.type === 'image') {
      return true;
    }
    // Vérifier les attachments
    const hasImageAttachment = message.attachments?.some(att => {
      return att.type?.startsWith('image/') || att.type === 'IMAGE';
    }) || false;
    // Vérifier les propriétés directes d'image
    const hasImageUrl = !!(message.imageUrl || message.image);
    return hasImageAttachment || hasImageUrl;
  }
  hasFile(message) {
    // Vérifier le type de message
    if (message.type === 'FILE' || message.type === 'file') {
      return true;
    }
    // Vérifier les attachments non-image
    const hasFileAttachment = message.attachments?.some(att => {
      return !att.type?.startsWith('image/') && att.type !== 'IMAGE';
    }) || false;
    return hasFileAttachment;
  }
  getImageUrl(message) {
    // Vérifier les propriétés directes d'image
    if (message.imageUrl) {
      return message.imageUrl;
    }
    if (message.image) {
      return message.image;
    }
    // Vérifier les attachments
    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/') || att.type === 'IMAGE');
    if (imageAttachment) {
      return imageAttachment.url || imageAttachment.path || '';
    }
    return '';
  }
  getFileName(message) {
    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));
    return fileAttachment?.name || 'Fichier';
  }
  getFileSize(message) {
    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));
    if (!fileAttachment?.size) return '';
    const bytes = fileAttachment.size;
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';
    return Math.round(bytes / 1048576) + ' MB';
  }
  getFileIcon(message) {
    const fileAttachment = message.attachments?.find(att => !att.type?.startsWith('image/'));
    if (!fileAttachment?.type) return 'fas fa-file';
    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';
    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';
    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';
    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';
    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';
    return 'fas fa-file';
  }
  getUserColor(userId) {
    // Générer une couleur basée sur l'ID utilisateur
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    const index = userId.charCodeAt(0) % colors.length;
    return colors[index];
  }
  // === MÉTHODES D'INTERACTION ===
  onMessageClick(message, event) {}
  onInputChange(event) {
    // Gérer les changements dans le champ de saisie
    this.handleTypingIndicator();
  }
  onInputKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }
  onInputFocus() {
    // Gérer le focus sur le champ de saisie
  }
  onInputBlur() {
    // Gérer la perte de focus sur le champ de saisie
  }
  onScroll(event) {
    // Gérer le scroll pour charger plus de messages
    const element = event.target;
    if (element.scrollTop === 0 && this.hasMoreMessages && !this.isLoadingMore) {
      this.loadMoreMessages();
    }
  }
  openUserProfile(userId) {}
  onImageLoad(event, message) {
    console.log('🖼️ [Debug] Image loaded successfully for message:', message.id, event.target.src);
  }
  onImageError(event, message) {
    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {
      src: event.target.src,
      error: event
    });
    // Optionnel : afficher une image de remplacement
    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';
  }
  openImageViewer(message) {
    const imageAttachment = message.attachments?.find(att => att.type?.startsWith('image/'));
    if (imageAttachment?.url) {
      this.selectedImage = {
        url: imageAttachment.url,
        name: imageAttachment.name || 'Image',
        size: this.formatFileSize(imageAttachment.size || 0),
        message: message
      };
      this.showImageViewer = true;
    }
  }
  closeImageViewer() {
    this.showImageViewer = false;
    this.selectedImage = null;
  }
  downloadImage() {
    if (this.selectedImage?.url) {
      const link = document.createElement('a');
      link.href = this.selectedImage.url;
      link.download = this.selectedImage.name || 'image';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.toastService.showSuccess('Téléchargement démarré');
      console.log('🖼️ [ImageViewer] Download started:', this.selectedImage.name);
    }
  }
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch
  // Ces méthodes sont déjà définies plus loin dans le fichier
  searchMessages() {
    if (!this.searchQuery.trim()) {
      this.searchResults = [];
      return;
    }
    this.searchResults = this.messages.filter(message => message.content?.toLowerCase().includes(this.searchQuery.toLowerCase()) || message.sender?.username?.toLowerCase().includes(this.searchQuery.toLowerCase()));
  }
  onSearchQueryChange() {
    this.searchMessages();
  }
  clearSearch() {
    this.searchQuery = '';
    this.searchResults = [];
  }
  jumpToMessage(messageId) {
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      // Highlight temporairement le message
      messageElement.classList.add('highlight');
      setTimeout(() => {
        messageElement.classList.remove('highlight');
      }, 2000);
    }
  }
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // toggleMainMenu, toggleTheme, onMessageContextMenu
  // Ces méthodes sont déjà définies plus loin dans le fichier
  closeContextMenu() {
    this.showMessageContextMenu = false;
    this.selectedMessage = null;
  }
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker
  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker
  // Ces méthodes sont déjà définies plus loin dans le fichier
  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===
  // triggerFileInput - définie plus loin
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji
  // goBackToConversations, startVideoCall, startVoiceCall
  // Ces méthodes sont déjà définies plus loin dans le fichier
  initiateCall(callType) {
    console.log('📋 [MessageChat] Call details:', {
      callType,
      otherParticipant: this.otherParticipant,
      conversation: this.conversation?.id,
      currentUserId: this.currentUserId
    });
    if (!this.otherParticipant) {
      console.error('❌ [MessageChat] No recipient selected');
      this.toastService.showError('Aucun destinataire sélectionné');
      return;
    }
    const recipientId = this.otherParticipant.id || this.otherParticipant._id;
    if (!recipientId) {
      console.error('❌ [MessageChat] Recipient ID not found');
      this.toastService.showError('ID du destinataire introuvable');
      return;
    }
    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {
      recipientId,
      recipientName: this.otherParticipant.username || this.otherParticipant.name,
      conversationId: this.conversation?.id
    });
    this.isInCall = true;
    this.callType = callType === _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType.VIDEO ? 'VIDEO' : 'AUDIO';
    this.callDuration = 0;
    // Démarrer le timer d'appel
    this.startCallTimer();
    // Utiliser le CallService
    this.callService.initiateCall(recipientId, callType, this.conversation?.id).subscribe({
      next: call => {
        // L'appel est maintenant géré globalement par ActiveCallComponent
        this.toastService.showSuccess(`Appel ${callType === _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType.VIDEO ? 'vidéo' : 'audio'} initié`);
        console.log('📡 [MessageChat] Call should now be sent to recipient via WebSocket');
      },
      error: error => {
        console.error('❌ [MessageChat] Error initiating call:', {
          error: error.message || error,
          recipientId,
          callType,
          conversationId: this.conversation?.id
        });
        // Gestion d'erreur - l'appel sera automatiquement nettoyé par CallService
        this.toastService.showError("Erreur lors de l'initiation de l'appel");
      }
    });
  }
  acceptCall(incomingCall) {
    // L'acceptation d'appel est maintenant gérée par IncomingCallComponent
    console.log('📞 [MessageChat] Call acceptance handled by IncomingCallComponent');
  }
  rejectCall(incomingCall) {
    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({
      next: () => {
        this.toastService.showSuccess('Appel rejeté');
      },
      error: error => {
        console.error('❌ Error rejecting call:', error);
        this.toastService.showError("Erreur lors du rejet de l'appel");
      }
    });
  }
  startCallTimer() {
    this.callDuration = 0;
    this.callTimer = setInterval(() => {
      this.callDuration++;
      this.cdr.detectChanges();
    }, 1000);
  }
  resetCallState() {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
    this.isInCall = false;
    this.callType = null;
    this.callDuration = 0;
    // activeCall, isCallConnected, isMuted, isVideoEnabled gérés par ActiveCallComponent
  }
  // === CONTRÔLES D'APPEL ===
  // Contrôles d'appel maintenant gérés par ActiveCallComponent
  toggleMute() {
    console.log('📞 [MessageChat] Mute toggle handled by ActiveCallComponent');
  }
  toggleVideo() {
    console.log('📞 [MessageChat] Video toggle handled by ActiveCallComponent');
  }
  /**
   * Termine l'appel en cours
   */
  endCall() {
    console.log('📞 [MessageChat] Ending call...');
    // Réinitialiser l'état local
    this.isInCall = false;
    this.callType = null;
    this.callDuration = 0;
    // Arrêter le timer si actif
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
    // Le CallService gère la fin d'appel globalement
    // Les composants ActiveCall et IncomingCall se mettront à jour automatiquement
    this.toastService.showSuccess('Appel terminé');
  }
  formatCallDuration(duration) {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor(duration % 3600 / 60);
    const seconds = duration % 60;
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===
  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier
  startVoiceRecording() {
    var _this = this;
    return (0,C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        // Vérifier le support du navigateur
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error("Votre navigateur ne supporte pas l'enregistrement audio");
        }
        // Vérifier si MediaRecorder est supporté
        if (!window.MediaRecorder) {
          throw new Error("MediaRecorder n'est pas supporté par votre navigateur");
        }
        // Demander l'accès au microphone avec des contraintes optimisées
        const stream = yield navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100,
            channelCount: 1
          }
        });
        // Vérifier les types MIME supportés
        let mimeType = 'audio/webm;codecs=opus';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'audio/webm';
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'audio/mp4';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
              mimeType = ''; // Laisser le navigateur choisir
            }
          }
        }
        // Créer le MediaRecorder
        _this.mediaRecorder = new MediaRecorder(stream, {
          mimeType: mimeType || undefined
        });
        // Initialiser les variables
        _this.audioChunks = [];
        _this.isRecordingVoice = true;
        _this.voiceRecordingDuration = 0;
        _this.voiceRecordingState = 'recording';
        // Démarrer le timer
        _this.recordingTimer = setInterval(() => {
          _this.voiceRecordingDuration++;
          // Animer les waves
          _this.animateVoiceWaves();
          _this.cdr.detectChanges();
        }, 1000);
        // Gérer les événements du MediaRecorder
        _this.mediaRecorder.ondataavailable = event => {
          if (event.data.size > 0) {
            _this.audioChunks.push(event.data);
          }
        };
        _this.mediaRecorder.onstop = () => {
          _this.processRecordedAudio();
        };
        _this.mediaRecorder.onerror = event => {
          console.error('🎤 [Voice] MediaRecorder error:', event.error);
          _this.toastService.showError("Erreur lors de l'enregistrement");
          _this.cancelVoiceRecording();
        };
        // Démarrer l'enregistrement
        _this.mediaRecorder.start(100); // Collecter les données toutes les 100ms
        _this.toastService.showSuccess('🎤 Enregistrement vocal démarré');
      } catch (error) {
        console.error('🎤 [Voice] Error starting recording:', error);
        let errorMessage = "Impossible de démarrer l'enregistrement vocal";
        if (error.name === 'NotAllowedError') {
          errorMessage = "Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.";
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'Aucun microphone détecté. Veuillez connecter un microphone.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage = "Votre navigateur ne supporte pas l'enregistrement audio.";
        } else if (error.message) {
          errorMessage = error.message;
        }
        _this.toastService.showError(errorMessage);
        _this.cancelVoiceRecording();
      }
    })();
  }
  stopVoiceRecording() {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
    }
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
    this.isRecordingVoice = false;
    this.voiceRecordingState = 'processing';
  }
  cancelVoiceRecording() {
    if (this.mediaRecorder) {
      if (this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
      }
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
      this.mediaRecorder = null;
    }
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
    this.isRecordingVoice = false;
    this.voiceRecordingDuration = 0;
    this.voiceRecordingState = 'idle';
    this.audioChunks = [];
  }
  processRecordedAudio() {
    var _this2 = this;
    return (0,C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        // Vérifier qu'on a des données audio
        if (_this2.audioChunks.length === 0) {
          console.error('🎤 [Voice] No audio chunks available');
          _this2.toastService.showError('Aucun audio enregistré');
          _this2.cancelVoiceRecording();
          return;
        }
        console.log('🎤 [Voice] Audio chunks:', _this2.audioChunks.length, 'Duration:', _this2.voiceRecordingDuration);
        // Vérifier la durée minimale
        if (_this2.voiceRecordingDuration < 1) {
          console.error('🎤 [Voice] Recording too short:', _this2.voiceRecordingDuration);
          _this2.toastService.showError('Enregistrement trop court (minimum 1 seconde)');
          _this2.cancelVoiceRecording();
          return;
        }
        // Déterminer le type MIME du blob
        let mimeType = 'audio/webm;codecs=opus';
        if (_this2.mediaRecorder?.mimeType) {
          mimeType = _this2.mediaRecorder.mimeType;
        }
        // Créer le blob audio
        const audioBlob = new Blob(_this2.audioChunks, {
          type: mimeType
        });
        console.log('🎤 [Voice] Audio blob created:', {
          size: audioBlob.size,
          type: audioBlob.type
        });
        // Déterminer l'extension du fichier
        let extension = '.webm';
        if (mimeType.includes('mp4')) {
          extension = '.mp4';
        } else if (mimeType.includes('wav')) {
          extension = '.wav';
        } else if (mimeType.includes('ogg')) {
          extension = '.ogg';
        }
        // Créer le fichier
        const audioFile = new File([audioBlob], `voice_${Date.now()}${extension}`, {
          type: mimeType
        });
        console.log('🎤 [Voice] Audio file created:', {
          name: audioFile.name,
          size: audioFile.size,
          type: audioFile.type
        });
        // Envoyer le message vocal
        _this2.voiceRecordingState = 'processing';
        yield _this2.sendVoiceMessage(audioFile);
        _this2.toastService.showSuccess('🎤 Message vocal envoyé');
      } catch (error) {
        console.error('🎤 [Voice] Error processing audio:', error);
        _this2.toastService.showError("Erreur lors de l'envoi du message vocal: " + (error.message || 'Erreur inconnue'));
      } finally {
        // Nettoyer l'état
        _this2.voiceRecordingState = 'idle';
        _this2.voiceRecordingDuration = 0;
        _this2.audioChunks = [];
        _this2.isRecordingVoice = false;
      }
    })();
  }
  sendVoiceMessage(audioFile) {
    var _this3 = this;
    return (0,C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const receiverId = _this3.otherParticipant?.id || _this3.otherParticipant?._id;
      if (!receiverId) {
        throw new Error('Destinataire introuvable');
      }
      return new Promise((resolve, reject) => {
        _this3.MessageService.sendMessage(receiverId, '', audioFile, 'AUDIO', _this3.conversation.id).subscribe({
          next: message => {
            _this3.messages.push(message);
            _this3.scrollToBottom();
            resolve();
          },
          error: error => {
            console.error("Erreur lors de l'envoi du message vocal:", error);
            reject(error);
          }
        });
      });
    })();
  }
  formatRecordingDuration(duration) {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===
  onRecordStart(event) {
    event.preventDefault();
    console.log('🎤 [Voice] Current state:', {
      isRecordingVoice: this.isRecordingVoice,
      voiceRecordingState: this.voiceRecordingState,
      voiceRecordingDuration: this.voiceRecordingDuration,
      mediaRecorder: !!this.mediaRecorder
    });
    // Vérifier si on peut enregistrer
    if (this.voiceRecordingState === 'processing') {
      this.toastService.showWarning('Traitement en cours...');
      return;
    }
    if (this.isRecordingVoice) {
      this.toastService.showWarning('Enregistrement déjà en cours...');
      return;
    }
    // Afficher un message de début
    this.toastService.showInfo("🎤 Démarrage de l'enregistrement vocal...");
    // Démarrer l'enregistrement
    this.startVoiceRecording().catch(error => {
      console.error('🎤 [Voice] Failed to start recording:', error);
      this.toastService.showError("Impossible de démarrer l'enregistrement vocal: " + (error.message || 'Erreur inconnue'));
    });
  }
  onRecordEnd(event) {
    event.preventDefault();
    if (!this.isRecordingVoice) {
      return;
    }
    // Arrêter l'enregistrement et envoyer
    this.stopVoiceRecording();
  }
  onRecordCancel(event) {
    event.preventDefault();
    if (!this.isRecordingVoice) {
      return;
    }
    // Annuler l'enregistrement
    this.cancelVoiceRecording();
  }
  getRecordingFormat() {
    if (this.mediaRecorder?.mimeType) {
      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';
      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';
      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';
      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';
    }
    return 'Auto';
  }
  // === ANIMATION DES WAVES VOCALES ===
  animateVoiceWaves() {
    // Animer les waves pendant l'enregistrement
    this.voiceWaves = this.voiceWaves.map(() => {
      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px
    });
  }

  onFileSelected(event) {
    const files = event.target.files;
    if (!files || files.length === 0) {
      return;
    }
    for (let file of files) {
      console.log(`📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`);
      this.uploadFile(file);
    }
  }
  uploadFile(file) {
    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;
    if (!receiverId) {
      console.error('📁 [Upload] No receiver ID found');
      this.toastService.showError('Destinataire introuvable');
      return;
    }
    // Vérifier la taille du fichier (max 50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      console.error(`📁 [Upload] File too large: ${file.size} bytes`);
      this.toastService.showError('Fichier trop volumineux (max 50MB)');
      return;
    }
    // 🖼️ Compression d'image si nécessaire
    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {
      // > 1MB
      console.log('🖼️ [Compression] Compressing image:', file.name, 'Original size:', file.size);
      this.compressImage(file).then(compressedFile => {
        console.log('🖼️ [Compression] ✅ Image compressed successfully. New size:', compressedFile.size);
        this.sendFileToServer(compressedFile, receiverId);
      }).catch(error => {
        console.error('🖼️ [Compression] ❌ Error compressing image:', error);
        // Envoyer le fichier original en cas d'erreur
        this.sendFileToServer(file, receiverId);
      });
      return;
    }
    // Envoyer le fichier sans compression
    this.sendFileToServer(file, receiverId);
  }
  sendFileToServer(file, receiverId) {
    const messageType = this.getFileMessageType(file);
    this.isSendingMessage = true;
    this.isUploading = true;
    this.uploadProgress = 0;
    // Simuler la progression d'upload
    const progressInterval = setInterval(() => {
      this.uploadProgress += Math.random() * 15;
      if (this.uploadProgress >= 90) {
        clearInterval(progressInterval);
      }
      this.cdr.detectChanges();
    }, 300);
    this.MessageService.sendMessage(receiverId, '', file, messageType, this.conversation.id).subscribe({
      next: message => {
        console.log('📁 [Debug] Sent message structure:', {
          id: message.id,
          type: message.type,
          attachments: message.attachments,
          hasImage: this.hasImage(message),
          hasFile: this.hasFile(message),
          imageUrl: this.getImageUrl(message)
        });
        clearInterval(progressInterval);
        this.uploadProgress = 100;
        setTimeout(() => {
          this.messages.push(message);
          this.scrollToBottom();
          this.toastService.showSuccess('Fichier envoyé avec succès');
          this.resetUploadState();
        }, 500);
      },
      error: error => {
        console.error('📁 [Upload] ❌ Error sending file:', error);
        clearInterval(progressInterval);
        this.toastService.showError("Erreur lors de l'envoi du fichier");
        this.resetUploadState();
      }
    });
  }
  getFileMessageType(file) {
    if (file.type.startsWith('image/')) return 'IMAGE';
    if (file.type.startsWith('video/')) return 'VIDEO';
    if (file.type.startsWith('audio/')) return 'AUDIO';
    return 'FILE';
  }
  getFileAcceptTypes() {
    return '*/*';
  }
  resetUploadState() {
    this.isSendingMessage = false;
    this.isUploading = false;
    this.uploadProgress = 0;
  }
  // === DRAG & DROP ===
  onDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }
  onDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    // Vérifier si on quitte vraiment la zone (pas un enfant)
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      this.isDragOver = false;
    }
  }
  onDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      // Traiter chaque fichier
      Array.from(files).forEach(file => {
        console.log('📁 [Drag&Drop] Processing file:', file.name, file.type, file.size);
        this.uploadFile(file);
      });
      this.toastService.showSuccess(`${files.length} fichier(s) en cours d'envoi`);
    }
  }
  // === COMPRESSION D'IMAGES ===
  compressImage(file, quality = 0.8) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      img.onload = () => {
        // Calculer les nouvelles dimensions (max 1920x1080)
        const maxWidth = 1920;
        const maxHeight = 1080;
        let {
          width,
          height
        } = img;
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }
        canvas.width = width;
        canvas.height = height;
        // Dessiner l'image redimensionnée
        ctx?.drawImage(img, 0, 0, width, height);
        // Convertir en blob avec compression
        canvas.toBlob(blob => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        }, file.type, quality);
      };
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }
  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===
  handleTypingIndicator() {
    if (!this.isTyping) {
      this.isTyping = true;
      // Envoyer l'indicateur de frappe à l'autre utilisateur
      this.sendTypingIndicator(true);
    }
    // Reset le timer
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
    this.typingTimeout = setTimeout(() => {
      this.isTyping = false;
      // Arrêter l'indicateur de frappe
      this.sendTypingIndicator(false);
    }, 2000);
  }
  sendTypingIndicator(isTyping) {
    // Envoyer l'indicateur de frappe via WebSocket/GraphQL
    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;
    if (receiverId && this.conversation?.id) {
      console.log(`📝 Sending typing indicator: ${isTyping} to user ${receiverId}`);
      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription
      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);
    }
  }
  // === MÉTHODES POUR L'INTERFACE D'APPEL ===
  onCallAccepted(call) {
    // Gestion d'appel accepté maintenant dans ActiveCallComponent
    this.isInCall = true;
    this.startCallTimer();
    this.toastService.showSuccess('Appel accepté');
  }
  onCallRejected() {
    // Gestion de rejet d'appel maintenant dans IncomingCallComponent
    this.toastService.showInfo('Appel rejeté');
  }
  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===
  playVoiceMessage(message) {
    this.toggleVoicePlayback(message);
  }
  isVoicePlaying(messageId) {
    return this.playingMessageId === messageId;
  }
  toggleVoicePlayback(message) {
    const messageId = message.id;
    const audioUrl = this.getVoiceUrl(message);
    if (!audioUrl) {
      console.error('🎵 [Voice] No audio URL found for message:', messageId);
      this.toastService.showError('Fichier audio introuvable');
      return;
    }
    // Si c'est déjà en cours de lecture, arrêter
    if (this.isVoicePlaying(messageId)) {
      this.stopVoicePlayback();
      return;
    }
    // Arrêter toute autre lecture en cours
    this.stopVoicePlayback();
    // Démarrer la nouvelle lecture
    this.startVoicePlayback(message, audioUrl);
  }
  startVoicePlayback(message, audioUrl) {
    const messageId = message.id;
    try {
      console.log('🎵 [Voice] Starting playback for:', messageId, 'URL:', audioUrl);
      this.currentAudio = new Audio(audioUrl);
      this.playingMessageId = messageId;
      // Initialiser les valeurs par défaut avec la nouvelle structure
      const currentData = this.getVoicePlaybackData(messageId);
      this.setVoicePlaybackData(messageId, {
        progress: 0,
        currentTime: 0,
        speed: currentData.speed || 1,
        duration: currentData.duration || 0
      });
      // Configurer la vitesse de lecture
      this.currentAudio.playbackRate = currentData.speed || 1;
      // Événements audio
      this.currentAudio.addEventListener('loadedmetadata', () => {
        if (this.currentAudio) {
          this.setVoicePlaybackData(messageId, {
            duration: this.currentAudio.duration
          });
          console.log('🎵 [Voice] Audio loaded, duration:', this.currentAudio.duration);
        }
      });
      this.currentAudio.addEventListener('timeupdate', () => {
        if (this.currentAudio && this.playingMessageId === messageId) {
          const currentTime = this.currentAudio.currentTime;
          const progress = currentTime / this.currentAudio.duration * 100;
          this.setVoicePlaybackData(messageId, {
            currentTime,
            progress
          });
          this.cdr.detectChanges();
        }
      });
      this.currentAudio.addEventListener('ended', () => {
        this.stopVoicePlayback();
      });
      this.currentAudio.addEventListener('error', error => {
        console.error('🎵 [Voice] Audio error:', error);
        this.toastService.showError('Erreur lors de la lecture audio');
        this.stopVoicePlayback();
      });
      // Démarrer la lecture
      this.currentAudio.play().then(() => {
        this.toastService.showSuccess('🎵 Lecture du message vocal');
      }).catch(error => {
        console.error('🎵 [Voice] Error starting playback:', error);
        this.toastService.showError('Impossible de lire le message vocal');
        this.stopVoicePlayback();
      });
    } catch (error) {
      console.error('🎵 [Voice] Error creating audio:', error);
      this.toastService.showError('Erreur lors de la lecture audio');
      this.stopVoicePlayback();
    }
  }
  stopVoicePlayback() {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }
    this.playingMessageId = null;
    this.cdr.detectChanges();
  }
  getVoiceUrl(message) {
    // Vérifier les propriétés directes d'audio
    if (message.voiceUrl) return message.voiceUrl;
    if (message.audioUrl) return message.audioUrl;
    if (message.voice) return message.voice;
    // Vérifier les attachments audio
    const audioAttachment = message.attachments?.find(att => att.type?.startsWith('audio/') || att.type === 'AUDIO');
    if (audioAttachment) {
      return audioAttachment.url || audioAttachment.path || '';
    }
    return '';
  }
  getVoiceWaves(message) {
    // Générer des waves basées sur l'ID du message pour la cohérence
    const messageId = message.id || '';
    const seed = messageId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const waves = [];
    for (let i = 0; i < 16; i++) {
      const height = 4 + (seed + i * 7) % 20;
      waves.push(height);
    }
    return waves;
  }
  getVoiceProgress(message) {
    const data = this.getVoicePlaybackData(message.id);
    const totalWaves = 16;
    return Math.floor(data.progress / 100 * totalWaves);
  }
  getVoiceCurrentTime(message) {
    const data = this.getVoicePlaybackData(message.id);
    return this.formatAudioTime(data.currentTime);
  }
  getVoiceDuration(message) {
    const data = this.getVoicePlaybackData(message.id);
    const duration = data.duration || message.metadata?.duration || 0;
    if (typeof duration === 'string') {
      return duration; // Déjà formaté
    }

    return this.formatAudioTime(duration);
  }
  formatAudioTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  seekVoiceMessage(message, waveIndex) {
    const messageId = message.id;
    if (!this.currentAudio || this.playingMessageId !== messageId) {
      return;
    }
    const totalWaves = 16;
    const seekPercentage = waveIndex / totalWaves * 100;
    const seekTime = seekPercentage / 100 * this.currentAudio.duration;
    this.currentAudio.currentTime = seekTime;
  }
  toggleVoiceSpeed(message) {
    const messageId = message.id;
    const data = this.getVoicePlaybackData(messageId);
    // Cycle entre 1x, 1.5x, 2x
    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;
    this.setVoicePlaybackData(messageId, {
      speed: newSpeed
    });
    if (this.currentAudio && this.playingMessageId === messageId) {
      this.currentAudio.playbackRate = newSpeed;
    }
    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);
  }
  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===
  changeVoiceSpeed(message) {
    this.toggleVoiceSpeed(message);
  }
  getVoiceSpeed(message) {
    const data = this.getVoicePlaybackData(message.id);
    return data.speed || 1;
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    // Nettoyer les timers
    if (this.callTimer) {
      clearInterval(this.callTimer);
    }
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
    // Nettoyer les ressources audio
    if (this.mediaRecorder) {
      if (this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
      }
      this.mediaRecorder.stream?.getTracks().forEach(track => track.stop());
    }
    // Nettoyer la lecture audio
    this.stopVoicePlayback();
  }
  static {
    this.ɵfac = function MessageChatComponent_Factory(t) {
      return new (t || MessageChatComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_9__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_9__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_message_service__WEBPACK_IMPORTED_MODULE_3__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_call_service__WEBPACK_IMPORTED_MODULE_4__.CallService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_toast_service__WEBPACK_IMPORTED_MODULE_5__.ToastService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_6__.ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: MessageChatComponent,
      selectors: [["app-message-chat"]],
      viewQuery: function MessageChatComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c0, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c1, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c2, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c3, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c4, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c5, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.messagesContainer = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.fileInput = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.localVideo = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.remoteVideo = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.localVideoHidden = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.remoteVideoHidden = _t.first);
        }
      },
      decls: 57,
      vars: 59,
      consts: [["autoplay", "", "muted", "", "playsinline", "", 2, "position", "absolute", "top", "-9999px", "left", "-9999px", "width", "1px", "height", "1px"], ["localVideo", ""], ["autoplay", "", "playsinline", "", 2, "position", "absolute", "top", "-9999px", "left", "-9999px", "width", "1px", "height", "1px"], ["remoteVideo", ""], [2, "display", "flex", "flex-direction", "column", "height", "100vh", "background", "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)", "color", "#1f2937", "font-family", "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"], [2, "display", "flex", "align-items", "center", "padding", "12px 16px", "background", "#ffffff", "border-bottom", "1px solid #e5e7eb", "box-shadow", "0 1px 3px rgba(0, 0, 0, 0.1)", "position", "relative", "z-index", "10"], ["onmouseover", "this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'", "onmouseout", "this.style.background='transparent'; this.style.transform='scale(1)'", "title", "Retour aux conversations", 2, "padding", "10px", "margin-right", "12px", "border-radius", "50%", "border", "none", "background", "transparent", "cursor", "pointer", "transition", "all 0.2s ease", "display", "flex", "align-items", "center", "justify-content", "center", "min-width", "40px", "min-height", "40px", 3, "click"], [1, "fas", "fa-arrow-left", 2, "color", "#374151", "font-size", "18px", "font-weight", "bold"], [2, "display", "flex", "align-items", "center", "flex", "1", "min-width", "0"], [2, "position", "relative", "margin-right", "12px"], ["onmouseover", "this.style.transform='scale(1.05)'", "onmouseout", "this.style.transform='scale(1)'", "title", "Voir le profil", 2, "width", "40px", "height", "40px", "border-radius", "50%", "object-fit", "cover", "border", "2px solid transparent", "cursor", "pointer", "transition", "transform 0.2s ease", 3, "src", "alt", "click"], ["style", "\n            position: absolute;\n            bottom: 0;\n            right: 0;\n            width: 12px;\n            height: 12px;\n            background: #10b981;\n            border: 2px solid transparent;\n            border-radius: 50%;\n            animation: pulse 2s infinite;\n          ", 4, "ngIf"], [2, "flex", "1", "min-width", "0"], [2, "font-weight", "600", "color", "#111827", "margin", "0", "font-size", "16px", "white-space", "nowrap", "overflow", "hidden", "text-overflow", "ellipsis"], [2, "font-size", "14px", "color", "#6b7280", "margin-top", "2px"], ["style", "display: flex; align-items: center; gap: 4px; color: #10b981", 4, "ngIf"], [4, "ngIf"], [2, "display", "flex", "align-items", "center", "gap", "8px"], ["title", "Appel vid\u00E9o", "onmouseover", "this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'", "onmouseout", "this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'", 2, "padding", "10px", "border-radius", "50%", "border", "none", "background", "linear-gradient(135deg, #3b82f6, #1d4ed8)", "color", "white", "cursor", "pointer", "transition", "all 0.3s", "box-shadow", "0 4px 12px rgba(59, 130, 246, 0.3)", "width", "40px", "height", "40px", "display", "flex", "align-items", "center", "justify-content", "center", 3, "click"], [1, "fas", "fa-video", 2, "font-size", "14px"], ["title", "Appel vocal", "onmouseover", "this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'", "onmouseout", "this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'", 2, "padding", "10px", "border-radius", "50%", "border", "none", "background", "linear-gradient(135deg, #10b981, #047857)", "color", "white", "cursor", "pointer", "transition", "all 0.3s", "box-shadow", "0 4px 12px rgba(16, 185, 129, 0.3)", "width", "40px", "height", "40px", "display", "flex", "align-items", "center", "justify-content", "center", 3, "click"], [1, "fas", "fa-phone", 2, "font-size", "14px"], ["title", "Rechercher", 2, "padding", "8px", "border-radius", "50%", "border", "none", "background", "transparent", "color", "#6b7280", "cursor", "pointer", "transition", "all 0.2s", 3, "click"], [1, "fas", "fa-search"], ["title", "Recharger la conversation", "onmouseover", "this.style.background='#f3f4f6'; this.style.color='#374151'", "onmouseout", "this.style.background='transparent'; this.style.color='#6b7280'", 2, "padding", "8px", "border-radius", "50%", "border", "none", "background", "transparent", "color", "#6b7280", "cursor", "pointer", "transition", "all 0.2s", 3, "disabled", "click"], [1, "fas", "fa-sync-alt"], ["title", "Menu", 2, "padding", "8px", "border-radius", "50%", "border", "none", "background", "transparent", "color", "#6b7280", "cursor", "pointer", "transition", "all 0.2s", "position", "relative", 3, "click"], [1, "fas", "fa-ellipsis-v"], ["style", "\n        position: absolute;\n        top: 64px;\n        right: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 192px;\n      ", 4, "ngIf"], [2, "flex", "1", "overflow-y", "auto", "padding", "16px", "position", "relative", 3, "scroll", "dragover", "dragleave", "drop"], ["messagesContainer", ""], ["style", "\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(34, 197, 94, 0.2);\n        border: 2px dashed transparent;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 50;\n        backdrop-filter: blur(2px);\n        animation: pulse 2s infinite;\n      ", 4, "ngIf"], ["style", "\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 32px 0;\n      ", 4, "ngIf"], ["style", "\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 64px 0;\n      ", 4, "ngIf"], ["style", "display: flex; flex-direction: column; gap: 8px", 4, "ngIf"], [2, "background", "#ffffff", "border-top", "1px solid #e5e7eb", "padding", "16px"], [2, "display", "flex", "align-items", "end", "gap", "12px", 3, "formGroup", "ngSubmit"], [2, "display", "flex", "gap", "8px"], ["type", "button", "title", "\u00C9mojis", "onmouseover", "this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background", "onmouseout", "this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background", 2, "padding", "8px", "border-radius", "50%", "border", "none", "background", "transparent", "color", "#6b7280", "cursor", "pointer", "transition", "all 0.2s", 3, "click"], [1, "fas", "fa-smile"], ["type", "button", "title", "Pi\u00E8ces jointes", "onmouseover", "this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background", "onmouseout", "this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background", 2, "padding", "8px", "border-radius", "50%", "border", "none", "background", "transparent", "color", "#6b7280", "cursor", "pointer", "transition", "all 0.2s", 3, "click"], [1, "fas", "fa-paperclip"], ["type", "button", "title", "Maintenir pour enregistrer un message vocal", "onmouseover", "if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'", "onmouseout", "if(this.style.background === '#f3f4f6') this.style.background='transparent'", 2, "padding", "8px", "border-radius", "50%", "border", "none", "background", "transparent", "color", "#6b7280", "cursor", "pointer", "transition", "all 0.2s", "position", "relative", 3, "mousedown", "mouseup", "mouseleave", "touchstart", "touchend", "touchcancel"], ["style", "\n              position: absolute;\n              top: -2px;\n              right: -2px;\n              width: 8px;\n              height: 8px;\n              background: #ef4444;\n              border-radius: 50%;\n              animation: ping 1s infinite;\n            ", 4, "ngIf"], [2, "flex", "1", "position", "relative"], ["formControlName", "content", "placeholder", "Tapez votre message...", 2, "width", "100%", "min-height", "44px", "max-height", "120px", "padding", "12px 16px", "border", "1px solid #e5e7eb", "border-radius", "22px", "resize", "none", "outline", "none", "font-family", "inherit", "font-size", "14px", "line-height", "1.4", "background", "#ffffff", "color", "#111827", "transition", "all 0.2s", 3, "disabled", "keydown", "input", "focus"], ["type", "submit", "title", "Envoyer", "onmouseover", "if(!this.disabled) this.style.background='#2563eb'", "onmouseout", "if(!this.disabled) this.style.background='#3b82f6'", 2, "padding", "12px", "border-radius", "50%", "border", "none", "background", "#3b82f6", "color", "#ffffff", "cursor", "pointer", "transition", "all 0.2s", "display", "flex", "align-items", "center", "justify-content", "center", "min-width", "44px", "min-height", "44px", 3, "disabled"], ["class", "fas fa-paper-plane", 4, "ngIf"], ["style", "\n            width: 16px;\n            height: 16px;\n            border: 2px solid #ffffff;\n            border-top-color: transparent;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n          ", 4, "ngIf"], ["style", "\n        position: absolute;\n        bottom: 80px;\n        left: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        width: 320px;\n        max-height: 300px;\n        overflow-y: auto;\n      ", 4, "ngIf"], ["style", "\n        position: absolute;\n        bottom: 80px;\n        left: 60px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 200px;\n      ", 4, "ngIf"], ["type", "file", "multiple", "", 2, "display", "none", 3, "accept", "change"], ["fileInput", ""], ["style", "\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.25);\n      z-index: 40;\n    ", 3, "click", 4, "ngIf"], ["style", "\n      position: fixed;\n      bottom: 100px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: linear-gradient(135deg, #f59e0b, #d97706);\n      color: white;\n      padding: 20px 24px;\n      border-radius: 20px;\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\n      z-index: 60;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      min-width: 280px;\n      animation: slideInUp 0.3s ease-out;\n    ", 4, "ngIf"], [2, "position", "absolute", "bottom", "0", "right", "0", "width", "12px", "height", "12px", "background", "#10b981", "border", "2px solid transparent", "border-radius", "50%", "animation", "pulse 2s infinite"], [2, "display", "flex", "align-items", "center", "gap", "4px", "color", "#10b981"], [2, "display", "flex", "gap", "2px"], [2, "width", "4px", "height", "4px", "background", "#10b981", "border-radius", "50%", "animation", "bounce 1s infinite"], [2, "width", "4px", "height", "4px", "background", "#10b981", "border-radius", "50%", "animation", "bounce 1s infinite 0.1s"], [2, "width", "4px", "height", "4px", "background", "#10b981", "border-radius", "50%", "animation", "bounce 1s infinite 0.2s"], [2, "position", "absolute", "top", "64px", "right", "16px", "background", "#ffffff", "border-radius", "16px", "box-shadow", "0 20px 25px rgba(0, 0, 0, 0.1)", "border", "1px solid #e5e7eb", "z-index", "50", "min-width", "192px"], [2, "padding", "8px"], ["onmouseover", "this.style.background='#f3f4f6'", "onmouseout", "this.style.background='transparent'", 2, "width", "100%", "display", "flex", "align-items", "center", "gap", "12px", "padding", "8px 12px", "border-radius", "8px", "border", "none", "background", "transparent", "cursor", "pointer", "transition", "all 0.2s", "text-align", "left", 3, "click"], [1, "fas", "fa-search", 2, "color", "#3b82f6"], [2, "color", "#374151"], ["onmouseover", "this.style.background='#f3f4f6'", "onmouseout", "this.style.background='transparent'", 2, "width", "100%", "display", "flex", "align-items", "center", "gap", "12px", "padding", "8px 12px", "border-radius", "8px", "border", "none", "background", "transparent", "cursor", "pointer", "transition", "all 0.2s", "text-align", "left"], [1, "fas", "fa-user", 2, "color", "#10b981"], [2, "margin", "8px 0", "border-color", "#e5e7eb"], [1, "fas", "fa-cog", 2, "color", "#6b7280"], [2, "position", "absolute", "top", "0", "left", "0", "right", "0", "bottom", "0", "background", "rgba(34, 197, 94, 0.2)", "border", "2px dashed transparent", "border-radius", "8px", "display", "flex", "align-items", "center", "justify-content", "center", "z-index", "50", "backdrop-filter", "blur(2px)", "animation", "pulse 2s infinite"], [2, "text-align", "center", "background", "#ffffff", "padding", "24px", "border-radius", "12px", "box-shadow", "0 10px 15px rgba(0, 0, 0, 0.1)", "border", "1px solid transparent"], [1, "fas", "fa-cloud-upload-alt", 2, "font-size", "48px", "color", "#10b981", "margin-bottom", "12px", "animation", "bounce 1s infinite"], [2, "font-size", "20px", "font-weight", "bold", "color", "#047857", "margin-bottom", "8px"], [2, "font-size", "14px", "color", "#10b981"], [2, "display", "flex", "flex-direction", "column", "align-items", "center", "justify-content", "center", "padding", "32px 0"], [2, "width", "32px", "height", "32px", "border", "2px solid #e5e7eb", "border-bottom-color", "#10b981", "border-radius", "50%", "animation", "spin 1s linear infinite", "margin-bottom", "16px"], [2, "color", "#6b7280"], [2, "display", "flex", "flex-direction", "column", "align-items", "center", "justify-content", "center", "padding", "64px 0"], [2, "font-size", "64px", "color", "#d1d5db", "margin-bottom", "16px"], [1, "fas", "fa-comments"], [2, "font-size", "20px", "font-weight", "600", "color", "#374151", "margin-bottom", "8px"], [2, "color", "#6b7280", "text-align", "center"], [2, "display", "flex", "flex-direction", "column", "gap", "8px"], [4, "ngFor", "ngForOf", "ngForTrackBy"], ["style", "display: flex; align-items: start; gap: 8px", 4, "ngIf"], ["style", "display: flex; justify-content: center; margin: 16px 0", 4, "ngIf"], [2, "display", "flex", 3, "id", "click", "contextmenu"], ["style", "margin-right: 8px; flex-shrink: 0", 4, "ngIf"], [2, "max-width", "320px", "padding", "12px 16px", "border-radius", "18px", "box-shadow", "0 1px 3px rgba(0, 0, 0, 0.1)", "position", "relative", "word-wrap", "break-word", "overflow-wrap", "break-word", "border", "none"], ["style", "\n                font-size: 12px;\n                font-weight: 600;\n                margin-bottom: 4px;\n                opacity: 0.75;\n              ", 3, "color", 4, "ngIf"], ["style", "word-wrap: break-word; overflow-wrap: break-word", 4, "ngIf"], ["style", "margin: 8px 0", 4, "ngIf"], ["style", "\n                display: flex;\n                align-items: center;\n                gap: 12px;\n                padding: 12px;\n                background: rgba(255, 255, 255, 0.1);\n                border-radius: 12px;\n                margin: 8px 0;\n                min-width: 200px;\n                max-width: 280px;\n              ", 4, "ngIf"], [2, "display", "flex", "align-items", "center", "justify-content", "flex-end", "gap", "4px", "margin-top", "4px", "font-size", "12px", "opacity", "0.75"], ["style", "display: flex; align-items: center", 4, "ngIf"], [2, "display", "flex", "justify-content", "center", "margin", "16px 0"], [2, "background", "#ffffff", "padding", "4px 12px", "border-radius", "20px", "box-shadow", "0 1px 3px rgba(0, 0, 0, 0.1)"], [2, "font-size", "12px", "color", "#6b7280"], [2, "margin-right", "8px", "flex-shrink", "0"], ["onmouseover", "this.style.transform='scale(1.05)'", "onmouseout", "this.style.transform='scale(1)'", 2, "width", "32px", "height", "32px", "border-radius", "50%", "object-fit", "cover", "cursor", "pointer", "transition", "transform 0.2s", 3, "src", "alt", "click"], [2, "font-size", "12px", "font-weight", "600", "margin-bottom", "4px", "opacity", "0.75"], [2, "word-wrap", "break-word", "overflow-wrap", "break-word"], [3, "innerHTML"], [2, "margin", "8px 0"], ["onmouseover", "this.style.transform='scale(1.02)'", "onmouseout", "this.style.transform='scale(1)'", 2, "max-width", "280px", "height", "auto", "border-radius", "12px", "cursor", "pointer", "transition", "transform 0.2s", 3, "src", "alt", "click", "load", "error"], ["style", "font-size: 14px; margin-top: 8px; line-height: 1.4", 3, "color", "innerHTML", 4, "ngIf"], [2, "font-size", "14px", "margin-top", "8px", "line-height", "1.4", 3, "innerHTML"], [2, "display", "flex", "align-items", "center", "gap", "12px", "padding", "12px", "background", "rgba(255, 255, 255, 0.1)", "border-radius", "12px", "margin", "8px 0", "min-width", "200px", "max-width", "280px"], ["onmouseover", "this.style.background='rgba(255, 255, 255, 0.3)'", "onmouseout", "this.style.background='rgba(255, 255, 255, 0.2)'", "title", "Lire/Pause", 2, "width", "40px", "height", "40px", "border-radius", "50%", "border", "none", "background", "rgba(255, 255, 255, 0.2)", "color", "inherit", "cursor", "pointer", "display", "flex", "align-items", "center", "justify-content", "center", "transition", "all 0.2s", "flex-shrink", "0", 3, "click"], [2, "font-size", "14px"], [2, "flex", "1", "display", "flex", "align-items", "center", "gap", "2px", "height", "24px", "overflow", "hidden"], ["style", "\n                    width: 3px;\n                    background: currentColor;\n                    border-radius: 2px;\n                    opacity: 0.7;\n                    transition: height 0.3s ease;\n                  ", 3, "height", "animation", "animation-delay", 4, "ngFor", "ngForOf"], [2, "display", "flex", "align-items", "center", "gap", "8px", "flex-shrink", "0"], [2, "font-size", "12px", "opacity", "0.8", "min-width", "40px", "text-align", "right"], ["style", "\n                    padding: 4px 8px;\n                    border-radius: 12px;\n                    border: none;\n                    background: rgba(255, 255, 255, 0.2);\n                    color: inherit;\n                    cursor: pointer;\n                    font-size: 11px;\n                    transition: all 0.2s;\n                  ", "onmouseover", "this.style.background='rgba(255, 255, 255, 0.3)'", "onmouseout", "this.style.background='rgba(255, 255, 255, 0.2)'", "title", "Changer la vitesse", 3, "click", 4, "ngIf"], [2, "width", "3px", "background", "currentColor", "border-radius", "2px", "opacity", "0.7", "transition", "height 0.3s ease"], ["onmouseover", "this.style.background='rgba(255, 255, 255, 0.3)'", "onmouseout", "this.style.background='rgba(255, 255, 255, 0.2)'", "title", "Changer la vitesse", 2, "padding", "4px 8px", "border-radius", "12px", "border", "none", "background", "rgba(255, 255, 255, 0.2)", "color", "inherit", "cursor", "pointer", "font-size", "11px", "transition", "all 0.2s", 3, "click"], [2, "display", "flex", "align-items", "center"], ["class", "fas fa-clock", "title", "Envoi en cours", 4, "ngIf"], ["class", "fas fa-check", "title", "Envoy\u00E9", 4, "ngIf"], ["class", "fas fa-check-double", "title", "Livr\u00E9", 4, "ngIf"], ["class", "fas fa-check-double", "style", "color: #3b82f6", "title", "Lu", 4, "ngIf"], ["title", "Envoi en cours", 1, "fas", "fa-clock"], ["title", "Envoy\u00E9", 1, "fas", "fa-check"], ["title", "Livr\u00E9", 1, "fas", "fa-check-double"], ["title", "Lu", 1, "fas", "fa-check-double", 2, "color", "#3b82f6"], [2, "display", "flex", "align-items", "start", "gap", "8px"], [2, "width", "32px", "height", "32px", "border-radius", "50%", "object-fit", "cover", 3, "src", "alt"], [2, "background", "#ffffff", "padding", "12px 16px", "border-radius", "18px", "box-shadow", "0 1px 3px rgba(0, 0, 0, 0.1)"], [2, "display", "flex", "gap", "4px"], [2, "width", "8px", "height", "8px", "background", "#6b7280", "border-radius", "50%", "animation", "bounce 1s infinite"], [2, "width", "8px", "height", "8px", "background", "#6b7280", "border-radius", "50%", "animation", "bounce 1s infinite 0.1s"], [2, "width", "8px", "height", "8px", "background", "#6b7280", "border-radius", "50%", "animation", "bounce 1s infinite 0.2s"], [2, "position", "absolute", "top", "-2px", "right", "-2px", "width", "8px", "height", "8px", "background", "#ef4444", "border-radius", "50%", "animation", "ping 1s infinite"], [1, "fas", "fa-paper-plane"], [2, "width", "16px", "height", "16px", "border", "2px solid #ffffff", "border-top-color", "transparent", "border-radius", "50%", "animation", "spin 1s linear infinite"], [2, "position", "absolute", "bottom", "80px", "left", "16px", "background", "#ffffff", "border-radius", "16px", "box-shadow", "0 20px 25px rgba(0, 0, 0, 0.1)", "border", "1px solid #e5e7eb", "z-index", "50", "width", "320px", "max-height", "300px", "overflow-y", "auto"], [2, "padding", "16px"], [2, "margin", "0 0 12px 0", "font-size", "14px", "font-weight", "600", "color", "#374151"], [2, "display", "grid", "grid-template-columns", "repeat(8, 1fr)", "gap", "8px"], ["style", "\n              padding: 8px;\n              border: none;\n              background: transparent;\n              border-radius: 8px;\n              cursor: pointer;\n              font-size: 20px;\n              transition: all 0.2s;\n            ", "onmouseover", "this.style.background='#f3f4f6'", "onmouseout", "this.style.background='transparent'", 3, "title", "click", 4, "ngFor", "ngForOf"], ["onmouseover", "this.style.background='#f3f4f6'", "onmouseout", "this.style.background='transparent'", 2, "padding", "8px", "border", "none", "background", "transparent", "border-radius", "8px", "cursor", "pointer", "font-size", "20px", "transition", "all 0.2s", 3, "title", "click"], [2, "position", "absolute", "bottom", "80px", "left", "60px", "background", "#ffffff", "border-radius", "16px", "box-shadow", "0 20px 25px rgba(0, 0, 0, 0.1)", "border", "1px solid #e5e7eb", "z-index", "50", "min-width", "200px"], [2, "display", "grid", "grid-template-columns", "repeat(2, 1fr)", "gap", "12px"], ["onmouseover", "this.style.background='#f3f4f6'", "onmouseout", "this.style.background='transparent'", 2, "display", "flex", "flex-direction", "column", "align-items", "center", "gap", "8px", "padding", "16px", "border", "none", "background", "transparent", "border-radius", "12px", "cursor", "pointer", "transition", "all 0.2s", 3, "click"], [2, "width", "48px", "height", "48px", "background", "#dbeafe", "border-radius", "50%", "display", "flex", "align-items", "center", "justify-content", "center"], [1, "fas", "fa-image", 2, "color", "#3b82f6", "font-size", "20px"], [2, "font-size", "14px", "font-weight", "500", "color", "#374151"], [2, "width", "48px", "height", "48px", "background", "#fef3c7", "border-radius", "50%", "display", "flex", "align-items", "center", "justify-content", "center"], [1, "fas", "fa-file-alt", 2, "color", "#f59e0b", "font-size", "20px"], [2, "width", "48px", "height", "48px", "background", "#dcfce7", "border-radius", "50%", "display", "flex", "align-items", "center", "justify-content", "center"], [1, "fas", "fa-camera", 2, "color", "#10b981", "font-size", "20px"], [2, "position", "fixed", "top", "0", "left", "0", "right", "0", "bottom", "0", "background", "rgba(0, 0, 0, 0.25)", "z-index", "40", 3, "click"], [2, "position", "fixed", "bottom", "100px", "left", "50%", "transform", "translateX(-50%)", "background", "linear-gradient(135deg, #f59e0b, #d97706)", "color", "white", "padding", "20px 24px", "border-radius", "20px", "box-shadow", "0 20px 25px rgba(0, 0, 0, 0.2)", "z-index", "60", "display", "flex", "align-items", "center", "gap", "16px", "min-width", "280px", "animation", "slideInUp 0.3s ease-out"], [2, "width", "48px", "height", "48px", "background", "rgba(255, 255, 255, 0.2)", "border-radius", "50%", "display", "flex", "align-items", "center", "justify-content", "center", "animation", "pulse 1s infinite"], [1, "fas", "fa-microphone", 2, "font-size", "20px"], [2, "flex", "1"], [2, "font-size", "18px", "font-weight", "bold", "margin-bottom", "4px"], [2, "display", "flex", "align-items", "center", "gap", "2px", "height", "20px"], ["style", "\n            width: 3px;\n            background: rgba(255, 255, 255, 0.8);\n            border-radius: 2px;\n            transition: height 0.3s ease;\n          ", 3, "height", "animation", "animation-delay", 4, "ngFor", "ngForOf"], [2, "font-size", "12px", "opacity", "0.8", "margin-top", "4px"], ["onmouseover", "this.style.background='rgba(239, 68, 68, 1)'", "onmouseout", "this.style.background='rgba(239, 68, 68, 0.8)'", "title", "Annuler l'enregistrement", 2, "width", "40px", "height", "40px", "border-radius", "50%", "border", "none", "background", "rgba(239, 68, 68, 0.8)", "color", "white", "cursor", "pointer", "display", "flex", "align-items", "center", "justify-content", "center", "transition", "all 0.2s", 3, "click"], [1, "fas", "fa-times", 2, "font-size", "16px"], ["onmouseover", "this.style.background='rgba(34, 197, 94, 1)'", "onmouseout", "this.style.background='rgba(34, 197, 94, 0.8)'", "title", "Envoyer le message vocal", 2, "width", "40px", "height", "40px", "border-radius", "50%", "border", "none", "background", "rgba(34, 197, 94, 0.8)", "color", "white", "cursor", "pointer", "display", "flex", "align-items", "center", "justify-content", "center", "transition", "all 0.2s", 3, "click"], [1, "fas", "fa-paper-plane", 2, "font-size", "16px"], [2, "width", "3px", "background", "rgba(255, 255, 255, 0.8)", "border-radius", "2px", "transition", "height 0.3s ease"]],
      template: function MessageChatComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "video", 0, 1)(2, "video", 2, 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "div", 4)(5, "header", 5)(6, "button", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_button_click_6_listener() {
            return ctx.goBackToConversations();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](7, "i", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "div", 8)(9, "div", 9)(10, "img", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_img_click_10_listener() {
            return ctx.openUserProfile(ctx.otherParticipant == null ? null : ctx.otherParticipant.id);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](11, MessageChatComponent_div_11_Template, 1, 0, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](12, "div", 12)(13, "h3", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](14);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](15, "div", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](16, MessageChatComponent_div_16_Template, 7, 0, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](17, MessageChatComponent_span_17_Template, 2, 1, "span", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](18, "div", 17)(19, "button", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_button_click_19_listener() {
            return ctx.startVideoCall();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](20, "i", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](21, "button", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_button_click_21_listener() {
            return ctx.startVoiceCall();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](22, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](23, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_button_click_23_listener() {
            return ctx.toggleSearch();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](24, "i", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](25, "button", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_button_click_25_listener() {
            return ctx.reloadConversation();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](26, "i", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](27, "button", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_button_click_27_listener() {
            return ctx.toggleMainMenu();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](28, "i", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](29, MessageChatComponent_div_29_Template, 15, 0, "div", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](30, "main", 29, 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("scroll", function MessageChatComponent_Template_main_scroll_30_listener($event) {
            return ctx.onScroll($event);
          })("dragover", function MessageChatComponent_Template_main_dragover_30_listener($event) {
            return ctx.onDragOver($event);
          })("dragleave", function MessageChatComponent_Template_main_dragleave_30_listener($event) {
            return ctx.onDragLeave($event);
          })("drop", function MessageChatComponent_Template_main_drop_30_listener($event) {
            return ctx.onDrop($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](32, MessageChatComponent_div_32_Template, 7, 0, "div", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](33, MessageChatComponent_div_33_Template, 4, 0, "div", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](34, MessageChatComponent_div_34_Template, 7, 1, "div", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](35, MessageChatComponent_div_35_Template, 3, 3, "div", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](36, "footer", 35)(37, "form", 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("ngSubmit", function MessageChatComponent_Template_form_ngSubmit_37_listener() {
            return ctx.sendMessage();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](38, "div", 37)(39, "button", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_button_click_39_listener() {
            return ctx.toggleEmojiPicker();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](40, "i", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](41, "button", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_Template_button_click_41_listener() {
            return ctx.toggleAttachmentMenu();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](42, "i", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](43, "button", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("mousedown", function MessageChatComponent_Template_button_mousedown_43_listener($event) {
            return ctx.onRecordStart($event);
          })("mouseup", function MessageChatComponent_Template_button_mouseup_43_listener($event) {
            return ctx.onRecordEnd($event);
          })("mouseleave", function MessageChatComponent_Template_button_mouseleave_43_listener($event) {
            return ctx.onRecordCancel($event);
          })("touchstart", function MessageChatComponent_Template_button_touchstart_43_listener($event) {
            return ctx.onRecordStart($event);
          })("touchend", function MessageChatComponent_Template_button_touchend_43_listener($event) {
            return ctx.onRecordEnd($event);
          })("touchcancel", function MessageChatComponent_Template_button_touchcancel_43_listener($event) {
            return ctx.onRecordCancel($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](44, "i");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](45, MessageChatComponent_div_45_Template, 1, 0, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](46, "div", 44)(47, "textarea", 45);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("keydown", function MessageChatComponent_Template_textarea_keydown_47_listener($event) {
            return ctx.onInputKeyDown($event);
          })("input", function MessageChatComponent_Template_textarea_input_47_listener($event) {
            return ctx.onInputChange($event);
          })("focus", function MessageChatComponent_Template_textarea_focus_47_listener() {
            return ctx.onInputFocus();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](48, "button", 46);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](49, MessageChatComponent_i_49_Template, 1, 0, "i", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](50, MessageChatComponent_div_50_Template, 1, 0, "div", 48);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](51, MessageChatComponent_div_51_Template, 6, 1, "div", 49);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](52, MessageChatComponent_div_52_Template, 20, 0, "div", 50);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](53, "input", 51, 52);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("change", function MessageChatComponent_Template_input_change_53_listener($event) {
            return ctx.onFileSelected($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](55, MessageChatComponent_div_55_Template, 1, 0, "div", 53);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](56, MessageChatComponent_div_56_Template, 15, 3, "div", 54);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"])("alt", ctx.otherParticipant == null ? null : ctx.otherParticipant.username);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", (ctx.otherParticipant == null ? null : ctx.otherParticipant.username) || "Utilisateur", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isUserTyping);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.isUserTyping);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("background", ctx.searchMode ? "#dcfce7" : "transparent")("color", ctx.searchMode ? "#16a34a" : "#6b7280");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("opacity", ctx.isLoading ? "0.5" : "1");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("disabled", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("animation", ctx.isLoading ? "spin 1s linear infinite" : "none");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("background", ctx.showMainMenu ? "#dcfce7" : "transparent")("color", ctx.showMainMenu ? "#16a34a" : "#6b7280");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.showMainMenu);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("background", ctx.isDragOver ? "rgba(34, 197, 94, 0.1)" : "transparent");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isDragOver);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.isLoading && ctx.messages.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.isLoading && ctx.messages.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("formGroup", ctx.messageForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("background", ctx.showEmojiPicker ? "#dcfce7" : "transparent")("color", ctx.showEmojiPicker ? "#16a34a" : "#6b7280");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("background", ctx.showAttachmentMenu ? "#dcfce7" : "transparent")("color", ctx.showAttachmentMenu ? "#16a34a" : "#6b7280");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("background", ctx.isRecordingVoice ? "#fef3c7" : "transparent")("color", ctx.isRecordingVoice ? "#f59e0b" : "#6b7280")("transform", ctx.isRecordingVoice ? "scale(1.1)" : "scale(1)");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassMap"](ctx.isRecordingVoice ? "fas fa-stop" : "fas fa-microphone");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("animation", ctx.isRecordingVoice ? "pulse 1s infinite" : "none");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isRecordingVoice);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("disabled", ctx.isInputDisabled());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵstyleProp"]("background", !ctx.messageForm.valid || ctx.isSendingMessage ? "#9ca3af" : "#3b82f6")("cursor", !ctx.messageForm.valid || ctx.isSendingMessage ? "not-allowed" : "pointer");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("disabled", !ctx.messageForm.valid || ctx.isSendingMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.isSendingMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isSendingMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.showEmojiPicker);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.showAttachmentMenu);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("accept", ctx.getFileAcceptTypes());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.showEmojiPicker || ctx.showAttachmentMenu || ctx.showMainMenu);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isRecordingVoice);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_10__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_8__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControlName],
      styles: ["@keyframes _ngcontent-%COMP%_pulse {\n      0%,\n      100% {\n        opacity: 1;\n      }\n      50% {\n        opacity: 0.5;\n      }\n    }\n    @keyframes _ngcontent-%COMP%_bounce {\n      0%,\n      20%,\n      53%,\n      80%,\n      100% {\n        transform: translateY(0);\n      }\n      40%,\n      43% {\n        transform: translateY(-8px);\n      }\n      70% {\n        transform: translateY(-4px);\n      }\n    }\n    @keyframes _ngcontent-%COMP%_spin {\n      from {\n        transform: rotate(0deg);\n      }\n      to {\n        transform: rotate(360deg);\n      }\n    }\n    @keyframes _ngcontent-%COMP%_ping {\n      75%,\n      100% {\n        transform: scale(2);\n        opacity: 0;\n      }\n    }\n    @keyframes _ngcontent-%COMP%_slideInUp {\n      from {\n        transform: translateX(-50%) translateY(20px);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(-50%) translateY(0);\n        opacity: 1;\n      }\n    }"]
    });
  }
}

/***/ }),

/***/ 14682:
/*!*******************************************************************************!*\
  !*** ./src/app/views/front/messages/messages-list/messages-list.component.ts ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessagesListComponent: () => (/* binding */ MessagesListComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ 75797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 70271);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _app_services_message_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @app/services/message.service */ 54537);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/authuser.service */ 99271);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/toast.service */ 68397);
/* harmony import */ var src_app_services_logger_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/services/logger.service */ 34798);
/* harmony import */ var _app_services_theme_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @app/services/theme.service */ 70487);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 34456);










function MessagesListComponent_div_29_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 31)(1, "span", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "div", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const count_r6 = ctx.ngIf;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", count_r6, " ");
  }
}
function MessagesListComponent_div_38_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Chargement des conversations...");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessagesListComponent_div_39_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 37)(1, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 40)(4, "h3", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5, " Erreur de chargement des conversations ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "p", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](8, "button", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessagesListComponent_div_39_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r8);
      const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r7.loadConversations());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](9, "i", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](10, " R\u00E9essayer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r2.error, " ");
  }
}
function MessagesListComponent_div_40_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 45)(1, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "h3", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, "Aucune conversation");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "p", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, " D\u00E9marrez une nouvelle conversation pour communiquer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "button", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessagesListComponent_div_40_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r10);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r9.startNewConversation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](8, "i", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9, " Nouvelle Conversation ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessagesListComponent_div_41_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 52)(1, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "h3", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, "Aucun r\u00E9sultat trouv\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "p", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, "Essayez un autre terme de recherche");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessagesListComponent_ul_42_li_1_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "div", 69);
  }
}
function MessagesListComponent_ul_42_li_1_span_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Vous: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function MessagesListComponent_ul_42_li_1_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const conv_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", conv_r12.unreadCount, " ");
  }
}
const _c0 = function (a0) {
  return {
    "futuristic-conversation-selected": a0
  };
};
function MessagesListComponent_ul_42_li_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "li", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessagesListComponent_ul_42_li_1_Template_li_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r18);
      const conv_r12 = restoredCtx.$implicit;
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r17.openConversation(conv_r12.id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 57)(2, "div", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "img", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, MessagesListComponent_ul_42_li_1_div_4_Template, 1, 0, "div", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 61)(6, "div", 62)(7, "h3", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "span", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](11, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "div", 65)(13, "p", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](14, MessagesListComponent_ul_42_li_1_span_14_Template, 2, 0, "span", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](16, MessagesListComponent_ul_42_li_1_div_16_Template, 2, 1, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const conv_r12 = ctx.$implicit;
    const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    let tmp_1_0;
    let tmp_2_0;
    let tmp_3_0;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction1"](11, _c0, ctx_r11.selectedConversationId === conv_r12.id));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("src", (conv_r12.participants ? (tmp_1_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_1_0.image : null) || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", conv_r12.participants && ((tmp_2_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_2_0.isOnline));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", (conv_r12.participants ? (tmp_3_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_3_0.username : null) || "Utilisateur inconnu", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](11, 8, conv_r12.lastMessage == null ? null : conv_r12.lastMessage.timestamp, "shortTime") || "", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", (conv_r12.lastMessage == null ? null : conv_r12.lastMessage.sender == null ? null : conv_r12.lastMessage.sender.id) === ctx_r11.currentUserId);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", (conv_r12.lastMessage == null ? null : conv_r12.lastMessage.content) || "Pas encore de messages", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", conv_r12.unreadCount && conv_r12.unreadCount > 0);
  }
}
function MessagesListComponent_ul_42_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "ul", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, MessagesListComponent_ul_42_li_1_Template, 17, 13, "li", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r5.filteredConversations);
  }
}
class MessagesListComponent {
  constructor(MessageService, authService, router, route, toastService, logger, themeService) {
    this.MessageService = MessageService;
    this.authService = authService;
    this.router = router;
    this.route = route;
    this.toastService = toastService;
    this.logger = logger;
    this.themeService = themeService;
    this.conversations = [];
    this.filteredConversations = [];
    this.loading = true;
    this.currentUserId = null;
    this.searchQuery = '';
    this.selectedConversationId = null;
    this.unreadCount = new rxjs__WEBPACK_IMPORTED_MODULE_6__.BehaviorSubject(0);
    this.unreadCount$ = this.unreadCount.asObservable();
    this.subscriptions = [];
    this.isDarkMode$ = this.themeService.darkMode$;
  }
  ngOnInit() {
    this.currentUserId = this.authService.getCurrentUserId();
    if (!this.currentUserId) {
      this.handleError('User not authenticated');
      return;
    }
    this.loadConversations();
    this.subscribeToUserStatus();
    this.subscribeToConversationUpdates();
    // Check for active conversation from route
    this.route.firstChild?.params.subscribe(params => {
      this.selectedConversationId = params['conversationId'] || null;
    });
  }
  loadConversations() {
    this.loading = true;
    this.error = null;
    const sub = this.MessageService.getConversations().subscribe({
      next: conversations => {
        this.conversations = Array.isArray(conversations) ? [...conversations] : [];
        this.filterConversations();
        this.updateUnreadCount();
        this.sortConversations();
        this.loading = false;
      },
      error: error => {
        this.error = error;
        this.loading = false;
        this.toastService.showError('Failed to load conversations');
      }
    });
    this.subscriptions.push(sub);
  }
  filterConversations() {
    if (!this.searchQuery) {
      this.filteredConversations = [...this.conversations];
      return;
    }
    const query = this.searchQuery.toLowerCase();
    this.filteredConversations = this.conversations.filter(conv => {
      const otherParticipant = conv.participants ? this.getOtherParticipant(conv.participants) : undefined;
      return otherParticipant?.username.toLowerCase().includes(query) || conv.lastMessage?.content?.toLowerCase().includes(query) || false;
    });
  }
  updateUnreadCount() {
    const count = this.conversations.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0);
    this.unreadCount.next(count);
  }
  sortConversations() {
    this.conversations.sort((a, b) => {
      const dateA = this.getConversationDate(a);
      const dateB = this.getConversationDate(b);
      return dateB.getTime() - dateA.getTime();
    });
    this.filterConversations();
  }
  getConversationDate(conv) {
    // Utiliser une date par défaut si aucune date n'est disponible
    const defaultDate = new Date(0); // 1970-01-01
    if (conv.lastMessage?.timestamp) {
      return typeof conv.lastMessage.timestamp === 'string' ? new Date(conv.lastMessage.timestamp) : conv.lastMessage.timestamp;
    }
    if (conv.updatedAt) {
      return typeof conv.updatedAt === 'string' ? new Date(conv.updatedAt) : conv.updatedAt;
    }
    if (conv.createdAt) {
      return typeof conv.createdAt === 'string' ? new Date(conv.createdAt) : conv.createdAt;
    }
    return defaultDate;
  }
  getOtherParticipant(participants) {
    if (!participants || !Array.isArray(participants)) {
      return undefined;
    }
    return participants.find(p => p._id !== this.currentUserId && p.id !== this.currentUserId);
  }
  subscribeToUserStatus() {
    const sub = this.MessageService.subscribeToUserStatus().pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_7__.map)(user => this.MessageService.normalizeUser(user))).subscribe({
      next: user => {
        if (user) {
          this.updateUserStatus(user);
        }
      },
      error: error => {
        this.toastService.showError('Connection to status updates lost');
      }
    });
    this.subscriptions.push(sub);
  }
  subscribeToConversationUpdates() {
    const sub = this.MessageService.subscribeToConversationUpdates('global').subscribe({
      next: updatedConv => {
        const index = this.conversations.findIndex(c => c.id === updatedConv.id);
        if (index >= 0) {
          this.conversations[index] = updatedConv;
        } else {
          this.conversations.unshift(updatedConv);
        }
        this.sortConversations();
      },
      error: error => {
        // Handle error silently
      }
    });
    this.subscriptions.push(sub);
  }
  updateUserStatus(updatedUser) {
    this.conversations = this.conversations.map(conv => {
      if (!conv.participants) {
        return conv;
      }
      const participants = conv.participants.map(p => {
        const userIdMatches = p._id === updatedUser._id || p.id === updatedUser._id;
        return userIdMatches ? {
          ...p,
          isOnline: updatedUser.isOnline,
          lastActive: updatedUser.lastActive
        } : p;
      });
      return {
        ...conv,
        participants
      };
    });
    this.filterConversations();
  }
  openConversation(conversationId) {
    if (!conversationId) {
      return;
    }
    this.selectedConversationId = conversationId;
    this.router.navigate(['chat', conversationId], {
      relativeTo: this.route
    });
  }
  startNewConversation() {
    this.router.navigate(['/messages/users']);
  }
  formatLastActive(lastActive) {
    return this.MessageService.formatLastActive(lastActive);
  }
  handleError(message, error) {
    this.logger.error('MessagesListComponent', message, error);
    this.error = message;
    this.loading = false;
    this.toastService.showError(message);
  }
  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  static {
    this.ɵfac = function MessagesListComponent_Factory(t) {
      return new (t || MessagesListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_app_services_message_service__WEBPACK_IMPORTED_MODULE_0__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_2__.ToastService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_services_logger_service__WEBPACK_IMPORTED_MODULE_3__.LoggerService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_app_services_theme_service__WEBPACK_IMPORTED_MODULE_4__.ThemeService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: MessagesListComponent,
      selectors: [["app-messages-list"]],
      decls: 45,
      vars: 13,
      consts: [[1, "flex", "h-screen", "futuristic-messages-page", "relative"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#6d78c9]"], [1, "w-full", "md:w-80", "lg:w-96", "futuristic-sidebar", "flex", "flex-col", "relative", "z-10", "backdrop-blur-sm"], [1, "futuristic-header", "sticky", "top-0", "z-10", "backdrop-blur-sm"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "blur-md"], [1, "flex", "justify-between", "items-center", "mb-4"], [1, "futuristic-title", "text-xl", "font-bold"], [1, "flex", "items-center", "space-x-2"], ["title", "Nouvelle conversation", 1, "p-2", "rounded-full", "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:bg-[#4f5fad]/10", "dark:hover:bg-[#6d78c9]/10", "transition-all", "relative", "group", "overflow-hidden", 3, "click"], [1, "absolute", "inset-0", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "rounded-full", "blur-md"], [1, "fas", "fa-edit", "relative", "z-10", "group-hover:scale-110", "transition-transform"], ["class", "relative", 4, "ngIf"], [1, "relative", "group"], ["type", "text", "placeholder", "Rechercher des conversations...", 1, "w-full", "pl-10", "pr-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all", 3, "ngModel", "ngModelChange"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none"], [1, "fas", "fa-search", "text-[#bdc6cc]", "dark:text-[#6d6870]", "group-focus-within:text-[#4f5fad]", "dark:group-focus-within:text-[#6d78c9]", "transition-colors"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none", "opacity-0", "group-focus-within:opacity-100", "transition-opacity"], [1, "w-0.5", "h-4", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "rounded-full"], [1, "flex-1", "overflow-y-auto", "futuristic-conversations-list"], ["class", "flex flex-col items-center justify-center h-full p-4", 4, "ngIf"], ["class", "futuristic-error-container", 4, "ngIf"], ["class", "futuristic-empty-state", 4, "ngIf"], ["class", "futuristic-no-results", 4, "ngIf"], ["class", "futuristic-conversations", 4, "ngIf"], [1, "flex-1", "hidden", "md:flex", "flex-col", "futuristic-main-area"], [1, "relative"], [1, "futuristic-badge"], [1, "absolute", "inset-0", "bg-[#4f5fad]/30", "dark:bg-[#6d78c9]/30", "rounded-full", "blur-md", "transform", "scale-150", "-z-10"], [1, "flex", "flex-col", "items-center", "justify-center", "h-full", "p-4"], [1, "futuristic-loading-circle"], [1, "futuristic-loading-text"], [1, "futuristic-error-container"], [1, "futuristic-error-icon"], [1, "fas", "fa-exclamation-triangle"], [1, "flex-1"], [1, "futuristic-error-title"], [1, "futuristic-error-message"], [1, "futuristic-retry-button", 3, "click"], [1, "fas", "fa-sync-alt", "mr-1.5"], [1, "futuristic-empty-state"], [1, "futuristic-empty-icon"], [1, "fas", "fa-comments"], [1, "futuristic-empty-title"], [1, "futuristic-empty-text"], [1, "futuristic-start-button", 3, "click"], [1, "fas", "fa-plus-circle", "mr-2"], [1, "futuristic-no-results"], [1, "fas", "fa-search"], [1, "futuristic-conversations"], ["class", "futuristic-conversation-item", 3, "ngClass", "click", 4, "ngFor", "ngForOf"], [1, "futuristic-conversation-item", 3, "ngClass", "click"], [1, "flex", "items-center"], [1, "futuristic-avatar"], ["alt", "User avatar", 3, "src"], ["class", "futuristic-online-indicator", 4, "ngIf"], [1, "futuristic-conversation-details"], [1, "futuristic-conversation-header"], [1, "futuristic-conversation-name"], [1, "futuristic-conversation-time"], [1, "futuristic-conversation-preview"], [1, "futuristic-conversation-message"], ["class", "futuristic-you-prefix", 4, "ngIf"], ["class", "futuristic-unread-badge", 4, "ngIf"], [1, "futuristic-online-indicator"], [1, "futuristic-you-prefix"], [1, "futuristic-unread-badge"]],
      template: function MessagesListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](1, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "div", 2)(4, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 4)(6, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6)(17, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "div", 7)(19, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](20, "div", 9)(21, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](22, "div", 11)(23, "h1", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](24, "Messages");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](25, "div", 13)(26, "button", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessagesListComponent_Template_button_click_26_listener() {
            return ctx.startNewConversation();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](27, "div", 15)(28, "i", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](29, MessagesListComponent_div_29_Template, 4, 1, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](30, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "div", 18)(32, "input", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function MessagesListComponent_Template_input_ngModelChange_32_listener($event) {
            return ctx.searchQuery = $event;
          })("ngModelChange", function MessagesListComponent_Template_input_ngModelChange_32_listener() {
            return ctx.filterConversations();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](34, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](36, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](38, MessagesListComponent_div_38_Template, 4, 0, "div", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](39, MessagesListComponent_div_39_Template, 11, 1, "div", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](40, MessagesListComponent_div_40_Template, 10, 0, "div", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](41, MessagesListComponent_div_41_Template, 7, 0, "div", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](42, MessagesListComponent_ul_42_Template, 2, 1, "ul", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](43, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](44, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("dark", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](1, 9, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](29);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](30, 11, ctx.unreadCount$));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.searchQuery);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.filteredConversations.length === 0 && !ctx.searchQuery);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.filteredConversations.length === 0 && ctx.searchQuery);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.filteredConversations.length > 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_8__.RouterOutlet, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_9__.AsyncPipe, _angular_common__WEBPACK_IMPORTED_MODULE_9__.DatePipe],
      styles: ["\n\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%] {\n  background-color: #f0f4f8;\n  color: #6d6870;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%] {\n  background-color: var(--dark-bg);\n  color: var(--text-light);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-color: rgba(79, 95, 173, 0.2);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%] {\n  background-color: var(--medium-bg);\n  border-color: rgba(0, 247, 255, 0.2);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-bottom: 1px solid rgba(79, 95, 173, 0.2);\n  padding: 15px;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%] {\n  background-color: var(--medium-bg);\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\n  padding: 15px;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\n  color: #4f5fad;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\n  color: var(--text-light);\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: 9999px;\n  padding: 2px 8px;\n  min-width: 1.5rem;\n  text-align: center;\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%] {\n  background: var(--primary-gradient);\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: 9999px;\n  padding: 2px 8px;\n  min-width: 1.5rem;\n  text-align: center;\n  box-shadow: var(--glow-effect);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%] {\n  scrollbar-width: thin;\n  scrollbar-color: #4f5fad #f0f4f8;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 5px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: #f0f4f8;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background-color: #4f5fad;\n  border-radius: 10px;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%] {\n  scrollbar-width: thin;\n  scrollbar-color: var(--accent-color) var(--medium-bg);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 5px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: var(--medium-bg);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background-color: var(--accent-color);\n  border-radius: 10px;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 3px solid rgba(79, 95, 173, 0.1);\n  border-top-color: #4f5fad;\n  animation: _ngcontent-%COMP%_spin-light 1.5s linear infinite;\n}\n\n@keyframes _ngcontent-%COMP%_spin-light {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%] {\n  color: #4f5fad;\n  font-size: 0.875rem;\n  letter-spacing: 0.5px;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 3px solid rgba(0, 247, 255, 0.1);\n  border-top-color: var(--accent-color);\n  animation: _ngcontent-%COMP%_spin-dark 1.5s linear infinite;\n}\n\n@keyframes _ngcontent-%COMP%_spin-dark {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.dark[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%] {\n  color: var(--accent-color);\n  font-size: 0.875rem;\n  letter-spacing: 0.5px;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%] {\n  margin: 15px;\n  padding: 15px;\n  background: rgba(255, 107, 105, 0.1);\n  border-left: 3px solid #ff6b69;\n  border-radius: 5px;\n  display: flex;\n  align-items: flex-start;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%] {\n  color: #ff6b69;\n  font-size: 1.25rem;\n  margin-right: 15px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%] {\n  color: #ff6b69;\n  font-size: 0.875rem;\n  font-weight: 600;\n  margin-bottom: 5px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%] {\n  color: #6d6870;\n  font-size: 0.8125rem;\n  margin-bottom: 10px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%] {\n  background: rgba(255, 107, 105, 0.2);\n  color: #ff6b69;\n  border: none;\n  border-radius: 5px;\n  padding: 5px 10px;\n  font-size: 0.75rem;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 107, 105, 0.3);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%] {\n  margin: 15px;\n  padding: 15px;\n  background: rgba(255, 0, 0, 0.1);\n  border-left: 3px solid #ff3b30;\n  border-radius: 5px;\n  display: flex;\n  align-items: flex-start;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%] {\n  color: #ff3b30;\n  font-size: 1.25rem;\n  margin-right: 15px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%] {\n  color: #ff3b30;\n  font-size: 0.875rem;\n  font-weight: 600;\n  margin-bottom: 5px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n  font-size: 0.8125rem;\n  margin-bottom: 10px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%] {\n  background: rgba(255, 0, 0, 0.2);\n  color: #ff3b30;\n  border: none;\n  border-radius: 5px;\n  padding: 5px 10px;\n  font-size: 0.75rem;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 0, 0, 0.3);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)[_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%] {\n  color: #6d6870;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  color: #4f5fad;\n  margin-bottom: 20px;\n  opacity: 0.7;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #4f5fad;\n  margin-bottom: 5px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\n  color: #6d6870;\n  font-size: 0.875rem;\n  margin-bottom: 20px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\n  color: white;\n  border: none;\n  border-radius: 20px;\n  padding: 8px 16px;\n  font-size: 0.875rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  transition: all var(--transition-fast);\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  color: var(--accent-color);\n  margin-bottom: 20px;\n  opacity: 0.7;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--text-light);\n  margin-bottom: 5px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n  font-size: 0.875rem;\n  margin-bottom: 20px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%] {\n  background: var(--primary-gradient);\n  color: white;\n  border: none;\n  border-radius: 20px;\n  padding: 8px 16px;\n  font-size: 0.875rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  transition: all var(--transition-fast);\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--glow-effect);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%] {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 12px 15px;\n  cursor: pointer;\n  transition: background-color var(--transition-fast);\n  border-bottom: 1px solid rgba(79, 95, 173, 0.05);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover {\n  background-color: rgba(79, 95, 173, 0.05);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%] {\n  background-color: rgba(79, 95, 173, 0.1) !important;\n  border-left: 3px solid #4f5fad;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%] {\n  position: relative;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 12px;\n  flex-shrink: 0;\n  border: 2px solid rgba(79, 95, 173, 0.3);\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.2);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  background: #4caf50;\n  border: 2px solid #ffffff;\n  box-shadow: 0 0 5px rgba(76, 175, 80, 0.8);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%] {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 12px 15px;\n  cursor: pointer;\n  transition: background-color var(--transition-fast);\n  border-bottom: 1px solid rgba(0, 247, 255, 0.05);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.05);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%] {\n  background-color: rgba(0, 247, 255, 0.1) !important;\n  border-left: 3px solid var(--accent-color);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%] {\n  position: relative;\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 12px;\n  flex-shrink: 0;\n  border: 2px solid rgba(0, 247, 255, 0.3);\n  box-shadow: var(--glow-effect);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  background: #00ff9d;\n  border: 2px solid var(--medium-bg);\n  box-shadow: 0 0 5px rgba(0, 255, 157, 0.8);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: baseline;\n  margin-bottom: 4px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%] {\n  font-size: 0.9375rem;\n  font-weight: 600;\n  color: #4f5fad;\n  margin: 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #6d6870;\n  white-space: nowrap;\n  margin-left: 8px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%] {\n  font-size: 0.8125rem;\n  color: #6d6870;\n  margin: 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%] {\n  color: #4f5fad;\n  font-weight: 500;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 600;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 8px;\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: baseline;\n  margin-bottom: 4px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%] {\n  font-size: 0.9375rem;\n  font-weight: 600;\n  color: var(--text-light);\n  margin: 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: var(--text-dim);\n  white-space: nowrap;\n  margin-left: 8px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%] {\n  font-size: 0.8125rem;\n  color: var(--text-dim);\n  margin: 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%] {\n  color: rgba(0, 247, 255, 0.7);\n  font-weight: 500;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%] {\n  background: var(--accent-color);\n  color: var(--dark-bg);\n  font-size: 0.75rem;\n  font-weight: 600;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 8px;\n  box-shadow: var(--glow-effect);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%] {\n  background-color: #f0f4f8;\n  position: relative;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: linear-gradient(\n      rgba(79, 95, 173, 0.03) 1px,\n      transparent 1px\n    ),\n    linear-gradient(90deg, rgba(79, 95, 173, 0.03) 1px, transparent 1px);\n  background-size: 20px 20px;\n  pointer-events: none;\n  z-index: 0;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%] {\n  background-color: var(--dark-bg);\n  position: relative;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: linear-gradient(\n      rgba(0, 247, 255, 0.03) 1px,\n      transparent 1px\n    ),\n    linear-gradient(90deg, rgba(0, 247, 255, 0.03) 1px, transparent 1px);\n  background-size: 20px 20px;\n  pointer-events: none;\n  z-index: 0;\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 87031:
/*!*****************************************************************!*\
  !*** ./src/app/views/front/messages/messages-routing.module.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessagesRoutingModule: () => (/* binding */ MessagesRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./message-chat/message-chat.component */ 38096);
/* harmony import */ var _messages_list_messages_list_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./messages-list/messages-list.component */ 14682);
/* harmony import */ var _user_list_user_list_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./user-list/user-list.component */ 13122);
/* harmony import */ var _message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./message-layout/message-layout.component */ 88076);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);







const routes = [{
  path: '',
  component: _message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_3__.MessageLayoutComponent,
  children: [{
    path: '',
    redirectTo: 'conversations',
    pathMatch: 'full'
  }, {
    path: 'conversations',
    component: _messages_list_messages_list_component__WEBPACK_IMPORTED_MODULE_1__.MessagesListComponent,
    data: {
      title: 'Conversations'
    }
  }, {
    path: 'conversations/chat/:id',
    component: _message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_0__.MessageChatComponent,
    data: {
      title: 'Chat'
    }
  }, {
    path: 'chat/:id',
    component: _message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_0__.MessageChatComponent,
    data: {
      title: 'Chat'
    }
  }, {
    path: 'users',
    component: _user_list_user_list_component__WEBPACK_IMPORTED_MODULE_2__.UserListComponent,
    data: {
      title: 'Utilisateurs'
    }
  }, {
    path: 'new',
    redirectTo: 'users',
    pathMatch: 'full'
  }]
}];
class MessagesRoutingModule {
  static {
    this.ɵfac = function MessagesRoutingModule_Factory(t) {
      return new (t || MessagesRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineNgModule"]({
      type: MessagesRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsetNgModuleScope"](MessagesRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
  });
})();

/***/ }),

/***/ 38534:
/*!*********************************************************!*\
  !*** ./src/app/views/front/messages/messages.module.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessagesModule: () => (/* binding */ MessagesModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _messages_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messages-routing.module */ 87031);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var apollo_angular__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! apollo-angular */ 7797);
/* harmony import */ var _message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./message-chat/message-chat.component */ 38096);
/* harmony import */ var _messages_list_messages_list_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./messages-list/messages-list.component */ 14682);
/* harmony import */ var _user_list_user_list_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./user-list/user-list.component */ 13122);
/* harmony import */ var _message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./message-layout/message-layout.component */ 88076);
/* harmony import */ var src_app_services_user_status_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/services/user-status.service */ 59722);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/app/services/message.service */ 54537);
/* harmony import */ var src_app_components_voice_message_voice_message_module__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/app/components/voice-message/voice-message.module */ 50356);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);













class MessagesModule {
  static {
    this.ɵfac = function MessagesModule_Factory(t) {
      return new (t || MessagesModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineNgModule"]({
      type: MessagesModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjector"]({
      providers: [src_app_services_user_status_service__WEBPACK_IMPORTED_MODULE_5__.UserStatusService, src_app_services_message_service__WEBPACK_IMPORTED_MODULE_6__.MessageService],
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _messages_routing_module__WEBPACK_IMPORTED_MODULE_0__.MessagesRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.ReactiveFormsModule, apollo_angular__WEBPACK_IMPORTED_MODULE_11__.ApolloModule, _angular_router__WEBPACK_IMPORTED_MODULE_12__.RouterModule, src_app_components_voice_message_voice_message_module__WEBPACK_IMPORTED_MODULE_7__.VoiceMessageModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵsetNgModuleScope"](MessagesModule, {
    declarations: [_message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_1__.MessageChatComponent, _messages_list_messages_list_component__WEBPACK_IMPORTED_MODULE_2__.MessagesListComponent, _user_list_user_list_component__WEBPACK_IMPORTED_MODULE_3__.UserListComponent, _message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_4__.MessageLayoutComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _messages_routing_module__WEBPACK_IMPORTED_MODULE_0__.MessagesRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.ReactiveFormsModule, apollo_angular__WEBPACK_IMPORTED_MODULE_11__.ApolloModule, _angular_router__WEBPACK_IMPORTED_MODULE_12__.RouterModule, src_app_components_voice_message_voice_message_module__WEBPACK_IMPORTED_MODULE_7__.VoiceMessageModule]
  });
})();

/***/ }),

/***/ 13122:
/*!***********************************************************************!*\
  !*** ./src/app/views/front/messages/user-list/user-list.component.ts ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserListComponent: () => (/* binding */ UserListComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs */ 2510);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs */ 19240);
/* harmony import */ var src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/models/message.model */ 15293);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/message.service */ 54537);
/* harmony import */ var src_app_services_call_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/call.service */ 49454);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/services/authuser.service */ 99271);
/* harmony import */ var src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/services/toast.service */ 68397);
/* harmony import */ var src_app_services_logger_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/services/logger.service */ 34798);
/* harmony import */ var _app_services_theme_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @app/services/theme.service */ 70487);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/common */ 60316);













function UserListComponent_div_57_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 44)(1, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate2"]("Affichage de ", ctx_r0.users.length, " sur ", ctx_r0.totalUsers, " utilisateurs");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate2"]("Page ", ctx_r0.currentPage, " sur ", ctx_r0.totalPages, "");
  }
}
function UserListComponent_div_59_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](1, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](2, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](3, "Chargement des utilisateurs...");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
function UserListComponent_div_60_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 48)(1, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](2, "i", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "h3", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](4, "Aucun utilisateur trouv\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](5, "p", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](6, " Essayez un autre terme de recherche ou effacez les filtres ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
function UserListComponent_ul_61_li_1_span_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](0, "span", 66);
  }
}
function UserListComponent_ul_61_li_1_button_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "button", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r13);
      const user_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]().$implicit;
      const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r11.startAudioCall(user_r7.id || user_r7._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](1, "i", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function UserListComponent_ul_61_li_1_button_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "button", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r16);
      const user_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]().$implicit;
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r14.startVideoCall(user_r7.id || user_r7._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](1, "i", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function UserListComponent_ul_61_li_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "li", 55)(1, "div", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_ul_61_li_1_Template_div_click_1_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r18);
      const user_r7 = restoredCtx.$implicit;
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r17.startConversation(user_r7.id || user_r7._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](2, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](3, "img", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](4, UserListComponent_ul_61_li_1_span_4_Template, 1, 0, "span", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](5, "div", 60)(6, "h3", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](8, "p", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](10, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](11, UserListComponent_ul_61_li_1_button_11_Template, 2, 0, "button", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](12, UserListComponent_ul_61_li_1_button_12_Template, 2, 0, "button", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const user_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("src", user_r7.image || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", user_r7.isOnline);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate1"](" ", user_r7.username, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](user_r7.email);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", user_r7.isOnline);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", user_r7.isOnline);
  }
}
function UserListComponent_ul_61_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "ul", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](1, UserListComponent_ul_61_li_1_Template, 13, 6, "li", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngForOf", ctx_r3.users);
  }
}
function UserListComponent_div_62_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 71)(1, "div", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](2, "div", 73)(3, "div", 74)(4, "div", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](5, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](6, " Chargement de plus d'utilisateurs... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
function UserListComponent_div_63_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 76)(1, "button", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_div_63_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r20);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r19.loadNextPage());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](2, "i", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](3, " Charger plus d'utilisateurs ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
class UserListComponent {
  constructor(MessageService, callService, router, route, authService, toastService, logger, themeService) {
    this.MessageService = MessageService;
    this.callService = callService;
    this.router = router;
    this.route = route;
    this.authService = authService;
    this.toastService = toastService;
    this.logger = logger;
    this.themeService = themeService;
    this.users = [];
    this.loading = true;
    this.currentUserId = null;
    // Pagination
    this.currentPage = 1;
    this.pageSize = 10;
    this.totalUsers = 0;
    this.totalPages = 0;
    this.hasNextPage = false;
    this.hasPreviousPage = false;
    // Sorting and filtering
    this.sortBy = 'username';
    this.sortOrder = 'asc';
    this.filterForm = new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroup({
      searchQuery: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(''),
      isOnline: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(null)
    });
    // Auto-refresh
    this.autoRefreshEnabled = true;
    this.autoRefreshInterval = 30000; // 30 seconds
    this.loadingMore = false;
    this.subscriptions = new rxjs__WEBPACK_IMPORTED_MODULE_9__.Subscription();
    this.isDarkMode$ = this.themeService.darkMode$;
  }
  ngOnInit() {
    this.currentUserId = this.authService.getCurrentUserId();
    this.setupFilterListeners();
    this.setupAutoRefresh();
    this.loadUsers();
  }
  setupFilterListeners() {
    // Subscribe to search query changes
    const searchSub = this.filterForm.get('searchQuery').valueChanges.subscribe(() => {
      this.resetPagination();
      this.loadUsers();
    });
    this.subscriptions.add(searchSub);
    // Subscribe to online status filter changes
    const onlineSub = this.filterForm.get('isOnline').valueChanges.subscribe(() => {
      this.resetPagination();
      this.loadUsers();
    });
    this.subscriptions.add(onlineSub);
  }
  setupAutoRefresh() {
    if (this.autoRefreshEnabled) {
      this.autoRefreshSubscription = (0,rxjs__WEBPACK_IMPORTED_MODULE_10__.interval)(this.autoRefreshInterval).subscribe(() => {
        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {
          this.loadUsers(true);
        }
      });
    }
  }
  toggleAutoRefresh() {
    this.autoRefreshEnabled = !this.autoRefreshEnabled;
    if (this.autoRefreshEnabled) {
      this.setupAutoRefresh();
    } else if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
      this.autoRefreshSubscription = undefined;
    }
  }
  resetPagination() {
    this.currentPage = 1;
  }
  // Get searchQuery from the form
  get searchQuery() {
    return this.filterForm.get('searchQuery')?.value || '';
  }
  // Set searchQuery in the form
  set searchQuery(value) {
    this.filterForm.get('searchQuery')?.setValue(value);
  }
  // Helper function for template type casting
  $any(item) {
    return item;
  }
  loadUsers(forceRefresh = false) {
    if (this.loadingMore) return;
    this.loading = true;
    const searchQuery = this.filterForm.get('searchQuery')?.value || '';
    const isOnline = this.filterForm.get('isOnline')?.value;
    const sub = this.MessageService.getAllUsers(forceRefresh, searchQuery, this.currentPage, this.pageSize, this.sortBy, this.sortOrder, isOnline === true ? true : undefined).subscribe({
      next: users => {
        if (!Array.isArray(users)) {
          this.users = [];
          this.loading = false;
          this.loadingMore = false;
          this.toastService.showError('Failed to load users: Invalid data');
          return;
        }
        // If first page, replace users array; otherwise append
        if (this.currentPage === 1) {
          // Filter out current user
          this.users = users.filter(user => {
            if (!user) return false;
            const userId = user.id || user._id;
            return userId !== this.currentUserId;
          });
        } else {
          // Append new users to existing array, avoiding duplicates and filtering out current user
          const newUsers = users.filter(newUser => {
            if (!newUser) return false;
            const userId = newUser.id || newUser._id;
            return userId !== this.currentUserId && !this.users.some(existingUser => (existingUser.id || existingUser._id) === userId);
          });
          this.users = [...this.users, ...newUsers];
        }
        // Update pagination metadata from service
        const pagination = this.MessageService.currentUserPagination;
        this.totalUsers = pagination.totalCount;
        this.totalPages = pagination.totalPages;
        this.hasNextPage = pagination.hasNextPage;
        this.hasPreviousPage = pagination.hasPreviousPage;
        this.loading = false;
        this.loadingMore = false;
      },
      error: error => {
        this.loading = false;
        this.loadingMore = false;
        this.toastService.showError(`Failed to load users: ${error.message || 'Unknown error'}`);
        if (this.currentPage === 1) {
          this.users = [];
        }
      },
      complete: () => {
        this.loading = false;
        this.loadingMore = false;
      }
    });
    this.subscriptions.add(sub);
  }
  startConversation(userId) {
    if (!userId) {
      this.toastService.showError('Cannot start conversation with undefined user');
      return;
    }
    this.toastService.showInfo('Creating conversation...');
    this.MessageService.createConversation(userId).subscribe({
      next: conversation => {
        if (!conversation || !conversation.id) {
          this.toastService.showError('Failed to create conversation: Invalid response');
          return;
        }
        this.router.navigate(['/messages/conversations/chat', conversation.id]).then(success => {
          if (!success) {
            this.toastService.showError('Failed to open conversation');
          }
        });
      },
      error: error => {
        this.toastService.showError(`Failed to create conversation: ${error.message || 'Unknown error'}`);
      }
    });
  }
  startAudioCall(userId) {
    if (!userId) return;
    this.callService.initiateCall(userId, src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.CallType.AUDIO).subscribe({
      next: call => {
        this.toastService.showSuccess('Audio call initiated');
      },
      error: error => {
        this.toastService.showError('Failed to initiate audio call');
      }
    });
  }
  startVideoCall(userId) {
    if (!userId) return;
    this.callService.initiateCall(userId, src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.CallType.VIDEO).subscribe({
      next: call => {
        this.toastService.showSuccess('Video call initiated');
      },
      error: error => {
        this.toastService.showError('Failed to initiate video call');
      }
    });
  }
  loadNextPage() {
    if (this.hasNextPage && !this.loading) {
      this.loadingMore = true;
      this.currentPage++;
      this.loadUsers();
    }
  }
  loadPreviousPage() {
    if (this.hasPreviousPage && !this.loading) {
      this.loadingMore = true;
      this.currentPage--;
      this.loadUsers();
    }
  }
  refreshUsers() {
    this.resetPagination();
    this.loadUsers(true);
  }
  clearFilters() {
    this.filterForm.reset({
      searchQuery: '',
      isOnline: null
    });
    this.resetPagination();
    this.loadUsers(true);
  }
  changeSortOrder(field) {
    if (this.sortBy === field) {
      // Toggle sort order if clicking the same field
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      // Set new sort field with default ascending order
      this.sortBy = field;
      this.sortOrder = 'asc';
    }
    this.resetPagination();
    this.loadUsers(true);
  }
  /**
   * Navigue vers la liste des conversations
   */
  goBackToConversations() {
    this.router.navigate(['/messages/conversations']);
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
    }
  }
  static {
    this.ɵfac = function UserListComponent_Factory(t) {
      return new (t || UserListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_message_service__WEBPACK_IMPORTED_MODULE_1__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_call_service__WEBPACK_IMPORTED_MODULE_2__.CallService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_11__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_11__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_3__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_4__.ToastService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_logger_service__WEBPACK_IMPORTED_MODULE_5__.LoggerService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_app_services_theme_service__WEBPACK_IMPORTED_MODULE_6__.ThemeService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineComponent"]({
      type: UserListComponent,
      selectors: [["app-user-list"]],
      decls: 64,
      vars: 18,
      consts: [[1, "flex", "flex-col", "h-full", "futuristic-users-container"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/10", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/10", "dark:to-transparent", "blur-3xl"], [1, "absolute", "top-[40%]", "right-[30%]", "w-40", "h-40", "rounded-full", "bg-gradient-to-br", "from-transparent", "to-transparent", "dark:from-[#00f7ff]/5", "dark:to-transparent", "blur-3xl", "opacity-0", "dark:opacity-100"], [1, "absolute", "bottom-[60%]", "left-[25%]", "w-32", "h-32", "rounded-full", "bg-gradient-to-tl", "from-transparent", "to-transparent", "dark:from-[#00f7ff]/5", "dark:to-transparent", "blur-3xl", "opacity-0", "dark:opacity-100"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-0"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]"], [1, "absolute", "inset-0", "opacity-0", "dark:opacity-100", "overflow-hidden"], [1, "h-px", "w-full", "bg-[#00f7ff]/20", "absolute", "animate-scan"], [1, "futuristic-users-header"], [1, "flex", "justify-between", "items-center", "mb-4"], [1, "futuristic-title"], [1, "flex", "space-x-2"], ["title", "Rafra\u00EEchir la liste", 1, "futuristic-action-button", 3, "click"], [1, "fas", "fa-sync-alt"], [1, "futuristic-action-button", 3, "click"], [1, "fas", "fa-arrow-left"], [1, "space-y-3"], [1, "relative"], ["type", "text", "placeholder", "Rechercher des utilisateurs...", 1, "w-full", "pl-10", "pr-4", "py-2", "rounded-lg", "futuristic-input-field", 3, "ngModel", "ngModelChange"], [1, "fas", "fa-search", "absolute", "left-3", "top-3", "text-[#6d6870]", "dark:text-[#a0a0a0]"], [1, "flex", "items-center", "justify-between"], [1, "flex", "items-center", "space-x-4"], [1, "flex", "items-center", "space-x-2"], [1, "futuristic-checkbox-container"], ["type", "checkbox", "id", "onlineFilter", 1, "futuristic-checkbox", 3, "checked", "change"], [1, "futuristic-checkbox-checkmark"], ["for", "onlineFilter", 1, "futuristic-label"], [1, "futuristic-label"], [1, "futuristic-select", 3, "change"], ["value", "username", 3, "selected"], ["value", "email", 3, "selected"], ["value", "lastActive", 3, "selected"], [1, "futuristic-sort-button", 3, "title", "click"], [1, "futuristic-clear-button", 3, "click"], ["class", "flex justify-between items-center futuristic-pagination-info", 4, "ngIf"], [1, "futuristic-users-list", 3, "scroll"], ["class", "futuristic-loading-container", 4, "ngIf"], ["class", "futuristic-empty-state", 4, "ngIf"], ["class", "futuristic-users-grid", 4, "ngIf"], ["class", "futuristic-loading-more", 4, "ngIf"], ["class", "futuristic-load-more-container", 4, "ngIf"], [1, "flex", "justify-between", "items-center", "futuristic-pagination-info"], [1, "futuristic-loading-container"], [1, "futuristic-loading-circle"], [1, "futuristic-loading-text"], [1, "futuristic-empty-state"], [1, "futuristic-empty-icon"], [1, "fas", "fa-users"], [1, "futuristic-empty-title"], [1, "futuristic-empty-text"], [1, "futuristic-users-grid"], ["class", "futuristic-user-card", 4, "ngFor", "ngForOf"], [1, "futuristic-user-card"], [1, "futuristic-user-content", 3, "click"], [1, "futuristic-avatar"], ["alt", "User avatar", 3, "src"], ["class", "futuristic-online-indicator", 4, "ngIf"], [1, "futuristic-user-info"], [1, "futuristic-username"], [1, "futuristic-user-email"], [1, "futuristic-call-buttons"], ["class", "futuristic-call-button", "title", "Appel audio", 3, "click", 4, "ngIf"], ["class", "futuristic-call-button", "title", "Appel vid\u00E9o", 3, "click", 4, "ngIf"], [1, "futuristic-online-indicator"], ["title", "Appel audio", 1, "futuristic-call-button", 3, "click"], [1, "fas", "fa-phone"], ["title", "Appel vid\u00E9o", 1, "futuristic-call-button", 3, "click"], [1, "fas", "fa-video"], [1, "futuristic-loading-more"], [1, "futuristic-loading-dots"], [1, "futuristic-loading-dot", 2, "animation-delay", "0s"], [1, "futuristic-loading-dot", 2, "animation-delay", "0.2s"], [1, "futuristic-loading-dot", 2, "animation-delay", "0.4s"], [1, "futuristic-load-more-container"], [1, "futuristic-load-more-button", 3, "click"], [1, "fas", "fa-chevron-down", "mr-2"]],
      template: function UserListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpipe"](1, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](2, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](3, "div", 2)(4, "div", 3)(5, "div", 4)(6, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](7, "div", 6)(8, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](9, "div", 8)(10, "div", 8)(11, "div", 8)(12, "div", 8)(13, "div", 8)(14, "div", 8)(15, "div", 8)(16, "div", 8)(17, "div", 8)(18, "div", 8)(19, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](20, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](21, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](22, "div", 11)(23, "div", 12)(24, "h1", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](25, "Nouvelle Conversation");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](26, "div", 14)(27, "button", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_Template_button_click_27_listener() {
            return ctx.refreshUsers();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](28, "i", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](29, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_Template_button_click_29_listener() {
            return ctx.goBackToConversations();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](30, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](31, "div", 19)(32, "div", 20)(33, "input", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("ngModelChange", function UserListComponent_Template_input_ngModelChange_33_listener($event) {
            return ctx.searchQuery = $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](34, "i", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](35, "div", 23)(36, "div", 24)(37, "div", 25)(38, "label", 26)(39, "input", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("change", function UserListComponent_Template_input_change_39_listener($event) {
            let tmp_b_0;
            return (tmp_b_0 = ctx.filterForm.get("isOnline")) == null ? null : tmp_b_0.setValue($event.target.checked ? true : null);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](40, "span", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](41, "label", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](42, "En ligne uniquement");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](43, "div", 25)(44, "span", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](45, "Trier par:");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](46, "select", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("change", function UserListComponent_Template_select_change_46_listener($event) {
            return ctx.changeSortOrder($event.target.value);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](47, "option", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](48, " Nom ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](49, "option", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](50, " Email ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](51, "option", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](52, " Derni\u00E8re activit\u00E9 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](53, "button", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_Template_button_click_53_listener() {
            ctx.sortOrder = ctx.sortOrder === "asc" ? "desc" : "asc";
            return ctx.loadUsers(true);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](54, "i");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](55, "button", 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_Template_button_click_55_listener() {
            return ctx.clearFilters();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](56, " Effacer les filtres ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](57, UserListComponent_div_57_Template, 5, 4, "div", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](58, "div", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("scroll", function UserListComponent_Template_div_scroll_58_listener($event) {
            return $event.target.scrollTop + $event.target.clientHeight >= $event.target.scrollHeight - 200 && ctx.loadNextPage();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](59, UserListComponent_div_59_Template, 4, 0, "div", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](60, UserListComponent_div_60_Template, 7, 0, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](61, UserListComponent_ul_61_Template, 2, 1, "ul", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](62, UserListComponent_div_62_Template, 7, 0, "div", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](63, UserListComponent_div_63_Template, 4, 0, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          let tmp_2_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵclassProp"]("dark", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpipeBind1"](1, 16, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](33);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngModel", ctx.searchQuery);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("checked", ((tmp_2_0 = ctx.filterForm.get("isOnline")) == null ? null : tmp_2_0.value) === true);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("selected", ctx.sortBy === "username");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("selected", ctx.sortBy === "email");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("selected", ctx.sortBy === "lastActive");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("title", ctx.sortOrder === "asc" ? "Ordre croissant" : "Ordre d\u00E9croissant");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵclassMap"](ctx.sortOrder === "asc" ? "fas fa-sort-up" : "fas fa-sort-down");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.totalUsers > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.loading && !ctx.users.length);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.users.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.users.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.loading && ctx.users.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.hasNextPage && !ctx.loading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_12__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_12__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_8__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_12__.AsyncPipe],
      styles: ["\n\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%] {\n  background-color: #f0f4f8;\n  color: #6d6870;\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%] {\n  background-color: var(--dark-bg);\n  color: var(--text-light);\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n@keyframes _ngcontent-%COMP%_grid-pulse {\n  0% {\n    opacity: 0.3;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 0.3;\n  }\n}\n\n\n\n@keyframes _ngcontent-%COMP%_scan {\n  0% {\n    top: -10%;\n    opacity: 0.5;\n  }\n  50% {\n    opacity: 0.8;\n  }\n  100% {\n    top: 110%;\n    opacity: 0.5;\n  }\n}\n\n.animate-scan[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_scan 8s linear infinite;\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.5);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: linear-gradient(\n      rgba(79, 95, 173, 0.03) 1px,\n      transparent 1px\n    ),\n    linear-gradient(90deg, rgba(79, 95, 173, 0.03) 1px, transparent 1px);\n  background-size: 20px 20px;\n  pointer-events: none;\n  z-index: 0;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: linear-gradient(\n      rgba(0, 247, 255, 0.07) 1px,\n      transparent 1px\n    ),\n    linear-gradient(90deg, rgba(0, 247, 255, 0.07) 1px, transparent 1px),\n    linear-gradient(rgba(0, 247, 255, 0.03) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(0, 247, 255, 0.03) 1px, transparent 1px);\n  background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;\n  pointer-events: none;\n  z-index: 0;\n  animation: _ngcontent-%COMP%_grid-pulse 4s infinite ease-in-out;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: repeating-linear-gradient(\n    to bottom,\n    transparent,\n    transparent 50px,\n    rgba(0, 247, 255, 0.03) 50px,\n    rgba(0, 247, 255, 0.03) 51px\n  );\n  pointer-events: none;\n  z-index: 0;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border-bottom: 1px solid rgba(79, 95, 173, 0.2);\n  background-color: rgba(255, 255, 255, 0.9);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\n  background-color: rgba(30, 30, 30, 0.9);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n  position: relative;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n  text-shadow: 0 0 10px rgba(79, 95, 173, 0.5);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  background: linear-gradient(\n    135deg,\n    var(--accent-color),\n    var(--secondary-color)\n  );\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n  text-shadow: 0 0 10px rgba(0, 247, 255, 0.5);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(79, 95, 173, 0.1);\n  color: #4f5fad;\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(79, 95, 173, 0.2);\n  transform: translateY(-2px);\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 247, 255, 0.1);\n  color: var(--accent-color);\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.2);\n  transform: translateY(-2px);\n  box-shadow: var(--glow-effect);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%] {\n  background-color: rgba(79, 95, 173, 0.05);\n  border: 1px solid rgba(79, 95, 173, 0.2);\n  border-radius: var(--border-radius-md);\n  color: #6d6870;\n  transition: all var(--transition-fast);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus, :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus {\n  background-color: rgba(79, 95, 173, 0.1);\n  border-color: #4f5fad;\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n  outline: none;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder, :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder {\n  color: #6d6870;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%] {\n  background-color: rgba(0, 247, 255, 0.05);\n  border: 1px solid rgba(0, 247, 255, 0.2);\n  border-radius: var(--border-radius-md);\n  color: var(--text-light);\n  transition: all var(--transition-fast);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus {\n  background-color: rgba(0, 247, 255, 0.1);\n  border-color: var(--accent-color);\n  box-shadow: var(--glow-effect);\n  outline: none;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder, .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder {\n  color: var(--text-dim);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%] {\n  position: relative;\n  display: inline-block;\n  width: 18px;\n  height: 18px;\n  cursor: pointer;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%] {\n  position: absolute;\n  opacity: 0;\n  cursor: pointer;\n  height: 0;\n  width: 0;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 18px;\n  width: 18px;\n  background-color: rgba(79, 95, 173, 0.05);\n  border: 1px solid rgba(79, 95, 173, 0.2);\n  border-radius: 4px;\n  transition: all var(--transition-fast);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%] {\n  background-color: #4f5fad;\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after {\n  content: \"\";\n  position: absolute;\n  display: none;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after {\n  display: block;\n  left: 6px;\n  top: 2px;\n  width: 5px;\n  height: 10px;\n  border: solid white;\n  border-width: 0 2px 2px 0;\n  transform: rotate(45deg);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%] {\n  color: #6d6870;\n  font-size: 0.875rem;\n  transition: color var(--transition-fast);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%] {\n  position: relative;\n  display: inline-block;\n  width: 18px;\n  height: 18px;\n  cursor: pointer;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%] {\n  position: absolute;\n  opacity: 0;\n  cursor: pointer;\n  height: 0;\n  width: 0;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 18px;\n  width: 18px;\n  background-color: rgba(0, 247, 255, 0.05);\n  border: 1px solid rgba(0, 247, 255, 0.2);\n  border-radius: 4px;\n  transition: all var(--transition-fast);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%] {\n  background-color: var(--accent-color);\n  box-shadow: var(--glow-effect);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after {\n  content: \"\";\n  position: absolute;\n  display: none;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after {\n  display: block;\n  left: 6px;\n  top: 2px;\n  width: 5px;\n  height: 10px;\n  border: solid white;\n  border-width: 0 2px 2px 0;\n  transform: rotate(45deg);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n  font-size: 0.875rem;\n  transition: color var(--transition-fast);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%] {\n  background-color: rgba(79, 95, 173, 0.05);\n  border: 1px solid rgba(79, 95, 173, 0.2);\n  border-radius: var(--border-radius-md);\n  color: #6d6870;\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f5fad' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\");\n  background-repeat: no-repeat;\n  background-position: right 0.5rem center;\n  background-size: 1em;\n  padding-right: 2rem;\n  transition: all var(--transition-fast);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus, :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus {\n  background-color: rgba(79, 95, 173, 0.1);\n  border-color: #4f5fad;\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n  outline: none;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  color: #6d6870;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%] {\n  background-color: rgba(0, 247, 255, 0.05);\n  border: 1px solid rgba(0, 247, 255, 0.2);\n  border-radius: var(--border-radius-md);\n  color: var(--text-light);\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300f7ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\");\n  background-repeat: no-repeat;\n  background-position: right 0.5rem center;\n  background-size: 1em;\n  padding-right: 2rem;\n  transition: all var(--transition-fast);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus {\n  background-color: rgba(0, 247, 255, 0.1);\n  border-color: var(--accent-color);\n  box-shadow: var(--glow-effect);\n  outline: none;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\n  background-color: var(--dark-bg);\n  color: var(--text-light);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%] {\n  width: 28px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(79, 95, 173, 0.05);\n  color: #4f5fad;\n  border: none;\n  border-radius: var(--border-radius-sm);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(79, 95, 173, 0.1);\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%] {\n  width: 28px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 247, 255, 0.05);\n  color: var(--accent-color);\n  border: none;\n  border-radius: var(--border-radius-sm);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.1);\n  box-shadow: var(--glow-effect);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #4f5fad;\n  background: none;\n  border: none;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  padding: 0.25rem 0.5rem;\n  border-radius: var(--border-radius-sm);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover {\n  color: #7826b5;\n  background-color: rgba(79, 95, 173, 0.05);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: var(--accent-color);\n  background: none;\n  border: none;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  padding: 0.25rem 0.5rem;\n  border-radius: var(--border-radius-sm);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover {\n  color: var(--secondary-color);\n  background-color: rgba(0, 247, 255, 0.05);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #6d6870;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: var(--text-dim);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow-y: auto;\n  padding: 1rem;\n  scrollbar-width: thin;\n  scrollbar-color: #4f5fad transparent;\n  position: relative;\n  z-index: 1;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 4px;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background-color: #4f5fad;\n  border-radius: 10px;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow-y: auto;\n  padding: 1rem;\n  scrollbar-width: thin;\n  scrollbar-color: var(--accent-color) transparent;\n  position: relative;\n  z-index: 1;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 4px;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background-color: var(--accent-color);\n  border-radius: 10px;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 2rem;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  border: 2px solid transparent;\n  border-top-color: #4f5fad;\n  border-bottom-color: #7826b5;\n  animation: _ngcontent-%COMP%_futuristic-spin-light 1.2s linear infinite;\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.3);\n}\n\n@keyframes _ngcontent-%COMP%_futuristic-spin-light {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  color: #6d6870;\n  font-size: 0.875rem;\n  text-align: center;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 2rem;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  border: 2px solid transparent;\n  border-top-color: var(--accent-color);\n  border-bottom-color: var(--secondary-color);\n  animation: _ngcontent-%COMP%_futuristic-spin-dark 1.2s linear infinite;\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);\n}\n\n@keyframes _ngcontent-%COMP%_futuristic-spin-dark {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.dark[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  color: var(--text-dim);\n  font-size: 0.875rem;\n  text-align: center;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 2rem;\n  text-align: center;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  color: #4f5fad;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #4f5fad;\n  margin-bottom: 0.5rem;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\n  color: #6d6870;\n  font-size: 0.875rem;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 2rem;\n  text-align: center;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  color: var(--accent-color);\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--text-light);\n  margin-bottom: 0.5rem;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n  font-size: 0.875rem;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 1rem;\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%] {\n  background-color: rgba(79, 95, 173, 0.05);\n  border: 1px solid rgba(79, 95, 173, 0.1);\n  border-radius: var(--border-radius-md);\n  overflow: hidden;\n  transition: all var(--transition-fast);\n  display: flex;\n  flex-direction: column;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n  background-color: rgba(79, 95, 173, 0.1);\n  border-color: rgba(79, 95, 173, 0.3);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  cursor: pointer;\n  flex: 1;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%] {\n  position: relative;\n  width: 48px;\n  height: 48px;\n  flex-shrink: 0;\n  margin-right: 1rem;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n  border: 2px solid rgba(79, 95, 173, 0.3);\n  transition: all var(--transition-fast);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  border-color: #4f5fad;\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.5);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 0;\n  right: 0;\n  width: 12px;\n  height: 12px;\n  background-color: #4caf50;\n  border-radius: 50%;\n  border: 2px solid #ffffff;\n  box-shadow: 0 0 8px rgba(76, 175, 80, 0.8);\n  animation: _ngcontent-%COMP%_pulse-light 2s infinite;\n}\n\n@keyframes _ngcontent-%COMP%_pulse-light {\n  0% {\n    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);\n  }\n  70% {\n    box-shadow: 0 0 0 6px rgba(76, 175, 80, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);\n  }\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #4f5fad;\n  margin-bottom: 0.25rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #6d6870;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  padding: 0.5rem 1rem;\n  background-color: rgba(79, 95, 173, 0.05);\n  border-top: 1px solid rgba(79, 95, 173, 0.1);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(79, 95, 173, 0.1);\n  color: #4f5fad;\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  margin-left: 0.5rem;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(79, 95, 173, 0.2);\n  transform: translateY(-2px);\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 1rem;\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%] {\n  background-color: rgba(0, 247, 255, 0.05);\n  border: 1px solid rgba(0, 247, 255, 0.1);\n  border-radius: var(--border-radius-md);\n  overflow: hidden;\n  transition: all var(--transition-fast);\n  display: flex;\n  flex-direction: column;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--glow-effect);\n  background-color: rgba(0, 247, 255, 0.1);\n  border-color: rgba(0, 247, 255, 0.3);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  cursor: pointer;\n  flex: 1;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%] {\n  position: relative;\n  width: 48px;\n  height: 48px;\n  flex-shrink: 0;\n  margin-right: 1rem;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n  border: 2px solid rgba(0, 247, 255, 0.3);\n  transition: all var(--transition-fast);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  border-color: var(--accent-color);\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 0;\n  right: 0;\n  width: 12px;\n  height: 12px;\n  background-color: var(--success-color);\n  border-radius: 50%;\n  border: 2px solid var(--dark-bg);\n  box-shadow: 0 0 8px rgba(0, 255, 128, 0.8);\n  animation: _ngcontent-%COMP%_pulse-dark 2s infinite;\n}\n\n@keyframes _ngcontent-%COMP%_pulse-dark {\n  0% {\n    box-shadow: 0 0 0 0 rgba(0, 255, 128, 0.4);\n  }\n  70% {\n    box-shadow: 0 0 0 6px rgba(0, 255, 128, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(0, 255, 128, 0);\n  }\n}\n\n.dark[_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: var(--text-light);\n  margin-bottom: 0.25rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: var(--text-dim);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  padding: 0.5rem 1rem;\n  background-color: rgba(0, 0, 0, 0.2);\n  border-top: 1px solid rgba(0, 247, 255, 0.1);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 247, 255, 0.1);\n  color: var(--accent-color);\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  margin-left: 0.5rem;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.2);\n  transform: translateY(-2px);\n  box-shadow: var(--glow-effect);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 1rem;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 0.5rem;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%] {\n  width: 8px;\n  height: 8px;\n  margin: 0 4px;\n  background-color: #4f5fad;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_dot-pulse-light 1.4s infinite ease-in-out;\n  box-shadow: 0 0 8px rgba(79, 95, 173, 0.5);\n}\n\n@keyframes _ngcontent-%COMP%_dot-pulse-light {\n  0%,\n  100% {\n    transform: scale(0.5);\n    opacity: 0.5;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 1rem;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  background: linear-gradient(\n    135deg,\n    rgba(79, 95, 173, 0.1),\n    rgba(79, 95, 173, 0.2)\n  );\n  color: #4f5fad;\n  border: 1px solid rgba(79, 95, 173, 0.3);\n  border-radius: var(--border-radius-md);\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(\n    135deg,\n    rgba(79, 95, 173, 0.2),\n    rgba(79, 95, 173, 0.3)\n  );\n  transform: translateY(-2px);\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 1rem;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 0.5rem;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%] {\n  width: 8px;\n  height: 8px;\n  margin: 0 4px;\n  background-color: var(--accent-color);\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_dot-pulse-dark 1.4s infinite ease-in-out;\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.5);\n}\n\n@keyframes _ngcontent-%COMP%_dot-pulse-dark {\n  0%,\n  100% {\n    transform: scale(0.5);\n    opacity: 0.5;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.dark[_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 1rem;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 247, 255, 0.1),\n    rgba(0, 247, 255, 0.2)\n  );\n  color: var(--accent-color);\n  border: 1px solid rgba(0, 247, 255, 0.3);\n  border-radius: var(--border-radius-md);\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(\n    135deg,\n    rgba(0, 247, 255, 0.2),\n    rgba(0, 247, 255, 0.3)\n  );\n  transform: translateY(-2px);\n  box-shadow: var(--glow-effect);\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 19240:
/*!********************************************************************!*\
  !*** ./node_modules/rxjs/dist/esm/internal/observable/interval.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   interval: () => (/* binding */ interval)
/* harmony export */ });
/* harmony import */ var _scheduler_async__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../scheduler/async */ 18473);
/* harmony import */ var _timer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./timer */ 14876);


function interval(period = 0, scheduler = _scheduler_async__WEBPACK_IMPORTED_MODULE_0__.asyncScheduler) {
  if (period < 0) {
    period = 0;
  }
  return (0,_timer__WEBPACK_IMPORTED_MODULE_1__.timer)(period, period, scheduler);
}

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_messages_messages_module_ts.js.map