{"version": 3, "file": "src_app_views_front_messages_messages_module_ts.js", "mappings": ";;;;;;;;;;;;;;;AACuC;;AAMjC,MAAOC,YAAY;EAKvBC,YAAA;IAJQ,KAAAC,aAAa,GAAG,IAAIH,iDAAe,CAAU,EAAE,CAAC;IACxD,KAAAI,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IACnC,KAAAC,SAAS,GAAG,CAAC;EAEN;EACPC,UAAUA,CAAA;IAChB,OAAOC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD;EAEQC,QAAQA,CAACC,KAAwB;IACvC,MAAMC,QAAQ,GAAU;MACtB,GAAGD,KAAK;MACRE,EAAE,EAAE,IAAI,CAACR,UAAU,EAAE;MACrBS,QAAQ,EAAEH,KAAK,CAACG,QAAQ,IAAI;KAC7B;IAED,MAAMC,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAEH,QAAQ,CAAC,CAAC;IAErD;IACA,IAAIA,QAAQ,CAACE,QAAQ,IAAIF,QAAQ,CAACE,QAAQ,GAAG,CAAC,EAAE;MAC9CI,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,WAAW,CAACP,QAAQ,CAACC,EAAE,CAAC;MAC/B,CAAC,EAAED,QAAQ,CAACE,QAAQ,CAAC;;EAEzB;EACAM,IAAIA,CACFC,OAAe,EACfC,IAAA,GAAiD,MAAM,EACvDR,QAAQ,GAAG,IAAI;IAEf,MAAMD,EAAE,GAAG,IAAI,CAACR,UAAU,EAAE;IAC5B,MAAMM,KAAK,GAAU;MAAEE,EAAE;MAAES,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEF,OAAO;MAAEP;IAAQ,CAAE;IAC/D,MAAMC,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAEJ,KAAK,CAAC,CAAC;IAElD,IAAIG,QAAQ,GAAG,CAAC,EAAE;MAChBI,UAAU,CAAC,MAAM,IAAI,CAACM,OAAO,CAACX,EAAE,CAAC,EAAEC,QAAQ,CAAC;;EAEhD;EAEAW,WAAWA,CAACJ,OAAe,EAAEP,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEP,QAAQ,CAAC;EACzC;EAEAY,SAASA,CAACL,OAAe,EAAEP,QAAQ,GAAG,IAAI;IACxC,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,OAAO,EAAEP,QAAQ,CAAC;EACvC;EAEAa,WAAWA,CAACN,OAAe,EAAEP,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEP,QAAQ,CAAC;EACzC;EAEAc,QAAQA,CAACP,OAAe,EAAEP,QAAQ,GAAG,IAAI;IACvC,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,MAAM,EAAEP,QAAQ,CAAC;EACtC;EAEAU,OAAOA,CAACX,EAAU;IAChB,MAAME,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK,CAACa,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjB,EAAE,KAAKA,EAAE,CAAC;IACzE,IAAI,CAACZ,aAAa,CAACgB,IAAI,CAACF,aAAa,CAAC;EACxC;EACAgB,OAAOA,CAACR,KAAa,EAAEF,OAAe,EAAEP,QAAiB;IACvD,IAAI,CAACJ,QAAQ,CAAC;MACZY,IAAI,EAAE,SAAS;MACfC,KAAK;MACLF,OAAO;MACPP,QAAQ;MACRkB,IAAI,EAAE;KACP,CAAC;EACJ;EACAC,KAAKA,CACHV,KAAa,EACbF,OAAe,EACfP,QAAiB,EACjBoB,MAAwB;IAExB,IAAI,CAACxB,QAAQ,CAAC;MACZY,IAAI,EAAE,OAAO;MACbC,KAAK;MACLF,OAAO;MACPP,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BkB,IAAI,EAAE,UAAU;MAChBE;KACD,CAAC;EACJ;EAEAC,OAAOA,CAACZ,KAAa,EAAEF,OAAe,EAAEP,QAAiB;IACvD,IAAI,CAACJ,QAAQ,CAAC;MACZY,IAAI,EAAE,SAAS;MACfC,KAAK;MACLF,OAAO;MACPP,QAAQ;MACRkB,IAAI,EAAE;KACP,CAAC;EACJ;EACA;EACAI,YAAYA,CAACF,MAAA,GAAiB,wBAAwB,EAAEG,IAAa;IACnE,MAAMC,QAAQ,GAAGD,IAAI,GAAG,WAAWA,IAAI,GAAG,GAAG,EAAE;IAC/C,IAAI,CAACJ,KAAK,CACR,cAAc,EACd,oDAAoDC,MAAM,GAAGI,QAAQ,EAAE,EACvE,IAAI,EACJ;MACEC,KAAK,EAAE,sBAAsB;MAC7BC,OAAO,EAAEA,CAAA,KAAK;QACZ;QACAC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;KACD,CACF;EACH;EAEAC,iBAAiBA,CAACC,QAAA,GAAmB,iBAAiB;IACpD,IAAI,CAACX,KAAK,CACR,qBAAqB,EACrB,2DAA2DW,QAAQ,EAAE,EACrE,IAAI,CACL;EACH;EAEAzB,WAAWA,CAACN,EAAU;IACpB,MAAME,aAAa,GAAG,IAAI,CAACd,aAAa,CAACe,KAAK;IAC9C,IAAI,CAACf,aAAa,CAACgB,IAAI,CAACF,aAAa,CAACc,MAAM,CAAElB,KAAK,IAAKA,KAAK,CAACE,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC3E;EACAgC,KAAKA,CAAA;IACH,IAAI,CAAC5C,aAAa,CAACgB,IAAI,CAAC,EAAE,CAAC;EAC7B;;;uBA/HWlB,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAA+C,OAAA,EAAZ/C,YAAY,CAAAgD,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACIgD;AAEhC;AAI4C;AACV;;;;;;;;;;;;;;;;IC2I9DK,uDAAA,cAaO;;;;;IAoBLA,4DAAA,cAGC;IACOA,oDAAA,6BAAiB;IAAAA,0DAAA,EAAO;IAC9BA,4DAAA,cAAqC;IACnCA,uDAAA,cAQO;IAmBTA,0DAAA,EAAM;;;;;IAGRA,4DAAA,WAA4B;IAC1BA,oDAAA,GAKF;IAAAA,0DAAA,EAAO;;;;IALLA,uDAAA,GAKF;IALEA,gEAAA,OAAAO,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAC,QAAA,iBAAAF,MAAA,CAAAG,gBAAA,CAAAH,MAAA,CAAAC,gBAAA,kBAAAD,MAAA,CAAAC,gBAAA,CAAAG,UAAA,OAKF;;;;;;IA0HNX,4DAAA,cAaC;IAGKA,wDAAA,mBAAAa,6DAAA;MAAAb,2DAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAAhB,2DAAA;MAASgB,OAAA,CAAAE,YAAA,EAAc;MAAA,OAAAlB,yDAAA,CAAAgB,OAAA,CAAAI,YAAA,GAAiB,KAAK;IAAA,EAAC;IAiB9CpB,uDAAA,YAAoD;IACpDA,4DAAA,eAA6B;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAO;IAEhDA,4DAAA,iBAgBC;IACCA,uDAAA,YAAkD;IAClDA,4DAAA,eAA6B;IAAAA,oDAAA,qBAAc;IAAAA,0DAAA,EAAO;IAEpDA,uDAAA,cAAmD;IACnDA,4DAAA,kBAgBC;IACCA,uDAAA,aAAiD;IACjDA,4DAAA,gBAA6B;IAAAA,oDAAA,uBAAU;IAAAA,0DAAA,EAAO;;;;;IAiBpDA,4DAAA,cAkBC;IAWGA,uDAAA,YAQK;IACLA,4DAAA,YAOC;IACCA,oDAAA,sCACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,YAA2C;IACzCA,oDAAA,0CACF;IAAAA,0DAAA,EAAI;;;;;IAKRA,4DAAA,cASC;IACCA,uDAAA,cAUO;IACPA,4DAAA,eAA6B;IAAAA,oDAAA,iCAA0B;IAAAA,0DAAA,EAAO;;;;;IAIhEA,4DAAA,cASC;IAEGA,uDAAA,YAA+B;IACjCA,0DAAA,EAAM;IACNA,4DAAA,aAOC;IACCA,oDAAA,sBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA8C;IAC5CA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,wCAAAqB,MAAA,CAAAb,gBAAA,kBAAAa,MAAA,CAAAb,gBAAA,CAAAc,QAAA,MACF;;;;;IAgBEtB,4DAAA,cAGC;IAUKA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAuB,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAAC,SAAA,OACF;;;;;;IAeF1B,4DAAA,cAGC;IAcGA,wDAAA,mBAAA2B,+EAAA;MAAA3B,2DAAA,CAAA4B,IAAA;MAAA,MAAAH,WAAA,GAAAzB,2DAAA,GAAA6B,SAAA;MAAA,MAAAC,OAAA,GAAA9B,2DAAA;MAAA,OAASA,yDAAA,CAAA8B,OAAA,CAAAC,eAAA,CAAAN,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,CAAoC;IAAA,EAAC;IAbhDwC,0DAAA,EAgBE;;;;IAfAA,uDAAA,GAEC;IAFDA,wDAAA,SAAAyB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAE,KAAA,yCAAAlC,2DAAA,CAEC,QAAAyB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA;;;;;IAoCHtB,4DAAA,eAaC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;;IAHJA,yDAAA,UAAAqC,OAAA,CAAAC,YAAA,CAAAb,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,EAAiD;IAEjDwC,uDAAA,GACF;IADEA,gEAAA,MAAAyB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAV,QAAA,MACF;;;;;IAGAtB,4DAAA,eAGC;IACCA,uDAAA,eAA+D;IACjEA,0DAAA,EAAM;;;;;IADCA,uDAAA,GAAmD;IAAnDA,wDAAA,cAAAuC,OAAA,CAAAC,oBAAA,CAAAf,WAAA,CAAAgB,OAAA,GAAAzC,4DAAA,CAAmD;;;;;IAsBxDA,uDAAA,eAOO;;;;;IALLA,yDAAA,WAAAyB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,MAAAmF,OAAA,CAAAC,aAAA,yBAEC;IAED5C,wDAAA,cAAA2C,OAAA,CAAAH,oBAAA,CAAAf,WAAA,CAAAgB,OAAA,GAAAzC,4DAAA,CAAmD;;;;;;IAxBvDA,4DAAA,eAAqD;IAIjDA,wDAAA,mBAAA6C,+EAAA;MAAA7C,2DAAA,CAAA8C,IAAA;MAAA,MAAArB,WAAA,GAAAzB,2DAAA,GAAA6B,SAAA;MAAA,MAAAkB,OAAA,GAAA/C,2DAAA;MAAA,OAASA,yDAAA,CAAA+C,OAAA,CAAAC,eAAA,CAAAvB,WAAA,CAAwB;IAAA,EAAC,kBAAAwB,8EAAAC,MAAA;MAAAlD,2DAAA,CAAA8C,IAAA;MAAA,MAAArB,WAAA,GAAAzB,2DAAA,GAAA6B,SAAA;MAAA,MAAAsB,OAAA,GAAAnD,2DAAA;MAAA,OAC1BA,yDAAA,CAAAmD,OAAA,CAAAC,WAAA,CAAAF,MAAA,EAAAzB,WAAA,CAA4B;IAAA,EADF,mBAAA4B,+EAAAH,MAAA;MAAAlD,2DAAA,CAAA8C,IAAA;MAAA,MAAArB,WAAA,GAAAzB,2DAAA,GAAA6B,SAAA;MAAA,MAAAyB,OAAA,GAAAtD,2DAAA;MAAA,OAEzBA,yDAAA,CAAAsD,OAAA,CAAAC,YAAA,CAAAL,MAAA,EAAAzB,WAAA,CAA6B;IAAA,EAFJ;IAHpCzB,0DAAA,EAeE;IAEFA,wDAAA,IAAAyD,+DAAA,mBAOO;IACTzD,0DAAA,EAAM;;;;;IAxBFA,uDAAA,GAA4B;IAA5BA,wDAAA,QAAA0D,OAAA,CAAAC,WAAA,CAAAlC,WAAA,GAAAzB,2DAAA,CAA4B,QAAAyB,WAAA,CAAAgB,OAAA;IAiB3BzC,uDAAA,GAAqB;IAArBA,wDAAA,SAAAyB,WAAA,CAAAgB,OAAA,CAAqB;;;;;IAgEtBzC,uDAAA,eAcO;;;;;;;IALLA,yDAAA,WAAA4D,OAAA,CAAAC,cAAA,CAAApC,WAAA,CAAAjE,EAAA,IAAAsG,QAAA,WAAyD,cAAAF,OAAA,CAAAC,cAAA,CAAApC,WAAA,CAAAjE,EAAA,qDAAAuG,KAAA;;;;;;IA8B3D/D,4DAAA,kBAgBC;IAdCA,wDAAA,mBAAAgE,2FAAA;MAAAhE,2DAAA,CAAAiE,IAAA;MAAA,MAAAxC,WAAA,GAAAzB,2DAAA,IAAA6B,SAAA;MAAA,MAAAqC,OAAA,GAAAlE,2DAAA;MAAA,OAASA,yDAAA,CAAAkE,OAAA,CAAAC,gBAAA,CAAA1C,WAAA,CAAyB;IAAA,EAAC;IAenCzB,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;;IADPA,uDAAA,GACF;IADEA,gEAAA,MAAAoE,OAAA,CAAAC,aAAA,CAAA5C,WAAA,QACF;;;;;;IA/GJzB,4DAAA,eAaC;IAGGA,wDAAA,mBAAAsE,kFAAA;MAAAtE,2DAAA,CAAAuE,IAAA;MAAA,MAAA9C,WAAA,GAAAzB,2DAAA,GAAA6B,SAAA;MAAA,MAAA2C,OAAA,GAAAxE,2DAAA;MAAA,OAASA,yDAAA,CAAAwE,OAAA,CAAAC,mBAAA,CAAAhD,WAAA,CAA4B;IAAA,EAAC;IAmBtCzB,uDAAA,aAKK;IACPA,0DAAA,EAAS;IAGTA,4DAAA,eASC;IACCA,wDAAA,IAAA0E,+DAAA,mBAcO;IACT1E,0DAAA,EAAM;IAGNA,4DAAA,eAOC;IAUGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IAGNA,wDAAA,IAAA2E,kEAAA,sBAkBS;IACX3E,0DAAA,EAAM;;;;;IA5EFA,uDAAA,GAEC;IAFDA,wDAAA,CAAA6E,OAAA,CAAAhB,cAAA,CAAApC,WAAA,CAAAjE,EAAA,mCAEC;IAiBgBwC,uDAAA,GAAe;IAAfA,wDAAA,YAAA6E,OAAA,CAAAC,UAAA,CAAe;IAkChC9E,uDAAA,GACF;IADEA,gEAAA,MAAA6E,OAAA,CAAAE,gBAAA,CAAAtD,WAAA,OACF;IAIGzB,uDAAA,GAAgC;IAAhCA,wDAAA,SAAA6E,OAAA,CAAAhB,cAAA,CAAApC,WAAA,CAAAjE,EAAA,EAAgC;;;;;IAsCnCwC,uDAAA,aAIK;;;;;IACLA,uDAAA,aAIK;;;;;IACLA,uDAAA,aAIK;;;;;IACLA,uDAAA,aAKK;;;;;IAxBPA,4DAAA,eAGC;IACCA,wDAAA,IAAAgF,8DAAA,iBAIK;IACLhF,wDAAA,IAAAiF,8DAAA,iBAIK;IACLjF,wDAAA,IAAAkF,8DAAA,iBAIK;IACLlF,wDAAA,IAAAmF,8DAAA,iBAKK;IACPnF,0DAAA,EAAM;;;;IAnBDA,uDAAA,GAAkC;IAAlCA,wDAAA,SAAAyB,WAAA,CAAA2D,MAAA,eAAkC;IAKlCpF,uDAAA,GAA+B;IAA/BA,wDAAA,SAAAyB,WAAA,CAAA2D,MAAA,YAA+B;IAK/BpF,uDAAA,GAAoC;IAApCA,wDAAA,SAAAyB,WAAA,CAAA2D,MAAA,iBAAoC;IAMpCpF,uDAAA,GAA+B;IAA/BA,wDAAA,SAAAyB,WAAA,CAAA2D,MAAA,YAA+B;;;;;;IA7R5CpF,qEAAA,GAMC;IAECA,wDAAA,IAAAsF,yDAAA,kBAgBM;IAGNtF,4DAAA,cAQC;IAFCA,wDAAA,mBAAAuF,yEAAArC,MAAA;MAAA,MAAAsC,WAAA,GAAAxF,2DAAA,CAAAyF,IAAA;MAAA,MAAAhE,WAAA,GAAA+D,WAAA,CAAA3D,SAAA;MAAA,MAAA6D,OAAA,GAAA1F,2DAAA;MAAA,OAASA,yDAAA,CAAA0F,OAAA,CAAAC,cAAA,CAAAlE,WAAA,EAAAyB,MAAA,CAA+B;IAAA,EAAC,yBAAA0C,+EAAA1C,MAAA;MAAA,MAAAsC,WAAA,GAAAxF,2DAAA,CAAAyF,IAAA;MAAA,MAAAhE,WAAA,GAAA+D,WAAA,CAAA3D,SAAA;MAAA,MAAAgE,OAAA,GAAA7F,2DAAA;MAAA,OAC1BA,yDAAA,CAAA6F,OAAA,CAAAC,oBAAA,CAAArE,WAAA,EAAAyB,MAAA,CAAqC;IAAA,EADX;IAIzClD,wDAAA,IAAA+F,yDAAA,kBAqBM;IAGN/F,4DAAA,cAiBC;IAECA,wDAAA,IAAAgG,yDAAA,kBAeM;IAGNhG,wDAAA,IAAAiG,yDAAA,kBAKM;IAGNjG,wDAAA,IAAAkG,yDAAA,kBA0BM;IAGNlG,wDAAA,IAAAmG,yDAAA,kBAiHM;IAGNnG,4DAAA,cAUC;IACOA,oDAAA,IAA0C;IAAAA,0DAAA,EAAO;IACvDA,wDAAA,KAAAoG,0DAAA,kBAyBM;IACRpG,0DAAA,EAAM;IAGZA,mEAAA,EAAe;;;;;;IA3RVA,uDAAA,GAAgC;IAAhCA,wDAAA,SAAAsG,OAAA,CAAAC,uBAAA,CAAAC,KAAA,EAAgC;IAoBjCxG,uDAAA,GAEC;IAFDA,yDAAA,qBAAAyB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,MAAA8I,OAAA,CAAA1D,aAAA,6BAEC;IACD5C,wDAAA,oBAAAyB,WAAA,CAAAjE,EAAA,CAA8B;IAM3BwC,uDAAA,GAAiE;IAAjEA,wDAAA,UAAAyB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,MAAA8I,OAAA,CAAA1D,aAAA,IAAA0D,OAAA,CAAAG,gBAAA,CAAAD,KAAA,EAAiE;IAwBlExG,uDAAA,GAEC;IAFDA,yDAAA,sBAAAyB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,MAAA8I,OAAA,CAAA1D,aAAA,yBAEC,WAAAnB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,MAAA8I,OAAA,CAAA1D,aAAA;IAiBE5C,uDAAA,GAKf;IALeA,wDAAA,SAAAsG,OAAA,CAAAI,mBAAA,OAAAjF,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,MAAA8I,OAAA,CAAA1D,aAAA,IAAA0D,OAAA,CAAAK,oBAAA,CAAAH,KAAA,EAKf;IAaexG,uDAAA,GAAwC;IAAxCA,wDAAA,SAAAsG,OAAA,CAAAM,cAAA,CAAAnF,WAAA,aAAwC;IAOrCzB,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAsG,OAAA,CAAAO,QAAA,CAAApF,WAAA,EAAuB;IA8B1BzB,uDAAA,GAAyC;IAAzCA,wDAAA,SAAAsG,OAAA,CAAAM,cAAA,CAAAnF,WAAA,cAAyC;IA8HpCzB,uDAAA,GAA0C;IAA1CA,+DAAA,CAAAsG,OAAA,CAAAS,iBAAA,CAAAtF,WAAA,CAAAC,SAAA,EAA0C;IAE7C1B,uDAAA,GAA0C;IAA1CA,wDAAA,UAAAyB,WAAA,CAAAO,MAAA,kBAAAP,WAAA,CAAAO,MAAA,CAAAxE,EAAA,MAAA8I,OAAA,CAAA1D,aAAA,CAA0C;;;;;IA+BrD5C,4DAAA,eAGC;IACCA,uDAAA,eASE;IACFA,4DAAA,eAOC;IAEGA,uDAAA,eAQO;IAmBTA,0DAAA,EAAM;;;;IA7CNA,uDAAA,GAAqE;IAArEA,wDAAA,SAAAgH,OAAA,CAAAxG,gBAAA,kBAAAwG,OAAA,CAAAxG,gBAAA,CAAA0B,KAAA,yCAAAlC,2DAAA,CAAqE,QAAAgH,OAAA,CAAAxG,gBAAA,kBAAAwG,OAAA,CAAAxG,gBAAA,CAAAc,QAAA;;;;;IAhT3EtB,4DAAA,cAGC;IACCA,wDAAA,IAAAiH,mDAAA,6BAoSe;IAGfjH,wDAAA,IAAAkH,0CAAA,kBAoDM;IACRlH,0DAAA,EAAM;;;;IA1VuBA,uDAAA,GACZ;IADYA,wDAAA,YAAAmH,OAAA,CAAAC,QAAA,CACZ,iBAAAD,OAAA,CAAAE,gBAAA;IAqSZrH,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAmH,OAAA,CAAAG,iBAAA,CAAuB;;;;;IA8ItBtH,uDAAA,eAYO;;;;;IA4DTA,uDAAA,aAA4D;;;;;IAC5DA,uDAAA,eAUO;;;;;;IAmCLA,4DAAA,kBAeC;IAbCA,wDAAA,mBAAAuH,sEAAA;MAAA,MAAA/B,WAAA,GAAAxF,2DAAA,CAAAwH,IAAA;MAAA,MAAAC,SAAA,GAAAjC,WAAA,CAAA3D,SAAA;MAAA,MAAA6F,OAAA,GAAA1H,2DAAA;MAAA,OAASA,yDAAA,CAAA0H,OAAA,CAAAC,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAc5BzH,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IAHPA,wDAAA,UAAAyH,SAAA,CAAAG,IAAA,CAAoB;IAEpB5H,uDAAA,GACF;IADEA,gEAAA,MAAAyH,SAAA,CAAAI,KAAA,MACF;;;;;IA/CN7H,4DAAA,eAeC;IAUKA,oDAAA,oBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAEC;IACCA,wDAAA,IAAA8H,6CAAA,sBAiBS;IACX9H,0DAAA,EAAM;;;;IAjBgBA,uDAAA,GAA8C;IAA9CA,wDAAA,YAAA+H,OAAA,CAAAC,oBAAA,CAAAD,OAAA,CAAAE,qBAAA,EAA8C;;;;;;IAsBxEjI,4DAAA,eAaC;IAUKA,oDAAA,4BACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAMC;IAGGA,wDAAA,mBAAAkI,6DAAA;MAAAlI,2DAAA,CAAAmI,IAAA;MAAA,MAAAC,OAAA,GAAApI,2DAAA;MAAA,OAASA,yDAAA,CAAAoI,OAAA,CAAAC,gBAAA,CAAiB,OAAO,CAAC;IAAA,EAAC;IAgBnCrI,4DAAA,eAUC;IACCA,uDAAA,aAGK;IACPA,0DAAA,EAAM;IACNA,4DAAA,gBACG;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EACR;IAIHA,4DAAA,mBAgBC;IAfCA,wDAAA,mBAAAsI,8DAAA;MAAAtI,2DAAA,CAAAmI,IAAA;MAAA,MAAAI,OAAA,GAAAvI,2DAAA;MAAA,OAASA,yDAAA,CAAAuI,OAAA,CAAAF,gBAAA,CAAiB,UAAU,CAAC;IAAA,EAAC;IAgBtCrI,4DAAA,gBAUC;IACCA,uDAAA,cAGK;IACPA,0DAAA,EAAM;IACNA,4DAAA,iBACG;IAAAA,oDAAA,iBAAS;IAAAA,0DAAA,EACX;IAIHA,4DAAA,mBAgBC;IAfCA,wDAAA,mBAAAwI,8DAAA;MAAAxI,2DAAA,CAAAmI,IAAA;MAAA,MAAAM,OAAA,GAAAzI,2DAAA;MAAA,OAASA,yDAAA,CAAAyI,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAgBtB1I,4DAAA,gBAUC;IACCA,uDAAA,cAGK;IACPA,0DAAA,EAAM;IACNA,4DAAA,iBACG;IAAAA,oDAAA,mBAAM;IAAAA,0DAAA,EACR;;;;;;IAkBXA,4DAAA,eAYC;IADCA,wDAAA,mBAAA2I,0DAAA;MAAA3I,2DAAA,CAAA4I,IAAA;MAAA,MAAAC,OAAA,GAAA7I,2DAAA;MAAA,OAASA,yDAAA,CAAA6I,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1B9I,0DAAA,EAAM;;;;;IAgDDA,uDAAA,eAWO;;;;;IAHLA,yDAAA,WAAA+I,QAAA,OAAwB,uDAAAC,KAAA;;;;;;IArDhChJ,4DAAA,eAmBC;IAcGA,uDAAA,aAAyD;IAC3DA,0DAAA,EAAM;IAGNA,4DAAA,eAAqB;IAGjBA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IAGNA,4DAAA,eAAwE;IACtEA,wDAAA,IAAAiJ,0CAAA,mBAWO;IACTjJ,0DAAA,EAAM;IAGNA,4DAAA,eAA4D;IAC1DA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IAIRA,4DAAA,eAAqC;IAGjCA,wDAAA,mBAAAkJ,8DAAAhG,MAAA;MAAAlD,2DAAA,CAAAmJ,IAAA;MAAA,MAAAC,OAAA,GAAApJ,2DAAA;MAAA,OAASA,yDAAA,CAAAoJ,OAAA,CAAAC,cAAA,CAAAnG,MAAA,CAAsB;IAAA,EAAC;IAkBhClD,uDAAA,cAAoD;IACtDA,0DAAA,EAAS;IAGTA,4DAAA,mBAkBC;IAjBCA,wDAAA,mBAAAsJ,8DAAApG,MAAA;MAAAlD,2DAAA,CAAAmJ,IAAA;MAAA,MAAAI,OAAA,GAAAvJ,2DAAA;MAAA,OAASA,yDAAA,CAAAuJ,OAAA,CAAAC,WAAA,CAAAtG,MAAA,CAAmB;IAAA,EAAC;IAkB7BlD,uDAAA,cAA0D;IAC5DA,0DAAA,EAAS;;;;IAvEPA,uDAAA,GACF;IADEA,gEAAA,MAAAyJ,OAAA,CAAAC,uBAAA,CAAAD,OAAA,CAAAE,sBAAA,OACF;IAKqB3J,uDAAA,GAAe;IAAfA,wDAAA,YAAAyJ,OAAA,CAAA3E,UAAA,CAAe;IAelC9E,uDAAA,GACF;IADEA,gEAAA,cAAAyJ,OAAA,CAAAG,kBAAA,QACF;;;AD30CA,MAAOC,oBAAoB;EA8I/BlN,YACUmN,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,GAAsB;IANtB,KAAAN,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IAvIb;IACA,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAjD,QAAQ,GAAU,EAAE;IACpB,KAAAxE,aAAa,GAAkB,IAAI;IACnC,KAAA0H,eAAe,GAAG,KAAK;IAEvB,KAAA9J,gBAAgB,GAAQ,IAAI;IAE5B;IACA,KAAA+J,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAA1D,iBAAiB,GAAG,KAAK;IACzB,KAAAlG,YAAY,GAAG,KAAK;IACpB,KAAA6J,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,mBAAmB,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,qBAAqB,GAAQ,IAAI;IAEjC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAlC,sBAAsB,GAAG,CAAC;IAC1B,KAAAmC,mBAAmB,GAAwC,MAAM;IACzD,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAQ,IAAI;IAClC,KAAAnH,UAAU,GAAa,CACrB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACzD;IAED;IACQ,KAAAoH,YAAY,GAA4B,IAAI;IAC5C,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,aAAa,GAOjB,EAAE;IAEN;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAA6B,IAAI;IACzC,KAAAC,YAAY,GAAG,CAAC;IACR,KAAAC,SAAS,GAAQ,IAAI;IAE7B;IACA;IAEA;IACA,KAAAC,eAAe,GAAU,CACvB;MACEjP,EAAE,EAAE,SAAS;MACboK,IAAI,EAAE,SAAS;MACfjJ,IAAI,EAAE,IAAI;MACV+N,MAAM,EAAE,CACN;QAAE7E,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAe,CAAE,EACtC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA6B,CAAE,EACpD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAiC,CAAE,EACxD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAyB,CAAE,EAChD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA0B,CAAE,EACjD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE,EAC/C;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAA+B,CAAE,EACtD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAgC,CAAE,EACvD;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAwB,CAAE;KAElD,EACD;MACEpK,EAAE,EAAE,QAAQ;MACZoK,IAAI,EAAE,QAAQ;MACdjJ,IAAI,EAAE,IAAI;MACV+N,MAAM,EAAE,CACN;QAAE7E,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAQ,CAAE,EAC/B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE,EAC9B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAW,CAAE;KAErC,EACD;MACEpK,EAAE,EAAE,QAAQ;MACZoK,IAAI,EAAE,QAAQ;MACdjJ,IAAI,EAAE,IAAI;MACV+N,MAAM,EAAE,CACN;QAAE7E,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAY,CAAE,EACnC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAS,CAAE,EAChC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAa,CAAE,EACpC;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAK,CAAE,EAC5B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAM,CAAE,EAC7B;QAAEC,KAAK,EAAE,IAAI;QAAED,IAAI,EAAE;MAAO,CAAE;KAEjC,CACF;IACD,KAAAK,qBAAqB,GAAG,IAAI,CAACwE,eAAe,CAAC,CAAC,CAAC;IAE/C;IACiB,KAAAE,oBAAoB,GAAG,EAAE,CAAC,CAAC;IACpC,KAAAC,WAAW,GAAG,CAAC;IAEvB;IACA,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,KAAK;IACZ,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAG,IAAInN,8CAAY,EAAE;IAWxC,IAAI,CAACoN,WAAW,GAAG,IAAI,CAACnD,EAAE,CAACoD,KAAK,CAAC;MAC/BzK,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7C,sDAAU,CAACuN,QAAQ,EAAEvN,sDAAU,CAACwN,SAAS,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,OACE,CAAC,IAAI,CAAC7M,gBAAgB,IAAI,IAAI,CAACqL,gBAAgB,IAAI,IAAI,CAACb,gBAAgB;EAE5E;EAEA;EACQsC,gBAAgBA,CAAA;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,IAAI,CAACH,eAAe,EAAE,EAAE;MAC1BE,cAAc,EAAEE,OAAO,EAAE;KAC1B,MAAM;MACLF,cAAc,EAAEG,MAAM,EAAE;;EAE5B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAACC,8BAA8B,EAAE;EACvC;EAEAC,eAAeA,CAAA;IACb;IACAjQ,UAAU,CAAC,MAAK;MACd,IAAI,CAACkQ,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;IAEP;IACAlQ,UAAU,CAAC,MAAK;MACd,IAAI,CAACkQ,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;IAEPlQ,UAAU,CAAC,MAAK;MACd,IAAI,CAACkQ,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;IAERlQ,UAAU,CAAC,MAAK;MACd,IAAI,CAACkQ,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQA,kBAAkBA,CAAA;IACxB;IACA,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,EAAE;MACvC,IAAI,CAAC/D,WAAW,CAACgE,gBAAgB,CAC/B,IAAI,CAACF,UAAU,CAACG,aAAa,EAC7B,IAAI,CAACF,WAAW,CAACE,aAAa,CAC/B;;IAEH;IAAA,KACK,IAAI,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxD,IAAI,CAACnE,WAAW,CAACgE,gBAAgB,CAC/B,IAAI,CAACE,gBAAgB,CAACD,aAAa,EACnC,IAAI,CAACE,iBAAiB,CAACF,aAAa,CACrC;KACF,MAAM;MACL,IAAI,CAACG,2BAA2B,EAAE;MAElC;MACAzQ,UAAU,CAAC,MAAK;QACd,IAAI,CAACkQ,kBAAkB,EAAE;MAC3B,CAAC,EAAE,GAAG,CAAC;MAEP;MACAlQ,UAAU,CAAC,MAAK;QACd,IAAI,CAACkQ,kBAAkB,EAAE;MAC3B,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;;;EAGQO,2BAA2BA,CAAA;IACjC;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAC1C,YAAY,CACO;IACrB,MAAMC,aAAa,GAAGF,QAAQ,CAACC,cAAc,CAC3C,aAAa,CACM;IAErB,IAAIF,YAAY,IAAIG,aAAa,EAAE;MACjCtP,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;MACD,IAAI,CAAC6K,WAAW,CAACgE,gBAAgB,CAACK,YAAY,EAAEG,aAAa,CAAC;KAC/D,MAAM;MACLtP,OAAO,CAACuP,IAAI,CAAC,yDAAyD,CAAC;MAEvE;MACA,MAAMX,UAAU,GAAGQ,QAAQ,CAACI,aAAa,CAAC,OAAO,CAAC;MAClDZ,UAAU,CAACxQ,EAAE,GAAG,YAAY;MAC5BwQ,UAAU,CAACa,QAAQ,GAAG,IAAI;MAC1Bb,UAAU,CAACc,KAAK,GAAG,IAAI;MACvBd,UAAU,CAACe,WAAW,GAAG,IAAI;MAC7Bf,UAAU,CAACgB,KAAK,CAACC,OAAO,GACtB,2EAA2E;MAE7E,MAAMhB,WAAW,GAAGO,QAAQ,CAACI,aAAa,CAAC,OAAO,CAAC;MACnDX,WAAW,CAACzQ,EAAE,GAAG,aAAa;MAC9ByQ,WAAW,CAACY,QAAQ,GAAG,IAAI;MAC3BZ,WAAW,CAACc,WAAW,GAAG,IAAI;MAC9Bd,WAAW,CAACe,KAAK,CAACC,OAAO,GACvB,2EAA2E;MAE7ET,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACnB,UAAU,CAAC;MACrCQ,QAAQ,CAACU,IAAI,CAACC,WAAW,CAAClB,WAAW,CAAC;MAEtC,IAAI,CAAC/D,WAAW,CAACgE,gBAAgB,CAACF,UAAU,EAAEC,WAAW,CAAC;;EAE9D;EAEQJ,8BAA8BA,CAAA;IACpC,MAAMuB,YAAY,GAAGA,CAAA,KAAK;MACxB,IAAI,CAAClF,WAAW,CAACkF,YAAY,EAAE;MAC/BZ,QAAQ,CAACa,mBAAmB,CAAC,OAAO,EAAED,YAAY,CAAC;MACnDZ,QAAQ,CAACa,mBAAmB,CAAC,SAAS,EAAED,YAAY,CAAC;MACrDZ,QAAQ,CAACa,mBAAmB,CAAC,YAAY,EAAED,YAAY,CAAC;IAC1D,CAAC;IAEDZ,QAAQ,CAACc,gBAAgB,CAAC,OAAO,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;IAChEf,QAAQ,CAACc,gBAAgB,CAAC,SAAS,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;IAClEf,QAAQ,CAACc,gBAAgB,CAAC,YAAY,EAAEF,YAAY,EAAE;MAAEG,IAAI,EAAE;IAAI,CAAE,CAAC;EACvE;EAEQ3B,mBAAmBA,CAAA;IACzB,IAAI,CAAC4B,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA;IACAtQ,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;EACrE;EAEQsQ,kBAAkBA,CAACC,YAA0B;IACnD;IACA;IACAxQ,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjCuQ,YAAY,CAACC,MAAM,CAACvO,QAAQ,CAC7B;IAED;IACA,IAAI,CAAC2I,cAAc,CAAC6F,IAAI,CAAC,UAAU,CAAC;IAEpC;IACA;IACA;EACF;;EAEQN,eAAeA,CAAA;IACrB,IAAI;MACF,MAAMO,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE/C,IAAI,CAACF,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtE3Q,OAAO,CAACR,KAAK,CAAC,gCAAgC,CAAC;QAC/C,IAAI,CAACgE,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC0H,eAAe,GAAG,KAAK;QAC5B;;MAGF,MAAM4F,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MAEnC;MACA,MAAMM,MAAM,GAAGH,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAAC1S,EAAE,IAAI0S,IAAI,CAACG,MAAM;MAEjD,IAAIA,MAAM,EAAE;QACV,IAAI,CAACzN,aAAa,GAAGyN,MAAM;QAC3B,IAAI,CAAC/F,eAAe,GAAG4F,IAAI,CAAC5O,QAAQ,IAAI4O,IAAI,CAACtI,IAAI,IAAI,KAAK;OAC3D,MAAM;QACLxI,OAAO,CAACR,KAAK,CAAC,0CAA0C,EAAEsR,IAAI,CAAC;QAC/D,IAAI,CAACtN,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC0H,eAAe,GAAG,KAAK;;KAE/B,CAAC,OAAO1L,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,IAAI,CAACgE,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC0H,eAAe,GAAG,KAAK;;EAEhC;EAEQmF,gBAAgBA,CAAA;IACtB,MAAMc,cAAc,GAAG,IAAI,CAACxG,KAAK,CAACyG,QAAQ,CAACC,QAAQ,CAACjD,GAAG,CAAC,IAAI,CAAC;IAE7D,IAAI,CAAC+C,cAAc,EAAE;MACnB,IAAI,CAACpG,YAAY,CAAC9L,SAAS,CAAC,6BAA6B,CAAC;MAC1D;;IAGF,IAAI,CAACkM,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACmG,oBAAoB,EAAE;IAE3B,IAAI,CAACzG,cAAc,CAAC0G,eAAe,CAACJ,cAAc,CAAC,CAACK,SAAS,CAAC;MAC5DhT,IAAI,EAAGyM,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACwG,mBAAmB,EAAE;QAC1B,IAAI,CAACC,YAAY,EAAE;QAEnB;QACA,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAACxG,SAAS,GAAG,KAAK;MACxB,CAAC;MACD3L,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CACzB,8CAA8C,CAC/C;QACD,IAAI,CAACkM,SAAS,GAAG,KAAK;QAEtB;QACA1M,UAAU,CAAC,MAAK;UACd,IAAI,CAAC4R,gBAAgB,EAAE;QACzB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EAEQoB,mBAAmBA,CAAA;IACzB,IACE,CAAC,IAAI,CAACxG,YAAY,EAAE2G,YAAY,IAChC,IAAI,CAAC3G,YAAY,CAAC2G,YAAY,CAACC,MAAM,KAAK,CAAC,EAC3C;MACA7R,OAAO,CAACuP,IAAI,CAAC,uCAAuC,CAAC;MACrD,IAAI,CAACnO,gBAAgB,GAAG,IAAI;MAC5B;;IAGF;IACA;IAEA,IAAI,IAAI,CAAC6J,YAAY,CAAC6G,OAAO,EAAE;MAC7B;MACA;MACA,IAAI,CAAC1Q,gBAAgB,GAAG,IAAI,CAAC6J,YAAY,CAAC2G,YAAY,CAACG,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC5T,EAAE,IAAI4T,CAAC,CAACd,GAAG;QACnC,OAAOgB,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAC1O,aAAa,CAAC;MAC7D,CAAC,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACpC,gBAAgB,GAAG,IAAI,CAAC6J,YAAY,CAAC2G,YAAY,CAACG,IAAI,CAAEC,CAAM,IAAI;QACrE,MAAMC,aAAa,GAAGD,CAAC,CAAC5T,EAAE,IAAI4T,CAAC,CAACd,GAAG;QACnClR,OAAO,CAACC,GAAG,CACT,2BAA2B,EAC3BgS,aAAa,EACb,uBAAuB,EACvB,IAAI,CAACzO,aAAa,CACnB;QACD,OAAO0O,MAAM,CAACD,aAAa,CAAC,KAAKC,MAAM,CAAC,IAAI,CAAC1O,aAAa,CAAC;MAC7D,CAAC,CAAC;;IAGJ;IACA,IAAI,CAAC,IAAI,CAACpC,gBAAgB,IAAI,IAAI,CAAC6J,YAAY,CAAC2G,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MACvE,IAAI,CAACzQ,gBAAgB,GAAG,IAAI,CAAC6J,YAAY,CAAC2G,YAAY,CAAC,CAAC,CAAC;MAEzD;MACA,IAAI,IAAI,CAAC3G,YAAY,CAAC2G,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMM,kBAAkB,GACtB,IAAI,CAAC/Q,gBAAgB,CAAChD,EAAE,IAAI,IAAI,CAACgD,gBAAgB,CAAC8P,GAAG;QACvD,IAAIgB,MAAM,CAACC,kBAAkB,CAAC,KAAKD,MAAM,CAAC,IAAI,CAAC1O,aAAa,CAAC,EAAE;UAC7DxD,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;UACD,IAAI,CAACmB,gBAAgB,GAAG,IAAI,CAAC6J,YAAY,CAAC2G,YAAY,CAAC,CAAC,CAAC;;;;IAK/D;IACA,IAAI,IAAI,CAACxQ,gBAAgB,EAAE;MACzB;MACApB,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACmB,gBAAgB,CAACc,QAAQ,CAC/B;MACDlC,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,IAAI,CAACmB,gBAAgB,CAACc,QAAQ,CAC/B;KACF,MAAM;MACLlC,OAAO,CAACR,KAAK,CAAC,uDAAuD,CAAC;MAEtE;;IAGF;IACA,IAAI,CAAC0O,gBAAgB,EAAE;EACzB;EAEQwD,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACzG,YAAY,EAAE7M,EAAE,EAAE;IAE5B;IACA,IAAI4J,QAAQ,GAAG,IAAI,CAACiD,YAAY,CAACjD,QAAQ,IAAI,EAAE;IAE/C;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACoK,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;MAC/C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAAC/P,SAAS,IAAI+P,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAAChQ,SAAS,IAAIgQ,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE;MAC5D,OAAOH,KAAK,GAAGI,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEF3S,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5C2S,KAAK,EAAE,IAAI,CAAC5K,QAAQ,CAAC6J,MAAM;MAC3BgB,KAAK,EAAE,IAAI,CAAC7K,QAAQ,CAAC,CAAC,CAAC,EAAE3E,OAAO;MAChCyP,IAAI,EAAE,IAAI,CAAC9K,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAAC6J,MAAM,GAAG,CAAC,CAAC,EAAExO;KAChD,CAAC;IAEF,IAAI,CAACgI,eAAe,GAAG,IAAI,CAACrD,QAAQ,CAAC6J,MAAM,KAAK,IAAI,CAACtE,oBAAoB;IACzE,IAAI,CAACpC,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC4H,cAAc,EAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC5H,aAAa,IAAI,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE7M,EAAE,EACvE;IAEF,IAAI,CAACgN,aAAa,GAAG,IAAI;IACzB,IAAI,CAACoC,WAAW,EAAE;IAElB;IACA,MAAMyF,MAAM,GAAG,IAAI,CAACjL,QAAQ,CAAC6J,MAAM;IAEnC,IAAI,CAAChH,cAAc,CAACqI,WAAW,CAC7B,IAAI,CAAC1P,aAAc;IAAE;IACrB,IAAI,CAACpC,gBAAgB,EAAEhD,EAAE,IAAI,IAAI,CAACgD,gBAAgB,EAAE8P,GAAI;IAAE;IAC1D,IAAI,CAACjG,YAAY,CAAC7M,EAAE,EACpB,IAAI,CAACoP,WAAW,EAChB,IAAI,CAACD,oBAAoB,CAC1B,CAACiE,SAAS,CAAC;MACVhT,IAAI,EAAG2U,WAAkB,IAAI;QAC3B,IAAIA,WAAW,IAAIA,WAAW,CAACtB,MAAM,GAAG,CAAC,EAAE;UACzC;UACA,IAAI,CAAC7J,QAAQ,GAAG,CAAC,GAAGmL,WAAW,CAACC,OAAO,EAAE,EAAE,GAAG,IAAI,CAACpL,QAAQ,CAAC;UAC5D,IAAI,CAACqD,eAAe,GAClB8H,WAAW,CAACtB,MAAM,KAAK,IAAI,CAACtE,oBAAoB;SACnD,MAAM;UACL,IAAI,CAAClC,eAAe,GAAG,KAAK;;QAE9B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC5B,CAAC;MACD5L,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,wCAAwC,CAAC;QACrE,IAAI,CAACmM,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACoC,WAAW,EAAE,CAAC,CAAC;MACtB;KACD,CAAC;EACJ;EAEA;;;EAGQ8D,oBAAoBA,CAAA;IAC1B,IAAI,CAAC1D,aAAa,CAACyF,WAAW,EAAE;IAChC,IAAI,CAACzF,aAAa,GAAG,IAAInN,8CAAY,EAAE;IAEvC;IACA,IAAI,IAAI,CAACuH,QAAQ,CAAC6J,MAAM,GAAG,GAAG,EAAE;MAC9B,IAAI,CAAC7J,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACsL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE9C;EAEA;;;EAGOC,kBAAkBA,CAAA;IACvB,IAAI,IAAI,CAACtI,YAAY,EAAE7M,EAAE,EAAE;MACzB;MACA,IAAI,CAAC4J,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACwF,WAAW,GAAG,CAAC;MACpB,IAAI,CAACnC,eAAe,GAAG,IAAI;MAE3B;MACA,IAAI,CAACgF,gBAAgB,EAAE;;EAE3B;EAEQsB,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC1G,YAAY,EAAE7M,EAAE,EAAE;MAC1B4B,OAAO,CAACuP,IAAI,CAAC,kDAAkD,CAAC;MAChE;;IAGFvP,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzD,IAAI,CAACgL,YAAY,CAAC7M,EAAE,CACrB;IAED;IAEA,IAAI,CAACwP,aAAa,CAAC4F,GAAG,CACpB,IAAI,CAAC3I,cAAc,CAAC4I,sBAAsB,CACxC,IAAI,CAACxI,YAAY,CAAC7M,EAAE,CACrB,CAACoT,SAAS,CAAC;MACVhT,IAAI,EAAGkV,UAAe,IAAI;QACxB;QACA,IAAI/S,kEAAW,CAACgT,UAAU,KAAK,KAAK,EAAE;UACpC3T,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyT,UAAU,CAACtV,EAAE,CAAC;;QAGxD;QACA,MAAMwV,aAAa,GAAG,IAAI,CAAC5L,QAAQ,CAAC6L,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAAC1V,EAAE,KAAKsV,UAAU,CAACtV,EAAE,CAClC;QACD,IAAI,CAACwV,aAAa,EAAE;UAClB;UACA,IAAI,CAAC5L,QAAQ,CAAC+L,IAAI,CAACL,UAAU,CAAC;UAC9B1T,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAC+H,QAAQ,CAAC6J,MAAM,CACrB;UAED;UACA,IAAI,CAAC7G,GAAG,CAACgJ,aAAa,EAAE;UAExB;UACAvV,UAAU,CAAC,MAAK;YACd,IAAI,CAACsU,cAAc,EAAE;UACvB,CAAC,EAAE,EAAE,CAAC;UAEN;UACA,MAAMkB,QAAQ,GAAGP,UAAU,CAAC9Q,MAAM,EAAExE,EAAE,IAAIsV,UAAU,CAACO,QAAQ;UAC7DjU,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;YAC9DgU,QAAQ;YACRzQ,aAAa,EAAE,IAAI,CAACA,aAAa;YACjC0Q,gBAAgB,EAAED,QAAQ,KAAK,IAAI,CAACzQ;WACrC,CAAC;UAEF,IAAIyQ,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACzQ,aAAa,EAAE;YAC/C,IAAI,CAAC2Q,iBAAiB,CAACT,UAAU,CAACtV,EAAE,CAAC;;;MAG3C,CAAC;MACDoB,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,kCAAkC,CAAC;QAE/D;QACAR,UAAU,CAAC,MAAK;UACd,IAAI,CAACkT,kBAAkB,EAAE;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC,CACH;IAED;IAEA,IAAI,CAAC/D,aAAa,CAAC4F,GAAG,CACpB,IAAI,CAAC3I,cAAc,CAACuJ,0BAA0B,CAC5C,IAAI,CAACnJ,YAAY,CAAC7M,EAAE,CACrB,CAACoT,SAAS,CAAC;MACVhT,IAAI,EAAG6V,UAAe,IAAI;QACxB;QACA,IAAIA,UAAU,CAACpD,MAAM,KAAK,IAAI,CAACzN,aAAa,EAAE;UAC5C,IAAI,CAAC0E,iBAAiB,GAAGmM,UAAU,CAAC5G,QAAQ;UAC5C,IAAI,CAACC,YAAY,GAAG2G,UAAU,CAAC5G,QAAQ,CAAC,CAAC;UACzCzN,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC,IAAI,CAACiI,iBAAiB,CACvB;UACD,IAAI,CAAC8C,GAAG,CAACgJ,aAAa,EAAE;;MAE5B,CAAC;MACDxU,KAAK,EAAGA,KAAU,IAAI;QACpBQ,OAAO,CAACR,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACoO,aAAa,CAAC4F,GAAG,CACpB,IAAI,CAAC3I,cAAc,CAACyJ,8BAA8B,CAChD,IAAI,CAACrJ,YAAY,CAAC7M,EAAE,CACrB,CAACoT,SAAS,CAAC;MACVhT,IAAI,EAAG+V,kBAAuB,IAAI;QAChC;QACA,IAAIA,kBAAkB,CAACnW,EAAE,KAAK,IAAI,CAAC6M,YAAY,CAAC7M,EAAE,EAAE;UAClD,IAAI,CAAC6M,YAAY,GAAG;YAAE,GAAG,IAAI,CAACA,YAAY;YAAE,GAAGsJ;UAAkB,CAAE;UACnE,IAAI,CAACvJ,GAAG,CAACgJ,aAAa,EAAE;;MAE5B,CAAC;MACDxU,KAAK,EAAGA,KAAU,IAAI;QACpBQ,OAAO,CAACR,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;KACD,CAAC,CACH;EACH;EAEQ2U,iBAAiBA,CAACK,SAAiB;IACzC,IAAI,CAAC3J,cAAc,CAACsJ,iBAAiB,CAACK,SAAS,CAAC,CAAChD,SAAS,CAAC;MACzDhT,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdgB,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA;EACAiV,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5G,WAAW,CAAC6G,KAAK,IAAI,CAAC,IAAI,CAACzJ,YAAY,EAAE7M,EAAE,EAAE;IAEvD,MAAMiF,OAAO,GAAG,IAAI,CAACwK,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAE7P,KAAK,EAAEoW,IAAI,EAAE;IAC9D,IAAI,CAACtR,OAAO,EAAE;IAEd,MAAMuR,UAAU,GAAG,IAAI,CAACxT,gBAAgB,EAAEhD,EAAE,IAAI,IAAI,CAACgD,gBAAgB,EAAE8P,GAAG;IAE1E,IAAI,CAAC0D,UAAU,EAAE;MACf,IAAI,CAAC7J,YAAY,CAAC9L,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,IAAI,CAAC2M,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACsC,gBAAgB,EAAE;IAEvBlO,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCoD,OAAO;MACPuR,UAAU;MACVzD,cAAc,EAAE,IAAI,CAAClG,YAAY,CAAC7M;KACnC,CAAC;IAEF,IAAI,CAACyM,cAAc,CAAC4J,WAAW,CAC7BG,UAAU,EACVvR,OAAO,EACPwR,SAAS,EACT,MAAa,EACb,IAAI,CAAC5J,YAAY,CAAC7M,EAAE,CACrB,CAACoT,SAAS,CAAC;MACVhT,IAAI,EAAGI,OAAY,IAAI;QACrB;QACA,MAAMgV,aAAa,GAAG,IAAI,CAAC5L,QAAQ,CAAC6L,IAAI,CACrCC,GAAG,IAAKA,GAAG,CAAC1V,EAAE,KAAKQ,OAAO,CAACR,EAAE,CAC/B;QACD,IAAI,CAACwV,aAAa,EAAE;UAClB,IAAI,CAAC5L,QAAQ,CAAC+L,IAAI,CAACnV,OAAO,CAAC;UAC3BoB,OAAO,CAACC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAAC+H,QAAQ,CAAC6J,MAAM,CACrB;;QAGH;QACA,IAAI,CAAChE,WAAW,CAACiH,KAAK,EAAE;QACxB,IAAI,CAAClJ,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACsC,gBAAgB,EAAE;QAEvB;QACA,IAAI,CAAClD,GAAG,CAACgJ,aAAa,EAAE;QACxBvV,UAAU,CAAC,MAAK;UACd,IAAI,CAACsU,cAAc,EAAE;QACvB,CAAC,EAAE,EAAE,CAAC;MACR,CAAC;MACDvT,KAAK,EAAGA,KAAU,IAAI;QACpBQ,OAAO,CAACR,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAAC2M,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACsC,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEA6E,cAAcA,CAAA;IACZtU,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACsW,iBAAiB,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAAChG,aAAa;QACpDiG,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACE,YAAY;;IAE5C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA5T,gBAAgBA,CAACC,UAAgC;IAC/C,IAAI,CAACA,UAAU,EAAE,OAAO,YAAY;IAEpC,MAAM4T,QAAQ,GAAGtX,IAAI,CAACuX,KAAK,CACzB,CAAC5C,IAAI,CAAC6C,GAAG,EAAE,GAAG,IAAI7C,IAAI,CAACjR,UAAU,CAAC,CAACmR,OAAO,EAAE,IAAI,KAAK,CACtD;IAED,IAAIyC,QAAQ,GAAG,CAAC,EAAE,OAAO,aAAa;IACtC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,UAAUA,QAAQ,MAAM;IAClD,IAAIA,QAAQ,GAAG,IAAI,EAAE,OAAO,UAAUtX,IAAI,CAACuX,KAAK,CAACD,QAAQ,GAAG,EAAE,CAAC,GAAG;IAClE,OAAO,UAAUtX,IAAI,CAACuX,KAAK,CAACD,QAAQ,GAAG,IAAI,CAAC,GAAG;EACjD;EAEA;EACAG,oBAAoBA,CAACd,SAAiB;IACpC,OACE,IAAI,CAACxH,aAAa,CAACwH,SAAS,CAAC,IAAI;MAC/Be,QAAQ,EAAE,CAAC;MACXlX,QAAQ,EAAE,CAAC;MACXmX,WAAW,EAAE,CAAC;MACdC,KAAK,EAAE;KACR;EAEL;EAEQC,oBAAoBA,CAC1BlB,SAAiB,EACjBmB,IAAkD;IAElD,IAAI,CAAC3I,aAAa,CAACwH,SAAS,CAAC,GAAG;MAC9B,GAAG,IAAI,CAACc,oBAAoB,CAACd,SAAS,CAAC;MACvC,GAAGmB;KACJ;EACH;EAEA;EAEA;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACxU,gBAAgB,EAAEhD,EAAE,EAAE;MAC9B,IAAI,CAAC2M,YAAY,CAAC9L,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,IAAI,CAAC4W,YAAY,CAACnV,2DAAQ,CAACoV,KAAK,CAAC;EACnC;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC3U,gBAAgB,EAAEhD,EAAE,EAAE;MAC9B,IAAI,CAAC2M,YAAY,CAAC9L,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF;IACA,IAAI,CAAC0P,kBAAkB,EAAE;IAEzB,IAAI,CAACkH,YAAY,CAACnV,2DAAQ,CAACsV,KAAK,CAAC;EACnC;EAEA;EAEA;EACA;EAEA;EAEAC,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOrY,IAAI,CAACsY,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOrY,IAAI,CAACsY,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAE,YAAYA,CAACxX,OAAY;IACvB,MAAMyX,cAAc,GAAGzX,OAAO,CAAC0X,WAAW,EAAEvE,IAAI,CAC7CwE,GAAQ,IAAK,CAACA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAIH,cAAc,EAAEI,GAAG,EAAE;MACvB,MAAMC,IAAI,GAAGtH,QAAQ,CAACI,aAAa,CAAC,GAAG,CAAC;MACxCkH,IAAI,CAACC,IAAI,GAAGN,cAAc,CAACI,GAAG;MAC9BC,IAAI,CAACE,QAAQ,GAAGP,cAAc,CAAC7N,IAAI,IAAI,MAAM;MAC7CkO,IAAI,CAACG,MAAM,GAAG,QAAQ;MACtBzH,QAAQ,CAACU,IAAI,CAACC,WAAW,CAAC2G,IAAI,CAAC;MAC/BA,IAAI,CAACI,KAAK,EAAE;MACZ1H,QAAQ,CAACU,IAAI,CAACiH,WAAW,CAACL,IAAI,CAAC;MAC/B,IAAI,CAAC3L,YAAY,CAAC/L,WAAW,CAAC,wBAAwB,CAAC;;EAE3D;EAEA;EAEA8C,YAAYA,CAAA;IACV,IAAI,CAAC6J,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,UAAU,GAAG,IAAI,CAACG,UAAU;EACnC;EAEAqL,cAAcA,CAAA;IACZ,IAAI,CAAChV,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAiV,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAACrM,MAAM,CACRsM,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAC3CC,IAAI,CAAC,MAAK,CAAE,CAAC,CAAC,CACdC,KAAK,CAAE5X,KAAK,IAAI;MACfQ,OAAO,CAACR,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C;MACA,IAAI,CAACoL,MAAM,CAACsM,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAACE,KAAK,CAAC,MAAK;QACnD;QACAC,MAAM,CAACC,QAAQ,CAACX,IAAI,GAAG,+BAA+B;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAEA;EAEAjN,aAAaA,CAAA;IACX,IAAI,CAAC4B,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACvJ,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC6J,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACK,kBAAkB,GAAG,KAAK;EACjC;EAEAxF,oBAAoBA,CAAC9H,OAAY,EAAE2Y,KAAiB;IAClDA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAC1L,eAAe,GAAGlN,OAAO;IAC9B,IAAI,CAACmN,mBAAmB,GAAG;MAAEC,CAAC,EAAEuL,KAAK,CAACE,OAAO;MAAExL,CAAC,EAAEsL,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAAC7L,sBAAsB,GAAG,IAAI;EACpC;EAEA8L,kBAAkBA,CAAC/Y,OAAY,EAAE2Y,KAAiB;IAChDA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACzL,qBAAqB,GAAGvN,OAAO;IACpC,IAAI,CAACmN,mBAAmB,GAAG;MAAEC,CAAC,EAAEuL,KAAK,CAACE,OAAO;MAAExL,CAAC,EAAEsL,KAAK,CAACG;IAAO,CAAE;IACjE,IAAI,CAACxL,kBAAkB,GAAG,IAAI;EAChC;EAEA2L,UAAUA,CAACpP,KAAa;IACtB,IAAI,IAAI,CAAC0D,qBAAqB,EAAE;MAC9B,IAAI,CAAC2L,cAAc,CAAC,IAAI,CAAC3L,qBAAqB,CAAC/N,EAAE,EAAEqK,KAAK,CAAC;;IAE3D,IAAI,CAACyD,kBAAkB,GAAG,KAAK;EACjC;EAEA4L,cAAcA,CAACtD,SAAiB,EAAE/L,KAAa;IAC7C,IAAI,CAAC+L,SAAS,IAAI,CAAC/L,KAAK,EAAE;MACxBzI,OAAO,CAACR,KAAK,CAAC,2CAA2C,CAAC;MAC1D;;IAGF;IACA,IAAI,CAACqL,cAAc,CAACkN,cAAc,CAACvD,SAAS,EAAE/L,KAAK,CAAC,CAAC+I,SAAS,CAAC;MAC7DhT,IAAI,EAAGwZ,MAAM,IAAI;QACf;QACA,MAAMC,YAAY,GAAG,IAAI,CAACjQ,QAAQ,CAACkQ,SAAS,CACzCpE,GAAG,IAAKA,GAAG,CAAC1V,EAAE,KAAKoW,SAAS,CAC9B;QACD,IAAIyD,YAAY,KAAK,CAAC,CAAC,EAAE;UACvB,IAAI,CAACjQ,QAAQ,CAACiQ,YAAY,CAAC,GAAGD,MAAM;UACpC,IAAI,CAAChN,GAAG,CAACgJ,aAAa,EAAE;;QAG1B,IAAI,CAACjJ,YAAY,CAAC/L,WAAW,CAAC,YAAYyJ,KAAK,UAAU,CAAC;MAC5D,CAAC;MACDjJ,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,uCAAuC,CAAC;MACtE;KACD,CAAC;EACJ;EAEAkZ,cAAcA,CAACC,QAAa,EAAEnH,MAAc;IAC1C,OAAOmH,QAAQ,CAACnH,MAAM,KAAKA,MAAM;EACnC;EAEAoH,cAAcA,CAACzZ,OAAY;IACzB,IAAI,CAAC8K,aAAa,EAAE;EACtB;EAEA4O,cAAcA,CAAC1Z,OAAY;IACzB,IAAI,CAAC8K,aAAa,EAAE;EACtB;EAEA6O,aAAaA,CAAC3Z,OAAY;IACxB,IAAI,CAACA,OAAO,CAACR,EAAE,EAAE;MACf4B,OAAO,CAACR,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF;IACA,MAAMuZ,SAAS,GACb5Z,OAAO,CAACgE,MAAM,EAAExE,EAAE,KAAK,IAAI,CAACoF,aAAa,IACzC5E,OAAO,CAACqV,QAAQ,KAAK,IAAI,CAACzQ,aAAa;IAEzC,IAAI,CAACgV,SAAS,EAAE;MACd,IAAI,CAACzN,YAAY,CAAC9L,SAAS,CACzB,mDAAmD,CACpD;MACD,IAAI,CAACyK,aAAa,EAAE;MACpB;;IAGF;IACA,IAAI,CAAC+O,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC/D,IAAI,CAAC/O,aAAa,EAAE;MACpB;;IAGF;IACA,IAAI,CAACmB,cAAc,CAAC0N,aAAa,CAAC3Z,OAAO,CAACR,EAAE,CAAC,CAACoT,SAAS,CAAC;MACtDhT,IAAI,EAAGwZ,MAAM,IAAI;QACf;QACA,IAAI,CAAChQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC5I,MAAM,CAAE0U,GAAG,IAAKA,GAAG,CAAC1V,EAAE,KAAKQ,OAAO,CAACR,EAAE,CAAC;QAEpE,IAAI,CAAC2M,YAAY,CAAC/L,WAAW,CAAC,kBAAkB,CAAC;QACjD,IAAI,CAACgM,GAAG,CAACgJ,aAAa,EAAE;MAC1B,CAAC;MACDxU,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,0CAA0C,CAAC;MACzE;KACD,CAAC;IAEF,IAAI,CAACyK,aAAa,EAAE;EACtB;EAEA;EAEAgP,iBAAiBA,CAAA;IACf,IAAI,CAACpN,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAqN,mBAAmBA,CAACC,QAAa;IAC/B,IAAI,CAAC/P,qBAAqB,GAAG+P,QAAQ;EACvC;EAEAhQ,oBAAoBA,CAACgQ,QAAa;IAChC,OAAOA,QAAQ,EAAEtL,MAAM,IAAI,EAAE;EAC/B;EAEA/E,WAAWA,CAACE,KAAU;IACpB,MAAMoQ,cAAc,GAAG,IAAI,CAAChL,WAAW,CAACO,GAAG,CAAC,SAAS,CAAC,EAAE7P,KAAK,IAAI,EAAE;IACnE,MAAMua,UAAU,GAAGD,cAAc,GAAGpQ,KAAK,CAACA,KAAK;IAC/C,IAAI,CAACoF,WAAW,CAACkL,UAAU,CAAC;MAAE1V,OAAO,EAAEyV;IAAU,CAAE,CAAC;IACpD,IAAI,CAACxN,eAAe,GAAG,KAAK;EAC9B;EAEA0N,oBAAoBA,CAAA;IAClB,IAAI,CAACzN,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EAEAtD,gBAAgBA,CAACgR,KAAa,EAAEra,OAAY;IAC1C,OAAOA,OAAO,CAACR,EAAE,IAAIQ,OAAO,CAACsS,GAAG,IAAI+H,KAAK,CAAClb,QAAQ,EAAE;EACtD;EAEA;EAEAmb,cAAcA,CAAA;IACZ,MAAMC,WAAW,GAAG;MAClB/a,EAAE,EAAE,QAAQoU,IAAI,CAAC6C,GAAG,EAAE,EAAE;MACxBhS,OAAO,EAAE,mBAAmB,IAAImP,IAAI,EAAE,CAAC4G,kBAAkB,EAAE,EAAE;MAC7D9W,SAAS,EAAE,IAAIkQ,IAAI,EAAE,CAAC6G,WAAW,EAAE;MACnCzW,MAAM,EAAE;QACNxE,EAAE,EAAE,IAAI,CAACgD,gBAAgB,EAAEhD,EAAE,IAAI,WAAW;QAC5C8D,QAAQ,EAAE,IAAI,CAACd,gBAAgB,EAAEc,QAAQ,IAAI,WAAW;QACxDY,KAAK,EACH,IAAI,CAAC1B,gBAAgB,EAAE0B,KAAK,IAAI;OACnC;MACDjE,IAAI,EAAE,MAAM;MACZya,MAAM,EAAE;KACT;IACD,IAAI,CAACtR,QAAQ,CAAC+L,IAAI,CAACoF,WAAW,CAAC;IAC/B,IAAI,CAACnO,GAAG,CAACgJ,aAAa,EAAE;IACxBvV,UAAU,CAAC,MAAM,IAAI,CAACsU,cAAc,EAAE,EAAE,EAAE,CAAC;EAC7C;EAEAzL,mBAAmBA,CAAA;IACjB,OACE,IAAI,CAAC2D,YAAY,EAAE6G,OAAO,IAC1B,IAAI,CAAC7G,YAAY,EAAE2G,YAAY,EAAEC,MAAM,GAAG,CAAC,IAC3C,KAAK;EAET;EAEAvI,UAAUA,CAAA;IACR,IAAI,CAACiC,kBAAkB,GAAG,KAAK;IAC/B;EACF;;EAEAgO,SAASA,CAACC,MAAc;IACtB,MAAMC,YAAY,GAAGrK,QAAQ,CAACsK,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGF,YAAY,CAAC7J,KAAK,CAACgK,SAAS,IAAI,UAAU;MACnE,MAAMC,YAAY,GAAGC,UAAU,CAC7BH,gBAAgB,CAACI,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CACvD;MACD,MAAMC,QAAQ,GAAGnc,IAAI,CAACoc,GAAG,CAAC,GAAG,EAAEpc,IAAI,CAACqc,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGL,MAAM,CAAC,CAAC;MAClEC,YAAY,CAAC7J,KAAK,CAACgK,SAAS,GAAG,SAASI,QAAQ,GAAG;MACnD,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChBP,YAAY,CAACU,SAAS,CAAC3G,GAAG,CAAC,QAAQ,CAAC;OACrC,MAAM;QACLiG,YAAY,CAACU,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;;EAG7C;EAEAC,SAASA,CAAA;IACP,MAAMZ,YAAY,GAAGrK,QAAQ,CAACsK,aAAa,CACzC,oBAAoB,CACN;IAChB,IAAID,YAAY,EAAE;MAChBA,YAAY,CAAC7J,KAAK,CAACgK,SAAS,GAAG,UAAU;MACzCH,YAAY,CAACU,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;;EAE3C;EAEA;EACA;EACA;EACA;EAEAnR,gBAAgBA,CAACpK,IAAa;IAC5B,MAAMyb,KAAK,GAAG,IAAI,CAACC,SAAS,EAAExL,aAAa;IAC3C,IAAI,CAACuL,KAAK,EAAE;MACVta,OAAO,CAACR,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAIX,IAAI,KAAK,OAAO,EAAE;MACpByb,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI3b,IAAI,KAAK,OAAO,EAAE;MAC3Byb,KAAK,CAACE,MAAM,GAAG,SAAS;KACzB,MAAM,IAAI3b,IAAI,KAAK,UAAU,EAAE;MAC9Byb,KAAK,CAACE,MAAM,GAAG,iCAAiC;KACjD,MAAM;MACLF,KAAK,CAACE,MAAM,GAAG,KAAK;;IAGtB;IACAF,KAAK,CAAC/b,KAAK,GAAG,EAAE;IAEhB;IACA+b,KAAK,CAACxD,KAAK,EAAE;IACb,IAAI,CAACvL,kBAAkB,GAAG,KAAK;EACjC;EAEA5D,iBAAiBA,CAACrF,SAAwB;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMmY,IAAI,GAAG,IAAIjI,IAAI,CAAClQ,SAAS,CAAC;IAChC,OAAOmY,IAAI,CAACrB,kBAAkB,CAAC,OAAO,EAAE;MACtCsB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAvY,mBAAmBA,CAACE,SAAwB;IAC1C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMmY,IAAI,GAAG,IAAIjI,IAAI,CAAClQ,SAAS,CAAC;IAChC,MAAMsY,KAAK,GAAG,IAAIpI,IAAI,EAAE;IACxB,MAAMqI,SAAS,GAAG,IAAIrI,IAAI,CAACoI,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAIN,IAAI,CAACO,YAAY,EAAE,KAAKJ,KAAK,CAACI,YAAY,EAAE,EAAE;MAChD,OAAO,aAAa;KACrB,MAAM,IAAIP,IAAI,CAACO,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MAC3D,OAAO,MAAM;KACd,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;EAEA7X,oBAAoBA,CAACC,OAAe;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB;IACA,MAAM6X,QAAQ,GAAG,sBAAsB;IACvC,OAAO7X,OAAO,CAAC8X,OAAO,CACpBD,QAAQ,EACR,qEAAqE,CACtE;EACH;EAEA/T,uBAAuBA,CAAC8R,KAAa;IACnC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,MAAMmC,cAAc,GAAG,IAAI,CAACpT,QAAQ,CAACiR,KAAK,CAAC;IAC3C,MAAMoC,eAAe,GAAG,IAAI,CAACrT,QAAQ,CAACiR,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACmC,cAAc,EAAE9Y,SAAS,IAAI,CAAC+Y,eAAe,EAAE/Y,SAAS,EAAE,OAAO,KAAK;IAE3E,MAAMgZ,WAAW,GAAG,IAAI9I,IAAI,CAAC4I,cAAc,CAAC9Y,SAAS,CAAC,CAAC0Y,YAAY,EAAE;IACrE,MAAMO,YAAY,GAAG,IAAI/I,IAAI,CAAC6I,eAAe,CAAC/Y,SAAS,CAAC,CAAC0Y,YAAY,EAAE;IAEvE,OAAOM,WAAW,KAAKC,YAAY;EACrC;EAEAlU,gBAAgBA,CAAC4R,KAAa;IAC5B,MAAMmC,cAAc,GAAG,IAAI,CAACpT,QAAQ,CAACiR,KAAK,CAAC;IAC3C,MAAMuC,WAAW,GAAG,IAAI,CAACxT,QAAQ,CAACiR,KAAK,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACuC,WAAW,EAAE,OAAO,IAAI;IAE7B,OAAOJ,cAAc,CAACxY,MAAM,EAAExE,EAAE,KAAKod,WAAW,CAAC5Y,MAAM,EAAExE,EAAE;EAC7D;EAEAmJ,oBAAoBA,CAAC0R,KAAa;IAChC,MAAMmC,cAAc,GAAG,IAAI,CAACpT,QAAQ,CAACiR,KAAK,CAAC;IAC3C,MAAMoC,eAAe,GAAG,IAAI,CAACrT,QAAQ,CAACiR,KAAK,GAAG,CAAC,CAAC;IAEhD,IAAI,CAACoC,eAAe,EAAE,OAAO,IAAI;IAEjC,OAAOD,cAAc,CAACxY,MAAM,EAAExE,EAAE,KAAKid,eAAe,CAACzY,MAAM,EAAExE,EAAE;EACjE;EAEAoJ,cAAcA,CAAC5I,OAAY;IACzB;IACA,IAAIA,OAAO,CAACC,IAAI,EAAE;MAChB,IAAID,OAAO,CAACC,IAAI,KAAK,OAAO,IAAID,OAAO,CAACC,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAID,OAAO,CAACC,IAAI,KAAK,OAAO,IAAID,OAAO,CAACC,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAID,OAAO,CAACC,IAAI,KAAK,OAAO,IAAID,OAAO,CAACC,IAAI,KAAK,OAAO,EAAE,OAAO,OAAO;MACxE,IAAID,OAAO,CAACC,IAAI,KAAK,eAAe,EAAE,OAAO,OAAO;MACpD,IAAID,OAAO,CAACC,IAAI,KAAK,MAAM,IAAID,OAAO,CAACC,IAAI,KAAK,MAAM,EAAE,OAAO,MAAM;;IAGvE;IACA,IAAID,OAAO,CAAC0X,WAAW,IAAI1X,OAAO,CAAC0X,WAAW,CAACzE,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM4J,UAAU,GAAG7c,OAAO,CAAC0X,WAAW,CAAC,CAAC,CAAC;MACzC,IAAImF,UAAU,CAAC5c,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIiF,UAAU,CAAC5c,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,IAAIiF,UAAU,CAAC5c,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;MACzD,OAAO,MAAM;;IAGf;IACA,IAAI5X,OAAO,CAAC8c,QAAQ,IAAI9c,OAAO,CAAC+c,QAAQ,IAAI/c,OAAO,CAACgd,KAAK,EAAE,OAAO,OAAO;IAEzE,OAAO,MAAM;EACf;EAEAnU,QAAQA,CAAC7I,OAAY;IACnB;IACA,IAAIA,OAAO,CAACC,IAAI,KAAK,OAAO,IAAID,OAAO,CAACC,IAAI,KAAK,OAAO,EAAE;MACxD,OAAO,IAAI;;IAGb;IACA,MAAMgd,kBAAkB,GACtBjd,OAAO,CAAC0X,WAAW,EAAEzC,IAAI,CAAE0C,GAAQ,IAAI;MACrC,OAAOA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,IAAID,GAAG,CAAC1X,IAAI,KAAK,OAAO;IAC/D,CAAC,CAAC,IAAI,KAAK;IAEb;IACA,MAAMid,WAAW,GAAG,CAAC,EAAEld,OAAO,CAACmd,QAAQ,IAAInd,OAAO,CAACkE,KAAK,CAAC;IAEzD,OAAO+Y,kBAAkB,IAAIC,WAAW;EAC1C;EAEAE,OAAOA,CAACpd,OAAY;IAClB;IACA,IAAIA,OAAO,CAACC,IAAI,KAAK,MAAM,IAAID,OAAO,CAACC,IAAI,KAAK,MAAM,EAAE;MACtD,OAAO,IAAI;;IAGb;IACA,MAAMod,iBAAiB,GACrBrd,OAAO,CAAC0X,WAAW,EAAEzC,IAAI,CAAE0C,GAAQ,IAAI;MACrC,OAAO,CAACA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,IAAID,GAAG,CAAC1X,IAAI,KAAK,OAAO;IAChE,CAAC,CAAC,IAAI,KAAK;IAEb,OAAOod,iBAAiB;EAC1B;EAEA1X,WAAWA,CAAC3F,OAAY;IACtB;IACA,IAAIA,OAAO,CAACmd,QAAQ,EAAE;MACpB,OAAOnd,OAAO,CAACmd,QAAQ;;IAEzB,IAAInd,OAAO,CAACkE,KAAK,EAAE;MACjB,OAAOlE,OAAO,CAACkE,KAAK;;IAGtB;IACA,MAAMoZ,eAAe,GAAGtd,OAAO,CAAC0X,WAAW,EAAEvE,IAAI,CAC9CwE,GAAQ,IAAKA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,IAAID,GAAG,CAAC1X,IAAI,KAAK,OAAO,CACrE;IAED,IAAIqd,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACzF,GAAG,IAAIyF,eAAe,CAACC,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAC,WAAWA,CAACxd,OAAY;IACtB,MAAMyX,cAAc,GAAGzX,OAAO,CAAC0X,WAAW,EAAEvE,IAAI,CAC7CwE,GAAQ,IAAK,CAACA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,OAAOH,cAAc,EAAE7N,IAAI,IAAI,SAAS;EAC1C;EAEA6T,WAAWA,CAACzd,OAAY;IACtB,MAAMyX,cAAc,GAAGzX,OAAO,CAAC0X,WAAW,EAAEvE,IAAI,CAC7CwE,GAAQ,IAAK,CAACA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACH,cAAc,EAAEiG,IAAI,EAAE,OAAO,EAAE;IAEpC,MAAMpG,KAAK,GAAGG,cAAc,CAACiG,IAAI;IACjC,IAAIpG,KAAK,GAAG,IAAI,EAAE,OAAOA,KAAK,GAAG,IAAI;IACrC,IAAIA,KAAK,GAAG,OAAO,EAAE,OAAOrY,IAAI,CAACsY,KAAK,CAACD,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK;IAC5D,OAAOrY,IAAI,CAACsY,KAAK,CAACD,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK;EAC5C;EAEAqG,WAAWA,CAAC3d,OAAY;IACtB,MAAMyX,cAAc,GAAGzX,OAAO,CAAC0X,WAAW,EAAEvE,IAAI,CAC7CwE,GAAQ,IAAK,CAACA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,CAC9C;IACD,IAAI,CAACH,cAAc,EAAExX,IAAI,EAAE,OAAO,aAAa;IAE/C,IAAIwX,cAAc,CAACxX,IAAI,CAAC2X,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAIH,cAAc,CAACxX,IAAI,CAAC2X,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACxE,IAAIH,cAAc,CAACxX,IAAI,CAAC2d,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IACjE,IAAInG,cAAc,CAACxX,IAAI,CAAC2d,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,kBAAkB;IACnE,IAAInG,cAAc,CAACxX,IAAI,CAAC2d,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IACrE,OAAO,aAAa;EACtB;EAEAtZ,YAAYA,CAAC+N,MAAc;IACzB;IACA,MAAMwL,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;IACD,MAAMxD,KAAK,GAAGhI,MAAM,CAACyL,UAAU,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC5K,MAAM;IAClD,OAAO4K,MAAM,CAACxD,KAAK,CAAC;EACtB;EAEA;EACA1S,cAAcA,CAAC3H,OAAY,EAAE2Y,KAAU,GAAS;EAEhDoF,aAAaA,CAACpF,KAAU;IACtB;IACA,IAAI,CAACqF,qBAAqB,EAAE;EAC9B;EAEAC,cAAcA,CAACtF,KAAoB;IACjC,IAAIA,KAAK,CAACuF,GAAG,KAAK,OAAO,IAAI,CAACvF,KAAK,CAACwF,QAAQ,EAAE;MAC5CxF,KAAK,CAACC,cAAc,EAAE;MACtB,IAAI,CAAC/C,WAAW,EAAE;;EAEtB;EAEAuI,YAAYA,CAAA;IACV;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFC,QAAQA,CAAC3F,KAAU;IACjB;IACA,MAAMvC,OAAO,GAAGuC,KAAK,CAACV,MAAM;IAC5B,IACE7B,OAAO,CAACC,SAAS,KAAK,CAAC,IACvB,IAAI,CAAC5J,eAAe,IACpB,CAAC,IAAI,CAACD,aAAa,EACnB;MACA,IAAI,CAAC4H,gBAAgB,EAAE;;EAE3B;EAEArQ,eAAeA,CAACsO,MAAc,GAAS;EAEvCjN,WAAWA,CAACuT,KAAU,EAAE3Y,OAAY;IAClCoB,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpDrB,OAAO,CAACR,EAAE,EACVmZ,KAAK,CAACV,MAAM,CAACsG,GAAG,CACjB;EACH;EAEAhZ,YAAYA,CAACoT,KAAU,EAAE3Y,OAAY;IACnCoB,OAAO,CAACR,KAAK,CAAC,+CAA+C,EAAEZ,OAAO,CAACR,EAAE,EAAE;MACzE+e,GAAG,EAAE5F,KAAK,CAACV,MAAM,CAACsG,GAAG;MACrB3d,KAAK,EAAE+X;KACR,CAAC;IACF;IACAA,KAAK,CAACV,MAAM,CAACsG,GAAG,GACd,4WAA4W;EAChX;EAEAvZ,eAAeA,CAAChF,OAAY;IAC1B,MAAMsd,eAAe,GAAGtd,OAAO,CAAC0X,WAAW,EAAEvE,IAAI,CAAEwE,GAAQ,IACzDA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,CAC/B;IACD,IAAI0F,eAAe,EAAEzF,GAAG,EAAE;MACxB,IAAI,CAACpK,aAAa,GAAG;QACnBoK,GAAG,EAAEyF,eAAe,CAACzF,GAAG;QACxBjO,IAAI,EAAE0T,eAAe,CAAC1T,IAAI,IAAI,OAAO;QACrC8T,IAAI,EAAE,IAAI,CAACrG,cAAc,CAACiG,eAAe,CAACI,IAAI,IAAI,CAAC,CAAC;QACpD1d,OAAO,EAAEA;OACV;MACD,IAAI,CAACwN,eAAe,GAAG,IAAI;;EAE/B;EAEAgR,gBAAgBA,CAAA;IACd,IAAI,CAAChR,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;EAC3B;EAEAgR,aAAaA,CAAA;IACX,IAAI,IAAI,CAAChR,aAAa,EAAEoK,GAAG,EAAE;MAC3B,MAAMC,IAAI,GAAGtH,QAAQ,CAACI,aAAa,CAAC,GAAG,CAAC;MACxCkH,IAAI,CAACC,IAAI,GAAG,IAAI,CAACtK,aAAa,CAACoK,GAAG;MAClCC,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACvK,aAAa,CAAC7D,IAAI,IAAI,OAAO;MAClDkO,IAAI,CAACG,MAAM,GAAG,QAAQ;MACtBzH,QAAQ,CAACU,IAAI,CAACC,WAAW,CAAC2G,IAAI,CAAC;MAC/BA,IAAI,CAACI,KAAK,EAAE;MACZ1H,QAAQ,CAACU,IAAI,CAACiH,WAAW,CAACL,IAAI,CAAC;MAC/B,IAAI,CAAC3L,YAAY,CAAC/L,WAAW,CAAC,wBAAwB,CAAC;MACvDgB,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACoM,aAAa,CAAC7D,IAAI,CACxB;;EAEL;EAEA;EACA;EACA;EAEA8U,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC7R,WAAW,CAACkJ,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACjJ,aAAa,GAAG,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC1D,QAAQ,CAAC5I,MAAM,CACtCR,OAAO,IACNA,OAAO,CAACyE,OAAO,EACXka,WAAW,EAAE,CACdf,QAAQ,CAAC,IAAI,CAAC/Q,WAAW,CAAC8R,WAAW,EAAE,CAAC,IAC3C3e,OAAO,CAACgE,MAAM,EAAEV,QAAQ,EACpBqb,WAAW,EAAE,CACdf,QAAQ,CAAC,IAAI,CAAC/Q,WAAW,CAAC8R,WAAW,EAAE,CAAC,CAC9C;EACH;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACF,cAAc,EAAE;EACvB;EAEAG,WAAWA,CAAA;IACT,IAAI,CAAChS,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;EACzB;EAEAgS,aAAaA,CAAClJ,SAAiB;IAC7B,MAAMmJ,cAAc,GAAGvO,QAAQ,CAACC,cAAc,CAAC,WAAWmF,SAAS,EAAE,CAAC;IACtE,IAAImJ,cAAc,EAAE;MAClBA,cAAc,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAQ,CAAE,CAAC;MACtE;MACAH,cAAc,CAACxD,SAAS,CAAC3G,GAAG,CAAC,WAAW,CAAC;MACzC/U,UAAU,CAAC,MAAK;QACdkf,cAAc,CAACxD,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEA;EACA;EACA;EAEA2D,gBAAgBA,CAAA;IACd,IAAI,CAAClS,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEQ+J,YAAYA,CAAC3I,QAAkB;IACrClN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CiN,QAAQ;MACR9L,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvC6J,YAAY,EAAE,IAAI,CAACA,YAAY,EAAE7M,EAAE;MACnCoF,aAAa,EAAE,IAAI,CAACA;KACrB,CAAC;IAEF,IAAI,CAAC,IAAI,CAACpC,gBAAgB,EAAE;MAC1BpB,OAAO,CAACR,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGF,MAAM+e,WAAW,GAAG,IAAI,CAAC5c,gBAAgB,CAAChD,EAAE,IAAI,IAAI,CAACgD,gBAAgB,CAAC8P,GAAG;IACzE,IAAI,CAAC8M,WAAW,EAAE;MAChBhe,OAAO,CAACR,KAAK,CAAC,wCAAwC,CAAC;MACvD,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,gCAAgC,CAAC;MAC7D;;IAGFe,OAAO,CAACC,GAAG,CAAC,+BAA+BiN,QAAQ,gBAAgB,EAAE;MACnE8Q,WAAW;MACXC,aAAa,EACX,IAAI,CAAC7c,gBAAgB,CAACc,QAAQ,IAAI,IAAI,CAACd,gBAAgB,CAACoH,IAAI;MAC9D2I,cAAc,EAAE,IAAI,CAAClG,YAAY,EAAE7M;KACpC,CAAC;IAEF,IAAI,CAAC6O,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,KAAKxM,2DAAQ,CAACoV,KAAK,GAAG,OAAO,GAAG,OAAO;IAC/D,IAAI,CAAC3I,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,CAAC+Q,cAAc,EAAE;IAErB;IACA,IAAI,CAACpT,WAAW,CACb+K,YAAY,CAACmI,WAAW,EAAE9Q,QAAQ,EAAE,IAAI,CAACjC,YAAY,EAAE7M,EAAE,CAAC,CAC1DoT,SAAS,CAAC;MACThT,IAAI,EAAG2f,IAAU,IAAI;QACnB;QACA,IAAI,CAACpT,YAAY,CAAC/L,WAAW,CAC3B,SAASkO,QAAQ,KAAKxM,2DAAQ,CAACoV,KAAK,GAAG,OAAO,GAAG,OAAO,SAAS,CAClE;QAED9V,OAAO,CAACC,GAAG,CACT,qEAAqE,CACtE;MACH,CAAC;MACDT,KAAK,EAAGA,KAAU,IAAI;QACpBQ,OAAO,CAACR,KAAK,CAAC,wCAAwC,EAAE;UACtDA,KAAK,EAAEA,KAAK,CAACZ,OAAO,IAAIY,KAAK;UAC7Bwe,WAAW;UACX9Q,QAAQ;UACRiE,cAAc,EAAE,IAAI,CAAClG,YAAY,EAAE7M;SACpC,CAAC;QAEF;QACA,IAAI,CAAC2M,YAAY,CAAC9L,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;EAEAmf,UAAUA,CAAC5N,YAA0B;IACnC;IACAxQ,OAAO,CAACC,GAAG,CACT,mEAAmE,CACpE;EACH;EAEAoe,UAAUA,CAAC7N,YAA0B;IACnC,IAAI,CAAC1F,WAAW,CAACuT,UAAU,CAAC7N,YAAY,CAACpS,EAAE,EAAE,eAAe,CAAC,CAACoT,SAAS,CAAC;MACtEhT,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACuM,YAAY,CAAC/L,WAAW,CAAC,cAAc,CAAC;MAC/C,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;EACJ;EAEQif,cAAcA,CAAA;IACpB,IAAI,CAAC/Q,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAGkR,WAAW,CAAC,MAAK;MAChC,IAAI,CAACnR,YAAY,EAAE;MACnB,IAAI,CAACnC,GAAG,CAACgJ,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAEQuK,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACnR,SAAS,EAAE;MAClBoR,aAAa,CAAC,IAAI,CAACpR,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB;EACF;EAEA;EACA;EACAsR,UAAUA,CAAA;IACRze,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;EAC5E;EAEAye,WAAWA,CAAA;IACT1e,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;EAC7E;EAEA;;;EAGA0e,OAAOA,CAAA;IACL3e,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAACgN,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,IAAI,CAACC,SAAS,EAAE;MAClBoR,aAAa,CAAC,IAAI,CAACpR,SAAS,CAAC;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;;IAGvB;IACA;IAEA,IAAI,CAACrC,YAAY,CAAC/L,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA4f,kBAAkBA,CAACvgB,QAAgB;IACjC,MAAMwgB,KAAK,GAAGhhB,IAAI,CAACuX,KAAK,CAAC/W,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAMygB,OAAO,GAAGjhB,IAAI,CAACuX,KAAK,CAAE/W,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,MAAM0gB,OAAO,GAAG1gB,QAAQ,GAAG,EAAE;IAE7B,IAAIwgB,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAAC/gB,QAAQ,EAAE,CAACihB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAID,OAAO,CAC9DhhB,QAAQ,EAAE,CACVihB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEvB,OAAO,GAAGF,OAAO,IAAIC,OAAO,CAAChhB,QAAQ,EAAE,CAACihB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EACA;EAEMC,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,kKAAA;MACvB,IAAI;QACF;QACA,IAAI,CAACC,SAAS,CAACC,YAAY,IAAI,CAACD,SAAS,CAACC,YAAY,CAACC,YAAY,EAAE;UACnE,MAAM,IAAIC,KAAK,CACb,yDAAyD,CAC1D;;QAGH;QACA,IAAI,CAAClI,MAAM,CAACmI,aAAa,EAAE;UACzB,MAAM,IAAID,KAAK,CACb,uDAAuD,CACxD;;QAGH;QACA,MAAME,MAAM,SAASL,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UACvDI,KAAK,EAAE;YACLC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;;SAEjB,CAAC;QAEF;QACA,IAAIC,QAAQ,GAAG,wBAAwB;QACvC,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;UAC5CA,QAAQ,GAAG,YAAY;UACvB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;YAC5CA,QAAQ,GAAG,WAAW;YACtB,IAAI,CAACR,aAAa,CAACS,eAAe,CAACD,QAAQ,CAAC,EAAE;cAC5CA,QAAQ,GAAG,EAAE,CAAC,CAAC;;;;QAKrB;QACAd,KAAI,CAACvS,aAAa,GAAG,IAAI6S,aAAa,CAACC,MAAM,EAAE;UAC7CO,QAAQ,EAAEA,QAAQ,IAAInL;SACvB,CAAC;QAEF;QACAqK,KAAI,CAACtS,WAAW,GAAG,EAAE;QACrBsS,KAAI,CAACzS,gBAAgB,GAAG,IAAI;QAC5ByS,KAAI,CAAC3U,sBAAsB,GAAG,CAAC;QAC/B2U,KAAI,CAACxS,mBAAmB,GAAG,WAAW;QAEtC;QACAwS,KAAI,CAACrS,cAAc,GAAGyR,WAAW,CAAC,MAAK;UACrCY,KAAI,CAAC3U,sBAAsB,EAAE;UAC7B;UACA2U,KAAI,CAACgB,iBAAiB,EAAE;UACxBhB,KAAI,CAAClU,GAAG,CAACgJ,aAAa,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACAkL,KAAI,CAACvS,aAAa,CAACwT,eAAe,GAAI5I,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAAC5B,IAAI,CAAC2G,IAAI,GAAG,CAAC,EAAE;YACvB4C,KAAI,CAACtS,WAAW,CAACmH,IAAI,CAACwD,KAAK,CAAC5B,IAAI,CAAC;;QAErC,CAAC;QAEDuJ,KAAI,CAACvS,aAAa,CAACyT,MAAM,GAAG,MAAK;UAC/BlB,KAAI,CAACmB,oBAAoB,EAAE;QAC7B,CAAC;QAEDnB,KAAI,CAACvS,aAAa,CAAC2T,OAAO,GAAI/I,KAAU,IAAI;UAC1CvX,OAAO,CAACR,KAAK,CAAC,iCAAiC,EAAE+X,KAAK,CAAC/X,KAAK,CAAC;UAC7D0f,KAAI,CAACnU,YAAY,CAAC9L,SAAS,CAAC,iCAAiC,CAAC;UAC9DigB,KAAI,CAACqB,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACArB,KAAI,CAACvS,aAAa,CAAC6T,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAE/BtB,KAAI,CAACnU,YAAY,CAAC/L,WAAW,CAAC,iCAAiC,CAAC;OACjE,CAAC,OAAOQ,KAAU,EAAE;QACnBQ,OAAO,CAACR,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAE5D,IAAIihB,YAAY,GAAG,+CAA+C;QAElE,IAAIjhB,KAAK,CAACgJ,IAAI,KAAK,iBAAiB,EAAE;UACpCiY,YAAY,GACV,iGAAiG;SACpG,MAAM,IAAIjhB,KAAK,CAACgJ,IAAI,KAAK,eAAe,EAAE;UACzCiY,YAAY,GACV,6DAA6D;SAChE,MAAM,IAAIjhB,KAAK,CAACgJ,IAAI,KAAK,mBAAmB,EAAE;UAC7CiY,YAAY,GACV,0DAA0D;SAC7D,MAAM,IAAIjhB,KAAK,CAACZ,OAAO,EAAE;UACxB6hB,YAAY,GAAGjhB,KAAK,CAACZ,OAAO;;QAG9BsgB,KAAI,CAACnU,YAAY,CAAC9L,SAAS,CAACwhB,YAAY,CAAC;QACzCvB,KAAI,CAACqB,oBAAoB,EAAE;;IAC5B;EACH;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC/T,aAAa,IAAI,IAAI,CAACA,aAAa,CAACgU,KAAK,KAAK,WAAW,EAAE;MAClE,IAAI,CAAChU,aAAa,CAACiU,IAAI,EAAE;MACzB,IAAI,CAACjU,aAAa,CAAC8S,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGxE,IAAI,IAAI,CAAC/T,cAAc,EAAE;MACvB2R,aAAa,CAAC,IAAI,CAAC3R,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,YAAY;EACzC;EAEA6T,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC5T,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACgU,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAChU,aAAa,CAACiU,IAAI,EAAE;;MAE3B,IAAI,CAACjU,aAAa,CAAC8S,MAAM,CAACoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;MACtE,IAAI,CAACjU,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,cAAc,EAAE;MACvB2R,aAAa,CAAC,IAAI,CAAC3R,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACJ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAClC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACmC,mBAAmB,GAAG,MAAM;IACjC,IAAI,CAACE,WAAW,GAAG,EAAE;EACvB;EAEcyT,oBAAoBA,CAAA;IAAA,IAAAW,MAAA;IAAA,OAAA7B,kKAAA;MAChC,IAAI;QACF;QACA,IAAI6B,MAAI,CAACpU,WAAW,CAACiF,MAAM,KAAK,CAAC,EAAE;UACjC7R,OAAO,CAACR,KAAK,CAAC,sCAAsC,CAAC;UACrDwhB,MAAI,CAACjW,YAAY,CAAC9L,SAAS,CAAC,wBAAwB,CAAC;UACrD+hB,MAAI,CAACT,oBAAoB,EAAE;UAC3B;;QAGFvgB,OAAO,CAACC,GAAG,CACT,0BAA0B,EAC1B+gB,MAAI,CAACpU,WAAW,CAACiF,MAAM,EACvB,WAAW,EACXmP,MAAI,CAACzW,sBAAsB,CAC5B;QAED;QACA,IAAIyW,MAAI,CAACzW,sBAAsB,GAAG,CAAC,EAAE;UACnCvK,OAAO,CAACR,KAAK,CACX,iCAAiC,EACjCwhB,MAAI,CAACzW,sBAAsB,CAC5B;UACDyW,MAAI,CAACjW,YAAY,CAAC9L,SAAS,CACzB,+CAA+C,CAChD;UACD+hB,MAAI,CAACT,oBAAoB,EAAE;UAC3B;;QAGF;QACA,IAAIP,QAAQ,GAAG,wBAAwB;QACvC,IAAIgB,MAAI,CAACrU,aAAa,EAAEqT,QAAQ,EAAE;UAChCA,QAAQ,GAAGgB,MAAI,CAACrU,aAAa,CAACqT,QAAQ;;QAGxC;QACA,MAAMiB,SAAS,GAAG,IAAIC,IAAI,CAACF,MAAI,CAACpU,WAAW,EAAE;UAC3C/N,IAAI,EAAEmhB;SACP,CAAC;QAEFhgB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5Cqc,IAAI,EAAE2E,SAAS,CAAC3E,IAAI;UACpBzd,IAAI,EAAEoiB,SAAS,CAACpiB;SACjB,CAAC;QAEF;QACA,IAAIsiB,SAAS,GAAG,OAAO;QACvB,IAAInB,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,EAAE;UAC5B2E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAInB,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC2E,SAAS,GAAG,MAAM;SACnB,MAAM,IAAInB,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnC2E,SAAS,GAAG,MAAM;;QAGpB;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CACxB,CAACJ,SAAS,CAAC,EACX,SAASzO,IAAI,CAAC6C,GAAG,EAAE,GAAG8L,SAAS,EAAE,EACjC;UACEtiB,IAAI,EAAEmhB;SACP,CACF;QAEDhgB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CuI,IAAI,EAAE4Y,SAAS,CAAC5Y,IAAI;UACpB8T,IAAI,EAAE8E,SAAS,CAAC9E,IAAI;UACpBzd,IAAI,EAAEuiB,SAAS,CAACviB;SACjB,CAAC;QAEF;QACAmiB,MAAI,CAACtU,mBAAmB,GAAG,YAAY;QACvC,MAAMsU,MAAI,CAACM,gBAAgB,CAACF,SAAS,CAAC;QAEtCJ,MAAI,CAACjW,YAAY,CAAC/L,WAAW,CAAC,yBAAyB,CAAC;OACzD,CAAC,OAAOQ,KAAU,EAAE;QACnBQ,OAAO,CAACR,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DwhB,MAAI,CAACjW,YAAY,CAAC9L,SAAS,CACzB,2CAA2C,IACxCO,KAAK,CAACZ,OAAO,IAAI,iBAAiB,CAAC,CACvC;OACF,SAAS;QACR;QACAoiB,MAAI,CAACtU,mBAAmB,GAAG,MAAM;QACjCsU,MAAI,CAACzW,sBAAsB,GAAG,CAAC;QAC/ByW,MAAI,CAACpU,WAAW,GAAG,EAAE;QACrBoU,MAAI,CAACvU,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EAEc6U,gBAAgBA,CAACF,SAAe;IAAA,IAAAG,MAAA;IAAA,OAAApC,kKAAA;MAC5C,MAAMvK,UAAU,GAAG2M,MAAI,CAACngB,gBAAgB,EAAEhD,EAAE,IAAImjB,MAAI,CAACngB,gBAAgB,EAAE8P,GAAG;MAE1E,IAAI,CAAC0D,UAAU,EAAE;QACf,MAAM,IAAI2K,KAAK,CAAC,0BAA0B,CAAC;;MAG7C,OAAO,IAAIiC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrCH,MAAI,CAAC1W,cAAc,CAAC4J,WAAW,CAC7BG,UAAU,EACV,EAAE,EACFwM,SAAS,EACT,OAAc,EACdG,MAAI,CAACtW,YAAY,CAAC7M,EAAE,CACrB,CAACoT,SAAS,CAAC;UACVhT,IAAI,EAAGI,OAAY,IAAI;YACrB2iB,MAAI,CAACvZ,QAAQ,CAAC+L,IAAI,CAACnV,OAAO,CAAC;YAC3B2iB,MAAI,CAACxO,cAAc,EAAE;YACrB0O,OAAO,EAAE;UACX,CAAC;UACDjiB,KAAK,EAAGA,KAAU,IAAI;YACpBQ,OAAO,CAACR,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;YAChEkiB,MAAM,CAACliB,KAAK,CAAC;UACf;SACD,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEA8K,uBAAuBA,CAACjM,QAAgB;IACtC,MAAMygB,OAAO,GAAGjhB,IAAI,CAACuX,KAAK,CAAC/W,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAM0gB,OAAO,GAAG1gB,QAAQ,GAAG,EAAE;IAC7B,OAAO,GAAGygB,OAAO,IAAIC,OAAO,CAAChhB,QAAQ,EAAE,CAACihB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA;EAEA2C,aAAaA,CAACpK,KAAY;IACxBA,KAAK,CAACC,cAAc,EAAE;IAEtBxX,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCwM,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CnC,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;MACnDoC,aAAa,EAAE,CAAC,CAAC,IAAI,CAACA;KACvB,CAAC;IAEF;IACA,IAAI,IAAI,CAACD,mBAAmB,KAAK,YAAY,EAAE;MAC7C,IAAI,CAAC3B,YAAY,CAAC7L,WAAW,CAAC,wBAAwB,CAAC;MACvD;;IAGF,IAAI,IAAI,CAACuN,gBAAgB,EAAE;MACzB,IAAI,CAAC1B,YAAY,CAAC7L,WAAW,CAAC,iCAAiC,CAAC;MAChE;;IAGF;IACA,IAAI,CAAC6L,YAAY,CAAC5L,QAAQ,CAAC,2CAA2C,CAAC;IAEvE;IACA,IAAI,CAAC8f,mBAAmB,EAAE,CAAC7H,KAAK,CAAE5X,KAAK,IAAI;MACzCQ,OAAO,CAACR,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CACzB,iDAAiD,IAC9CO,KAAK,CAACZ,OAAO,IAAI,iBAAiB,CAAC,CACvC;IACH,CAAC,CAAC;EACJ;EAEAwL,WAAWA,CAACmN,KAAY;IACtBA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC,IAAI,CAAC/K,gBAAgB,EAAE;MAC1B;;IAGF;IACA,IAAI,CAACiU,kBAAkB,EAAE;EAC3B;EAEAzW,cAAcA,CAACsN,KAAY;IACzBA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC,IAAI,CAAC/K,gBAAgB,EAAE;MAC1B;;IAGF;IACA,IAAI,CAAC8T,oBAAoB,EAAE;EAC7B;EAEA/V,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACmC,aAAa,EAAEqT,QAAQ,EAAE;MAChC,IAAI,IAAI,CAACrT,aAAa,CAACqT,QAAQ,CAACxD,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;MAC/D,IAAI,IAAI,CAAC7P,aAAa,CAACqT,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAAC7P,aAAa,CAACqT,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC7D,IAAI,IAAI,CAAC7P,aAAa,CAACqT,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;;IAE/D,OAAO,MAAM;EACf;EAEA;EAEQ0D,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACxa,UAAU,GAAG,IAAI,CAACA,UAAU,CAACkc,GAAG,CAAC,MAAK;MACzC,OAAO/jB,IAAI,CAACuX,KAAK,CAACvX,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;;EAEA+jB,cAAcA,CAACtK,KAAU;IACvB,MAAMuK,KAAK,GAAGvK,KAAK,CAACV,MAAM,CAACiL,KAAK;IAEhC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACjQ,MAAM,KAAK,CAAC,EAAE;MAChC;;IAGF,KAAK,IAAIkQ,IAAI,IAAID,KAAK,EAAE;MACtB9hB,OAAO,CAACC,GAAG,CACT,gCAAgC8hB,IAAI,CAACvZ,IAAI,WAAWuZ,IAAI,CAACzF,IAAI,WAAWyF,IAAI,CAACljB,IAAI,EAAE,CACpF;MACD,IAAI,CAACmjB,UAAU,CAACD,IAAI,CAAC;;EAEzB;EAEQC,UAAUA,CAACD,IAAU;IAC3B,MAAMnN,UAAU,GAAG,IAAI,CAACxT,gBAAgB,EAAEhD,EAAE,IAAI,IAAI,CAACgD,gBAAgB,EAAE8P,GAAG;IAE1E,IAAI,CAAC0D,UAAU,EAAE;MACf5U,OAAO,CAACR,KAAK,CAAC,kCAAkC,CAAC;MACjD,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,0BAA0B,CAAC;MACvD;;IAGF;IACA,MAAMgjB,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIF,IAAI,CAACzF,IAAI,GAAG2F,OAAO,EAAE;MACvBjiB,OAAO,CAACR,KAAK,CAAC,+BAA+BuiB,IAAI,CAACzF,IAAI,QAAQ,CAAC;MAC/D,IAAI,CAACvR,YAAY,CAAC9L,SAAS,CAAC,oCAAoC,CAAC;MACjE;;IAGF;IACA,IAAI8iB,IAAI,CAACljB,IAAI,CAAC2X,UAAU,CAAC,QAAQ,CAAC,IAAIuL,IAAI,CAACzF,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAC7D;MACAtc,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC8hB,IAAI,CAACvZ,IAAI,EACT,gBAAgB,EAChBuZ,IAAI,CAACzF,IAAI,CACV;MACD,IAAI,CAAC4F,aAAa,CAACH,IAAI,CAAC,CACrB5K,IAAI,CAAEgL,cAAc,IAAI;QACvBniB,OAAO,CAACC,GAAG,CACT,8DAA8D,EAC9DkiB,cAAc,CAAC7F,IAAI,CACpB;QACD,IAAI,CAAC8F,gBAAgB,CAACD,cAAc,EAAEvN,UAAU,CAAC;MACnD,CAAC,CAAC,CACDwC,KAAK,CAAE5X,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA,IAAI,CAAC4iB,gBAAgB,CAACL,IAAI,EAAEnN,UAAU,CAAC;MACzC,CAAC,CAAC;MACJ;;IAGF;IACA,IAAI,CAACwN,gBAAgB,CAACL,IAAI,EAAEnN,UAAU,CAAC;EACzC;EAEQwN,gBAAgBA,CAACL,IAAU,EAAEnN,UAAkB;IACrD,MAAMyN,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACP,IAAI,CAAC;IAEjD,IAAI,CAACnW,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACW,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,cAAc,GAAG,CAAC;IAEvB;IACA,MAAMiW,gBAAgB,GAAGjE,WAAW,CAAC,MAAK;MACxC,IAAI,CAAChS,cAAc,IAAIzO,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE;MACzC,IAAI,IAAI,CAACwO,cAAc,IAAI,EAAE,EAAE;QAC7BkS,aAAa,CAAC+D,gBAAgB,CAAC;;MAEjC,IAAI,CAACvX,GAAG,CAACgJ,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAACnJ,cAAc,CAAC4J,WAAW,CAC7BG,UAAU,EACV,EAAE,EACFmN,IAAI,EACJM,WAAW,EACX,IAAI,CAACpX,YAAY,CAAC7M,EAAE,CACrB,CAACoT,SAAS,CAAC;MACVhT,IAAI,EAAGI,OAAY,IAAI;QACrBoB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChD7B,EAAE,EAAEQ,OAAO,CAACR,EAAE;UACdS,IAAI,EAAED,OAAO,CAACC,IAAI;UAClByX,WAAW,EAAE1X,OAAO,CAAC0X,WAAW;UAChC7O,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC7I,OAAO,CAAC;UAChCod,OAAO,EAAE,IAAI,CAACA,OAAO,CAACpd,OAAO,CAAC;UAC9Bmd,QAAQ,EAAE,IAAI,CAACxX,WAAW,CAAC3F,OAAO;SACnC,CAAC;QAEF4f,aAAa,CAAC+D,gBAAgB,CAAC;QAC/B,IAAI,CAACjW,cAAc,GAAG,GAAG;QAEzB7N,UAAU,CAAC,MAAK;UACd,IAAI,CAACuJ,QAAQ,CAAC+L,IAAI,CAACnV,OAAO,CAAC;UAC3B,IAAI,CAACmU,cAAc,EAAE;UACrB,IAAI,CAAChI,YAAY,CAAC/L,WAAW,CAAC,4BAA4B,CAAC;UAC3D,IAAI,CAACwjB,gBAAgB,EAAE;QACzB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDhjB,KAAK,EAAGA,KAAU,IAAI;QACpBQ,OAAO,CAACR,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDgf,aAAa,CAAC+D,gBAAgB,CAAC;QAC/B,IAAI,CAACxX,YAAY,CAAC9L,SAAS,CAAC,mCAAmC,CAAC;QAChE,IAAI,CAACujB,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEQF,kBAAkBA,CAACP,IAAU;IACnC,IAAIA,IAAI,CAACljB,IAAI,CAAC2X,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIuL,IAAI,CAACljB,IAAI,CAAC2X,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,IAAIuL,IAAI,CAACljB,IAAI,CAAC2X,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAc;IACzD,OAAO,MAAa;EACtB;EAEAiM,kBAAkBA,CAAA;IAChB,OAAO,KAAK;EACd;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAAC5W,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACW,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,cAAc,GAAG,CAAC;EACzB;EAEA;EAEAoW,UAAUA,CAACnL,KAAgB;IACzBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACpL,UAAU,GAAG,IAAI;EACxB;EAEAmW,WAAWA,CAACpL,KAAgB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB;IACA,MAAMgL,IAAI,GAAIrL,KAAK,CAACsL,aAA6B,CAACC,qBAAqB,EAAE;IACzE,MAAM9W,CAAC,GAAGuL,KAAK,CAACE,OAAO;IACvB,MAAMxL,CAAC,GAAGsL,KAAK,CAACG,OAAO;IAEvB,IAAI1L,CAAC,GAAG4W,IAAI,CAACG,IAAI,IAAI/W,CAAC,GAAG4W,IAAI,CAACI,KAAK,IAAI/W,CAAC,GAAG2W,IAAI,CAACK,GAAG,IAAIhX,CAAC,GAAG2W,IAAI,CAACM,MAAM,EAAE;MACtE,IAAI,CAAC1W,UAAU,GAAG,KAAK;;EAE3B;EAEA2W,MAAMA,CAAC5L,KAAgB;IACrBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACpL,UAAU,GAAG,KAAK;IAEvB,MAAMsV,KAAK,GAAGvK,KAAK,CAAC6L,YAAY,EAAEtB,KAAK;IACvC,IAAIA,KAAK,IAAIA,KAAK,CAACjQ,MAAM,GAAG,CAAC,EAAE;MAC7B;MACAwR,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC,CAAChB,OAAO,CAAEiB,IAAI,IAAI;QACjC/hB,OAAO,CAACC,GAAG,CACT,iCAAiC,EACjC8hB,IAAI,CAACvZ,IAAI,EACTuZ,IAAI,CAACljB,IAAI,EACTkjB,IAAI,CAACzF,IAAI,CACV;QACD,IAAI,CAAC0F,UAAU,CAACD,IAAI,CAAC;MACvB,CAAC,CAAC;MAEF,IAAI,CAAChX,YAAY,CAAC/L,WAAW,CAC3B,GAAG8iB,KAAK,CAACjQ,MAAM,8BAA8B,CAC9C;;EAEL;EAEA;EAEQqQ,aAAaA,CAACH,IAAU,EAAEwB,OAAA,GAAkB,GAAG;IACrD,OAAO,IAAI/B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAM8B,MAAM,GAAGpU,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMiU,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAK;QAChB;QACA,MAAMC,QAAQ,GAAG,IAAI;QACrB,MAAMC,SAAS,GAAG,IAAI;QACtB,IAAI;UAAEC,KAAK;UAAEC;QAAM,CAAE,GAAGN,GAAG;QAE3B,IAAIK,KAAK,GAAGF,QAAQ,IAAIG,MAAM,GAAGF,SAAS,EAAE;UAC1C,MAAMG,KAAK,GAAGrmB,IAAI,CAACqc,GAAG,CAAC4J,QAAQ,GAAGE,KAAK,EAAED,SAAS,GAAGE,MAAM,CAAC;UAC5DD,KAAK,IAAIE,KAAK;UACdD,MAAM,IAAIC,KAAK;;QAGjBV,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;QAEtB;QACAR,GAAG,EAAEU,SAAS,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEK,KAAK,EAAEC,MAAM,CAAC;QAExC;QACAT,MAAM,CAACY,MAAM,CACVC,IAAI,IAAI;UACP,IAAIA,IAAI,EAAE;YACR,MAAMlC,cAAc,GAAG,IAAId,IAAI,CAAC,CAACgD,IAAI,CAAC,EAAEtC,IAAI,CAACvZ,IAAI,EAAE;cACjD3J,IAAI,EAAEkjB,IAAI,CAACljB,IAAI;cACfylB,YAAY,EAAE9R,IAAI,CAAC6C,GAAG;aACvB,CAAC;YACFoM,OAAO,CAACU,cAAc,CAAC;WACxB,MAAM;YACLT,MAAM,CAAC,IAAInC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;QAEjD,CAAC,EACDwC,IAAI,CAACljB,IAAI,EACT0kB,OAAO,CACR;MACH,CAAC;MAEDI,GAAG,CAACrD,OAAO,GAAG,MAAMoB,MAAM,CAAC,IAAInC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAC7DoE,GAAG,CAACxG,GAAG,GAAGoH,GAAG,CAACC,eAAe,CAACzC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EAEQnF,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACnP,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB;MACA,IAAI,CAACgX,mBAAmB,CAAC,IAAI,CAAC;;IAGhC;IACA,IAAI,IAAI,CAAC9W,aAAa,EAAE;MACtB+W,YAAY,CAAC,IAAI,CAAC/W,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGlP,UAAU,CAAC,MAAK;MACnC,IAAI,CAACgP,QAAQ,GAAG,KAAK;MACrB;MACA,IAAI,CAACgX,mBAAmB,CAAC,KAAK,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,mBAAmBA,CAAChX,QAAiB;IAC3C;IACA,MAAMmH,UAAU,GAAG,IAAI,CAACxT,gBAAgB,EAAEhD,EAAE,IAAI,IAAI,CAACgD,gBAAgB,EAAE8P,GAAG;IAC1E,IAAI0D,UAAU,IAAI,IAAI,CAAC3J,YAAY,EAAE7M,EAAE,EAAE;MACvC4B,OAAO,CAACC,GAAG,CACT,gCAAgCwN,QAAQ,YAAYmH,UAAU,EAAE,CACjE;MACD;MACA;;EAEJ;EAEA;EAEA+P,cAAcA,CAACxG,IAAU;IACvB;IACA,IAAI,CAAClR,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACiR,cAAc,EAAE;IACrB,IAAI,CAACnT,YAAY,CAAC/L,WAAW,CAAC,eAAe,CAAC;EAChD;EAEA4lB,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC7Z,YAAY,CAAC5L,QAAQ,CAAC,cAAc,CAAC;EAC5C;EAEA;EAEA0lB,gBAAgBA,CAACjmB,OAAY;IAC3B,IAAI,CAACyG,mBAAmB,CAACzG,OAAO,CAAC;EACnC;EAEA6F,cAAcA,CAAC+P,SAAiB;IAC9B,OAAO,IAAI,CAACzH,gBAAgB,KAAKyH,SAAS;EAC5C;EAEAnP,mBAAmBA,CAACzG,OAAY;IAC9B,MAAM4V,SAAS,GAAG5V,OAAO,CAACR,EAAE;IAC5B,MAAMud,QAAQ,GAAG,IAAI,CAACmJ,WAAW,CAAClmB,OAAO,CAAC;IAE1C,IAAI,CAAC+c,QAAQ,EAAE;MACb3b,OAAO,CAACR,KAAK,CAAC,4CAA4C,EAAEgV,SAAS,CAAC;MACtE,IAAI,CAACzJ,YAAY,CAAC9L,SAAS,CAAC,2BAA2B,CAAC;MACxD;;IAGF;IACA,IAAI,IAAI,CAACwF,cAAc,CAAC+P,SAAS,CAAC,EAAE;MAClC,IAAI,CAACuQ,iBAAiB,EAAE;MACxB;;IAGF;IACA,IAAI,CAACA,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,kBAAkB,CAACpmB,OAAO,EAAE+c,QAAQ,CAAC;EAC5C;EAEQqJ,kBAAkBA,CAACpmB,OAAY,EAAE+c,QAAgB;IACvD,MAAMnH,SAAS,GAAG5V,OAAO,CAACR,EAAE;IAE5B,IAAI;MACF4B,OAAO,CAACC,GAAG,CACT,mCAAmC,EACnCuU,SAAS,EACT,MAAM,EACNmH,QAAQ,CACT;MAED,IAAI,CAAC7O,YAAY,GAAG,IAAImY,KAAK,CAACtJ,QAAQ,CAAC;MACvC,IAAI,CAAC5O,gBAAgB,GAAGyH,SAAS;MAEjC;MACA,MAAM0Q,WAAW,GAAG,IAAI,CAAC5P,oBAAoB,CAACd,SAAS,CAAC;MACxD,IAAI,CAACkB,oBAAoB,CAAClB,SAAS,EAAE;QACnCe,QAAQ,EAAE,CAAC;QACXC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAEyP,WAAW,CAACzP,KAAK,IAAI,CAAC;QAC7BpX,QAAQ,EAAE6mB,WAAW,CAAC7mB,QAAQ,IAAI;OACnC,CAAC;MAEF;MACA,IAAI,CAACyO,YAAY,CAACqY,YAAY,GAAGD,WAAW,CAACzP,KAAK,IAAI,CAAC;MAEvD;MACA,IAAI,CAAC3I,YAAY,CAACoD,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;QACxD,IAAI,IAAI,CAACpD,YAAY,EAAE;UACrB,IAAI,CAAC4I,oBAAoB,CAAClB,SAAS,EAAE;YACnCnW,QAAQ,EAAE,IAAI,CAACyO,YAAY,CAACzO;WAC7B,CAAC;UACF2B,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAAC6M,YAAY,CAACzO,QAAQ,CAC3B;;MAEL,CAAC,CAAC;MAEF,IAAI,CAACyO,YAAY,CAACoD,gBAAgB,CAAC,YAAY,EAAE,MAAK;QACpD,IAAI,IAAI,CAACpD,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKyH,SAAS,EAAE;UAC5D,MAAMgB,WAAW,GAAG,IAAI,CAAC1I,YAAY,CAAC0I,WAAW;UACjD,MAAMD,QAAQ,GAAIC,WAAW,GAAG,IAAI,CAAC1I,YAAY,CAACzO,QAAQ,GAAI,GAAG;UACjE,IAAI,CAACqX,oBAAoB,CAAClB,SAAS,EAAE;YAAEgB,WAAW;YAAED;UAAQ,CAAE,CAAC;UAC/D,IAAI,CAACvK,GAAG,CAACgJ,aAAa,EAAE;;MAE5B,CAAC,CAAC;MAEF,IAAI,CAAClH,YAAY,CAACoD,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC/C,IAAI,CAAC6U,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACjY,YAAY,CAACoD,gBAAgB,CAAC,OAAO,EAAG1Q,KAAK,IAAI;QACpDQ,OAAO,CAACR,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,iCAAiC,CAAC;QAC9D,IAAI,CAAC8lB,iBAAiB,EAAE;MAC1B,CAAC,CAAC;MAEF;MACA,IAAI,CAACjY,YAAY,CACd4D,IAAI,EAAE,CACNyG,IAAI,CAAC,MAAK;QACT,IAAI,CAACpM,YAAY,CAAC/L,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC,CAAC,CACDoY,KAAK,CAAE5X,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,qCAAqC,CAAC;QAClE,IAAI,CAAC8lB,iBAAiB,EAAE;MAC1B,CAAC,CAAC;KACL,CAAC,OAAOvlB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,iCAAiC,CAAC;MAC9D,IAAI,CAAC8lB,iBAAiB,EAAE;;EAE5B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACjY,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACsY,KAAK,EAAE;MACzB,IAAI,CAACtY,YAAY,CAAC0I,WAAW,GAAG,CAAC;MACjC,IAAI,CAAC1I,YAAY,GAAG,IAAI;;IAE1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC/B,GAAG,CAACgJ,aAAa,EAAE;EAC1B;EAEA8Q,WAAWA,CAAClmB,OAAY;IACtB;IACA,IAAIA,OAAO,CAAC8c,QAAQ,EAAE,OAAO9c,OAAO,CAAC8c,QAAQ;IAC7C,IAAI9c,OAAO,CAAC+c,QAAQ,EAAE,OAAO/c,OAAO,CAAC+c,QAAQ;IAC7C,IAAI/c,OAAO,CAACgd,KAAK,EAAE,OAAOhd,OAAO,CAACgd,KAAK;IAEvC;IACA,MAAMyJ,eAAe,GAAGzmB,OAAO,CAAC0X,WAAW,EAAEvE,IAAI,CAC9CwE,GAAQ,IAAKA,GAAG,CAAC1X,IAAI,EAAE2X,UAAU,CAAC,QAAQ,CAAC,IAAID,GAAG,CAAC1X,IAAI,KAAK,OAAO,CACrE;IAED,IAAIwmB,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAC5O,GAAG,IAAI4O,eAAe,CAAClJ,IAAI,IAAI,EAAE;;IAG1D,OAAO,EAAE;EACX;EAEAmJ,aAAaA,CAAC1mB,OAAY;IACxB;IACA,MAAM4V,SAAS,GAAG5V,OAAO,CAACR,EAAE,IAAI,EAAE;IAClC,MAAMmnB,IAAI,GAAG/Q,SAAS,CACnBgR,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAY,KAAKD,GAAG,GAAGC,IAAI,CAACjJ,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMkJ,KAAK,GAAa,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAM5B,MAAM,GAAG,CAAC,GAAI,CAACsB,IAAI,GAAGM,CAAC,GAAG,CAAC,IAAI,EAAG;MACxCD,KAAK,CAAC7R,IAAI,CAACkQ,MAAM,CAAC;;IAGpB,OAAO2B,KAAK;EACd;EAEAE,gBAAgBA,CAAClnB,OAAY;IAC3B,MAAM+W,IAAI,GAAG,IAAI,CAACL,oBAAoB,CAAC1W,OAAO,CAACR,EAAE,CAAC;IAClD,MAAM2nB,UAAU,GAAG,EAAE;IACrB,OAAOloB,IAAI,CAACuX,KAAK,CAAEO,IAAI,CAACJ,QAAQ,GAAG,GAAG,GAAIwQ,UAAU,CAAC;EACvD;EAEAC,mBAAmBA,CAACpnB,OAAY;IAC9B,MAAM+W,IAAI,GAAG,IAAI,CAACL,oBAAoB,CAAC1W,OAAO,CAACR,EAAE,CAAC;IAClD,OAAO,IAAI,CAAC6nB,eAAe,CAACtQ,IAAI,CAACH,WAAW,CAAC;EAC/C;EAEA7P,gBAAgBA,CAAC/G,OAAY;IAC3B,MAAM+W,IAAI,GAAG,IAAI,CAACL,oBAAoB,CAAC1W,OAAO,CAACR,EAAE,CAAC;IAClD,MAAMC,QAAQ,GAAGsX,IAAI,CAACtX,QAAQ,IAAIO,OAAO,CAACsnB,QAAQ,EAAE7nB,QAAQ,IAAI,CAAC;IAEjE,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAOA,QAAQ,CAAC,CAAC;;;IAGnB,OAAO,IAAI,CAAC4nB,eAAe,CAAC5nB,QAAQ,CAAC;EACvC;EAEQ4nB,eAAeA,CAAClH,OAAe;IACrC,MAAMD,OAAO,GAAGjhB,IAAI,CAACuX,KAAK,CAAC2J,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMoH,gBAAgB,GAAGtoB,IAAI,CAACuX,KAAK,CAAC2J,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGD,OAAO,IAAIqH,gBAAgB,CAACpoB,QAAQ,EAAE,CAACihB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEAoH,gBAAgBA,CAACxnB,OAAY,EAAEynB,SAAiB;IAC9C,MAAM7R,SAAS,GAAG5V,OAAO,CAACR,EAAE;IAE5B,IAAI,CAAC,IAAI,CAAC0O,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKyH,SAAS,EAAE;MAC7D;;IAGF,MAAMuR,UAAU,GAAG,EAAE;IACrB,MAAMO,cAAc,GAAID,SAAS,GAAGN,UAAU,GAAI,GAAG;IACrD,MAAMQ,QAAQ,GAAID,cAAc,GAAG,GAAG,GAAI,IAAI,CAACxZ,YAAY,CAACzO,QAAQ;IAEpE,IAAI,CAACyO,YAAY,CAAC0I,WAAW,GAAG+Q,QAAQ;EAC1C;EAEAC,gBAAgBA,CAAC5nB,OAAY;IAC3B,MAAM4V,SAAS,GAAG5V,OAAO,CAACR,EAAE;IAC5B,MAAMuX,IAAI,GAAG,IAAI,CAACL,oBAAoB,CAACd,SAAS,CAAC;IAEjD;IACA,MAAMiS,QAAQ,GAAG9Q,IAAI,CAACF,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACF,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IAEpE,IAAI,CAACC,oBAAoB,CAAClB,SAAS,EAAE;MAAEiB,KAAK,EAAEgR;IAAQ,CAAE,CAAC;IAEzD,IAAI,IAAI,CAAC3Z,YAAY,IAAI,IAAI,CAACC,gBAAgB,KAAKyH,SAAS,EAAE;MAC5D,IAAI,CAAC1H,YAAY,CAACqY,YAAY,GAAGsB,QAAQ;;IAG3C,IAAI,CAAC1b,YAAY,CAAC/L,WAAW,CAAC,YAAYynB,QAAQ,GAAG,CAAC;EACxD;EAEA;EAEA1hB,gBAAgBA,CAACnG,OAAY;IAC3B,IAAI,CAAC4nB,gBAAgB,CAAC5nB,OAAO,CAAC;EAChC;EAEAqG,aAAaA,CAACrG,OAAY;IACxB,MAAM+W,IAAI,GAAG,IAAI,CAACL,oBAAoB,CAAC1W,OAAO,CAACR,EAAE,CAAC;IAClD,OAAOuX,IAAI,CAACF,KAAK,IAAI,CAAC;EACxB;EAEAiR,WAAWA,CAAA;IACT,IAAI,CAAC9Y,aAAa,CAACyF,WAAW,EAAE;IAEhC;IACA,IAAI,IAAI,CAACjG,SAAS,EAAE;MAClBoR,aAAa,CAAC,IAAI,CAACpR,SAAS,CAAC;;IAE/B,IAAI,IAAI,CAACP,cAAc,EAAE;MACvB2R,aAAa,CAAC,IAAI,CAAC3R,cAAc,CAAC;;IAEpC,IAAI,IAAI,CAACc,aAAa,EAAE;MACtB+W,YAAY,CAAC,IAAI,CAAC/W,aAAa,CAAC;;IAGlC;IACA,IAAI,IAAI,CAAChB,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACgU,KAAK,KAAK,WAAW,EAAE;QAC5C,IAAI,CAAChU,aAAa,CAACiU,IAAI,EAAE;;MAE3B,IAAI,CAACjU,aAAa,CAAC8S,MAAM,EAAEoB,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACH,IAAI,EAAE,CAAC;;IAGzE;IACA,IAAI,CAACmE,iBAAiB,EAAE;EAC1B;;;uBA/5EWta,oBAAoB,EAAA7J,+DAAA,CAAAgmB,uDAAA,GAAAhmB,+DAAA,CAAAkmB,2DAAA,GAAAlmB,+DAAA,CAAAkmB,mDAAA,GAAAlmB,+DAAA,CAAAqmB,qEAAA,GAAArmB,+DAAA,CAAAsmB,+DAAA,GAAAtmB,+DAAA,CAAAwmB,iEAAA,GAAAxmB,+DAAA,CAAAA,4DAAA;IAAA;EAAA;;;YAApB6J,oBAAoB;MAAA6c,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAhE,GAAA;QAAA,IAAAgE,EAAA;;;;;;;;;;;;;;;;;;;;;;;UCnBjC7mB,uDAAA,kBAYS;UAcTA,4DAAA,aASC;UAsEKA,wDAAA,mBAAA8mB,sDAAA;YAAA,OAASjE,GAAA,CAAAxM,qBAAA,EAAuB;UAAA,EAAC;UAmBjCrW,uDAAA,WAGK;UACPA,0DAAA,EAAS;UAGTA,4DAAA,aAAuE;UAejEA,wDAAA,mBAAA+mB,oDAAA;YAAA,OAASlE,GAAA,CAAA9gB,eAAA,CAAA8gB,GAAA,CAAAriB,gBAAA,kBAAAqiB,GAAA,CAAAriB,gBAAA,CAAAhD,EAAA,CAAsC;UAAA,EAAC;UAZlDwC,0DAAA,EAgBE;UAEFA,wDAAA,KAAAgnB,oCAAA,kBAaO;UACThnB,0DAAA,EAAM;UAGNA,4DAAA,eAAmC;UAY/BA,oDAAA,IACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAA8D;UAE5DA,wDAAA,KAAAinB,oCAAA,kBAkCM;UAENjnB,wDAAA,KAAAknB,qCAAA,mBAMO;UACTlnB,0DAAA,EAAM;UAKVA,4DAAA,eAA0D;UAGtDA,wDAAA,mBAAAmnB,uDAAA;YAAA,OAAStE,GAAA,CAAA7N,cAAA,EAAgB;UAAA,EAAC;UAoB1BhV,uDAAA,aAAoD;UACtDA,0DAAA,EAAS;UAGTA,4DAAA,kBAoBC;UAnBCA,wDAAA,mBAAAonB,uDAAA;YAAA,OAASvE,GAAA,CAAA1N,cAAA,EAAgB;UAAA,EAAC;UAoB1BnV,uDAAA,aAAoD;UACtDA,0DAAA,EAAS;UAGTA,4DAAA,kBAcC;UAbCA,wDAAA,mBAAAqnB,uDAAA;YAAA,OAASxE,GAAA,CAAA3hB,YAAA,EAAc;UAAA,EAAC;UAcxBlB,uDAAA,aAA6B;UAC/BA,0DAAA,EAAS;UAGTA,4DAAA,kBAgBC;UAfCA,wDAAA,mBAAAsnB,uDAAA;YAAA,OAASzE,GAAA,CAAAlQ,kBAAA,EAAoB;UAAA,EAAC;UAgB9B3S,uDAAA,aAGK;UACPA,0DAAA,EAAS;UAGTA,4DAAA,kBAeC;UAdCA,wDAAA,mBAAAunB,uDAAA;YAAA,OAAS1E,GAAA,CAAAzM,cAAA,EAAgB;UAAA,EAAC;UAe1BpW,uDAAA,aAAiC;UACnCA,0DAAA,EAAS;UAIXA,wDAAA,KAAAwnB,oCAAA,mBA8EM;UACRxnB,0DAAA,EAAS;UAGTA,4DAAA,oBAQC;UALCA,wDAAA,oBAAAynB,sDAAAvkB,MAAA;YAAA,OAAU2f,GAAA,CAAAvG,QAAA,CAAApZ,MAAA,CAAgB;UAAA,EAAC,sBAAAwkB,wDAAAxkB,MAAA;YAAA,OACf2f,GAAA,CAAAf,UAAA,CAAA5e,MAAA,CAAkB;UAAA,EADH,uBAAAykB,yDAAAzkB,MAAA;YAAA,OAEd2f,GAAA,CAAAd,WAAA,CAAA7e,MAAA,CAAmB;UAAA,EAFL,kBAAA0kB,oDAAA1kB,MAAA;YAAA,OAGnB2f,GAAA,CAAAN,MAAA,CAAArf,MAAA,CAAc;UAAA,EAHK;UAO3BlD,wDAAA,KAAA6nB,oCAAA,kBAoDM;UAGN7nB,wDAAA,KAAA8nB,oCAAA,kBAsBM;UAGN9nB,wDAAA,KAAA+nB,oCAAA,kBA0BM;UAGN/nB,wDAAA,KAAAgoB,oCAAA,kBAgWM;UACRhoB,0DAAA,EAAO;UAGPA,4DAAA,kBAEC;UAGGA,wDAAA,sBAAAioB,wDAAA;YAAA,OAAYpF,GAAA,CAAAhP,WAAA,EAAa;UAAA,EAAC;UAI1B7T,4DAAA,eAAqC;UAIjCA,wDAAA,mBAAAkoB,uDAAA;YAAA,OAASrF,GAAA,CAAA/K,iBAAA,EAAmB;UAAA,EAAC;UAgB7B9X,uDAAA,aAA4B;UAC9BA,0DAAA,EAAS;UAGTA,4DAAA,kBAiBC;UAfCA,wDAAA,mBAAAmoB,uDAAA;YAAA,OAAStF,GAAA,CAAAzK,oBAAA,EAAsB;UAAA,EAAC;UAgBhCpY,uDAAA,aAAgC;UAClCA,0DAAA,EAAS;UAGTA,4DAAA,kBAwBC;UAtBCA,wDAAA,uBAAAooB,2DAAAllB,MAAA;YAAA,OAAa2f,GAAA,CAAA9B,aAAA,CAAA7d,MAAA,CAAqB;UAAA,EAAC,qBAAAmlB,yDAAAnlB,MAAA;YAAA,OACxB2f,GAAA,CAAArZ,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EADK,wBAAAolB,4DAAAplB,MAAA;YAAA,OAErB2f,GAAA,CAAAxZ,cAAA,CAAAnG,MAAA,CAAsB;UAAA,EAFD,wBAAAqlB,4DAAArlB,MAAA;YAAA,OAGrB2f,GAAA,CAAA9B,aAAA,CAAA7d,MAAA,CAAqB;UAAA,EAHA,sBAAAslB,0DAAAtlB,MAAA;YAAA,OAIvB2f,GAAA,CAAArZ,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EAJI,yBAAAulB,6DAAAvlB,MAAA;YAAA,OAKpB2f,GAAA,CAAAxZ,cAAA,CAAAnG,MAAA,CAAsB;UAAA,EALF;UAuBnClD,uDAAA,SAGK;UAGLA,wDAAA,KAAA0oB,oCAAA,kBAYO;UACT1oB,0DAAA,EAAS;UAIXA,4DAAA,eAAyC;UAIrCA,wDAAA,qBAAA2oB,2DAAAzlB,MAAA;YAAA,OAAW2f,GAAA,CAAA5G,cAAA,CAAA/Y,MAAA,CAAsB;UAAA,EAAC,mBAAA0lB,yDAAA1lB,MAAA;YAAA,OACzB2f,GAAA,CAAA9G,aAAA,CAAA7Y,MAAA,CAAqB;UAAA,EADI,mBAAA2lB,yDAAA;YAAA,OAEzBhG,GAAA,CAAAzG,YAAA,EAAc;UAAA,EAFW;UAoBnCpc,0DAAA,EAAW;UAIdA,4DAAA,kBA0BC;UACCA,wDAAA,KAAA8oB,kCAAA,gBAA4D;UAC5D9oB,wDAAA,KAAA+oB,oCAAA,kBAUO;UACT/oB,0DAAA,EAAS;UAIXA,wDAAA,KAAAgpB,oCAAA,kBAkDM;UAGNhpB,wDAAA,KAAAipB,oCAAA,mBAsJM;UAGNjpB,4DAAA,qBAOE;UAHAA,wDAAA,oBAAAkpB,uDAAAhmB,MAAA;YAAA,OAAU2f,GAAA,CAAA5B,cAAA,CAAA/d,MAAA,CAAsB;UAAA,EAAC;UAJnClD,0DAAA,EAOE;UAIJA,wDAAA,KAAAmpB,oCAAA,kBAYO;UAGPnpB,wDAAA,KAAAopB,oCAAA,mBAiHM;UACRppB,0DAAA,EAAM;;;UA3wCIA,uDAAA,IAAqE;UAArEA,wDAAA,SAAA6iB,GAAA,CAAAriB,gBAAA,kBAAAqiB,GAAA,CAAAriB,gBAAA,CAAA0B,KAAA,yCAAAlC,2DAAA,CAAqE,QAAA6iB,GAAA,CAAAriB,gBAAA,kBAAAqiB,GAAA,CAAAriB,gBAAA,CAAAc,QAAA;UAkBpEtB,uDAAA,GAAgC;UAAhCA,wDAAA,SAAA6iB,GAAA,CAAAriB,gBAAA,kBAAAqiB,GAAA,CAAAriB,gBAAA,CAAAC,QAAA,CAAgC;UA4BjCT,uDAAA,GACF;UADEA,gEAAA,OAAA6iB,GAAA,CAAAriB,gBAAA,kBAAAqiB,GAAA,CAAAriB,gBAAA,CAAAc,QAAA,wBACF;UAIKtB,uDAAA,GAAkB;UAAlBA,wDAAA,SAAA6iB,GAAA,CAAA/V,YAAA,CAAkB;UAmCd9M,uDAAA,GAAmB;UAAnBA,wDAAA,UAAA6iB,GAAA,CAAA/V,YAAA,CAAmB;UA2E5B9M,uDAAA,GAA2D;UAA3DA,yDAAA,eAAA6iB,GAAA,CAAA9X,UAAA,6BAA2D,UAAA8X,GAAA,CAAA9X,UAAA;UAmB3D/K,uDAAA,GAAyC;UAAzCA,yDAAA,YAAA6iB,GAAA,CAAAtY,SAAA,eAAyC;UACzCvK,wDAAA,aAAA6iB,GAAA,CAAAtY,SAAA,CAAsB;UAOpBvK,uDAAA,GAAkE;UAAlEA,yDAAA,cAAA6iB,GAAA,CAAAtY,SAAA,sCAAkE;UAiBpEvK,uDAAA,GAA6D;UAA7DA,yDAAA,eAAA6iB,GAAA,CAAAzhB,YAAA,6BAA6D,UAAAyhB,GAAA,CAAAzhB,YAAA;UAU9DpB,uDAAA,GAAkB;UAAlBA,wDAAA,SAAA6iB,GAAA,CAAAzhB,YAAA,CAAkB;UAwFrBpB,uDAAA,GAA0E;UAA1EA,yDAAA,eAAA6iB,GAAA,CAAAjX,UAAA,4CAA0E;UAIvE5L,uDAAA,GAAgB;UAAhBA,wDAAA,SAAA6iB,GAAA,CAAAjX,UAAA,CAAgB;UAuDhB5L,uDAAA,GAAe;UAAfA,wDAAA,SAAA6iB,GAAA,CAAAtY,SAAA,CAAe;UAyBfvK,uDAAA,GAAyC;UAAzCA,wDAAA,UAAA6iB,GAAA,CAAAtY,SAAA,IAAAsY,GAAA,CAAAzb,QAAA,CAAA6J,MAAA,OAAyC;UA6BzCjR,uDAAA,GAAuC;UAAvCA,wDAAA,UAAA6iB,GAAA,CAAAtY,SAAA,IAAAsY,GAAA,CAAAzb,QAAA,CAAA6J,MAAA,KAAuC;UAuWxCjR,uDAAA,GAAyB;UAAzBA,wDAAA,cAAA6iB,GAAA,CAAA5V,WAAA,CAAyB;UAmBrBjN,uDAAA,GAAgE;UAAhEA,yDAAA,eAAA6iB,GAAA,CAAAnY,eAAA,6BAAgE,UAAAmY,GAAA,CAAAnY,eAAA;UAsBhE1K,uDAAA,GAAmE;UAAnEA,yDAAA,eAAA6iB,GAAA,CAAAlY,kBAAA,6BAAmE,UAAAkY,GAAA,CAAAlY,kBAAA;UA4BnE3K,uDAAA,GAAiE;UAAjEA,yDAAA,eAAA6iB,GAAA,CAAAhX,gBAAA,6BAAiE,UAAAgX,GAAA,CAAAhX,gBAAA,uCAAAgX,GAAA,CAAAhX,gBAAA;UAQ/D7L,uDAAA,GAAgE;UAAhEA,wDAAA,CAAA6iB,GAAA,CAAAhX,gBAAA,uCAAgE;UAChE7L,yDAAA,cAAA6iB,GAAA,CAAAhX,gBAAA,gCAAmE;UAKlE7L,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA6iB,GAAA,CAAAhX,gBAAA,CAAsB;UAuCzB7L,uDAAA,GAA8B;UAA9BA,wDAAA,aAAA6iB,GAAA,CAAAxV,eAAA,GAA8B;UAsBhCrN,uDAAA,GAEC;UAFDA,yDAAA,gBAAA6iB,GAAA,CAAA5V,WAAA,CAAA6G,KAAA,IAAA+O,GAAA,CAAA7X,gBAAA,yBAEC,YAAA6X,GAAA,CAAA5V,WAAA,CAAA6G,KAAA,IAAA+O,GAAA,CAAA7X,gBAAA;UAjBDhL,wDAAA,cAAA6iB,GAAA,CAAA5V,WAAA,CAAA6G,KAAA,IAAA+O,GAAA,CAAA7X,gBAAA,CAAmD;UAyBpBhL,uDAAA,GAAuB;UAAvBA,wDAAA,UAAA6iB,GAAA,CAAA7X,gBAAA,CAAuB;UAEnDhL,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA6iB,GAAA,CAAA7X,gBAAA,CAAsB;UAe1BhL,uDAAA,GAAqB;UAArBA,wDAAA,SAAA6iB,GAAA,CAAAnY,eAAA,CAAqB;UAqDrB1K,uDAAA,GAAwB;UAAxBA,wDAAA,SAAA6iB,GAAA,CAAAlY,kBAAA,CAAwB;UA6JzB3K,uDAAA,GAA+B;UAA/BA,wDAAA,WAAA6iB,GAAA,CAAAhB,kBAAA,GAA+B;UAOhC7hB,uDAAA,GAA2D;UAA3DA,wDAAA,SAAA6iB,GAAA,CAAAnY,eAAA,IAAAmY,GAAA,CAAAlY,kBAAA,IAAAkY,GAAA,CAAAzhB,YAAA,CAA2D;UAe3DpB,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA6iB,GAAA,CAAAhX,gBAAA,CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnyC2C;;;;;;;;;;;;IC2D5D7L,4DAAA,cAA4D;IAExDA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IAEPA,uDAAA,cAEO;IACTA,0DAAA,EAAM;;;;IANFA,uDAAA,GACF;IADEA,gEAAA,MAAAqpB,QAAA,MACF;;;;;IAsCNrpB,4DAAA,cAGC;IACCA,uDAAA,cAA6C;IAC7CA,4DAAA,YAAmC;IAAAA,oDAAA,sCAA+B;IAAAA,0DAAA,EAAI;;;;;;IAIxEA,4DAAA,cAAsD;IAElDA,uDAAA,YAA2C;IAC7CA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,+CACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAoC;IAClCA,oDAAA,GACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,iBAAsE;IAA9DA,wDAAA,mBAAAspB,8DAAA;MAAAtpB,2DAAA,CAAAupB,GAAA;MAAA,MAAAC,MAAA,GAAAxpB,2DAAA;MAAA,OAASA,yDAAA,CAAAwpB,MAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACnCzpB,uDAAA,YAAsC;IAACA,oDAAA,wBACzC;IAAAA,0DAAA,EAAS;;;;IAJPA,uDAAA,GACF;IADEA,gEAAA,MAAA0pB,MAAA,CAAA9qB,KAAA,MACF;;;;;;IAQJoB,4DAAA,cAGC;IAEGA,uDAAA,YAA+B;IACjCA,0DAAA,EAAM;IAENA,4DAAA,aAAmC;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAK;IAE3DA,4DAAA,YAAiC;IAC/BA,oDAAA,iEACF;IAAAA,0DAAA,EAAI;IAEJA,4DAAA,iBAGC;IAFCA,wDAAA,mBAAA2pB,8DAAA;MAAA3pB,2DAAA,CAAA4pB,IAAA;MAAA,MAAAvoB,MAAA,GAAArB,2DAAA;MAAA,OAASA,yDAAA,CAAAqB,MAAA,CAAAwoB,oBAAA,EAAsB;IAAA,EAAC;IAGhC7pB,uDAAA,YAAuC;IACvCA,oDAAA,8BACF;IAAAA,0DAAA,EAAS;;;;;IAIXA,4DAAA,cAGC;IAEGA,uDAAA,YAA6B;IAC/BA,0DAAA,EAAM;IAENA,4DAAA,aAAmC;IAAAA,oDAAA,sCAAqB;IAAAA,0DAAA,EAAK;IAE7DA,4DAAA,YAAiC;IAAAA,oDAAA,0CAAmC;IAAAA,0DAAA,EAAI;;;;;IA8BlEA,uDAAA,cAMO;;;;;IAoBHA,4DAAA,eAGG;IAAAA,oDAAA,aACH;IAAAA,0DAAA,EAAO;;;;;IAGTA,4DAAA,cAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA8pB,QAAA,CAAAC,WAAA,MACF;;;;;;;;;;;IA5DR/pB,4DAAA,aAQC;IANCA,wDAAA,mBAAAgqB,8DAAA;MAAA,MAAAxkB,WAAA,GAAAxF,2DAAA,CAAAiqB,IAAA;MAAA,MAAAH,QAAA,GAAAtkB,WAAA,CAAA3D,SAAA;MAAA,MAAAqoB,OAAA,GAAAlqB,2DAAA;MAAA,OAASA,yDAAA,CAAAkqB,OAAA,CAAAC,gBAAA,CAAAL,QAAA,CAAAtsB,EAAA,CAAyB;IAAA,EAAC;IAOnCwC,4DAAA,cAA+B;IAG3BA,uDAAA,cAOE;IAGFA,wDAAA,IAAAoqB,+CAAA,kBAMO;IACTpqB,0DAAA,EAAM;IAGNA,4DAAA,cAA6C;IAGvCA,oDAAA,GAKF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAA2C;IACzCA,oDAAA,IACF;;IAAAA,0DAAA,EAAO;IAGTA,4DAAA,eAA6C;IAEzCA,wDAAA,KAAAqqB,iDAAA,mBAIO;IACPrqB,oDAAA,IACF;IAAAA,0DAAA,EAAI;IACJA,wDAAA,KAAAsqB,gDAAA,kBAKM;IACRtqB,0DAAA,EAAM;;;;;;;;IAzDVA,wDAAA,YAAAA,6DAAA,KAAAwqB,GAAA,EAAAC,OAAA,CAAAC,sBAAA,KAAAZ,QAAA,CAAAtsB,EAAA,EAGE;IAMIwC,uDAAA,GAIC;IAJDA,wDAAA,SAAA8pB,QAAA,CAAA9Y,YAAA,IAAA2Z,OAAA,GAAAF,OAAA,CAAAG,mBAAA,CAAAd,QAAA,CAAA9Y,YAAA,oBAAA2Z,OAAA,CAAAzoB,KAAA,gDAAAlC,2DAAA,CAIC;IAMAA,uDAAA,GAIjB;IAJiBA,wDAAA,SAAA8pB,QAAA,CAAA9Y,YAAA,MAAA6Z,OAAA,GAAAJ,OAAA,CAAAG,mBAAA,CAAAd,QAAA,CAAA9Y,YAAA,oBAAA6Z,OAAA,CAAApqB,QAAA,EAIjB;IAQkBT,uDAAA,GAKF;IALEA,gEAAA,OAAA8pB,QAAA,CAAA9Y,YAAA,IAAA8Z,OAAA,GAAAL,OAAA,CAAAG,mBAAA,CAAAd,QAAA,CAAA9Y,YAAA,oBAAA8Z,OAAA,CAAAxpB,QAAA,uCAKF;IAEEtB,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,QAAA8pB,QAAA,CAAAkB,WAAA,kBAAAlB,QAAA,CAAAkB,WAAA,CAAAtpB,SAAA,0BACF;IAMK1B,uDAAA,GAAoD;IAApDA,wDAAA,UAAA8pB,QAAA,CAAAkB,WAAA,kBAAAlB,QAAA,CAAAkB,WAAA,CAAAhpB,MAAA,kBAAA8nB,QAAA,CAAAkB,WAAA,CAAAhpB,MAAA,CAAAxE,EAAA,MAAAitB,OAAA,CAAA7nB,aAAA,CAAoD;IAIvD5C,uDAAA,GACF;IADEA,gEAAA,OAAA8pB,QAAA,CAAAkB,WAAA,kBAAAlB,QAAA,CAAAkB,WAAA,CAAAvoB,OAAA,mCACF;IAEGzC,uDAAA,GAA8C;IAA9CA,wDAAA,SAAA8pB,QAAA,CAAAC,WAAA,IAAAD,QAAA,CAAAC,WAAA,KAA8C;;;;;IA5D3D/pB,4DAAA,aAGC;IACCA,wDAAA,IAAAirB,yCAAA,mBAgEK;IACPjrB,0DAAA,EAAK;;;;IAhEgBA,uDAAA,GAAwB;IAAxBA,wDAAA,YAAAkrB,MAAA,CAAAC,qBAAA,CAAwB;;;AD5J7C,MAAOC,qBAAqB;EAchCzuB,YACUsN,cAA8B,EAC9BohB,WAA4B,EAC5BrhB,MAAc,EACdD,KAAqB,EACrBI,YAA0B,EAC1BmhB,MAAqB,EACrBC,YAA0B;IAN1B,KAAAthB,cAAc,GAAdA,cAAc;IACd,KAAAohB,WAAW,GAAXA,WAAW;IACX,KAAArhB,MAAM,GAANA,MAAM;IACN,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAI,YAAY,GAAZA,YAAY;IACZ,KAAAmhB,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IApBtB,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAL,qBAAqB,GAAmB,EAAE;IAC1C,KAAAM,OAAO,GAAG,IAAI;IAEd,KAAA7oB,aAAa,GAAkB,IAAI;IACnC,KAAAiI,WAAW,GAAG,EAAE;IAChB,KAAA6f,sBAAsB,GAAkB,IAAI;IAGpC,KAAAX,WAAW,GAAG,IAAIttB,iDAAe,CAAS,CAAC,CAAC;IAC7C,KAAAivB,YAAY,GAAG,IAAI,CAAC3B,WAAW,CAACjtB,YAAY,EAAE;IAC7C,KAAAkQ,aAAa,GAAmB,EAAE;IAWxC,IAAI,CAAC2e,WAAW,GAAG,IAAI,CAACJ,YAAY,CAACK,SAAS;EAChD;EAEAje,QAAQA,CAAA;IACN,IAAI,CAAC/K,aAAa,GAAG,IAAI,CAACyoB,WAAW,CAACQ,gBAAgB,EAAE;IACxD,IAAI,CAAC,IAAI,CAACjpB,aAAa,EAAE;MACvB,IAAI,CAACkpB,WAAW,CAAC,wBAAwB,CAAC;MAC1C;;IAGF,IAAI,CAACrC,iBAAiB,EAAE;IACxB,IAAI,CAACsC,qBAAqB,EAAE;IAC5B,IAAI,CAACrY,8BAA8B,EAAE;IAErC;IACA,IAAI,CAAC3J,KAAK,CAACiiB,UAAU,EAAEC,MAAM,CAACrb,SAAS,CAAEqb,MAAM,IAAI;MACjD,IAAI,CAACvB,sBAAsB,GAAGuB,MAAM,CAAC,gBAAgB,CAAC,IAAI,IAAI;IAChE,CAAC,CAAC;EACJ;EAEAxC,iBAAiBA,CAAA;IACf,IAAI,CAACgC,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC7sB,KAAK,GAAG,IAAI;IAEjB,MAAMstB,GAAG,GAAG,IAAI,CAACjiB,cAAc,CAACkiB,gBAAgB,EAAE,CAACvb,SAAS,CAAC;MAC3DhT,IAAI,EAAG4tB,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAG/I,KAAK,CAAC2J,OAAO,CAACZ,aAAa,CAAC,GAC7C,CAAC,GAAGA,aAAa,CAAC,GAClB,EAAE;QAEN,IAAI,CAACa,mBAAmB,EAAE;QAC1B,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACd,OAAO,GAAG,KAAK;MACtB,CAAC;MACD7sB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAC6sB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACthB,YAAY,CAAC9L,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;IACF,IAAI,CAAC2O,aAAa,CAACmG,IAAI,CAAC+Y,GAAG,CAAC;EAC9B;EAEAG,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACxhB,WAAW,EAAE;MACrB,IAAI,CAACsgB,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAACK,aAAa,CAAC;MACpD;;IAGF,MAAMgB,KAAK,GAAG,IAAI,CAAC3hB,WAAW,CAAC8R,WAAW,EAAE;IAC5C,IAAI,CAACwO,qBAAqB,GAAG,IAAI,CAACK,aAAa,CAAChtB,MAAM,CAAEiuB,IAAI,IAAI;MAC9D,MAAMjsB,gBAAgB,GAAGisB,IAAI,CAACzb,YAAY,GACtC,IAAI,CAAC4Z,mBAAmB,CAAC6B,IAAI,CAACzb,YAAY,CAAC,GAC3CiD,SAAS;MACb,OACEzT,gBAAgB,EAAEc,QAAQ,CAACqb,WAAW,EAAE,CAACf,QAAQ,CAAC4Q,KAAK,CAAC,IACxDC,IAAI,CAACzB,WAAW,EAAEvoB,OAAO,EAAEka,WAAW,EAAE,CAACf,QAAQ,CAAC4Q,KAAK,CAAC,IACxD,KAAK;IAET,CAAC,CAAC;EACJ;EAEQF,iBAAiBA,CAAA;IACvB,MAAMI,KAAK,GAAG,IAAI,CAAClB,aAAa,CAAC3G,MAAM,CACrC,CAAC8H,GAAG,EAAEF,IAAI,KAAKE,GAAG,IAAIF,IAAI,CAAC1C,WAAW,IAAI,CAAC,CAAC,EAC5C,CAAC,CACF;IACD,IAAI,CAACA,WAAW,CAACnsB,IAAI,CAAC8uB,KAAK,CAAC;EAC9B;EAEAH,iBAAiBA,CAAA;IACf,IAAI,CAACf,aAAa,CAACha,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACib,mBAAmB,CAACnb,CAAC,CAAC;MACzC,MAAMM,KAAK,GAAG,IAAI,CAAC6a,mBAAmB,CAAClb,CAAC,CAAC;MACzC,OAAOK,KAAK,CAACD,OAAO,EAAE,GAAGH,KAAK,CAACG,OAAO,EAAE;IAC1C,CAAC,CAAC;IACF,IAAI,CAACua,mBAAmB,EAAE;EAC5B;EAEQO,mBAAmBA,CAACH,IAAkB;IAC5C;IACA,MAAMI,WAAW,GAAG,IAAIjb,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjC,IAAI6a,IAAI,CAACzB,WAAW,EAAEtpB,SAAS,EAAE;MAC/B,OAAO,OAAO+qB,IAAI,CAACzB,WAAW,CAACtpB,SAAS,KAAK,QAAQ,GACjD,IAAIkQ,IAAI,CAAC6a,IAAI,CAACzB,WAAW,CAACtpB,SAAS,CAAC,GACpC+qB,IAAI,CAACzB,WAAW,CAACtpB,SAAS;;IAGhC,IAAI+qB,IAAI,CAACK,SAAS,EAAE;MAClB,OAAO,OAAOL,IAAI,CAACK,SAAS,KAAK,QAAQ,GACrC,IAAIlb,IAAI,CAAC6a,IAAI,CAACK,SAAS,CAAC,GACxBL,IAAI,CAACK,SAAS;;IAGpB,IAAIL,IAAI,CAAC5a,SAAS,EAAE;MAClB,OAAO,OAAO4a,IAAI,CAAC5a,SAAS,KAAK,QAAQ,GACrC,IAAID,IAAI,CAAC6a,IAAI,CAAC5a,SAAS,CAAC,GACxB4a,IAAI,CAAC5a,SAAS;;IAGpB,OAAOgb,WAAW;EACpB;EAEAjC,mBAAmBA,CAAC5Z,YAAgC;IAClD,IAAI,CAACA,YAAY,IAAI,CAACyR,KAAK,CAAC2J,OAAO,CAACpb,YAAY,CAAC,EAAE;MACjD,OAAOiD,SAAS;;IAElB,OAAOjD,YAAY,CAACG,IAAI,CACrBC,CAAC,IAAKA,CAAC,CAACd,GAAG,KAAK,IAAI,CAAC1N,aAAa,IAAIwO,CAAC,CAAC5T,EAAE,KAAK,IAAI,CAACoF,aAAa,CACnE;EACH;EAEAmpB,qBAAqBA,CAAA;IACnB,MAAMG,GAAG,GAAG,IAAI,CAACjiB,cAAc,CAAC8hB,qBAAqB,EAAE,CACpDgB,IAAI,CAAC/L,yCAAG,CAAE9Q,IAAI,IAAK,IAAI,CAACjG,cAAc,CAAC+iB,aAAa,CAAC9c,IAAI,CAAC,CAAC,CAAC,CAC5DU,SAAS,CAAC;MACThT,IAAI,EAAGsS,IAAU,IAAI;QACnB,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC+c,gBAAgB,CAAC/c,IAAI,CAAC;;MAE/B,CAAC;MACDtR,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;IACJ,IAAI,CAAC2O,aAAa,CAACmG,IAAI,CAAC+Y,GAAG,CAAC;EAC9B;EAEAxY,8BAA8BA,CAAA;IAC5B,MAAMwY,GAAG,GAAG,IAAI,CAACjiB,cAAc,CAACyJ,8BAA8B,CAC5D,QAAQ,CACT,CAAC9C,SAAS,CAAC;MACVhT,IAAI,EAAGsvB,WAAW,IAAI;QACpB,MAAM7U,KAAK,GAAG,IAAI,CAACmT,aAAa,CAAClU,SAAS,CACvC6V,CAAC,IAAKA,CAAC,CAAC3vB,EAAE,KAAK0vB,WAAW,CAAC1vB,EAAE,CAC/B;QACD,IAAI6a,KAAK,IAAI,CAAC,EAAE;UACd,IAAI,CAACmT,aAAa,CAACnT,KAAK,CAAC,GAAG6U,WAAW;SACxC,MAAM;UACL,IAAI,CAAC1B,aAAa,CAAC4B,OAAO,CAACF,WAAW,CAAC;;QAEzC,IAAI,CAACX,iBAAiB,EAAE;MAC1B,CAAC;MACD3tB,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;IACF,IAAI,CAACoO,aAAa,CAACmG,IAAI,CAAC+Y,GAAG,CAAC;EAC9B;EAEAe,gBAAgBA,CAACI,WAAiB;IAChC,IAAI,CAAC7B,aAAa,GAAG,IAAI,CAACA,aAAa,CAACxK,GAAG,CAAEyL,IAAI,IAAI;MACnD,IAAI,CAACA,IAAI,CAACzb,YAAY,EAAE;QACtB,OAAOyb,IAAI;;MAEb,MAAMzb,YAAY,GAAGyb,IAAI,CAACzb,YAAY,CAACgQ,GAAG,CAAE5P,CAAC,IAAI;QAC/C,MAAMkc,aAAa,GACjBlc,CAAC,CAACd,GAAG,KAAK+c,WAAW,CAAC/c,GAAG,IAAIc,CAAC,CAAC5T,EAAE,KAAK6vB,WAAW,CAAC/c,GAAG;QACvD,OAAOgd,aAAa,GAChB;UACE,GAAGlc,CAAC;UACJ3Q,QAAQ,EAAE4sB,WAAW,CAAC5sB,QAAQ;UAC9BE,UAAU,EAAE0sB,WAAW,CAAC1sB;SACzB,GACDyQ,CAAC;MACP,CAAC,CAAC;MACF,OAAO;QAAE,GAAGqb,IAAI;QAAEzb;MAAY,CAAE;IAClC,CAAC,CAAC;IACF,IAAI,CAACqb,mBAAmB,EAAE;EAC5B;EAEAlC,gBAAgBA,CAAC5Z,cAAkC;IACjD,IAAI,CAACA,cAAc,EAAE;MACnB;;IAGF,IAAI,CAACma,sBAAsB,GAAGna,cAAc;IAC5C,IAAI,CAACvG,MAAM,CAACsM,QAAQ,CAAC,CAAC,MAAM,EAAE/F,cAAc,CAAC,EAAE;MAAEgd,UAAU,EAAE,IAAI,CAACxjB;IAAK,CAAE,CAAC;EAC5E;EAEA8f,oBAAoBA,CAAA;IAClB,IAAI,CAAC7f,MAAM,CAACsM,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA5V,gBAAgBA,CAACC,UAAkB;IACjC,OAAO,IAAI,CAACsJ,cAAc,CAACvJ,gBAAgB,CAACC,UAAU,CAAC;EACzD;EAEQmrB,WAAWA,CAAC9tB,OAAe,EAAEY,KAAW;IAC9C,IAAI,CAAC0sB,MAAM,CAAC1sB,KAAK,CAAC,uBAAuB,EAAEZ,OAAO,EAAEY,KAAK,CAAC;IAC1D,IAAI,CAACA,KAAK,GAAGZ,OAAO;IACpB,IAAI,CAACytB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACthB,YAAY,CAAC9L,SAAS,CAACL,OAAO,CAAC;EACtC;EAEA8nB,WAAWA,CAAA;IACT,IAAI,CAAC9Y,aAAa,CAACkT,OAAO,CAAEgM,GAAG,IAAKA,GAAG,CAACzZ,WAAW,EAAE,CAAC;EACxD;;;uBA9NW2Y,qBAAqB,EAAAprB,+DAAA,CAAAgmB,yEAAA,GAAAhmB,+DAAA,CAAAkmB,8EAAA,GAAAlmB,+DAAA,CAAAqmB,mDAAA,GAAArmB,+DAAA,CAAAqmB,2DAAA,GAAArmB,+DAAA,CAAAsmB,wEAAA,GAAAtmB,+DAAA,CAAAwmB,0EAAA,GAAAxmB,+DAAA,CAAA0tB,qEAAA;IAAA;EAAA;;;YAArBtC,qBAAqB;MAAA1E,SAAA;MAAAkH,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAnH,EAAA,EAAAhE,GAAA;QAAA,IAAAgE,EAAA;UCflC7mB,4DAAA,aAGC;;UAECA,4DAAA,aAAkE;UAChEA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAKVA,4DAAA,cAEC;UAIGA,uDAAA,cAEO;UAKPA,4DAAA,eAAoD;UACHA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAK;UAC5DA,4DAAA,eAAyC;UAErCA,wDAAA,mBAAAiuB,wDAAA;YAAA,OAASpL,GAAA,CAAAgH,oBAAA,EAAsB;UAAA,EAAC;UAIhC7pB,uDAAA,eAEO;UAITA,0DAAA,EAAS;UACTA,wDAAA,KAAAkuB,qCAAA,kBAQM;;UACRluB,0DAAA,EAAM;UAIRA,4DAAA,eAA4B;UAExBA,wDAAA,2BAAAmuB,+DAAAjrB,MAAA;YAAA,OAAA2f,GAAA,CAAAhY,WAAA,GAAA3H,MAAA;UAAA,EAAyB,2BAAAirB,+DAAA;YAAA,OACRtL,GAAA,CAAAwJ,mBAAA,EAAqB;UAAA,EADb;UAD3BrsB,0DAAA,EAME;UACFA,4DAAA,eAEC;UACCA,uDAAA,aAEK;UACPA,0DAAA,EAAM;UACNA,4DAAA,eAEC;UACCA,uDAAA,eAEO;UACTA,0DAAA,EAAM;UAKVA,4DAAA,eAAkE;UAEhEA,wDAAA,KAAAouB,qCAAA,kBAMM;UAGNpuB,wDAAA,KAAAquB,qCAAA,mBAeM;UAGNruB,wDAAA,KAAAsuB,qCAAA,mBAqBM;UAGNtuB,wDAAA,KAAAuuB,qCAAA,kBAWM;UAGNvuB,wDAAA,KAAAwuB,oCAAA,iBAqEK;UACPxuB,0DAAA,EAAM;UAIRA,4DAAA,eAAiE;UAE/DA,uDAAA,qBAA+B;UACjCA,0DAAA,EAAM;;;UAjPNA,yDAAA,SAAAA,yDAAA,OAAA6iB,GAAA,CAAA8I,WAAA,EAAkC;UA0DpB3rB,uDAAA,IAA2B;UAA3BA,wDAAA,SAAAA,yDAAA,SAAA6iB,GAAA,CAAA6I,YAAA,EAA2B;UAejC1rB,uDAAA,GAAyB;UAAzBA,wDAAA,YAAA6iB,GAAA,CAAAhY,WAAA,CAAyB;UA2B1B7K,uDAAA,GAAa;UAAbA,wDAAA,SAAA6iB,GAAA,CAAA4I,OAAA,CAAa;UAQVzrB,uDAAA,GAAW;UAAXA,wDAAA,SAAA6iB,GAAA,CAAAjkB,KAAA,CAAW;UAmBdoB,uDAAA,GAAoE;UAApEA,wDAAA,UAAA6iB,GAAA,CAAA4I,OAAA,IAAA5I,GAAA,CAAAsI,qBAAA,CAAAla,MAAA,WAAA4R,GAAA,CAAAhY,WAAA,CAAoE;UAwBpE7K,uDAAA,GAAmE;UAAnEA,wDAAA,UAAA6iB,GAAA,CAAA4I,OAAA,IAAA5I,GAAA,CAAAsI,qBAAA,CAAAla,MAAA,UAAA4R,GAAA,CAAAhY,WAAA,CAAmE;UAcnE7K,uDAAA,GAAkD;UAAlDA,wDAAA,UAAA6iB,GAAA,CAAA4I,OAAA,IAAA5I,GAAA,CAAAsI,qBAAA,CAAAla,MAAA,KAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtKJ;AACsB;AACG;AACZ;AACe;;;AAEnF,MAAM6d,MAAM,GAAW,CACrB;EACEvT,IAAI,EAAE,EAAE;EACRwT,SAAS,EAAEF,4FAAsB;EACjCG,QAAQ,EAAE,CACR;IAAEzT,IAAI,EAAE,EAAE;IAAE0T,UAAU,EAAE,eAAe;IAAEC,SAAS,EAAE;EAAM,CAAE,EAC5D;IACE3T,IAAI,EAAE,eAAe;IACrBwT,SAAS,EAAE3D,yFAAqB;IAChCrW,IAAI,EAAE;MAAE7W,KAAK,EAAE;IAAe;GAC/B,EACD;IACEqd,IAAI,EAAE,wBAAwB;IAC9BwT,SAAS,EAAEllB,sFAAoB;IAC/BkL,IAAI,EAAE;MAAE7W,KAAK,EAAE;IAAM;GACtB,EACD;IACEqd,IAAI,EAAE,UAAU;IAChBwT,SAAS,EAAEllB,sFAAoB;IAC/BkL,IAAI,EAAE;MAAE7W,KAAK,EAAE;IAAM;GACtB,EACD;IACEqd,IAAI,EAAE,OAAO;IACbwT,SAAS,EAAEH,6EAAiB;IAC5B7Z,IAAI,EAAE;MAAE7W,KAAK,EAAE;IAAc;GAC9B,EAED;IACEqd,IAAI,EAAE,KAAK;IACX0T,UAAU,EAAE,OAAO;IACnBC,SAAS,EAAE;GACZ;CAEJ,CACF;AAMK,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBR,yDAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,yDAAY;IAAA;EAAA;;;sHAEXQ,qBAAqB;IAAAE,OAAA,GAAArJ,yDAAA;IAAAsJ,OAAA,GAFtBX,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5CuB;AACA;AAEmB;AACA;AACpB;AAC+B;AACG;AACZ;AACe;AAEV;AACP;AACyB;;AAoBrF,MAAOkB,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACF,mFAAiB,EAAE1lB,4EAAc,CAAC;MAAAolB,OAAA,GAR5CE,yDAAY,EACZJ,2EAAqB,EACrBK,wDAAW,EACXC,gEAAmB,EACnBC,yDAAY,EACZf,0DAAY,EACZiB,qGAAkB;IAAA;EAAA;;;sHAITC,cAAc;IAAAC,YAAA,GAhBvBjmB,sFAAoB,EACpBuhB,yFAAqB,EACrBwD,6EAAiB,EACjBC,4FAAsB;IAAAQ,OAAA,GAGtBE,yDAAY,EACZJ,2EAAqB,EACrBK,wDAAW,EACXC,gEAAmB,EACnBC,yDAAY,EACZf,0DAAY,EACZiB,qGAAkB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5BoC;AAOI;AAEN;;;;;;;;;;;;;ICyIlD5vB,4DAAA,cAGC;IAEIA,oDAAA,GAC4B;IAAAA,0DAAA,EAC9B;IACDA,4DAAA,WAAM;IAAAA,oDAAA,GAA2C;IAAAA,0DAAA,EAAO;;;;IAHrDA,uDAAA,GAC4B;IAD5BA,gEAAA,kBAAAmwB,MAAA,CAAAC,KAAA,CAAAnf,MAAA,WAAAkf,MAAA,CAAAE,UAAA,kBAC4B;IAEzBrwB,uDAAA,GAA2C;IAA3CA,gEAAA,UAAAmwB,MAAA,CAAAvjB,WAAA,WAAAujB,MAAA,CAAAG,UAAA,KAA2C;;;;;IAcrDtwB,4DAAA,cAA2E;IACzEA,uDAAA,cAA6C;IAC7CA,4DAAA,cAAqC;IAAAA,oDAAA,qCAA8B;IAAAA,0DAAA,EAAM;;;;;IAI3EA,4DAAA,cAA2E;IAEvEA,uDAAA,YAA4B;IAC9BA,0DAAA,EAAM;IACNA,4DAAA,aAAmC;IAAAA,oDAAA,oCAAwB;IAAAA,0DAAA,EAAK;IAChEA,4DAAA,YAAiC;IAC/BA,oDAAA,mEACF;IAAAA,0DAAA,EAAI;;;;;IAeEA,uDAAA,eAGQ;;;;;;IAYVA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAAuwB,wEAAA;MAAAvwB,2DAAA,CAAAwwB,IAAA;MAAA,MAAAC,OAAA,GAAAzwB,2DAAA,GAAA6B,SAAA;MAAA,MAAA4oB,OAAA,GAAAzqB,2DAAA;MAAA,OAASA,yDAAA,CAAAyqB,OAAA,CAAAiG,cAAA,CAAAD,OAAA,CAAAjzB,EAAA,IAAAizB,OAAA,CAAAngB,GAAA,CAAmC;IAAA,EAAC;IAI7CtQ,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IACTA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAA2wB,wEAAA;MAAA3wB,2DAAA,CAAA4wB,IAAA;MAAA,MAAAH,OAAA,GAAAzwB,2DAAA,GAAA6B,SAAA;MAAA,MAAAkG,OAAA,GAAA/H,2DAAA;MAAA,OAASA,yDAAA,CAAA+H,OAAA,CAAAiN,cAAA,CAAAyb,OAAA,CAAAjzB,EAAA,IAAAizB,OAAA,CAAAngB,GAAA,CAAmC;IAAA,EAAC;IAI7CtQ,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IAxCbA,4DAAA,aAA4D;IAGxDA,wDAAA,mBAAA6wB,2DAAA;MAAA,MAAArrB,WAAA,GAAAxF,2DAAA,CAAAiqB,IAAA;MAAA,MAAAwG,OAAA,GAAAjrB,WAAA,CAAA3D,SAAA;MAAA,MAAAqoB,OAAA,GAAAlqB,2DAAA;MAAA,OAASA,yDAAA,CAAAkqB,OAAA,CAAA4G,iBAAA,CAAAL,OAAA,CAAAjzB,EAAA,IAAAizB,OAAA,CAAAngB,GAAA,CAAsC;IAAA,EAAC;IAEhDtQ,4DAAA,cAA+B;IAC7BA,uDAAA,cAGE;IACFA,wDAAA,IAAA+wB,4CAAA,mBAGQ;IACV/wB,0DAAA,EAAM;IACNA,4DAAA,cAAkC;IAE9BA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAiC;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAI;IAKzDA,4DAAA,eAAqC;IACnCA,wDAAA,KAAAgxB,+CAAA,qBAOS;IACThxB,wDAAA,KAAAixB,+CAAA,qBAOS;IACXjxB,0DAAA,EAAM;;;;IAlCAA,uDAAA,GAAwD;IAAxDA,wDAAA,QAAAywB,OAAA,CAAAvuB,KAAA,wCAAAlC,2DAAA,CAAwD;IAIvDA,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAywB,OAAA,CAAAhwB,QAAA,CAAmB;IAMpBT,uDAAA,GACF;IADEA,gEAAA,MAAAywB,OAAA,CAAAnvB,QAAA,MACF;IACiCtB,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAywB,OAAA,CAAAS,KAAA,CAAgB;IAOhDlxB,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAywB,OAAA,CAAAhwB,QAAA,CAAmB;IAQnBT,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAywB,OAAA,CAAAhwB,QAAA,CAAmB;;;;;IAnC5BT,4DAAA,aAA2D;IACzDA,wDAAA,IAAAmxB,qCAAA,kBA0CK;IACPnxB,0DAAA,EAAK;;;;IA3CkBA,uDAAA,GAAQ;IAARA,wDAAA,YAAAoxB,MAAA,CAAAhB,KAAA,CAAQ;;;;;IA8C/BpwB,4DAAA,cAAyE;IAErEA,uDAAA,cAAsE;IAGxEA,0DAAA,EAAM;IACNA,4DAAA,cAAqC;IACnCA,oDAAA,6CACF;IAAAA,0DAAA,EAAM;;;;;;IAIRA,4DAAA,cAA4E;IAClEA,wDAAA,mBAAAqxB,0DAAA;MAAArxB,2DAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAAhB,2DAAA;MAAA,OAASA,yDAAA,CAAAgB,OAAA,CAAAswB,YAAA,EAAc;IAAA,EAAC;IAC9BtxB,uDAAA,YAAwC;IACxCA,oDAAA,oCACF;IAAAA,0DAAA,EAAS;;;ADvOT,MAAO4uB,iBAAiB;EA8B5BjyB,YACUsN,cAA8B,EAC9BC,WAAwB,EACzBF,MAAc,EACdD,KAAqB,EACpBshB,WAA4B,EAC5BlhB,YAA0B,EAC1BmhB,MAAqB,EACrBC,YAA0B;IAP1B,KAAAthB,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAD,KAAK,GAALA,KAAK;IACJ,KAAAshB,WAAW,GAAXA,WAAW;IACX,KAAAlhB,YAAY,GAAZA,YAAY;IACZ,KAAAmhB,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IArCtB,KAAA6E,KAAK,GAAW,EAAE;IAClB,KAAA3E,OAAO,GAAG,IAAI;IACd,KAAA7oB,aAAa,GAAkB,IAAI;IAGnC;IACA,KAAAgK,WAAW,GAAG,CAAC;IACf,KAAA2kB,QAAQ,GAAG,EAAE;IACb,KAAAlB,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAkB,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,IAAI3B,qDAAS,CAAC;MACzBplB,WAAW,EAAE,IAAImlB,uDAAW,CAAC,EAAE,CAAC;MAChCvvB,QAAQ,EAAE,IAAIuvB,uDAAW,CAAiB,IAAI;KAC/C,CAAC;IAEF;IACA,KAAA6B,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAGrB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAA/kB,aAAa,GAAiB,IAAInN,8CAAY,EAAE;IAYtD,IAAI,CAAC8rB,WAAW,GAAG,IAAI,CAACJ,YAAY,CAACK,SAAS;EAChD;EAEAje,QAAQA,CAAA;IACN,IAAI,CAAC/K,aAAa,GAAG,IAAI,CAACyoB,WAAW,CAACQ,gBAAgB,EAAE;IACxD,IAAI,CAACmG,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEQF,oBAAoBA,CAAA;IAC1B;IACA,MAAMG,SAAS,GAAG,IAAI,CAACP,UAAU,CAC9BpkB,GAAG,CAAC,aAAa,CAAE,CACnB4kB,YAAY,CAACxhB,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACyhB,eAAe,EAAE;MACtB,IAAI,CAACH,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAACllB,aAAa,CAAC4F,GAAG,CAACuf,SAAS,CAAC;IAEjC;IACA,MAAMG,SAAS,GAAG,IAAI,CAACV,UAAU,CAC9BpkB,GAAG,CAAC,UAAU,CAAE,CAChB4kB,YAAY,CAACxhB,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACyhB,eAAe,EAAE;MACtB,IAAI,CAACH,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAACllB,aAAa,CAAC4F,GAAG,CAAC0f,SAAS,CAAC;EACnC;EAEQL,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACJ,kBAAkB,EAAE;MAC3B,IAAI,CAACU,uBAAuB,GAAGxC,+CAAQ,CACrC,IAAI,CAAC+B,mBAAmB,CACzB,CAAClhB,SAAS,CAAC,MAAK;QACf,IAAI,CAAC,IAAI,CAAC6a,OAAO,IAAI,CAAC,IAAI,CAACmG,UAAU,CAACpkB,GAAG,CAAC,aAAa,CAAC,EAAE7P,KAAK,EAAE;UAC/D,IAAI,CAACu0B,SAAS,CAAC,IAAI,CAAC;;MAExB,CAAC,CAAC;;EAEN;EAEAM,iBAAiBA,CAAA;IACf,IAAI,CAACX,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAElD,IAAI,IAAI,CAACA,kBAAkB,EAAE;MAC3B,IAAI,CAACI,gBAAgB,EAAE;KACxB,MAAM,IAAI,IAAI,CAACM,uBAAuB,EAAE;MACvC,IAAI,CAACA,uBAAuB,CAAC9f,WAAW,EAAE;MAC1C,IAAI,CAAC8f,uBAAuB,GAAGte,SAAS;;EAE5C;EAEAoe,eAAeA,CAAA;IACb,IAAI,CAACzlB,WAAW,GAAG,CAAC;EACtB;EAEA;EACA,IAAI/B,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC+mB,UAAU,CAACpkB,GAAG,CAAC,aAAa,CAAC,EAAE7P,KAAK,IAAI,EAAE;EACxD;EAEA;EACA,IAAIkN,WAAWA,CAAClN,KAAa;IAC3B,IAAI,CAACi0B,UAAU,CAACpkB,GAAG,CAAC,aAAa,CAAC,EAAEilB,QAAQ,CAAC90B,KAAK,CAAC;EACrD;EAEA;EACA+0B,IAAIA,CAACC,IAAS;IACZ,OAAOA,IAAI;EACb;EAEAT,SAASA,CAACU,YAAY,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACb,WAAW,EAAE;IAEtB,IAAI,CAACtG,OAAO,GAAG,IAAI;IAEnB,MAAM5gB,WAAW,GAAG,IAAI,CAAC+mB,UAAU,CAACpkB,GAAG,CAAC,aAAa,CAAC,EAAE7P,KAAK,IAAI,EAAE;IACnE,MAAM8C,QAAQ,GAAG,IAAI,CAACmxB,UAAU,CAACpkB,GAAG,CAAC,UAAU,CAAC,EAAE7P,KAAK;IAEvD,MAAMuuB,GAAG,GAAG,IAAI,CAACjiB,cAAc,CAAC4oB,WAAW,CACzCD,YAAY,EACZ/nB,WAAW,EACX,IAAI,CAAC+B,WAAW,EAChB,IAAI,CAAC2kB,QAAQ,EACb,IAAI,CAACG,MAAM,EACX,IAAI,CAACC,SAAS,EACdlxB,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAGwT,SAAS,CACrC,CAACrD,SAAS,CAAC;MACVhT,IAAI,EAAGwyB,KAAK,IAAI;QACd,IAAI,CAAC3N,KAAK,CAAC2J,OAAO,CAACgE,KAAK,CAAC,EAAE;UACzB,IAAI,CAACA,KAAK,GAAG,EAAE;UACf,IAAI,CAAC3E,OAAO,GAAG,KAAK;UACpB,IAAI,CAACsG,WAAW,GAAG,KAAK;UACxB,IAAI,CAAC5nB,YAAY,CAAC9L,SAAS,CAAC,oCAAoC,CAAC;UACjE;;QAGF;QACA,IAAI,IAAI,CAACuO,WAAW,KAAK,CAAC,EAAE;UAC1B;UACA,IAAI,CAACwjB,KAAK,GAAGA,KAAK,CAAC5xB,MAAM,CAAE0R,IAAI,IAAI;YACjC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;YACvB,MAAMG,MAAM,GAAGH,IAAI,CAAC1S,EAAE,IAAI0S,IAAI,CAACI,GAAG;YAClC,OAAOD,MAAM,KAAK,IAAI,CAACzN,aAAa;UACtC,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAMkwB,QAAQ,GAAG1C,KAAK,CAAC5xB,MAAM,CAAEu0B,OAAO,IAAI;YACxC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;YAC1B,MAAM1iB,MAAM,GAAG0iB,OAAO,CAACv1B,EAAE,IAAIu1B,OAAO,CAACziB,GAAG;YACxC,OACED,MAAM,KAAK,IAAI,CAACzN,aAAa,IAC7B,CAAC,IAAI,CAACwtB,KAAK,CAACnd,IAAI,CACb+f,YAAY,IACX,CAACA,YAAY,CAACx1B,EAAE,IAAIw1B,YAAY,CAAC1iB,GAAG,MAAMD,MAAM,CACnD;UAEL,CAAC,CAAC;UAEF,IAAI,CAAC+f,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAG0C,QAAQ,CAAC;;QAG3C;QACA,MAAMG,UAAU,GAAG,IAAI,CAAChpB,cAAc,CAACipB,qBAAqB;QAC5D,IAAI,CAAC7C,UAAU,GAAG4C,UAAU,CAACE,UAAU;QACvC,IAAI,CAAC7C,UAAU,GAAG2C,UAAU,CAAC3C,UAAU;QACvC,IAAI,CAACkB,WAAW,GAAGyB,UAAU,CAACzB,WAAW;QACzC,IAAI,CAACC,eAAe,GAAGwB,UAAU,CAACxB,eAAe;QAEjD,IAAI,CAAChG,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsG,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDnzB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC6sB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsG,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC5nB,YAAY,CAAC9L,SAAS,CACzB,yBAAyBO,KAAK,CAACZ,OAAO,IAAI,eAAe,EAAE,CAC5D;QAED,IAAI,IAAI,CAAC4O,WAAW,KAAK,CAAC,EAAE;UAC1B,IAAI,CAACwjB,KAAK,GAAG,EAAE;;MAEnB,CAAC;MACDgD,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC3H,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsG,WAAW,GAAG,KAAK;MAC1B;KACD,CAAC;IAEF,IAAI,CAAC/kB,aAAa,CAAC4F,GAAG,CAACsZ,GAAG,CAAC;EAC7B;EAEA4E,iBAAiBA,CAACzgB,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAClG,YAAY,CAAC9L,SAAS,CACzB,+CAA+C,CAChD;MACD;;IAGF,IAAI,CAAC8L,YAAY,CAAC5L,QAAQ,CAAC,0BAA0B,CAAC;IAEtD,IAAI,CAAC0L,cAAc,CAACopB,kBAAkB,CAAChjB,MAAM,CAAC,CAACO,SAAS,CAAC;MACvDhT,IAAI,EAAGyM,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAAC7M,EAAE,EAAE;UACrC,IAAI,CAAC2M,YAAY,CAAC9L,SAAS,CACzB,iDAAiD,CAClD;UACD;;QAGF,IAAI,CAAC2L,MAAM,CACRsM,QAAQ,CAAC,CAAC,8BAA8B,EAAEjM,YAAY,CAAC7M,EAAE,CAAC,CAAC,CAC3D+Y,IAAI,CAAE7X,OAAO,IAAI;UAChB,IAAI,CAACA,OAAO,EAAE;YACZ,IAAI,CAACyL,YAAY,CAAC9L,SAAS,CAAC,6BAA6B,CAAC;;QAE9D,CAAC,CAAC;MACN,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CACzB,kCAAkCO,KAAK,CAACZ,OAAO,IAAI,eAAe,EAAE,CACrE;MACH;KACD,CAAC;EACJ;EAEA0yB,cAAcA,CAACrgB,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACnG,WAAW,CAAC+K,YAAY,CAAC5E,MAAM,EAAEvQ,kEAAQ,CAACsV,KAAK,CAAC,CAACxE,SAAS,CAAC;MAC9DhT,IAAI,EAAG2f,IAAU,IAAI;QACnB,IAAI,CAACpT,YAAY,CAAC/L,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDQ,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEA2W,cAAcA,CAAC3E,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACnG,WAAW,CAAC+K,YAAY,CAAC5E,MAAM,EAAEvQ,kEAAQ,CAACoV,KAAK,CAAC,CAACtE,SAAS,CAAC;MAC9DhT,IAAI,EAAG2f,IAAU,IAAI;QACnB,IAAI,CAACpT,YAAY,CAAC/L,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDQ,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACuL,YAAY,CAAC9L,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEAizB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACE,WAAW,IAAI,CAAC,IAAI,CAAC/F,OAAO,EAAE;MACrC,IAAI,CAACsG,WAAW,GAAG,IAAI;MACvB,IAAI,CAACnlB,WAAW,EAAE;MAClB,IAAI,CAACslB,SAAS,EAAE;;EAEpB;EAEAoB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC7B,eAAe,IAAI,CAAC,IAAI,CAAChG,OAAO,EAAE;MACzC,IAAI,CAACsG,WAAW,GAAG,IAAI;MACvB,IAAI,CAACnlB,WAAW,EAAE;MAClB,IAAI,CAACslB,SAAS,EAAE;;EAEpB;EAEAqB,YAAYA,CAAA;IACV,IAAI,CAAClB,eAAe,EAAE;IACtB,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC;EACtB;EAEAsB,YAAYA,CAAA;IACV,IAAI,CAAC5B,UAAU,CAAC1d,KAAK,CAAC;MACpBrJ,WAAW,EAAE,EAAE;MACfpK,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAAC4xB,eAAe,EAAE;IACtB,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC;EACtB;EAEAuB,eAAeA,CAACC,KAAa;IAC3B,IAAI,IAAI,CAAChC,MAAM,KAAKgC,KAAK,EAAE;MACzB;MACA,IAAI,CAAC/B,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KAC3D,MAAM;MACL;MACA,IAAI,CAACD,MAAM,GAAGgC,KAAK;MACnB,IAAI,CAAC/B,SAAS,GAAG,KAAK;;IAGxB,IAAI,CAACU,eAAe,EAAE;IACtB,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA;;;EAGA7b,qBAAqBA,CAAA;IACnB,IAAI,CAACrM,MAAM,CAACsM,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEAwP,WAAWA,CAAA;IACT,IAAI,CAAC9Y,aAAa,CAACyF,WAAW,EAAE;IAChC,IAAI,IAAI,CAAC8f,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAAC9f,WAAW,EAAE;;EAE9C;;;uBAxTWmc,iBAAiB,EAAA5uB,+DAAA,CAAAgmB,4EAAA,GAAAhmB,+DAAA,CAAAkmB,sEAAA,GAAAlmB,+DAAA,CAAAqmB,oDAAA,GAAArmB,+DAAA,CAAAqmB,4DAAA,GAAArmB,+DAAA,CAAAsmB,8EAAA,GAAAtmB,+DAAA,CAAAwmB,wEAAA,GAAAxmB,+DAAA,CAAA0tB,0EAAA,GAAA1tB,+DAAA,CAAA2zB,qEAAA;IAAA;EAAA;;;YAAjB/E,iBAAiB;MAAAlI,SAAA;MAAAkH,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA6F,2BAAA/M,EAAA,EAAAhE,GAAA;QAAA,IAAAgE,EAAA;UCnB9B7mB,4DAAA,aAGC;;UAECA,4DAAA,aAAkE;UAEhEA,uDAAA,aAEO;UAcPA,4DAAA,aAAuD;UAEnDA,uDAAA,aAA6C;UAW/CA,0DAAA,EAAM;UAIRA,4DAAA,cAAyE;UACvEA,uDAAA,eAAqE;UACvEA,0DAAA,EAAM;UAGRA,4DAAA,eAAqC;UAEJA,oDAAA,6BAAqB;UAAAA,0DAAA,EAAK;UACvDA,4DAAA,eAA4B;UAExBA,wDAAA,mBAAA6zB,oDAAA;YAAA,OAAShR,GAAA,CAAA0Q,YAAA,EAAc;UAAA,EAAC;UAIxBvzB,uDAAA,aAA+B;UACjCA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAA8zB,oDAAA;YAAA,OAASjR,GAAA,CAAAxM,qBAAA,EAAuB;UAAA,EAAC;UAGjCrW,uDAAA,aAAiC;UACnCA,0DAAA,EAAS;UAKbA,4DAAA,eAAuB;UAKjBA,wDAAA,2BAAA+zB,2DAAA7wB,MAAA;YAAA,OAAA2f,GAAA,CAAAhY,WAAA,GAAA3H,MAAA;UAAA,EAAsC;UAFxClD,0DAAA,EAME;UACFA,uDAAA,aAEK;UACPA,0DAAA,EAAM;UAGNA,4DAAA,eAA+C;UAUrCA,wDAAA,oBAAAg0B,oDAAA9wB,MAAA;YAAA,IAAA+wB,OAAA;YAAA,QAAAA,OAAA,GAERpR,GAAA,CAAA+O,UAAA,CAAApkB,GAAA,CACD,UAAU,CAAC,mBADVymB,OAAA,CAAAxB,QAAA,CAAAvvB,MAAA,CAAA+S,MAAA,CAAAie,OAAA,GAEF,IAAI,GAAG,IAAI,CACjB;UAAA,EADiB;UATHl0B,0DAAA,EAUE;UACFA,uDAAA,gBAAmD;UACrDA,0DAAA,EAAQ;UACRA,4DAAA,iBACG;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EACrB;UAIHA,4DAAA,eAAyC;UACRA,oDAAA,kBAAU;UAAAA,0DAAA,EAAO;UAChDA,4DAAA,kBAGC;UAFCA,wDAAA,oBAAAm0B,qDAAAjxB,MAAA;YAAA,OAAU2f,GAAA,CAAA4Q,eAAA,CAAAvwB,MAAA,CAAA+S,MAAA,CAAAtY,KAAA,CAA0C;UAAA,EAAC;UAGrDqC,4DAAA,kBAA4D;UAC1DA,oDAAA,aACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAsD;UACpDA,oDAAA,eACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAgE;UAC9DA,oDAAA,qCACF;UAAAA,0DAAA,EAAS;UAEXA,4DAAA,kBASC;UARCA,wDAAA,mBAAAo0B,oDAAA;YAAAvR,GAAA,CAAA8O,SAAA,GAAA9O,GAAA,CAAA8O,SAAA,KAC6C,KAAK,GAC/D,MAAM,GAAG,KAAK;YAAA,OACd9O,GAAA,CAAAqP,SAAA,CAAU,IAAI,CACf;UAAA,EADe;UAMDlyB,uDAAA,SAIK;UACPA,0DAAA,EAAS;UAKbA,4DAAA,kBAAiE;UAAzDA,wDAAA,mBAAAq0B,oDAAA;YAAA,OAASxR,GAAA,CAAA2Q,YAAA,EAAc;UAAA,EAAC;UAC9BxzB,oDAAA,6BACF;UAAAA,0DAAA,EAAS;UAIXA,wDAAA,KAAAs0B,iCAAA,kBASM;UACRt0B,0DAAA,EAAM;UAIRA,4DAAA,eAMC;UAJCA,wDAAA,oBAAAu0B,kDAAArxB,MAAA;YAAA,OAAAA,MAAA,CAAA+S,MAAA,CAAA5B,SAAA,GAAAnR,MAAA,CAAA+S,MAAA,CAAAue,YAAA,IAAAtxB,MAAA,CAAA+S,MAAA,CAAA3B,YAAA,GAE8C,GAAG,IAAIuO,GAAA,CAAAyO,YAAA,EAEzD;UAAA,EADK;UAGDtxB,wDAAA,KAAAy0B,iCAAA,kBAGM;UAGNz0B,wDAAA,KAAA00B,iCAAA,kBAQM;UAGN10B,wDAAA,KAAA20B,gCAAA,iBA4CK;UAGL30B,wDAAA,KAAA40B,iCAAA,kBASM;UAGN50B,wDAAA,KAAA60B,iCAAA,kBAKM;UACR70B,0DAAA,EAAM;;;;UA1PNA,yDAAA,SAAAA,yDAAA,QAAA6iB,GAAA,CAAA8I,WAAA,EAAkC;UAoE1B3rB,uDAAA,IAAuB;UAAvBA,wDAAA,YAAA6iB,GAAA,CAAAhY,WAAA,CAAuB;UAqBjB7K,uDAAA,GAAsD;UAAtDA,wDAAA,cAAA6qB,OAAA,GAAAhI,GAAA,CAAA+O,UAAA,CAAApkB,GAAA,+BAAAqd,OAAA,CAAAltB,KAAA,WAAsD;UAqBhDqC,uDAAA,GAAkC;UAAlCA,wDAAA,aAAA6iB,GAAA,CAAA6O,MAAA,gBAAkC;UAGlC1xB,uDAAA,GAA+B;UAA/BA,wDAAA,aAAA6iB,GAAA,CAAA6O,MAAA,aAA+B;UAG/B1xB,uDAAA,GAAoC;UAApCA,wDAAA,aAAA6iB,GAAA,CAAA6O,MAAA,kBAAoC;UAU5C1xB,uDAAA,GAEC;UAFDA,wDAAA,UAAA6iB,GAAA,CAAA8O,SAAA,0DAEC;UAGC3xB,uDAAA,GAEC;UAFDA,wDAAA,CAAA6iB,GAAA,CAAA8O,SAAA,mDAEC;UAcR3xB,uDAAA,GAAoB;UAApBA,wDAAA,SAAA6iB,GAAA,CAAAwN,UAAA,KAAoB;UAqBnBrwB,uDAAA,GAA8B;UAA9BA,wDAAA,SAAA6iB,GAAA,CAAA4I,OAAA,KAAA5I,GAAA,CAAAuN,KAAA,CAAAnf,MAAA,CAA8B;UAM9BjR,uDAAA,GAAoC;UAApCA,wDAAA,UAAA6iB,GAAA,CAAA4I,OAAA,IAAA5I,GAAA,CAAAuN,KAAA,CAAAnf,MAAA,OAAoC;UAWrCjR,uDAAA,GAAsB;UAAtBA,wDAAA,SAAA6iB,GAAA,CAAAuN,KAAA,CAAAnf,MAAA,KAAsB;UA+CrBjR,uDAAA,GAAiC;UAAjCA,wDAAA,SAAA6iB,GAAA,CAAA4I,OAAA,IAAA5I,GAAA,CAAAuN,KAAA,CAAAnf,MAAA,KAAiC;UAYjCjR,uDAAA,GAA6B;UAA7BA,wDAAA,SAAA6iB,GAAA,CAAA2O,WAAA,KAAA3O,GAAA,CAAA4I,OAAA,CAA6B;;;;;;;;;;;;;;;;;;;;;;;ACtPa;AACpB;AACzB,SAASsE,QAAQA,CAACiF,MAAM,GAAG,CAAC,EAAEC,SAAS,GAAGH,4DAAc,EAAE;EAC7D,IAAIE,MAAM,GAAG,CAAC,EAAE;IACZA,MAAM,GAAG,CAAC;EACd;EACA,OAAOD,6CAAK,CAACC,MAAM,EAAEA,MAAM,EAAEC,SAAS,CAAC;AAC3C", "sources": ["./src/app/services/toast.service.ts", "./src/app/views/front/messages/message-chat/message-chat.component.ts", "./src/app/views/front/messages/message-chat/message-chat.component.html", "./src/app/views/front/messages/messages-list/messages-list.component.ts", "./src/app/views/front/messages/messages-list/messages-list.component.html", "./src/app/views/front/messages/messages-routing.module.ts", "./src/app/views/front/messages/messages.module.ts", "./src/app/views/front/messages/user-list/user-list.component.ts", "./src/app/views/front/messages/user-list/user-list.component.html", "./node_modules/rxjs/dist/esm/internal/observable/interval.js"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { Toast } from 'src/app/models/message.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ToastService {\n  private toastsSubject = new BehaviorSubject<Toast[]>([]);\n  toasts$ = this.toastsSubject.asObservable();\n  private currentId = 0;\n\n  constructor() {}\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private addToast(toast: Omit<Toast, 'id'>): void {\n    const newToast: Toast = {\n      ...toast,\n      id: this.generateId(),\n      duration: toast.duration || 5000,\n    };\n\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, newToast]);\n\n    // Auto-remove toast after duration\n    if (newToast.duration && newToast.duration > 0) {\n      setTimeout(() => {\n        this.removeToast(newToast.id);\n      }, newToast.duration);\n    }\n  }\n  show(\n    message: string,\n    type: 'success' | 'error' | 'warning' | 'info' = 'info',\n    duration = 5000\n  ) {\n    const id = this.generateId();\n    const toast: Toast = { id, type, title: '', message, duration };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, toast]);\n\n    if (duration > 0) {\n      setTimeout(() => this.dismiss(id), duration);\n    }\n  }\n\n  showSuccess(message: string, duration = 3000) {\n    this.show(message, 'success', duration);\n  }\n\n  showError(message: string, duration = 5000) {\n    this.show(message, 'error', duration);\n  }\n\n  showWarning(message: string, duration = 4000) {\n    this.show(message, 'warning', duration);\n  }\n\n  showInfo(message: string, duration = 3000) {\n    this.show(message, 'info', duration);\n  }\n\n  dismiss(id: string) {\n    const currentToasts = this.toastsSubject.value.filter((t) => t.id !== id);\n    this.toastsSubject.next(currentToasts);\n  }\n  success(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'success',\n      title,\n      message,\n      duration,\n      icon: 'check-circle',\n    });\n  }\n  error(\n    title: string,\n    message: string,\n    duration?: number,\n    action?: Toast['action']\n  ): void {\n    this.addToast({\n      type: 'error',\n      title,\n      message,\n      duration: duration || 8000, // Longer duration for errors\n      icon: 'x-circle',\n      action,\n    });\n  }\n\n  warning(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'warning',\n      title,\n      message,\n      duration,\n      icon: 'exclamation-triangle',\n    });\n  }\n  // Méthodes spécifiques pour les erreurs d'autorisation\n  accessDenied(action: string = 'effectuer cette action', code?: number): void {\n    const codeText = code ? ` (Code: ${code})` : '';\n    this.error(\n      'Accès refusé',\n      `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`,\n      8000,\n      {\n        label: 'Comprendre les rôles',\n        handler: () => {\n          // Optionnel: rediriger vers une page d'aide\n          console.log(\"Redirection vers l'aide sur les rôles\");\n        },\n      }\n    );\n  }\n\n  ownershipRequired(resource: string = 'cette ressource'): void {\n    this.error(\n      'Propriétaire requis',\n      `Seul le propriétaire ou un administrateur peut modifier ${resource}`,\n      8000\n    );\n  }\n\n  removeToast(id: string): void {\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next(currentToasts.filter((toast) => toast.id !== id));\n  }\n  clear() {\n    this.toastsSubject.next([]);\n  }\n}\n", "import {\r\n  Compo<PERSON>,\r\n  <PERSON><PERSON><PERSON>t,\r\n  On<PERSON><PERSON>roy,\r\n  AfterViewInit,\r\n  ViewChild,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { MessageService } from '../../../../services/message.service';\r\nimport { CallService } from '../../../../services/call.service';\r\nimport { ToastService } from '../../../../services/toast.service';\r\nimport { CallType, Call, IncomingCall } from '../../../../models/message.model';\r\nimport { environment } from '../../../../../environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-message-chat',\r\n  templateUrl: './message-chat.component.html',\r\n})\r\nexport class MessageChatComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  // === RÉFÉRENCES DOM ===\r\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\r\n  @ViewChild('fileInput', { static: false })\r\n  fileInput!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('localVideo', { static: false })\r\n  localVideo!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('remoteVideo', { static: false })\r\n  remoteVideo!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('localVideoHidden', { static: false })\r\n  localVideoHidden!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('remoteVideoHidden', { static: false })\r\n  remoteVideoHidden!: ElementRef<HTMLVideoElement>;\r\n\r\n  // === DONNÉES PRINCIPALES ===\r\n  conversation: any = null;\r\n  messages: any[] = [];\r\n  currentUserId: string | null = null;\r\n  currentUsername = 'You';\r\n  messageForm: FormGroup;\r\n  otherParticipant: any = null;\r\n\r\n  // === ÉTATS DE L'INTERFACE ===\r\n  isLoading = false;\r\n  isLoadingMore = false;\r\n  hasMoreMessages = true;\r\n  showEmojiPicker = false;\r\n  showAttachmentMenu = false;\r\n  showSearch = false;\r\n  searchQuery = '';\r\n  searchResults: any[] = [];\r\n  searchMode = false;\r\n  isSendingMessage = false;\r\n  otherUserIsTyping = false;\r\n  showMainMenu = false;\r\n  showMessageContextMenu = false;\r\n  selectedMessage: any = null;\r\n  contextMenuPosition = { x: 0, y: 0 };\r\n  showReactionPicker = false;\r\n  reactionPickerMessage: any = null;\r\n\r\n  showImageViewer = false;\r\n  selectedImage: any = null;\r\n  uploadProgress = 0;\r\n  isUploading = false;\r\n  isDragOver = false;\r\n\r\n  // === GESTION VOCALE OPTIMISÉE ===\r\n  isRecordingVoice = false;\r\n  voiceRecordingDuration = 0;\r\n  voiceRecordingState: 'idle' | 'recording' | 'processing' = 'idle';\r\n  private mediaRecorder: MediaRecorder | null = null;\r\n  private audioChunks: Blob[] = [];\r\n  private recordingTimer: any = null;\r\n  voiceWaves: number[] = [\r\n    4, 8, 12, 16, 20, 16, 12, 8, 4, 8, 12, 16, 20, 16, 12, 8,\r\n  ];\r\n\r\n  // Lecture des messages vocaux\r\n  private currentAudio: HTMLAudioElement | null = null;\r\n  private playingMessageId: string | null = null;\r\n  private voicePlayback: {\r\n    [messageId: string]: {\r\n      progress: number;\r\n      duration: number;\r\n      currentTime: number;\r\n      speed: number;\r\n    };\r\n  } = {};\r\n\r\n  // === APPELS WEBRTC ===\r\n  isInCall = false;\r\n  callType: 'VIDEO' | 'AUDIO' | null = null;\r\n  callDuration = 0;\r\n  private callTimer: any = null;\r\n\r\n  // État de l'appel WebRTC - Géré globalement par les composants d'appel\r\n  // activeCall, isMuted, isVideoEnabled sont maintenant dans ActiveCallComponent\r\n\r\n  // === ÉMOJIS ===\r\n  emojiCategories: any[] = [\r\n    {\r\n      id: 'smileys',\r\n      name: 'Smileys',\r\n      icon: '😀',\r\n      emojis: [\r\n        { emoji: '😀', name: 'grinning face' },\r\n        { emoji: '😃', name: 'grinning face with big eyes' },\r\n        { emoji: '😄', name: 'grinning face with smiling eyes' },\r\n        { emoji: '😁', name: 'beaming face with smiling eyes' },\r\n        { emoji: '😆', name: 'grinning squinting face' },\r\n        { emoji: '😅', name: 'grinning face with sweat' },\r\n        { emoji: '😂', name: 'face with tears of joy' },\r\n        { emoji: '🤣', name: 'rolling on the floor laughing' },\r\n        { emoji: '😊', name: 'smiling face with smiling eyes' },\r\n        { emoji: '😇', name: 'smiling face with halo' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'people',\r\n      name: 'People',\r\n      icon: '👤',\r\n      emojis: [\r\n        { emoji: '👶', name: 'baby' },\r\n        { emoji: '🧒', name: 'child' },\r\n        { emoji: '👦', name: 'boy' },\r\n        { emoji: '👧', name: 'girl' },\r\n        { emoji: '🧑', name: 'person' },\r\n        { emoji: '👨', name: 'man' },\r\n        { emoji: '👩', name: 'woman' },\r\n        { emoji: '👴', name: 'old man' },\r\n        { emoji: '👵', name: 'old woman' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'nature',\r\n      name: 'Nature',\r\n      icon: '🌿',\r\n      emojis: [\r\n        { emoji: '🐶', name: 'dog face' },\r\n        { emoji: '🐱', name: 'cat face' },\r\n        { emoji: '🐭', name: 'mouse face' },\r\n        { emoji: '🐹', name: 'hamster' },\r\n        { emoji: '🐰', name: 'rabbit face' },\r\n        { emoji: '🦊', name: 'fox' },\r\n        { emoji: '🐻', name: 'bear' },\r\n        { emoji: '🐼', name: 'panda' },\r\n      ],\r\n    },\r\n  ];\r\n  selectedEmojiCategory = this.emojiCategories[0];\r\n\r\n  // === PAGINATION ===\r\n  private readonly MAX_MESSAGES_TO_LOAD = 25; // ✅ Increased batch size for better performance\r\n  private currentPage = 1;\r\n\r\n  // === AUTRES ÉTATS ===\r\n  isTyping = false;\r\n  isUserTyping = false;\r\n  private typingTimeout: any = null;\r\n  private subscriptions = new Subscription();\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private MessageService: MessageService,\r\n    private callService: CallService,\r\n    private toastService: ToastService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    this.messageForm = this.fb.group({\r\n      content: ['', [Validators.required, Validators.minLength(1)]],\r\n    });\r\n  }\r\n\r\n  // Méthode pour vérifier si le champ de saisie doit être désactivé\r\n  isInputDisabled(): boolean {\r\n    return (\r\n      !this.otherParticipant || this.isRecordingVoice || this.isSendingMessage\r\n    );\r\n  }\r\n\r\n  // Méthode pour gérer l'état du contrôle de saisie\r\n  private updateInputState(): void {\r\n    const contentControl = this.messageForm.get('content');\r\n    if (this.isInputDisabled()) {\r\n      contentControl?.disable();\r\n    } else {\r\n      contentControl?.enable();\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initializeComponent();\r\n\r\n    // Activer les sons après interaction utilisateur\r\n    this.enableSoundsOnFirstInteraction();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Configurer les éléments vidéo pour WebRTC après que la vue soit initialisée\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 100);\r\n\r\n    // Réessayer plusieurs fois pour s'assurer que les éléments sont configurés\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 500);\r\n\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 1000);\r\n\r\n    setTimeout(() => {\r\n      this.setupVideoElements();\r\n    }, 2000);\r\n  }\r\n\r\n  /**\r\n   * Configure les éléments vidéo pour WebRTC\r\n   */\r\n  private setupVideoElements(): void {\r\n    // Essayer d'abord les éléments visibles (pour appels vidéo)\r\n    if (this.localVideo && this.remoteVideo) {\r\n      this.callService.setVideoElements(\r\n        this.localVideo.nativeElement,\r\n        this.remoteVideo.nativeElement\r\n      );\r\n    }\r\n    // Sinon utiliser les éléments cachés (pour appels audio)\r\n    else if (this.localVideoHidden && this.remoteVideoHidden) {\r\n      this.callService.setVideoElements(\r\n        this.localVideoHidden.nativeElement,\r\n        this.remoteVideoHidden.nativeElement\r\n      );\r\n    } else {\r\n      this.createVideoElementsManually();\r\n\r\n      // Réessayer après un délai\r\n      setTimeout(() => {\r\n        this.setupVideoElements();\r\n      }, 500);\r\n\r\n      // Réessayer encore plus tard\r\n      setTimeout(() => {\r\n        this.setupVideoElements();\r\n      }, 1500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Crée les éléments vidéo manuellement si les ViewChild ne fonctionnent pas\r\n   */\r\n  private createVideoElementsManually(): void {\r\n    // Chercher les éléments dans le DOM\r\n    const localVideoEl = document.getElementById(\r\n      'localVideo'\r\n    ) as HTMLVideoElement;\r\n    const remoteVideoEl = document.getElementById(\r\n      'remoteVideo'\r\n    ) as HTMLVideoElement;\r\n\r\n    if (localVideoEl && remoteVideoEl) {\r\n      console.log(\r\n        '✅ [MessageChat] Found video elements in DOM, configuring...'\r\n      );\r\n      this.callService.setVideoElements(localVideoEl, remoteVideoEl);\r\n    } else {\r\n      console.warn('⚠️ [MessageChat] Video elements not found in DOM either');\r\n\r\n      // Créer les éléments dynamiquement\r\n      const localVideo = document.createElement('video');\r\n      localVideo.id = 'localVideo';\r\n      localVideo.autoplay = true;\r\n      localVideo.muted = true;\r\n      localVideo.playsInline = true;\r\n      localVideo.style.cssText =\r\n        'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\r\n\r\n      const remoteVideo = document.createElement('video');\r\n      remoteVideo.id = 'remoteVideo';\r\n      remoteVideo.autoplay = true;\r\n      remoteVideo.playsInline = true;\r\n      remoteVideo.style.cssText =\r\n        'position: absolute; top: -9999px; left: -9999px; width: 1px; height: 1px;';\r\n\r\n      document.body.appendChild(localVideo);\r\n      document.body.appendChild(remoteVideo);\r\n\r\n      this.callService.setVideoElements(localVideo, remoteVideo);\r\n    }\r\n  }\r\n\r\n  private enableSoundsOnFirstInteraction(): void {\r\n    const enableSounds = () => {\r\n      this.callService.enableSounds();\r\n      document.removeEventListener('click', enableSounds);\r\n      document.removeEventListener('keydown', enableSounds);\r\n      document.removeEventListener('touchstart', enableSounds);\r\n    };\r\n\r\n    document.addEventListener('click', enableSounds, { once: true });\r\n    document.addEventListener('keydown', enableSounds, { once: true });\r\n    document.addEventListener('touchstart', enableSounds, { once: true });\r\n  }\r\n\r\n  private initializeComponent(): void {\r\n    this.loadCurrentUser();\r\n    this.loadConversation();\r\n    this.setupCallSubscriptions();\r\n  }\r\n\r\n  private setupCallSubscriptions(): void {\r\n    // Les appels sont maintenant gérés globalement par app-incoming-call et app-active-call\r\n    // Plus besoin de subscriptions locales ici\r\n    console.log('📞 [MessageChat] Call subscriptions handled globally');\r\n  }\r\n\r\n  private handleIncomingCall(incomingCall: IncomingCall): void {\r\n    // Afficher une notification ou modal d'appel entrant\r\n    // Pour l'instant, on log juste\r\n    console.log(\r\n      '🔔 Handling incoming call from:',\r\n      incomingCall.caller.username\r\n    );\r\n\r\n    // Jouer la sonnerie\r\n    this.MessageService.play('ringtone');\r\n\r\n    // Ici on pourrait afficher une modal ou notification\r\n    // Pour l'instant, on accepte automatiquement pour tester\r\n    // this.acceptCall(incomingCall);\r\n  }\r\n\r\n  private loadCurrentUser(): void {\r\n    try {\r\n      const userString = localStorage.getItem('user');\r\n\r\n      if (!userString || userString === 'null' || userString === 'undefined') {\r\n        console.error('❌ No user data in localStorage');\r\n        this.currentUserId = null;\r\n        this.currentUsername = 'You';\r\n        return;\r\n      }\r\n\r\n      const user = JSON.parse(userString);\r\n\r\n      // Essayer différentes propriétés pour l'ID utilisateur\r\n      const userId = user._id || user.id || user.userId;\r\n\r\n      if (userId) {\r\n        this.currentUserId = userId;\r\n        this.currentUsername = user.username || user.name || 'You';\r\n      } else {\r\n        console.error('❌ No valid user ID found in user object:', user);\r\n        this.currentUserId = null;\r\n        this.currentUsername = 'You';\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error parsing user from localStorage:', error);\r\n      this.currentUserId = null;\r\n      this.currentUsername = 'You';\r\n    }\r\n  }\r\n\r\n  private loadConversation(): void {\r\n    const conversationId = this.route.snapshot.paramMap.get('id');\r\n\r\n    if (!conversationId) {\r\n      this.toastService.showError('ID de conversation manquant');\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n\r\n    // Nettoyer les subscriptions existantes avant de recharger\r\n    this.cleanupSubscriptions();\r\n\r\n    this.MessageService.getConversation(conversationId).subscribe({\r\n      next: (conversation) => {\r\n        this.conversation = conversation;\r\n        this.setOtherParticipant();\r\n        this.loadMessages();\r\n\r\n        // Configurer les subscriptions temps réel après le chargement de la conversation\r\n        this.setupSubscriptions();\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement de la conversation:', error);\r\n        this.toastService.showError(\r\n          'Erreur lors du chargement de la conversation'\r\n        );\r\n        this.isLoading = false;\r\n\r\n        // Réessayer après 2 secondes en cas d'erreur\r\n        setTimeout(() => {\r\n          this.loadConversation();\r\n        }, 2000);\r\n      },\r\n    });\r\n  }\r\n\r\n  private setOtherParticipant(): void {\r\n    if (\r\n      !this.conversation?.participants ||\r\n      this.conversation.participants.length === 0\r\n    ) {\r\n      console.warn('No participants found in conversation');\r\n      this.otherParticipant = null;\r\n      return;\r\n    }\r\n\r\n    // Dans une conversation 1-à-1, on veut afficher l'autre personne (pas l'utilisateur actuel)\r\n    // Dans une conversation de groupe, on peut afficher le nom du groupe ou le premier autre participant\r\n\r\n    if (this.conversation.isGroup) {\r\n      // Pour les groupes, on pourrait afficher le nom du groupe\r\n      // Mais pour l'instant, on prend le premier participant qui n'est pas l'utilisateur actuel\r\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\r\n        const participantId = p.id || p._id;\r\n        return String(participantId) !== String(this.currentUserId);\r\n      });\r\n    } else {\r\n      // Pour les conversations 1-à-1, on prend l'autre participant\r\n      this.otherParticipant = this.conversation.participants.find((p: any) => {\r\n        const participantId = p.id || p._id;\r\n        console.log(\r\n          'Comparing participant ID:',\r\n          participantId,\r\n          'with current user ID:',\r\n          this.currentUserId\r\n        );\r\n        return String(participantId) !== String(this.currentUserId);\r\n      });\r\n    }\r\n\r\n    // Fallback si aucun autre participant n'est trouvé\r\n    if (!this.otherParticipant && this.conversation.participants.length > 0) {\r\n      this.otherParticipant = this.conversation.participants[0];\r\n\r\n      // Si le premier participant est l'utilisateur actuel et qu'il y en a d'autres\r\n      if (this.conversation.participants.length > 1) {\r\n        const firstParticipantId =\r\n          this.otherParticipant.id || this.otherParticipant._id;\r\n        if (String(firstParticipantId) === String(this.currentUserId)) {\r\n          console.log(\r\n            'First participant is current user, using second participant'\r\n          );\r\n          this.otherParticipant = this.conversation.participants[1];\r\n        }\r\n      }\r\n    }\r\n\r\n    // Vérification finale et logs\r\n    if (this.otherParticipant) {\r\n      // Log très visible pour debug\r\n      console.log(\r\n        '🎯 FINAL RESULT: otherParticipant =',\r\n        this.otherParticipant.username\r\n      );\r\n      console.log(\r\n        '🎯 Should display in sidebar:',\r\n        this.otherParticipant.username\r\n      );\r\n    } else {\r\n      console.error('❌ No other participant found! This should not happen.');\r\n\r\n      // Log très visible pour debug\r\n    }\r\n\r\n    // Mettre à jour l'état du champ de saisie\r\n    this.updateInputState();\r\n  }\r\n\r\n  private loadMessages(): void {\r\n    if (!this.conversation?.id) return;\r\n\r\n    // Les messages sont déjà chargés avec la conversation\r\n    let messages = this.conversation.messages || [];\r\n\r\n    // Trier les messages par timestamp (plus anciens en premier)\r\n    this.messages = messages.sort((a: any, b: any) => {\r\n      const dateA = new Date(a.timestamp || a.createdAt).getTime();\r\n      const dateB = new Date(b.timestamp || b.createdAt).getTime();\r\n      return dateA - dateB; // Ordre croissant (plus anciens en premier)\r\n    });\r\n\r\n    console.log('📋 Messages loaded and sorted:', {\r\n      total: this.messages.length,\r\n      first: this.messages[0]?.content,\r\n      last: this.messages[this.messages.length - 1]?.content,\r\n    });\r\n\r\n    this.hasMoreMessages = this.messages.length === this.MAX_MESSAGES_TO_LOAD;\r\n    this.isLoading = false;\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  loadMoreMessages(): void {\r\n    if (this.isLoadingMore || !this.hasMoreMessages || !this.conversation?.id)\r\n      return;\r\n\r\n    this.isLoadingMore = true;\r\n    this.currentPage++;\r\n\r\n    // Calculer l'offset basé sur les messages déjà chargés\r\n    const offset = this.messages.length;\r\n\r\n    this.MessageService.getMessages(\r\n      this.currentUserId!, // senderId\r\n      this.otherParticipant?.id || this.otherParticipant?._id!, // receiverId\r\n      this.conversation.id,\r\n      this.currentPage,\r\n      this.MAX_MESSAGES_TO_LOAD\r\n    ).subscribe({\r\n      next: (newMessages: any[]) => {\r\n        if (newMessages && newMessages.length > 0) {\r\n          // Ajouter les nouveaux messages au début de la liste\r\n          this.messages = [...newMessages.reverse(), ...this.messages];\r\n          this.hasMoreMessages =\r\n            newMessages.length === this.MAX_MESSAGES_TO_LOAD;\r\n        } else {\r\n          this.hasMoreMessages = false;\r\n        }\r\n        this.isLoadingMore = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des messages:', error);\r\n        this.toastService.showError('Erreur lors du chargement des messages');\r\n        this.isLoadingMore = false;\r\n        this.currentPage--; // Revenir à la page précédente en cas d'erreur\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * ✅ Optimized subscription cleanup with memory management\r\n   */\r\n  private cleanupSubscriptions(): void {\r\n    this.subscriptions.unsubscribe();\r\n    this.subscriptions = new Subscription();\r\n\r\n    // ✅ Clear message cache to free memory when switching conversations\r\n    if (this.messages.length > 100) {\r\n      this.messages = this.messages.slice(-50); // Keep only last 50 messages\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Recharge la conversation actuelle\r\n   */\r\n  public reloadConversation(): void {\r\n    if (this.conversation?.id) {\r\n      // Réinitialiser l'état\r\n      this.messages = [];\r\n      this.currentPage = 1;\r\n      this.hasMoreMessages = true;\r\n\r\n      // Recharger\r\n      this.loadConversation();\r\n    }\r\n  }\r\n\r\n  private setupSubscriptions(): void {\r\n    if (!this.conversation?.id) {\r\n      console.warn('❌ Cannot setup subscriptions: no conversation ID');\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      '🔄 Setting up real-time subscriptions for conversation:',\r\n      this.conversation.id\r\n    );\r\n\r\n    // Subscription pour les nouveaux messages\r\n\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToNewMessages(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (newMessage: any) => {\r\n          // ✅ Reduced logging for better performance\r\n          if (environment.production === false) {\r\n            console.log('📨 New message received:', newMessage.id);\r\n          }\r\n\r\n          // Ajouter le message à la liste s'il n'existe pas déjà\r\n          const messageExists = this.messages.some(\r\n            (msg) => msg.id === newMessage.id\r\n          );\r\n          if (!messageExists) {\r\n            // Ajouter le nouveau message à la fin (en bas)\r\n            this.messages.push(newMessage);\r\n            console.log(\r\n              '✅ Message added to list, total messages:',\r\n              this.messages.length\r\n            );\r\n\r\n            // Forcer la détection de changements\r\n            this.cdr.detectChanges();\r\n\r\n            // Scroll vers le bas après un court délai\r\n            setTimeout(() => {\r\n              this.scrollToBottom();\r\n            }, 50);\r\n\r\n            // Marquer comme lu si ce n'est pas notre message\r\n            const senderId = newMessage.sender?.id || newMessage.senderId;\r\n            console.log('📨 Checking if message should be marked as read:', {\r\n              senderId,\r\n              currentUserId: this.currentUserId,\r\n              shouldMarkAsRead: senderId !== this.currentUserId,\r\n            });\r\n\r\n            if (senderId && senderId !== this.currentUserId) {\r\n              this.markMessageAsRead(newMessage.id);\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('❌ Error in message subscription:', error);\r\n          this.toastService.showError('Connexion temps réel interrompue');\r\n\r\n          // Réessayer la connexion après 5 secondes\r\n          setTimeout(() => {\r\n            this.setupSubscriptions();\r\n          }, 5000);\r\n        },\r\n      })\r\n    );\r\n\r\n    // Subscription pour les indicateurs de frappe\r\n\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToTypingIndicator(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (typingData: any) => {\r\n          // Afficher l'indicateur seulement si c'est l'autre utilisateur qui tape\r\n          if (typingData.userId !== this.currentUserId) {\r\n            this.otherUserIsTyping = typingData.isTyping;\r\n            this.isUserTyping = typingData.isTyping; // Pour compatibilité avec le template\r\n            console.log(\r\n              '📝 Other user typing status updated:',\r\n              this.otherUserIsTyping\r\n            );\r\n            this.cdr.detectChanges();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in typing subscription:', error);\r\n        },\r\n      })\r\n    );\r\n\r\n    // Subscription pour les mises à jour de conversation\r\n    this.subscriptions.add(\r\n      this.MessageService.subscribeToConversationUpdates(\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (conversationUpdate: any) => {\r\n          // Mettre à jour la conversation si nécessaire\r\n          if (conversationUpdate.id === this.conversation.id) {\r\n            this.conversation = { ...this.conversation, ...conversationUpdate };\r\n            this.cdr.detectChanges();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ Error in conversation subscription:', error);\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  private markMessageAsRead(messageId: string): void {\r\n    this.MessageService.markMessageAsRead(messageId).subscribe({\r\n      next: () => {},\r\n      error: (error) => {\r\n        console.error('❌ Error marking message as read:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  // === ENVOI DE MESSAGES ===\r\n  sendMessage(): void {\r\n    if (!this.messageForm.valid || !this.conversation?.id) return;\r\n\r\n    const content = this.messageForm.get('content')?.value?.trim();\r\n    if (!content) return;\r\n\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      this.toastService.showError('Destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    // Désactiver le bouton d'envoi\r\n    this.isSendingMessage = true;\r\n    this.updateInputState();\r\n\r\n    console.log('📤 Sending message:', {\r\n      content,\r\n      receiverId,\r\n      conversationId: this.conversation.id,\r\n    });\r\n\r\n    this.MessageService.sendMessage(\r\n      receiverId,\r\n      content,\r\n      undefined,\r\n      'TEXT' as any,\r\n      this.conversation.id\r\n    ).subscribe({\r\n      next: (message: any) => {\r\n        // Ajouter le message à la liste s'il n'y est pas déjà\r\n        const messageExists = this.messages.some(\r\n          (msg) => msg.id === message.id\r\n        );\r\n        if (!messageExists) {\r\n          this.messages.push(message);\r\n          console.log(\r\n            '📋 Message added to local list, total:',\r\n            this.messages.length\r\n          );\r\n        }\r\n\r\n        // Réinitialiser le formulaire\r\n        this.messageForm.reset();\r\n        this.isSendingMessage = false;\r\n        this.updateInputState();\r\n\r\n        // Forcer la détection de changements et scroll\r\n        this.cdr.detectChanges();\r\n        setTimeout(() => {\r\n          this.scrollToBottom();\r\n        }, 50);\r\n      },\r\n      error: (error: any) => {\r\n        console.error(\"❌ Erreur lors de l'envoi du message:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\r\n        this.isSendingMessage = false;\r\n        this.updateInputState();\r\n      },\r\n    });\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    setTimeout(() => {\r\n      if (this.messagesContainer) {\r\n        const element = this.messagesContainer.nativeElement;\r\n        element.scrollTop = element.scrollHeight;\r\n      }\r\n    }, 100);\r\n  }\r\n\r\n  // === MÉTHODES UTILITAIRES OPTIMISÉES ===\r\n  formatLastActive(lastActive: string | Date | null): string {\r\n    if (!lastActive) return 'Hors ligne';\r\n\r\n    const diffMins = Math.floor(\r\n      (Date.now() - new Date(lastActive).getTime()) / 60000\r\n    );\r\n\r\n    if (diffMins < 1) return \"À l'instant\";\r\n    if (diffMins < 60) return `Il y a ${diffMins} min`;\r\n    if (diffMins < 1440) return `Il y a ${Math.floor(diffMins / 60)}h`;\r\n    return `Il y a ${Math.floor(diffMins / 1440)}j`;\r\n  }\r\n\r\n  // Méthodes utilitaires pour les messages vocaux\r\n  getVoicePlaybackData(messageId: string) {\r\n    return (\r\n      this.voicePlayback[messageId] || {\r\n        progress: 0,\r\n        duration: 0,\r\n        currentTime: 0,\r\n        speed: 1,\r\n      }\r\n    );\r\n  }\r\n\r\n  private setVoicePlaybackData(\r\n    messageId: string,\r\n    data: Partial<(typeof this.voicePlayback)[string]>\r\n  ) {\r\n    this.voicePlayback[messageId] = {\r\n      ...this.getVoicePlaybackData(messageId),\r\n      ...data,\r\n    };\r\n  }\r\n\r\n  // === MÉTHODES POUR LES MESSAGES VOCAUX (TEMPLATE) ===\r\n\r\n  // === MÉTHODES POUR LES APPELS (TEMPLATE) ===\r\n\r\n  startVideoCall(): void {\r\n    if (!this.otherParticipant?.id) {\r\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\r\n      return;\r\n    }\r\n\r\n    this.initiateCall(CallType.VIDEO);\r\n  }\r\n\r\n  startVoiceCall(): void {\r\n    if (!this.otherParticipant?.id) {\r\n      this.toastService.showError(\"Impossible de démarrer l'appel\");\r\n      return;\r\n    }\r\n\r\n    // Forcer la configuration des éléments vidéo avant l'appel\r\n    this.setupVideoElements();\r\n\r\n    this.initiateCall(CallType.AUDIO);\r\n  }\r\n\r\n  // Méthodes d'appel supprimées - Gérées par ActiveCallComponent et IncomingCallComponent\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // onCallAccepted, onCallRejected - définies plus loin\r\n\r\n  // === MÉTHODES POUR LES FICHIERS (TEMPLATE) ===\r\n\r\n  formatFileSize(bytes: number): string {\r\n    if (bytes < 1024) return bytes + ' B';\r\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\r\n    return Math.round(bytes / 1048576) + ' MB';\r\n  }\r\n\r\n  downloadFile(message: any): void {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (fileAttachment?.url) {\r\n      const link = document.createElement('a');\r\n      link.href = fileAttachment.url;\r\n      link.download = fileAttachment.name || 'file';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.toastService.showSuccess('Téléchargement démarré');\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES POUR L'INTERFACE UTILISATEUR (TEMPLATE) ===\r\n\r\n  toggleSearch(): void {\r\n    this.searchMode = !this.searchMode;\r\n    this.showSearch = this.searchMode;\r\n  }\r\n\r\n  toggleMainMenu(): void {\r\n    this.showMainMenu = !this.showMainMenu;\r\n  }\r\n\r\n  goBackToConversations(): void {\r\n    // Naviguer vers la liste des conversations\r\n    this.router\r\n      .navigate(['/front/messages/conversations'])\r\n      .then(() => {})\r\n      .catch((error) => {\r\n        console.error('❌ Navigation error:', error);\r\n        // Fallback: essayer la route parent\r\n        this.router.navigate(['/front/messages']).catch(() => {\r\n          // Dernier recours: recharger la page\r\n          window.location.href = '/front/messages/conversations';\r\n        });\r\n      });\r\n  }\r\n\r\n  // === MÉTHODES POUR LES MENUS ET INTERACTIONS ===\r\n\r\n  closeAllMenus(): void {\r\n    this.showEmojiPicker = false;\r\n    this.showAttachmentMenu = false;\r\n    this.showMainMenu = false;\r\n    this.showMessageContextMenu = false;\r\n    this.showReactionPicker = false;\r\n  }\r\n\r\n  onMessageContextMenu(message: any, event: MouseEvent): void {\r\n    event.preventDefault();\r\n    this.selectedMessage = message;\r\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\r\n    this.showMessageContextMenu = true;\r\n  }\r\n\r\n  showQuickReactions(message: any, event: MouseEvent): void {\r\n    event.stopPropagation();\r\n    this.reactionPickerMessage = message;\r\n    this.contextMenuPosition = { x: event.clientX, y: event.clientY };\r\n    this.showReactionPicker = true;\r\n  }\r\n\r\n  quickReact(emoji: string): void {\r\n    if (this.reactionPickerMessage) {\r\n      this.toggleReaction(this.reactionPickerMessage.id, emoji);\r\n    }\r\n    this.showReactionPicker = false;\r\n  }\r\n\r\n  toggleReaction(messageId: string, emoji: string): void {\r\n    if (!messageId || !emoji) {\r\n      console.error('❌ Missing messageId or emoji for reaction');\r\n      return;\r\n    }\r\n\r\n    // Appeler le service pour ajouter/supprimer la réaction\r\n    this.MessageService.reactToMessage(messageId, emoji).subscribe({\r\n      next: (result) => {\r\n        // Mettre à jour le message local avec les nouvelles réactions\r\n        const messageIndex = this.messages.findIndex(\r\n          (msg) => msg.id === messageId\r\n        );\r\n        if (messageIndex !== -1) {\r\n          this.messages[messageIndex] = result;\r\n          this.cdr.detectChanges();\r\n        }\r\n\r\n        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error toggling reaction:', error);\r\n        this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\r\n      },\r\n    });\r\n  }\r\n\r\n  hasUserReacted(reaction: any, userId: string): boolean {\r\n    return reaction.userId === userId;\r\n  }\r\n\r\n  replyToMessage(message: any): void {\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  forwardMessage(message: any): void {\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  deleteMessage(message: any): void {\r\n    if (!message.id) {\r\n      console.error('❌ No message ID provided for deletion');\r\n      this.toastService.showError('Erreur: ID du message manquant');\r\n      return;\r\n    }\r\n\r\n    // Vérifier si l'utilisateur peut supprimer ce message\r\n    const canDelete =\r\n      message.sender?.id === this.currentUserId ||\r\n      message.senderId === this.currentUserId;\r\n\r\n    if (!canDelete) {\r\n      this.toastService.showError(\r\n        'Vous ne pouvez supprimer que vos propres messages'\r\n      );\r\n      this.closeAllMenus();\r\n      return;\r\n    }\r\n\r\n    // Demander confirmation\r\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\r\n      this.closeAllMenus();\r\n      return;\r\n    }\r\n\r\n    // Appeler le service pour supprimer le message\r\n    this.MessageService.deleteMessage(message.id).subscribe({\r\n      next: (result) => {\r\n        // Supprimer le message de la liste locale\r\n        this.messages = this.messages.filter((msg) => msg.id !== message.id);\r\n\r\n        this.toastService.showSuccess('Message supprimé');\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error deleting message:', error);\r\n        this.toastService.showError('Erreur lors de la suppression du message');\r\n      },\r\n    });\r\n\r\n    this.closeAllMenus();\r\n  }\r\n\r\n  // === MÉTHODES POUR LES ÉMOJIS ET PIÈCES JOINTES ===\r\n\r\n  toggleEmojiPicker(): void {\r\n    this.showEmojiPicker = !this.showEmojiPicker;\r\n  }\r\n\r\n  selectEmojiCategory(category: any): void {\r\n    this.selectedEmojiCategory = category;\r\n  }\r\n\r\n  getEmojisForCategory(category: any): any[] {\r\n    return category?.emojis || [];\r\n  }\r\n\r\n  insertEmoji(emoji: any): void {\r\n    const currentContent = this.messageForm.get('content')?.value || '';\r\n    const newContent = currentContent + emoji.emoji;\r\n    this.messageForm.patchValue({ content: newContent });\r\n    this.showEmojiPicker = false;\r\n  }\r\n\r\n  toggleAttachmentMenu(): void {\r\n    this.showAttachmentMenu = !this.showAttachmentMenu;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // triggerFileInput, getFileAcceptTypes, onFileSelected - définies plus loin\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // handleTypingIndicator - définie plus loin\r\n\r\n  // === MÉTHODES UTILITAIRES POUR LE TEMPLATE ===\r\n\r\n  trackByMessageId(index: number, message: any): string {\r\n    return message.id || message._id || index.toString();\r\n  }\r\n\r\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\r\n\r\n  testAddMessage(): void {\r\n    const testMessage = {\r\n      id: `test-${Date.now()}`,\r\n      content: `Message de test ${new Date().toLocaleTimeString()}`,\r\n      timestamp: new Date().toISOString(),\r\n      sender: {\r\n        id: this.otherParticipant?.id || 'test-user',\r\n        username: this.otherParticipant?.username || 'Test User',\r\n        image:\r\n          this.otherParticipant?.image || 'assets/images/default-avatar.png',\r\n      },\r\n      type: 'TEXT',\r\n      isRead: false,\r\n    };\r\n    this.messages.push(testMessage);\r\n    this.cdr.detectChanges();\r\n    setTimeout(() => this.scrollToBottom(), 50);\r\n  }\r\n\r\n  isGroupConversation(): boolean {\r\n    return (\r\n      this.conversation?.isGroup ||\r\n      this.conversation?.participants?.length > 2 ||\r\n      false\r\n    );\r\n  }\r\n\r\n  openCamera(): void {\r\n    this.showAttachmentMenu = false;\r\n    // TODO: Implémenter l'ouverture de la caméra\r\n  }\r\n\r\n  zoomImage(factor: number): void {\r\n    const imageElement = document.querySelector(\r\n      '.image-viewer-zoom'\r\n    ) as HTMLElement;\r\n    if (imageElement) {\r\n      const currentTransform = imageElement.style.transform || 'scale(1)';\r\n      const currentScale = parseFloat(\r\n        currentTransform.match(/scale\\(([^)]+)\\)/)?.[1] || '1'\r\n      );\r\n      const newScale = Math.max(0.5, Math.min(3, currentScale * factor));\r\n      imageElement.style.transform = `scale(${newScale})`;\r\n      if (newScale > 1) {\r\n        imageElement.classList.add('zoomed');\r\n      } else {\r\n        imageElement.classList.remove('zoomed');\r\n      }\r\n    }\r\n  }\r\n\r\n  resetZoom(): void {\r\n    const imageElement = document.querySelector(\r\n      '.image-viewer-zoom'\r\n    ) as HTMLElement;\r\n    if (imageElement) {\r\n      imageElement.style.transform = 'scale(1)';\r\n      imageElement.classList.remove('zoomed');\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // formatFileSize, downloadFile, toggleReaction, hasUserReacted, showQuickReactions\r\n  // toggleEmojiPicker, toggleAttachmentMenu, selectEmojiCategory, getEmojisForCategory, insertEmoji\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  triggerFileInput(type?: string): void {\r\n    const input = this.fileInput?.nativeElement;\r\n    if (!input) {\r\n      console.error('File input element not found');\r\n      return;\r\n    }\r\n\r\n    // Configurer le type de fichier accepté\r\n    if (type === 'image') {\r\n      input.accept = 'image/*';\r\n    } else if (type === 'video') {\r\n      input.accept = 'video/*';\r\n    } else if (type === 'document') {\r\n      input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';\r\n    } else {\r\n      input.accept = '*/*';\r\n    }\r\n\r\n    // Réinitialiser la valeur pour permettre la sélection du même fichier\r\n    input.value = '';\r\n\r\n    // Déclencher la sélection de fichier\r\n    input.click();\r\n    this.showAttachmentMenu = false;\r\n  }\r\n\r\n  formatMessageTime(timestamp: string | Date): string {\r\n    if (!timestamp) return '';\r\n\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleTimeString('fr-FR', {\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n  }\r\n\r\n  formatDateSeparator(timestamp: string | Date): string {\r\n    if (!timestamp) return '';\r\n\r\n    const date = new Date(timestamp);\r\n    const today = new Date();\r\n    const yesterday = new Date(today);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    if (date.toDateString() === today.toDateString()) {\r\n      return \"Aujourd'hui\";\r\n    } else if (date.toDateString() === yesterday.toDateString()) {\r\n      return 'Hier';\r\n    } else {\r\n      return date.toLocaleDateString('fr-FR');\r\n    }\r\n  }\r\n\r\n  formatMessageContent(content: string): string {\r\n    if (!content) return '';\r\n\r\n    // Remplacer les URLs par des liens\r\n    const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\r\n    return content.replace(\r\n      urlRegex,\r\n      '<a href=\"$1\" target=\"_blank\" class=\"text-blue-500 underline\">$1</a>'\r\n    );\r\n  }\r\n\r\n  shouldShowDateSeparator(index: number): boolean {\r\n    if (index === 0) return true;\r\n\r\n    const currentMessage = this.messages[index];\r\n    const previousMessage = this.messages[index - 1];\r\n\r\n    if (!currentMessage?.timestamp || !previousMessage?.timestamp) return false;\r\n\r\n    const currentDate = new Date(currentMessage.timestamp).toDateString();\r\n    const previousDate = new Date(previousMessage.timestamp).toDateString();\r\n\r\n    return currentDate !== previousDate;\r\n  }\r\n\r\n  shouldShowAvatar(index: number): boolean {\r\n    const currentMessage = this.messages[index];\r\n    const nextMessage = this.messages[index + 1];\r\n\r\n    if (!nextMessage) return true;\r\n\r\n    return currentMessage.sender?.id !== nextMessage.sender?.id;\r\n  }\r\n\r\n  shouldShowSenderName(index: number): boolean {\r\n    const currentMessage = this.messages[index];\r\n    const previousMessage = this.messages[index - 1];\r\n\r\n    if (!previousMessage) return true;\r\n\r\n    return currentMessage.sender?.id !== previousMessage.sender?.id;\r\n  }\r\n\r\n  getMessageType(message: any): string {\r\n    // Vérifier d'abord le type de message explicite\r\n    if (message.type) {\r\n      if (message.type === 'IMAGE' || message.type === 'image') return 'image';\r\n      if (message.type === 'VIDEO' || message.type === 'video') return 'video';\r\n      if (message.type === 'AUDIO' || message.type === 'audio') return 'audio';\r\n      if (message.type === 'VOICE_MESSAGE') return 'audio';\r\n      if (message.type === 'FILE' || message.type === 'file') return 'file';\r\n    }\r\n\r\n    // Ensuite vérifier les attachments\r\n    if (message.attachments && message.attachments.length > 0) {\r\n      const attachment = message.attachments[0];\r\n      if (attachment.type?.startsWith('image/')) return 'image';\r\n      if (attachment.type?.startsWith('video/')) return 'video';\r\n      if (attachment.type?.startsWith('audio/')) return 'audio';\r\n      return 'file';\r\n    }\r\n\r\n    // Vérifier si c'est un message vocal basé sur les propriétés\r\n    if (message.voiceUrl || message.audioUrl || message.voice) return 'audio';\r\n\r\n    return 'text';\r\n  }\r\n\r\n  hasImage(message: any): boolean {\r\n    // Vérifier le type de message\r\n    if (message.type === 'IMAGE' || message.type === 'image') {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les attachments\r\n    const hasImageAttachment =\r\n      message.attachments?.some((att: any) => {\r\n        return att.type?.startsWith('image/') || att.type === 'IMAGE';\r\n      }) || false;\r\n\r\n    // Vérifier les propriétés directes d'image\r\n    const hasImageUrl = !!(message.imageUrl || message.image);\r\n\r\n    return hasImageAttachment || hasImageUrl;\r\n  }\r\n\r\n  hasFile(message: any): boolean {\r\n    // Vérifier le type de message\r\n    if (message.type === 'FILE' || message.type === 'file') {\r\n      return true;\r\n    }\r\n\r\n    // Vérifier les attachments non-image\r\n    const hasFileAttachment =\r\n      message.attachments?.some((att: any) => {\r\n        return !att.type?.startsWith('image/') && att.type !== 'IMAGE';\r\n      }) || false;\r\n\r\n    return hasFileAttachment;\r\n  }\r\n\r\n  getImageUrl(message: any): string {\r\n    // Vérifier les propriétés directes d'image\r\n    if (message.imageUrl) {\r\n      return message.imageUrl;\r\n    }\r\n    if (message.image) {\r\n      return message.image;\r\n    }\r\n\r\n    // Vérifier les attachments\r\n    const imageAttachment = message.attachments?.find(\r\n      (att: any) => att.type?.startsWith('image/') || att.type === 'IMAGE'\r\n    );\r\n\r\n    if (imageAttachment) {\r\n      return imageAttachment.url || imageAttachment.path || '';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getFileName(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    return fileAttachment?.name || 'Fichier';\r\n  }\r\n\r\n  getFileSize(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (!fileAttachment?.size) return '';\r\n\r\n    const bytes = fileAttachment.size;\r\n    if (bytes < 1024) return bytes + ' B';\r\n    if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB';\r\n    return Math.round(bytes / 1048576) + ' MB';\r\n  }\r\n\r\n  getFileIcon(message: any): string {\r\n    const fileAttachment = message.attachments?.find(\r\n      (att: any) => !att.type?.startsWith('image/')\r\n    );\r\n    if (!fileAttachment?.type) return 'fas fa-file';\r\n\r\n    if (fileAttachment.type.startsWith('audio/')) return 'fas fa-file-audio';\r\n    if (fileAttachment.type.startsWith('video/')) return 'fas fa-file-video';\r\n    if (fileAttachment.type.includes('pdf')) return 'fas fa-file-pdf';\r\n    if (fileAttachment.type.includes('word')) return 'fas fa-file-word';\r\n    if (fileAttachment.type.includes('excel')) return 'fas fa-file-excel';\r\n    return 'fas fa-file';\r\n  }\r\n\r\n  getUserColor(userId: string): string {\r\n    // Générer une couleur basée sur l'ID utilisateur\r\n    const colors = [\r\n      '#FF6B6B',\r\n      '#4ECDC4',\r\n      '#45B7D1',\r\n      '#96CEB4',\r\n      '#FFEAA7',\r\n      '#DDA0DD',\r\n      '#98D8C8',\r\n    ];\r\n    const index = userId.charCodeAt(0) % colors.length;\r\n    return colors[index];\r\n  }\r\n\r\n  // === MÉTHODES D'INTERACTION ===\r\n  onMessageClick(message: any, event: any): void {}\r\n\r\n  onInputChange(event: any): void {\r\n    // Gérer les changements dans le champ de saisie\r\n    this.handleTypingIndicator();\r\n  }\r\n\r\n  onInputKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessage();\r\n    }\r\n  }\r\n\r\n  onInputFocus(): void {\r\n    // Gérer le focus sur le champ de saisie\r\n  }\r\n\r\n  onInputBlur(): void {\r\n    // Gérer la perte de focus sur le champ de saisie\r\n  }\r\n\r\n  onScroll(event: any): void {\r\n    // Gérer le scroll pour charger plus de messages\r\n    const element = event.target;\r\n    if (\r\n      element.scrollTop === 0 &&\r\n      this.hasMoreMessages &&\r\n      !this.isLoadingMore\r\n    ) {\r\n      this.loadMoreMessages();\r\n    }\r\n  }\r\n\r\n  openUserProfile(userId: string): void {}\r\n\r\n  onImageLoad(event: any, message: any): void {\r\n    console.log(\r\n      '🖼️ [Debug] Image loaded successfully for message:',\r\n      message.id,\r\n      event.target.src\r\n    );\r\n  }\r\n\r\n  onImageError(event: any, message: any): void {\r\n    console.error('🖼️ [Debug] Image failed to load for message:', message.id, {\r\n      src: event.target.src,\r\n      error: event,\r\n    });\r\n    // Optionnel : afficher une image de remplacement\r\n    event.target.src =\r\n      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vbiBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';\r\n  }\r\n\r\n  openImageViewer(message: any): void {\r\n    const imageAttachment = message.attachments?.find((att: any) =>\r\n      att.type?.startsWith('image/')\r\n    );\r\n    if (imageAttachment?.url) {\r\n      this.selectedImage = {\r\n        url: imageAttachment.url,\r\n        name: imageAttachment.name || 'Image',\r\n        size: this.formatFileSize(imageAttachment.size || 0),\r\n        message: message,\r\n      };\r\n      this.showImageViewer = true;\r\n    }\r\n  }\r\n\r\n  closeImageViewer(): void {\r\n    this.showImageViewer = false;\r\n    this.selectedImage = null;\r\n  }\r\n\r\n  downloadImage(): void {\r\n    if (this.selectedImage?.url) {\r\n      const link = document.createElement('a');\r\n      link.href = this.selectedImage.url;\r\n      link.download = this.selectedImage.name || 'image';\r\n      link.target = '_blank';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      this.toastService.showSuccess('Téléchargement démarré');\r\n      console.log(\r\n        '🖼️ [ImageViewer] Download started:',\r\n        this.selectedImage.name\r\n      );\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // zoomImage, resetZoom, formatFileSize, downloadFile, toggleReaction, hasUserReacted, toggleSearch\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  searchMessages(): void {\r\n    if (!this.searchQuery.trim()) {\r\n      this.searchResults = [];\r\n      return;\r\n    }\r\n\r\n    this.searchResults = this.messages.filter(\r\n      (message) =>\r\n        message.content\r\n          ?.toLowerCase()\r\n          .includes(this.searchQuery.toLowerCase()) ||\r\n        message.sender?.username\r\n          ?.toLowerCase()\r\n          .includes(this.searchQuery.toLowerCase())\r\n    );\r\n  }\r\n\r\n  onSearchQueryChange(): void {\r\n    this.searchMessages();\r\n  }\r\n\r\n  clearSearch(): void {\r\n    this.searchQuery = '';\r\n    this.searchResults = [];\r\n  }\r\n\r\n  jumpToMessage(messageId: string): void {\r\n    const messageElement = document.getElementById(`message-${messageId}`);\r\n    if (messageElement) {\r\n      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n      // Highlight temporairement le message\r\n      messageElement.classList.add('highlight');\r\n      setTimeout(() => {\r\n        messageElement.classList.remove('highlight');\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // toggleMainMenu, toggleTheme, onMessageContextMenu\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  closeContextMenu(): void {\r\n    this.showMessageContextMenu = false;\r\n    this.selectedMessage = null;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // replyToMessage, forwardMessage, deleteMessage, showQuickReactions, closeReactionPicker\r\n  // quickReact, closeAllMenus, testAddMessage, toggleAttachmentMenu, toggleEmojiPicker\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  // === MÉTHODE SUPPRIMÉE (DUPLICATION) ===\r\n  // triggerFileInput - définie plus loin\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // openCamera, getEmojisForCategory, selectEmojiCategory, insertEmoji\r\n  // goBackToConversations, startVideoCall, startVoiceCall\r\n  // Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  private initiateCall(callType: CallType): void {\r\n    console.log('📋 [MessageChat] Call details:', {\r\n      callType,\r\n      otherParticipant: this.otherParticipant,\r\n      conversation: this.conversation?.id,\r\n      currentUserId: this.currentUserId,\r\n    });\r\n\r\n    if (!this.otherParticipant) {\r\n      console.error('❌ [MessageChat] No recipient selected');\r\n      this.toastService.showError('Aucun destinataire sélectionné');\r\n      return;\r\n    }\r\n\r\n    const recipientId = this.otherParticipant.id || this.otherParticipant._id;\r\n    if (!recipientId) {\r\n      console.error('❌ [MessageChat] Recipient ID not found');\r\n      this.toastService.showError('ID du destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    console.log(`📞 [MessageChat] Initiating ${callType} call to user:`, {\r\n      recipientId,\r\n      recipientName:\r\n        this.otherParticipant.username || this.otherParticipant.name,\r\n      conversationId: this.conversation?.id,\r\n    });\r\n\r\n    this.isInCall = true;\r\n    this.callType = callType === CallType.VIDEO ? 'VIDEO' : 'AUDIO';\r\n    this.callDuration = 0;\r\n\r\n    // Démarrer le timer d'appel\r\n    this.startCallTimer();\r\n\r\n    // Utiliser le CallService\r\n    this.callService\r\n      .initiateCall(recipientId, callType, this.conversation?.id)\r\n      .subscribe({\r\n        next: (call: Call) => {\r\n          // L'appel est maintenant géré globalement par ActiveCallComponent\r\n          this.toastService.showSuccess(\r\n            `Appel ${callType === CallType.VIDEO ? 'vidéo' : 'audio'} initié`\r\n          );\r\n\r\n          console.log(\r\n            '📡 [MessageChat] Call should now be sent to recipient via WebSocket'\r\n          );\r\n        },\r\n        error: (error: any) => {\r\n          console.error('❌ [MessageChat] Error initiating call:', {\r\n            error: error.message || error,\r\n            recipientId,\r\n            callType,\r\n            conversationId: this.conversation?.id,\r\n          });\r\n\r\n          // Gestion d'erreur - l'appel sera automatiquement nettoyé par CallService\r\n          this.toastService.showError(\"Erreur lors de l'initiation de l'appel\");\r\n        },\r\n      });\r\n  }\r\n\r\n  acceptCall(incomingCall: IncomingCall): void {\r\n    // L'acceptation d'appel est maintenant gérée par IncomingCallComponent\r\n    console.log(\r\n      '📞 [MessageChat] Call acceptance handled by IncomingCallComponent'\r\n    );\r\n  }\r\n\r\n  rejectCall(incomingCall: IncomingCall): void {\r\n    this.callService.rejectCall(incomingCall.id, 'User rejected').subscribe({\r\n      next: () => {\r\n        this.toastService.showSuccess('Appel rejeté');\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Error rejecting call:', error);\r\n        this.toastService.showError(\"Erreur lors du rejet de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  private startCallTimer(): void {\r\n    this.callDuration = 0;\r\n    this.callTimer = setInterval(() => {\r\n      this.callDuration++;\r\n      this.cdr.detectChanges();\r\n    }, 1000);\r\n  }\r\n\r\n  private resetCallState(): void {\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n      this.callTimer = null;\r\n    }\r\n\r\n    this.isInCall = false;\r\n    this.callType = null;\r\n    this.callDuration = 0;\r\n    // activeCall, isCallConnected, isMuted, isVideoEnabled gérés par ActiveCallComponent\r\n  }\r\n\r\n  // === CONTRÔLES D'APPEL ===\r\n  // Contrôles d'appel maintenant gérés par ActiveCallComponent\r\n  toggleMute(): void {\r\n    console.log('📞 [MessageChat] Mute toggle handled by ActiveCallComponent');\r\n  }\r\n\r\n  toggleVideo(): void {\r\n    console.log('📞 [MessageChat] Video toggle handled by ActiveCallComponent');\r\n  }\r\n\r\n  /**\r\n   * Termine l'appel en cours\r\n   */\r\n  endCall(): void {\r\n    console.log('📞 [MessageChat] Ending call...');\r\n\r\n    // Réinitialiser l'état local\r\n    this.isInCall = false;\r\n    this.callType = null;\r\n    this.callDuration = 0;\r\n\r\n    // Arrêter le timer si actif\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n      this.callTimer = null;\r\n    }\r\n\r\n    // Le CallService gère la fin d'appel globalement\r\n    // Les composants ActiveCall et IncomingCall se mettront à jour automatiquement\r\n\r\n    this.toastService.showSuccess('Appel terminé');\r\n  }\r\n\r\n  formatCallDuration(duration: number): string {\r\n    const hours = Math.floor(duration / 3600);\r\n    const minutes = Math.floor((duration % 3600) / 60);\r\n    const seconds = duration % 60;\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds\r\n        .toString()\r\n        .padStart(2, '0')}`;\r\n    }\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  // === MÉTHODES SUPPRIMÉES (DUPLICATIONS) ===\r\n  // trackByMessageId, isGroupConversation - Ces méthodes sont déjà définies plus loin dans le fichier\r\n\r\n  async startVoiceRecording(): Promise<void> {\r\n    try {\r\n      // Vérifier le support du navigateur\r\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\r\n        throw new Error(\r\n          \"Votre navigateur ne supporte pas l'enregistrement audio\"\r\n        );\r\n      }\r\n\r\n      // Vérifier si MediaRecorder est supporté\r\n      if (!window.MediaRecorder) {\r\n        throw new Error(\r\n          \"MediaRecorder n'est pas supporté par votre navigateur\"\r\n        );\r\n      }\r\n\r\n      // Demander l'accès au microphone avec des contraintes optimisées\r\n      const stream = await navigator.mediaDevices.getUserMedia({\r\n        audio: {\r\n          echoCancellation: true,\r\n          noiseSuppression: true,\r\n          autoGainControl: true,\r\n          sampleRate: 44100,\r\n          channelCount: 1,\r\n        },\r\n      });\r\n\r\n      // Vérifier les types MIME supportés\r\n      let mimeType = 'audio/webm;codecs=opus';\r\n      if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n        mimeType = 'audio/webm';\r\n        if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n          mimeType = 'audio/mp4';\r\n          if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n            mimeType = ''; // Laisser le navigateur choisir\r\n          }\r\n        }\r\n      }\r\n\r\n      // Créer le MediaRecorder\r\n      this.mediaRecorder = new MediaRecorder(stream, {\r\n        mimeType: mimeType || undefined,\r\n      });\r\n\r\n      // Initialiser les variables\r\n      this.audioChunks = [];\r\n      this.isRecordingVoice = true;\r\n      this.voiceRecordingDuration = 0;\r\n      this.voiceRecordingState = 'recording';\r\n\r\n      // Démarrer le timer\r\n      this.recordingTimer = setInterval(() => {\r\n        this.voiceRecordingDuration++;\r\n        // Animer les waves\r\n        this.animateVoiceWaves();\r\n        this.cdr.detectChanges();\r\n      }, 1000);\r\n\r\n      // Gérer les événements du MediaRecorder\r\n      this.mediaRecorder.ondataavailable = (event) => {\r\n        if (event.data.size > 0) {\r\n          this.audioChunks.push(event.data);\r\n        }\r\n      };\r\n\r\n      this.mediaRecorder.onstop = () => {\r\n        this.processRecordedAudio();\r\n      };\r\n\r\n      this.mediaRecorder.onerror = (event: any) => {\r\n        console.error('🎤 [Voice] MediaRecorder error:', event.error);\r\n        this.toastService.showError(\"Erreur lors de l'enregistrement\");\r\n        this.cancelVoiceRecording();\r\n      };\r\n\r\n      // Démarrer l'enregistrement\r\n      this.mediaRecorder.start(100); // Collecter les données toutes les 100ms\r\n\r\n      this.toastService.showSuccess('🎤 Enregistrement vocal démarré');\r\n    } catch (error: any) {\r\n      console.error('🎤 [Voice] Error starting recording:', error);\r\n\r\n      let errorMessage = \"Impossible de démarrer l'enregistrement vocal\";\r\n\r\n      if (error.name === 'NotAllowedError') {\r\n        errorMessage =\r\n          \"Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur.\";\r\n      } else if (error.name === 'NotFoundError') {\r\n        errorMessage =\r\n          'Aucun microphone détecté. Veuillez connecter un microphone.';\r\n      } else if (error.name === 'NotSupportedError') {\r\n        errorMessage =\r\n          \"Votre navigateur ne supporte pas l'enregistrement audio.\";\r\n      } else if (error.message) {\r\n        errorMessage = error.message;\r\n      }\r\n\r\n      this.toastService.showError(errorMessage);\r\n      this.cancelVoiceRecording();\r\n    }\r\n  }\r\n\r\n  stopVoiceRecording(): void {\r\n    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\r\n      this.mediaRecorder.stop();\r\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n    }\r\n\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n      this.recordingTimer = null;\r\n    }\r\n\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingState = 'processing';\r\n  }\r\n\r\n  cancelVoiceRecording(): void {\r\n    if (this.mediaRecorder) {\r\n      if (this.mediaRecorder.state === 'recording') {\r\n        this.mediaRecorder.stop();\r\n      }\r\n      this.mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n      this.mediaRecorder = null;\r\n    }\r\n\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n      this.recordingTimer = null;\r\n    }\r\n\r\n    this.isRecordingVoice = false;\r\n    this.voiceRecordingDuration = 0;\r\n    this.voiceRecordingState = 'idle';\r\n    this.audioChunks = [];\r\n  }\r\n\r\n  private async processRecordedAudio(): Promise<void> {\r\n    try {\r\n      // Vérifier qu'on a des données audio\r\n      if (this.audioChunks.length === 0) {\r\n        console.error('🎤 [Voice] No audio chunks available');\r\n        this.toastService.showError('Aucun audio enregistré');\r\n        this.cancelVoiceRecording();\r\n        return;\r\n      }\r\n\r\n      console.log(\r\n        '🎤 [Voice] Audio chunks:',\r\n        this.audioChunks.length,\r\n        'Duration:',\r\n        this.voiceRecordingDuration\r\n      );\r\n\r\n      // Vérifier la durée minimale\r\n      if (this.voiceRecordingDuration < 1) {\r\n        console.error(\r\n          '🎤 [Voice] Recording too short:',\r\n          this.voiceRecordingDuration\r\n        );\r\n        this.toastService.showError(\r\n          'Enregistrement trop court (minimum 1 seconde)'\r\n        );\r\n        this.cancelVoiceRecording();\r\n        return;\r\n      }\r\n\r\n      // Déterminer le type MIME du blob\r\n      let mimeType = 'audio/webm;codecs=opus';\r\n      if (this.mediaRecorder?.mimeType) {\r\n        mimeType = this.mediaRecorder.mimeType;\r\n      }\r\n\r\n      // Créer le blob audio\r\n      const audioBlob = new Blob(this.audioChunks, {\r\n        type: mimeType,\r\n      });\r\n\r\n      console.log('🎤 [Voice] Audio blob created:', {\r\n        size: audioBlob.size,\r\n        type: audioBlob.type,\r\n      });\r\n\r\n      // Déterminer l'extension du fichier\r\n      let extension = '.webm';\r\n      if (mimeType.includes('mp4')) {\r\n        extension = '.mp4';\r\n      } else if (mimeType.includes('wav')) {\r\n        extension = '.wav';\r\n      } else if (mimeType.includes('ogg')) {\r\n        extension = '.ogg';\r\n      }\r\n\r\n      // Créer le fichier\r\n      const audioFile = new File(\r\n        [audioBlob],\r\n        `voice_${Date.now()}${extension}`,\r\n        {\r\n          type: mimeType,\r\n        }\r\n      );\r\n\r\n      console.log('🎤 [Voice] Audio file created:', {\r\n        name: audioFile.name,\r\n        size: audioFile.size,\r\n        type: audioFile.type,\r\n      });\r\n\r\n      // Envoyer le message vocal\r\n      this.voiceRecordingState = 'processing';\r\n      await this.sendVoiceMessage(audioFile);\r\n\r\n      this.toastService.showSuccess('🎤 Message vocal envoyé');\r\n    } catch (error: any) {\r\n      console.error('🎤 [Voice] Error processing audio:', error);\r\n      this.toastService.showError(\r\n        \"Erreur lors de l'envoi du message vocal: \" +\r\n          (error.message || 'Erreur inconnue')\r\n      );\r\n    } finally {\r\n      // Nettoyer l'état\r\n      this.voiceRecordingState = 'idle';\r\n      this.voiceRecordingDuration = 0;\r\n      this.audioChunks = [];\r\n      this.isRecordingVoice = false;\r\n    }\r\n  }\r\n\r\n  private async sendVoiceMessage(audioFile: File): Promise<void> {\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      throw new Error('Destinataire introuvable');\r\n    }\r\n\r\n    return new Promise((resolve, reject) => {\r\n      this.MessageService.sendMessage(\r\n        receiverId,\r\n        '',\r\n        audioFile,\r\n        'AUDIO' as any,\r\n        this.conversation.id\r\n      ).subscribe({\r\n        next: (message: any) => {\r\n          this.messages.push(message);\r\n          this.scrollToBottom();\r\n          resolve();\r\n        },\r\n        error: (error: any) => {\r\n          console.error(\"Erreur lors de l'envoi du message vocal:\", error);\r\n          reject(error);\r\n        },\r\n      });\r\n    });\r\n  }\r\n\r\n  formatRecordingDuration(duration: number): string {\r\n    const minutes = Math.floor(duration / 60);\r\n    const seconds = duration % 60;\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  // === MÉTHODES D'ENREGISTREMENT VOCAL AMÉLIORÉES ===\r\n\r\n  onRecordStart(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    console.log('🎤 [Voice] Current state:', {\r\n      isRecordingVoice: this.isRecordingVoice,\r\n      voiceRecordingState: this.voiceRecordingState,\r\n      voiceRecordingDuration: this.voiceRecordingDuration,\r\n      mediaRecorder: !!this.mediaRecorder,\r\n    });\r\n\r\n    // Vérifier si on peut enregistrer\r\n    if (this.voiceRecordingState === 'processing') {\r\n      this.toastService.showWarning('Traitement en cours...');\r\n      return;\r\n    }\r\n\r\n    if (this.isRecordingVoice) {\r\n      this.toastService.showWarning('Enregistrement déjà en cours...');\r\n      return;\r\n    }\r\n\r\n    // Afficher un message de début\r\n    this.toastService.showInfo(\"🎤 Démarrage de l'enregistrement vocal...\");\r\n\r\n    // Démarrer l'enregistrement\r\n    this.startVoiceRecording().catch((error) => {\r\n      console.error('🎤 [Voice] Failed to start recording:', error);\r\n      this.toastService.showError(\r\n        \"Impossible de démarrer l'enregistrement vocal: \" +\r\n          (error.message || 'Erreur inconnue')\r\n      );\r\n    });\r\n  }\r\n\r\n  onRecordEnd(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    if (!this.isRecordingVoice) {\r\n      return;\r\n    }\r\n\r\n    // Arrêter l'enregistrement et envoyer\r\n    this.stopVoiceRecording();\r\n  }\r\n\r\n  onRecordCancel(event: Event): void {\r\n    event.preventDefault();\r\n\r\n    if (!this.isRecordingVoice) {\r\n      return;\r\n    }\r\n\r\n    // Annuler l'enregistrement\r\n    this.cancelVoiceRecording();\r\n  }\r\n\r\n  getRecordingFormat(): string {\r\n    if (this.mediaRecorder?.mimeType) {\r\n      if (this.mediaRecorder.mimeType.includes('webm')) return 'WebM';\r\n      if (this.mediaRecorder.mimeType.includes('mp4')) return 'MP4';\r\n      if (this.mediaRecorder.mimeType.includes('wav')) return 'WAV';\r\n      if (this.mediaRecorder.mimeType.includes('ogg')) return 'OGG';\r\n    }\r\n    return 'Auto';\r\n  }\r\n\r\n  // === ANIMATION DES WAVES VOCALES ===\r\n\r\n  private animateVoiceWaves(): void {\r\n    // Animer les waves pendant l'enregistrement\r\n    this.voiceWaves = this.voiceWaves.map(() => {\r\n      return Math.floor(Math.random() * 20) + 4; // Hauteur entre 4 et 24px\r\n    });\r\n  }\r\n\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n\r\n    if (!files || files.length === 0) {\r\n      return;\r\n    }\r\n\r\n    for (let file of files) {\r\n      console.log(\r\n        `📁 [Upload] Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`\r\n      );\r\n      this.uploadFile(file);\r\n    }\r\n  }\r\n\r\n  private uploadFile(file: File): void {\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n\r\n    if (!receiverId) {\r\n      console.error('📁 [Upload] No receiver ID found');\r\n      this.toastService.showError('Destinataire introuvable');\r\n      return;\r\n    }\r\n\r\n    // Vérifier la taille du fichier (max 50MB)\r\n    const maxSize = 50 * 1024 * 1024; // 50MB\r\n    if (file.size > maxSize) {\r\n      console.error(`📁 [Upload] File too large: ${file.size} bytes`);\r\n      this.toastService.showError('Fichier trop volumineux (max 50MB)');\r\n      return;\r\n    }\r\n\r\n    // 🖼️ Compression d'image si nécessaire\r\n    if (file.type.startsWith('image/') && file.size > 1024 * 1024) {\r\n      // > 1MB\r\n      console.log(\r\n        '🖼️ [Compression] Compressing image:',\r\n        file.name,\r\n        'Original size:',\r\n        file.size\r\n      );\r\n      this.compressImage(file)\r\n        .then((compressedFile) => {\r\n          console.log(\r\n            '🖼️ [Compression] ✅ Image compressed successfully. New size:',\r\n            compressedFile.size\r\n          );\r\n          this.sendFileToServer(compressedFile, receiverId);\r\n        })\r\n        .catch((error) => {\r\n          console.error('🖼️ [Compression] ❌ Error compressing image:', error);\r\n          // Envoyer le fichier original en cas d'erreur\r\n          this.sendFileToServer(file, receiverId);\r\n        });\r\n      return;\r\n    }\r\n\r\n    // Envoyer le fichier sans compression\r\n    this.sendFileToServer(file, receiverId);\r\n  }\r\n\r\n  private sendFileToServer(file: File, receiverId: string): void {\r\n    const messageType = this.getFileMessageType(file);\r\n\r\n    this.isSendingMessage = true;\r\n    this.isUploading = true;\r\n    this.uploadProgress = 0;\r\n\r\n    // Simuler la progression d'upload\r\n    const progressInterval = setInterval(() => {\r\n      this.uploadProgress += Math.random() * 15;\r\n      if (this.uploadProgress >= 90) {\r\n        clearInterval(progressInterval);\r\n      }\r\n      this.cdr.detectChanges();\r\n    }, 300);\r\n\r\n    this.MessageService.sendMessage(\r\n      receiverId,\r\n      '',\r\n      file,\r\n      messageType,\r\n      this.conversation.id\r\n    ).subscribe({\r\n      next: (message: any) => {\r\n        console.log('📁 [Debug] Sent message structure:', {\r\n          id: message.id,\r\n          type: message.type,\r\n          attachments: message.attachments,\r\n          hasImage: this.hasImage(message),\r\n          hasFile: this.hasFile(message),\r\n          imageUrl: this.getImageUrl(message),\r\n        });\r\n\r\n        clearInterval(progressInterval);\r\n        this.uploadProgress = 100;\r\n\r\n        setTimeout(() => {\r\n          this.messages.push(message);\r\n          this.scrollToBottom();\r\n          this.toastService.showSuccess('Fichier envoyé avec succès');\r\n          this.resetUploadState();\r\n        }, 500);\r\n      },\r\n      error: (error: any) => {\r\n        console.error('📁 [Upload] ❌ Error sending file:', error);\r\n        clearInterval(progressInterval);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du fichier\");\r\n        this.resetUploadState();\r\n      },\r\n    });\r\n  }\r\n\r\n  private getFileMessageType(file: File): any {\r\n    if (file.type.startsWith('image/')) return 'IMAGE' as any;\r\n    if (file.type.startsWith('video/')) return 'VIDEO' as any;\r\n    if (file.type.startsWith('audio/')) return 'AUDIO' as any;\r\n    return 'FILE' as any;\r\n  }\r\n\r\n  getFileAcceptTypes(): string {\r\n    return '*/*';\r\n  }\r\n\r\n  resetUploadState(): void {\r\n    this.isSendingMessage = false;\r\n    this.isUploading = false;\r\n    this.uploadProgress = 0;\r\n  }\r\n\r\n  // === DRAG & DROP ===\r\n\r\n  onDragOver(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = true;\r\n  }\r\n\r\n  onDragLeave(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    // Vérifier si on quitte vraiment la zone (pas un enfant)\r\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n    const x = event.clientX;\r\n    const y = event.clientY;\r\n\r\n    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\r\n      this.isDragOver = false;\r\n    }\r\n  }\r\n\r\n  onDrop(event: DragEvent): void {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = false;\r\n\r\n    const files = event.dataTransfer?.files;\r\n    if (files && files.length > 0) {\r\n      // Traiter chaque fichier\r\n      Array.from(files).forEach((file) => {\r\n        console.log(\r\n          '📁 [Drag&Drop] Processing file:',\r\n          file.name,\r\n          file.type,\r\n          file.size\r\n        );\r\n        this.uploadFile(file);\r\n      });\r\n\r\n      this.toastService.showSuccess(\r\n        `${files.length} fichier(s) en cours d'envoi`\r\n      );\r\n    }\r\n  }\r\n\r\n  // === COMPRESSION D'IMAGES ===\r\n\r\n  private compressImage(file: File, quality: number = 0.8): Promise<File> {\r\n    return new Promise((resolve, reject) => {\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d');\r\n      const img = new Image();\r\n\r\n      img.onload = () => {\r\n        // Calculer les nouvelles dimensions (max 1920x1080)\r\n        const maxWidth = 1920;\r\n        const maxHeight = 1080;\r\n        let { width, height } = img;\r\n\r\n        if (width > maxWidth || height > maxHeight) {\r\n          const ratio = Math.min(maxWidth / width, maxHeight / height);\r\n          width *= ratio;\r\n          height *= ratio;\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Dessiner l'image redimensionnée\r\n        ctx?.drawImage(img, 0, 0, width, height);\r\n\r\n        // Convertir en blob avec compression\r\n        canvas.toBlob(\r\n          (blob) => {\r\n            if (blob) {\r\n              const compressedFile = new File([blob], file.name, {\r\n                type: file.type,\r\n                lastModified: Date.now(),\r\n              });\r\n              resolve(compressedFile);\r\n            } else {\r\n              reject(new Error('Failed to compress image'));\r\n            }\r\n          },\r\n          file.type,\r\n          quality\r\n        );\r\n      };\r\n\r\n      img.onerror = () => reject(new Error('Failed to load image'));\r\n      img.src = URL.createObjectURL(file);\r\n    });\r\n  }\r\n\r\n  // === MÉTHODES DE FORMATAGE SUPPLÉMENTAIRES ===\r\n\r\n  private handleTypingIndicator(): void {\r\n    if (!this.isTyping) {\r\n      this.isTyping = true;\r\n      // Envoyer l'indicateur de frappe à l'autre utilisateur\r\n      this.sendTypingIndicator(true);\r\n    }\r\n\r\n    // Reset le timer\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    this.typingTimeout = setTimeout(() => {\r\n      this.isTyping = false;\r\n      // Arrêter l'indicateur de frappe\r\n      this.sendTypingIndicator(false);\r\n    }, 2000);\r\n  }\r\n\r\n  private sendTypingIndicator(isTyping: boolean): void {\r\n    // Envoyer l'indicateur de frappe via WebSocket/GraphQL\r\n    const receiverId = this.otherParticipant?.id || this.otherParticipant?._id;\r\n    if (receiverId && this.conversation?.id) {\r\n      console.log(\r\n        `📝 Sending typing indicator: ${isTyping} to user ${receiverId}`\r\n      );\r\n      // TODO: Implémenter l'envoi via WebSocket/GraphQL subscription\r\n      // this.MessageService.sendTypingIndicator(this.conversation.id, receiverId, isTyping);\r\n    }\r\n  }\r\n\r\n  // === MÉTHODES POUR L'INTERFACE D'APPEL ===\r\n\r\n  onCallAccepted(call: Call): void {\r\n    // Gestion d'appel accepté maintenant dans ActiveCallComponent\r\n    this.isInCall = true;\r\n    this.startCallTimer();\r\n    this.toastService.showSuccess('Appel accepté');\r\n  }\r\n\r\n  onCallRejected(): void {\r\n    // Gestion de rejet d'appel maintenant dans IncomingCallComponent\r\n    this.toastService.showInfo('Appel rejeté');\r\n  }\r\n\r\n  // === MÉTHODES POUR LA LECTURE DES MESSAGES VOCAUX ===\r\n\r\n  playVoiceMessage(message: any): void {\r\n    this.toggleVoicePlayback(message);\r\n  }\r\n\r\n  isVoicePlaying(messageId: string): boolean {\r\n    return this.playingMessageId === messageId;\r\n  }\r\n\r\n  toggleVoicePlayback(message: any): void {\r\n    const messageId = message.id;\r\n    const audioUrl = this.getVoiceUrl(message);\r\n\r\n    if (!audioUrl) {\r\n      console.error('🎵 [Voice] No audio URL found for message:', messageId);\r\n      this.toastService.showError('Fichier audio introuvable');\r\n      return;\r\n    }\r\n\r\n    // Si c'est déjà en cours de lecture, arrêter\r\n    if (this.isVoicePlaying(messageId)) {\r\n      this.stopVoicePlayback();\r\n      return;\r\n    }\r\n\r\n    // Arrêter toute autre lecture en cours\r\n    this.stopVoicePlayback();\r\n\r\n    // Démarrer la nouvelle lecture\r\n    this.startVoicePlayback(message, audioUrl);\r\n  }\r\n\r\n  private startVoicePlayback(message: any, audioUrl: string): void {\r\n    const messageId = message.id;\r\n\r\n    try {\r\n      console.log(\r\n        '🎵 [Voice] Starting playback for:',\r\n        messageId,\r\n        'URL:',\r\n        audioUrl\r\n      );\r\n\r\n      this.currentAudio = new Audio(audioUrl);\r\n      this.playingMessageId = messageId;\r\n\r\n      // Initialiser les valeurs par défaut avec la nouvelle structure\r\n      const currentData = this.getVoicePlaybackData(messageId);\r\n      this.setVoicePlaybackData(messageId, {\r\n        progress: 0,\r\n        currentTime: 0,\r\n        speed: currentData.speed || 1,\r\n        duration: currentData.duration || 0,\r\n      });\r\n\r\n      // Configurer la vitesse de lecture\r\n      this.currentAudio.playbackRate = currentData.speed || 1;\r\n\r\n      // Événements audio\r\n      this.currentAudio.addEventListener('loadedmetadata', () => {\r\n        if (this.currentAudio) {\r\n          this.setVoicePlaybackData(messageId, {\r\n            duration: this.currentAudio.duration,\r\n          });\r\n          console.log(\r\n            '🎵 [Voice] Audio loaded, duration:',\r\n            this.currentAudio.duration\r\n          );\r\n        }\r\n      });\r\n\r\n      this.currentAudio.addEventListener('timeupdate', () => {\r\n        if (this.currentAudio && this.playingMessageId === messageId) {\r\n          const currentTime = this.currentAudio.currentTime;\r\n          const progress = (currentTime / this.currentAudio.duration) * 100;\r\n          this.setVoicePlaybackData(messageId, { currentTime, progress });\r\n          this.cdr.detectChanges();\r\n        }\r\n      });\r\n\r\n      this.currentAudio.addEventListener('ended', () => {\r\n        this.stopVoicePlayback();\r\n      });\r\n\r\n      this.currentAudio.addEventListener('error', (error) => {\r\n        console.error('🎵 [Voice] Audio error:', error);\r\n        this.toastService.showError('Erreur lors de la lecture audio');\r\n        this.stopVoicePlayback();\r\n      });\r\n\r\n      // Démarrer la lecture\r\n      this.currentAudio\r\n        .play()\r\n        .then(() => {\r\n          this.toastService.showSuccess('🎵 Lecture du message vocal');\r\n        })\r\n        .catch((error) => {\r\n          console.error('🎵 [Voice] Error starting playback:', error);\r\n          this.toastService.showError('Impossible de lire le message vocal');\r\n          this.stopVoicePlayback();\r\n        });\r\n    } catch (error) {\r\n      console.error('🎵 [Voice] Error creating audio:', error);\r\n      this.toastService.showError('Erreur lors de la lecture audio');\r\n      this.stopVoicePlayback();\r\n    }\r\n  }\r\n\r\n  private stopVoicePlayback(): void {\r\n    if (this.currentAudio) {\r\n      this.currentAudio.pause();\r\n      this.currentAudio.currentTime = 0;\r\n      this.currentAudio = null;\r\n    }\r\n    this.playingMessageId = null;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  getVoiceUrl(message: any): string {\r\n    // Vérifier les propriétés directes d'audio\r\n    if (message.voiceUrl) return message.voiceUrl;\r\n    if (message.audioUrl) return message.audioUrl;\r\n    if (message.voice) return message.voice;\r\n\r\n    // Vérifier les attachments audio\r\n    const audioAttachment = message.attachments?.find(\r\n      (att: any) => att.type?.startsWith('audio/') || att.type === 'AUDIO'\r\n    );\r\n\r\n    if (audioAttachment) {\r\n      return audioAttachment.url || audioAttachment.path || '';\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getVoiceWaves(message: any): number[] {\r\n    // Générer des waves basées sur l'ID du message pour la cohérence\r\n    const messageId = message.id || '';\r\n    const seed = messageId\r\n      .split('')\r\n      .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);\r\n    const waves: number[] = [];\r\n\r\n    for (let i = 0; i < 16; i++) {\r\n      const height = 4 + ((seed + i * 7) % 20);\r\n      waves.push(height);\r\n    }\r\n\r\n    return waves;\r\n  }\r\n\r\n  getVoiceProgress(message: any): number {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    const totalWaves = 16;\r\n    return Math.floor((data.progress / 100) * totalWaves);\r\n  }\r\n\r\n  getVoiceCurrentTime(message: any): string {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    return this.formatAudioTime(data.currentTime);\r\n  }\r\n\r\n  getVoiceDuration(message: any): string {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    const duration = data.duration || message.metadata?.duration || 0;\r\n\r\n    if (typeof duration === 'string') {\r\n      return duration; // Déjà formaté\r\n    }\r\n\r\n    return this.formatAudioTime(duration);\r\n  }\r\n\r\n  private formatAudioTime(seconds: number): string {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = Math.floor(seconds % 60);\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  seekVoiceMessage(message: any, waveIndex: number): void {\r\n    const messageId = message.id;\r\n\r\n    if (!this.currentAudio || this.playingMessageId !== messageId) {\r\n      return;\r\n    }\r\n\r\n    const totalWaves = 16;\r\n    const seekPercentage = (waveIndex / totalWaves) * 100;\r\n    const seekTime = (seekPercentage / 100) * this.currentAudio.duration;\r\n\r\n    this.currentAudio.currentTime = seekTime;\r\n  }\r\n\r\n  toggleVoiceSpeed(message: any): void {\r\n    const messageId = message.id;\r\n    const data = this.getVoicePlaybackData(messageId);\r\n\r\n    // Cycle entre 1x, 1.5x, 2x\r\n    const newSpeed = data.speed === 1 ? 1.5 : data.speed === 1.5 ? 2 : 1;\r\n\r\n    this.setVoicePlaybackData(messageId, { speed: newSpeed });\r\n\r\n    if (this.currentAudio && this.playingMessageId === messageId) {\r\n      this.currentAudio.playbackRate = newSpeed;\r\n    }\r\n\r\n    this.toastService.showSuccess(`Vitesse: ${newSpeed}x`);\r\n  }\r\n\r\n  // === MÉTHODES MANQUANTES POUR LE TEMPLATE ===\r\n\r\n  changeVoiceSpeed(message: any): void {\r\n    this.toggleVoiceSpeed(message);\r\n  }\r\n\r\n  getVoiceSpeed(message: any): number {\r\n    const data = this.getVoicePlaybackData(message.id);\r\n    return data.speed || 1;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.unsubscribe();\r\n\r\n    // Nettoyer les timers\r\n    if (this.callTimer) {\r\n      clearInterval(this.callTimer);\r\n    }\r\n    if (this.recordingTimer) {\r\n      clearInterval(this.recordingTimer);\r\n    }\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    // Nettoyer les ressources audio\r\n    if (this.mediaRecorder) {\r\n      if (this.mediaRecorder.state === 'recording') {\r\n        this.mediaRecorder.stop();\r\n      }\r\n      this.mediaRecorder.stream?.getTracks().forEach((track) => track.stop());\r\n    }\r\n\r\n    // Nettoyer la lecture audio\r\n    this.stopVoicePlayback();\r\n  }\r\n}\r\n", "<!-- ===== MESSAGE CHAT COMPONENT - REORGANIZED & OPTIMIZED ===== -->\n\n<!-- Éléments vidéo pour WebRTC - cachés visuellement mais audio actif -->\n<video\n  #localVideo\n  autoplay\n  muted\n  playsinline\n  style=\"\n    position: absolute;\n    top: -9999px;\n    left: -9999px;\n    width: 1px;\n    height: 1px;\n  \"\n></video>\n<video\n  #remoteVideo\n  autoplay\n  playsinline\n  style=\"\n    position: absolute;\n    top: -9999px;\n    left: -9999px;\n    width: 1px;\n    height: 1px;\n  \"\n></video>\n\n<div\n  style=\"\n    display: flex;\n    flex-direction: column;\n    height: 100vh;\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n    color: #1f2937;\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  \"\n>\n  <!-- ===== ANIMATIONS CSS ===== -->\n  <style>\n    @keyframes pulse {\n      0%,\n      100% {\n        opacity: 1;\n      }\n      50% {\n        opacity: 0.5;\n      }\n    }\n    @keyframes bounce {\n      0%,\n      20%,\n      53%,\n      80%,\n      100% {\n        transform: translateY(0);\n      }\n      40%,\n      43% {\n        transform: translateY(-8px);\n      }\n      70% {\n        transform: translateY(-4px);\n      }\n    }\n    @keyframes spin {\n      from {\n        transform: rotate(0deg);\n      }\n      to {\n        transform: rotate(360deg);\n      }\n    }\n    @keyframes ping {\n      75%,\n      100% {\n        transform: scale(2);\n        opacity: 0;\n      }\n    }\n    @keyframes slideInUp {\n      from {\n        transform: translateX(-50%) translateY(20px);\n        opacity: 0;\n      }\n      to {\n        transform: translateX(-50%) translateY(0);\n        opacity: 1;\n      }\n    }\n  </style>\n\n  <!-- ===== HEADER SECTION ===== -->\n  <header\n    style=\"\n      display: flex;\n      align-items: center;\n      padding: 12px 16px;\n      background: #ffffff;\n      border-bottom: 1px solid #e5e7eb;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      position: relative;\n      z-index: 10;\n    \"\n  >\n    <!-- Bouton retour -->\n    <button\n      (click)=\"goBackToConversations()\"\n      style=\"\n        padding: 10px;\n        margin-right: 12px;\n        border-radius: 50%;\n        border: none;\n        background: transparent;\n        cursor: pointer;\n        transition: all 0.2s ease;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        min-width: 40px;\n        min-height: 40px;\n      \"\n      onmouseover=\"this.style.background='#f3f4f6'; this.style.transform='scale(1.05)'\"\n      onmouseout=\"this.style.background='transparent'; this.style.transform='scale(1)'\"\n      title=\"Retour aux conversations\"\n    >\n      <i\n        class=\"fas fa-arrow-left\"\n        style=\"color: #374151; font-size: 18px; font-weight: bold\"\n      ></i>\n    </button>\n\n    <!-- Info utilisateur -->\n    <div style=\"display: flex; align-items: center; flex: 1; min-width: 0\">\n      <!-- Avatar avec statut -->\n      <div style=\"position: relative; margin-right: 12px\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            object-fit: cover;\n            border: 2px solid transparent;\n            cursor: pointer;\n            transition: transform 0.2s ease;\n          \"\n          (click)=\"openUserProfile(otherParticipant?.id!)\"\n          onmouseover=\"this.style.transform='scale(1.05)'\"\n          onmouseout=\"this.style.transform='scale(1)'\"\n          title=\"Voir le profil\"\n        />\n        <!-- Indicateur en ligne -->\n        <div\n          *ngIf=\"otherParticipant?.isOnline\"\n          style=\"\n            position: absolute;\n            bottom: 0;\n            right: 0;\n            width: 12px;\n            height: 12px;\n            background: #10b981;\n            border: 2px solid transparent;\n            border-radius: 50%;\n            animation: pulse 2s infinite;\n          \"\n        ></div>\n      </div>\n\n      <!-- Nom et statut -->\n      <div style=\"flex: 1; min-width: 0\">\n        <h3\n          style=\"\n            font-weight: 600;\n            color: #111827;\n            margin: 0;\n            font-size: 16px;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          \"\n        >\n          {{ otherParticipant?.username || \"Utilisateur\" }}\n        </h3>\n        <div style=\"font-size: 14px; color: #6b7280; margin-top: 2px\">\n          <!-- Indicateur de frappe -->\n          <div\n            *ngIf=\"isUserTyping\"\n            style=\"display: flex; align-items: center; gap: 4px; color: #10b981\"\n          >\n            <span>En train d'écrire</span>\n            <div style=\"display: flex; gap: 2px\">\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.1s;\n                \"\n              ></div>\n              <div\n                style=\"\n                  width: 4px;\n                  height: 4px;\n                  background: #10b981;\n                  border-radius: 50%;\n                  animation: bounce 1s infinite 0.2s;\n                \"\n              ></div>\n            </div>\n          </div>\n          <!-- Statut en ligne -->\n          <span *ngIf=\"!isUserTyping\">\n            {{\n              otherParticipant?.isOnline\n                ? \"En ligne\"\n                : formatLastActive(otherParticipant?.lastActive)\n            }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Actions -->\n    <div style=\"display: flex; align-items: center; gap: 8px\">\n      <!-- Appel vidéo -->\n      <button\n        (click)=\"startVideoCall()\"\n        style=\"\n          padding: 10px;\n          border-radius: 50%;\n          border: none;\n          background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n          color: white;\n          cursor: pointer;\n          transition: all 0.3s;\n          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        \"\n        title=\"Appel vidéo\"\n        onmouseover=\"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(59, 130, 246, 0.4)'\"\n        onmouseout=\"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'\"\n      >\n        <i class=\"fas fa-video\" style=\"font-size: 14px\"></i>\n      </button>\n\n      <!-- Appel vocal -->\n      <button\n        (click)=\"startVoiceCall()\"\n        style=\"\n          padding: 10px;\n          border-radius: 50%;\n          border: none;\n          background: linear-gradient(135deg, #10b981, #047857);\n          color: white;\n          cursor: pointer;\n          transition: all 0.3s;\n          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n          width: 40px;\n          height: 40px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        \"\n        title=\"Appel vocal\"\n        onmouseover=\"this.style.transform='scale(1.1)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'\"\n        onmouseout=\"this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(16, 185, 129, 0.3)'\"\n      >\n        <i class=\"fas fa-phone\" style=\"font-size: 14px\"></i>\n      </button>\n\n      <!-- Recherche -->\n      <button\n        (click)=\"toggleSearch()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        [style.background]=\"searchMode ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"searchMode ? '#16a34a' : '#6b7280'\"\n        title=\"Rechercher\"\n      >\n        <i class=\"fas fa-search\"></i>\n      </button>\n\n      <!-- Rechargement -->\n      <button\n        (click)=\"reloadConversation()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n        \"\n        [style.opacity]=\"isLoading ? '0.5' : '1'\"\n        [disabled]=\"isLoading\"\n        title=\"Recharger la conversation\"\n        onmouseover=\"this.style.background='#f3f4f6'; this.style.color='#374151'\"\n        onmouseout=\"this.style.background='transparent'; this.style.color='#6b7280'\"\n      >\n        <i\n          class=\"fas fa-sync-alt\"\n          [style.animation]=\"isLoading ? 'spin 1s linear infinite' : 'none'\"\n        ></i>\n      </button>\n\n      <!-- Menu principal -->\n      <button\n        (click)=\"toggleMainMenu()\"\n        style=\"\n          padding: 8px;\n          border-radius: 50%;\n          border: none;\n          background: transparent;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s;\n          position: relative;\n        \"\n        [style.background]=\"showMainMenu ? '#dcfce7' : 'transparent'\"\n        [style.color]=\"showMainMenu ? '#16a34a' : '#6b7280'\"\n        title=\"Menu\"\n      >\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n\n    <!-- Menu dropdown -->\n    <div\n      *ngIf=\"showMainMenu\"\n      style=\"\n        position: absolute;\n        top: 64px;\n        right: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 192px;\n      \"\n    >\n      <div style=\"padding: 8px\">\n        <button\n          (click)=\"toggleSearch(); showMainMenu = false\"\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-search\" style=\"color: #3b82f6\"></i>\n          <span style=\"color: #374151\">Rechercher</span>\n        </button>\n        <button\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-user\" style=\"color: #10b981\"></i>\n          <span style=\"color: #374151\">Voir le profil</span>\n        </button>\n        <hr style=\"margin: 8px 0; border-color: #e5e7eb\" />\n        <button\n          style=\"\n            width: 100%;\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            padding: 8px 12px;\n            border-radius: 8px;\n            border: none;\n            background: transparent;\n            cursor: pointer;\n            transition: all 0.2s;\n            text-align: left;\n          \"\n          onmouseover=\"this.style.background='#f3f4f6'\"\n          onmouseout=\"this.style.background='transparent'\"\n        >\n          <i class=\"fas fa-cog\" style=\"color: #6b7280\"></i>\n          <span style=\"color: #374151\">Paramètres</span>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- ===== MAIN MESSAGES SECTION ===== -->\n  <main\n    style=\"flex: 1; overflow-y: auto; padding: 16px; position: relative\"\n    #messagesContainer\n    (scroll)=\"onScroll($event)\"\n    (dragover)=\"onDragOver($event)\"\n    (dragleave)=\"onDragLeave($event)\"\n    (drop)=\"onDrop($event)\"\n    [style.background]=\"isDragOver ? 'rgba(34, 197, 94, 0.1)' : 'transparent'\"\n  >\n    <!-- Drag & Drop Overlay -->\n    <div\n      *ngIf=\"isDragOver\"\n      style=\"\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(34, 197, 94, 0.2);\n        border: 2px dashed transparent;\n        border-radius: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 50;\n        backdrop-filter: blur(2px);\n        animation: pulse 2s infinite;\n      \"\n    >\n      <div\n        style=\"\n          text-align: center;\n          background: #ffffff;\n          padding: 24px;\n          border-radius: 12px;\n          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);\n          border: 1px solid transparent;\n        \"\n      >\n        <i\n          class=\"fas fa-cloud-upload-alt\"\n          style=\"\n            font-size: 48px;\n            color: #10b981;\n            margin-bottom: 12px;\n            animation: bounce 1s infinite;\n          \"\n        ></i>\n        <p\n          style=\"\n            font-size: 20px;\n            font-weight: bold;\n            color: #047857;\n            margin-bottom: 8px;\n          \"\n        >\n          Déposez vos fichiers ici\n        </p>\n        <p style=\"font-size: 14px; color: #10b981\">\n          Images, vidéos, documents...\n        </p>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div\n      *ngIf=\"isLoading\"\n      style=\"\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 32px 0;\n      \"\n    >\n      <div\n        style=\"\n          width: 32px;\n          height: 32px;\n          border: 2px solid #e5e7eb;\n          border-bottom-color: #10b981;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin-bottom: 16px;\n        \"\n      ></div>\n      <span style=\"color: #6b7280\">Chargement des messages...</span>\n    </div>\n\n    <!-- Empty State -->\n    <div\n      *ngIf=\"!isLoading && messages.length === 0\"\n      style=\"\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 64px 0;\n      \"\n    >\n      <div style=\"font-size: 64px; color: #d1d5db; margin-bottom: 16px\">\n        <i class=\"fas fa-comments\"></i>\n      </div>\n      <h3\n        style=\"\n          font-size: 20px;\n          font-weight: 600;\n          color: #374151;\n          margin-bottom: 8px;\n        \"\n      >\n        Aucun message\n      </h3>\n      <p style=\"color: #6b7280; text-align: center\">\n        Commencez votre conversation avec {{ otherParticipant?.username }}\n      </p>\n    </div>\n\n    <!-- Messages List -->\n    <div\n      *ngIf=\"!isLoading && messages.length > 0\"\n      style=\"display: flex; flex-direction: column; gap: 8px\"\n    >\n      <ng-container\n        *ngFor=\"\n          let message of messages;\n          let i = index;\n          trackBy: trackByMessageId\n        \"\n      >\n        <!-- Date Separator -->\n        <div\n          *ngIf=\"shouldShowDateSeparator(i)\"\n          style=\"display: flex; justify-content: center; margin: 16px 0\"\n        >\n          <div\n            style=\"\n              background: #ffffff;\n              padding: 4px 12px;\n              border-radius: 20px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n            \"\n          >\n            <span style=\"font-size: 12px; color: #6b7280\">\n              {{ formatDateSeparator(message.timestamp) }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Message Container -->\n        <div\n          style=\"display: flex\"\n          [style.justify-content]=\"\n            message.sender?.id === currentUserId ? 'flex-end' : 'flex-start'\n          \"\n          [id]=\"'message-' + message.id\"\n          (click)=\"onMessageClick(message, $event)\"\n          (contextmenu)=\"onMessageContextMenu(message, $event)\"\n        >\n          <!-- Avatar for others -->\n          <div\n            *ngIf=\"message.sender?.id !== currentUserId && shouldShowAvatar(i)\"\n            style=\"margin-right: 8px; flex-shrink: 0\"\n          >\n            <img\n              [src]=\"\n                message.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              [alt]=\"message.sender?.username\"\n              style=\"\n                width: 32px;\n                height: 32px;\n                border-radius: 50%;\n                object-fit: cover;\n                cursor: pointer;\n                transition: transform 0.2s;\n              \"\n              (click)=\"openUserProfile(message.sender?.id!)\"\n              onmouseover=\"this.style.transform='scale(1.05)'\"\n              onmouseout=\"this.style.transform='scale(1)'\"\n            />\n          </div>\n\n          <!-- Message Bubble -->\n          <div\n            [style.background-color]=\"\n              message.sender?.id === currentUserId ? '#3b82f6' : '#ffffff'\n            \"\n            [style.color]=\"\n              message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n            \"\n            style=\"\n              max-width: 320px;\n              padding: 12px 16px;\n              border-radius: 18px;\n              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n              position: relative;\n              word-wrap: break-word;\n              overflow-wrap: break-word;\n              border: none;\n            \"\n          >\n            <!-- Sender Name (for groups) -->\n            <div\n              *ngIf=\"\n                isGroupConversation() &&\n                message.sender?.id !== currentUserId &&\n                shouldShowSenderName(i)\n              \"\n              style=\"\n                font-size: 12px;\n                font-weight: 600;\n                margin-bottom: 4px;\n                opacity: 0.75;\n              \"\n              [style.color]=\"getUserColor(message.sender?.id!)\"\n            >\n              {{ message.sender?.username }}\n            </div>\n\n            <!-- Text Content -->\n            <div\n              *ngIf=\"getMessageType(message) === 'text'\"\n              style=\"word-wrap: break-word; overflow-wrap: break-word\"\n            >\n              <div [innerHTML]=\"formatMessageContent(message.content)\"></div>\n            </div>\n\n            <!-- Image Content -->\n            <div *ngIf=\"hasImage(message)\" style=\"margin: 8px 0\">\n              <img\n                [src]=\"getImageUrl(message)\"\n                [alt]=\"message.content || 'Image'\"\n                (click)=\"openImageViewer(message)\"\n                (load)=\"onImageLoad($event, message)\"\n                (error)=\"onImageError($event, message)\"\n                style=\"\n                  max-width: 280px;\n                  height: auto;\n                  border-radius: 12px;\n                  cursor: pointer;\n                  transition: transform 0.2s;\n                \"\n                onmouseover=\"this.style.transform='scale(1.02)'\"\n                onmouseout=\"this.style.transform='scale(1)'\"\n              />\n              <!-- Image Caption -->\n              <div\n                *ngIf=\"message.content\"\n                [style.color]=\"\n                  message.sender?.id === currentUserId ? '#ffffff' : '#111827'\n                \"\n                style=\"font-size: 14px; margin-top: 8px; line-height: 1.4\"\n                [innerHTML]=\"formatMessageContent(message.content)\"\n              ></div>\n            </div>\n\n            <!-- Voice Message Content -->\n            <div\n              *ngIf=\"getMessageType(message) === 'audio'\"\n              style=\"\n                display: flex;\n                align-items: center;\n                gap: 12px;\n                padding: 12px;\n                background: rgba(255, 255, 255, 0.1);\n                border-radius: 12px;\n                margin: 8px 0;\n                min-width: 200px;\n                max-width: 280px;\n              \"\n            >\n              <!-- Bouton Play/Pause -->\n              <button\n                (click)=\"toggleVoicePlayback(message)\"\n                style=\"\n                  width: 40px;\n                  height: 40px;\n                  border-radius: 50%;\n                  border: none;\n                  background: rgba(255, 255, 255, 0.2);\n                  color: inherit;\n                  cursor: pointer;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n                  transition: all 0.2s;\n                  flex-shrink: 0;\n                \"\n                onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"\n                onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\"\n                title=\"Lire/Pause\"\n              >\n                <i\n                  [class]=\"\n                    isVoicePlaying(message.id) ? 'fas fa-pause' : 'fas fa-play'\n                  \"\n                  style=\"font-size: 14px\"\n                ></i>\n              </button>\n\n              <!-- Visualisation des ondes sonores -->\n              <div\n                style=\"\n                  flex: 1;\n                  display: flex;\n                  align-items: center;\n                  gap: 2px;\n                  height: 24px;\n                  overflow: hidden;\n                \"\n              >\n                <div\n                  *ngFor=\"let wave of voiceWaves; let i = index\"\n                  style=\"\n                    width: 3px;\n                    background: currentColor;\n                    border-radius: 2px;\n                    opacity: 0.7;\n                    transition: height 0.3s ease;\n                  \"\n                  [style.height.px]=\"isVoicePlaying(message.id) ? wave : 8\"\n                  [style.animation]=\"\n                    isVoicePlaying(message.id) ? 'pulse 1s infinite' : 'none'\n                  \"\n                  [style.animation-delay]=\"i * 0.1 + 's'\"\n                ></div>\n              </div>\n\n              <!-- Durée et contrôles -->\n              <div\n                style=\"\n                  display: flex;\n                  align-items: center;\n                  gap: 8px;\n                  flex-shrink: 0;\n                \"\n              >\n                <!-- Durée -->\n                <div\n                  style=\"\n                    font-size: 12px;\n                    opacity: 0.8;\n                    min-width: 40px;\n                    text-align: right;\n                  \"\n                >\n                  {{ getVoiceDuration(message) }}\n                </div>\n\n                <!-- Vitesse de lecture (si en cours de lecture) -->\n                <button\n                  *ngIf=\"isVoicePlaying(message.id)\"\n                  (click)=\"changeVoiceSpeed(message)\"\n                  style=\"\n                    padding: 4px 8px;\n                    border-radius: 12px;\n                    border: none;\n                    background: rgba(255, 255, 255, 0.2);\n                    color: inherit;\n                    cursor: pointer;\n                    font-size: 11px;\n                    transition: all 0.2s;\n                  \"\n                  onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"\n                  onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\"\n                  title=\"Changer la vitesse\"\n                >\n                  {{ getVoiceSpeed(message) }}x\n                </button>\n              </div>\n            </div>\n\n            <!-- Message Metadata -->\n            <div\n              style=\"\n                display: flex;\n                align-items: center;\n                justify-content: flex-end;\n                gap: 4px;\n                margin-top: 4px;\n                font-size: 12px;\n                opacity: 0.75;\n              \"\n            >\n              <span>{{ formatMessageTime(message.timestamp) }}</span>\n              <div\n                *ngIf=\"message.sender?.id === currentUserId\"\n                style=\"display: flex; align-items: center\"\n              >\n                <i\n                  class=\"fas fa-clock\"\n                  *ngIf=\"message.status === 'SENDING'\"\n                  title=\"Envoi en cours\"\n                ></i>\n                <i\n                  class=\"fas fa-check\"\n                  *ngIf=\"message.status === 'SENT'\"\n                  title=\"Envoyé\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  *ngIf=\"message.status === 'DELIVERED'\"\n                  title=\"Livré\"\n                ></i>\n                <i\n                  class=\"fas fa-check-double\"\n                  style=\"color: #3b82f6\"\n                  *ngIf=\"message.status === 'READ'\"\n                  title=\"Lu\"\n                ></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Typing Indicator -->\n      <div\n        *ngIf=\"otherUserIsTyping\"\n        style=\"display: flex; align-items: start; gap: 8px\"\n      >\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          [alt]=\"otherParticipant?.username\"\n          style=\"\n            width: 32px;\n            height: 32px;\n            border-radius: 50%;\n            object-fit: cover;\n          \"\n        />\n        <div\n          style=\"\n            background: #ffffff;\n            padding: 12px 16px;\n            border-radius: 18px;\n            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          \"\n        >\n          <div style=\"display: flex; gap: 4px\">\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite;\n              \"\n            ></div>\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite 0.1s;\n              \"\n            ></div>\n            <div\n              style=\"\n                width: 8px;\n                height: 8px;\n                background: #6b7280;\n                border-radius: 50%;\n                animation: bounce 1s infinite 0.2s;\n              \"\n            ></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n\n  <!-- ===== FOOTER INPUT SECTION ===== -->\n  <footer\n    style=\"background: #ffffff; border-top: 1px solid #e5e7eb; padding: 16px\"\n  >\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      style=\"display: flex; align-items: end; gap: 12px\"\n    >\n      <!-- Left Actions -->\n      <div style=\"display: flex; gap: 8px\">\n        <!-- Emoji Button -->\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n          \"\n          [style.background]=\"showEmojiPicker ? '#dcfce7' : 'transparent'\"\n          [style.color]=\"showEmojiPicker ? '#16a34a' : '#6b7280'\"\n          title=\"Émojis\"\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n        >\n          <i class=\"fas fa-smile\"></i>\n        </button>\n\n        <!-- Attachment Button -->\n        <button\n          type=\"button\"\n          (click)=\"toggleAttachmentMenu()\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n          \"\n          [style.background]=\"showAttachmentMenu ? '#dcfce7' : 'transparent'\"\n          [style.color]=\"showAttachmentMenu ? '#16a34a' : '#6b7280'\"\n          title=\"Pièces jointes\"\n          onmouseover=\"this.style.background=this.style.background === 'transparent' ? '#f3f4f6' : this.style.background\"\n          onmouseout=\"this.style.background=this.style.background === '#f3f4f6' ? 'transparent' : this.style.background\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n        </button>\n\n        <!-- Voice Recording Button -->\n        <button\n          type=\"button\"\n          (mousedown)=\"onRecordStart($event)\"\n          (mouseup)=\"onRecordEnd($event)\"\n          (mouseleave)=\"onRecordCancel($event)\"\n          (touchstart)=\"onRecordStart($event)\"\n          (touchend)=\"onRecordEnd($event)\"\n          (touchcancel)=\"onRecordCancel($event)\"\n          style=\"\n            padding: 8px;\n            border-radius: 50%;\n            border: none;\n            background: transparent;\n            color: #6b7280;\n            cursor: pointer;\n            transition: all 0.2s;\n            position: relative;\n          \"\n          [style.background]=\"isRecordingVoice ? '#fef3c7' : 'transparent'\"\n          [style.color]=\"isRecordingVoice ? '#f59e0b' : '#6b7280'\"\n          [style.transform]=\"isRecordingVoice ? 'scale(1.1)' : 'scale(1)'\"\n          title=\"Maintenir pour enregistrer un message vocal\"\n          onmouseover=\"if(!this.style.background || this.style.background === 'transparent') this.style.background='#f3f4f6'\"\n          onmouseout=\"if(this.style.background === '#f3f4f6') this.style.background='transparent'\"\n        >\n          <i\n            [class]=\"isRecordingVoice ? 'fas fa-stop' : 'fas fa-microphone'\"\n            [style.animation]=\"isRecordingVoice ? 'pulse 1s infinite' : 'none'\"\n          ></i>\n\n          <!-- Indicateur d'enregistrement -->\n          <div\n            *ngIf=\"isRecordingVoice\"\n            style=\"\n              position: absolute;\n              top: -2px;\n              right: -2px;\n              width: 8px;\n              height: 8px;\n              background: #ef4444;\n              border-radius: 50%;\n              animation: ping 1s infinite;\n            \"\n          ></div>\n        </button>\n      </div>\n\n      <!-- Message Input -->\n      <div style=\"flex: 1; position: relative\">\n        <textarea\n          formControlName=\"content\"\n          placeholder=\"Tapez votre message...\"\n          (keydown)=\"onInputKeyDown($event)\"\n          (input)=\"onInputChange($event)\"\n          (focus)=\"onInputFocus()\"\n          style=\"\n            width: 100%;\n            min-height: 44px;\n            max-height: 120px;\n            padding: 12px 16px;\n            border: 1px solid #e5e7eb;\n            border-radius: 22px;\n            resize: none;\n            outline: none;\n            font-family: inherit;\n            font-size: 14px;\n            line-height: 1.4;\n            background: #ffffff;\n            color: #111827;\n            transition: all 0.2s;\n          \"\n          [disabled]=\"isInputDisabled()\"\n        ></textarea>\n      </div>\n\n      <!-- Send Button -->\n      <button\n        type=\"submit\"\n        [disabled]=\"!messageForm.valid || isSendingMessage\"\n        style=\"\n          padding: 12px;\n          border-radius: 50%;\n          border: none;\n          background: #3b82f6;\n          color: #ffffff;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 44px;\n          min-height: 44px;\n        \"\n        [style.background]=\"\n          !messageForm.valid || isSendingMessage ? '#9ca3af' : '#3b82f6'\n        \"\n        [style.cursor]=\"\n          !messageForm.valid || isSendingMessage ? 'not-allowed' : 'pointer'\n        \"\n        title=\"Envoyer\"\n        onmouseover=\"if(!this.disabled) this.style.background='#2563eb'\"\n        onmouseout=\"if(!this.disabled) this.style.background='#3b82f6'\"\n      >\n        <i class=\"fas fa-paper-plane\" *ngIf=\"!isSendingMessage\"></i>\n        <div\n          *ngIf=\"isSendingMessage\"\n          style=\"\n            width: 16px;\n            height: 16px;\n            border: 2px solid #ffffff;\n            border-top-color: transparent;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n          \"\n        ></div>\n      </button>\n    </form>\n\n    <!-- Emoji Picker -->\n    <div\n      *ngIf=\"showEmojiPicker\"\n      style=\"\n        position: absolute;\n        bottom: 80px;\n        left: 16px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        width: 320px;\n        max-height: 300px;\n        overflow-y: auto;\n      \"\n    >\n      <div style=\"padding: 16px\">\n        <h4\n          style=\"\n            margin: 0 0 12px 0;\n            font-size: 14px;\n            font-weight: 600;\n            color: #374151;\n          \"\n        >\n          Émojis\n        </h4>\n        <div\n          style=\"display: grid; grid-template-columns: repeat(8, 1fr); gap: 8px\"\n        >\n          <button\n            *ngFor=\"let emoji of getEmojisForCategory(selectedEmojiCategory)\"\n            (click)=\"insertEmoji(emoji)\"\n            style=\"\n              padding: 8px;\n              border: none;\n              background: transparent;\n              border-radius: 8px;\n              cursor: pointer;\n              font-size: 20px;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n            [title]=\"emoji.name\"\n          >\n            {{ emoji.emoji }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Attachment Menu -->\n    <div\n      *ngIf=\"showAttachmentMenu\"\n      style=\"\n        position: absolute;\n        bottom: 80px;\n        left: 60px;\n        background: #ffffff;\n        border-radius: 16px;\n        box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\n        border: 1px solid #e5e7eb;\n        z-index: 50;\n        min-width: 200px;\n      \"\n    >\n      <div style=\"padding: 16px\">\n        <h4\n          style=\"\n            margin: 0 0 12px 0;\n            font-size: 14px;\n            font-weight: 600;\n            color: #374151;\n          \"\n        >\n          Pièces jointes\n        </h4>\n        <div\n          style=\"\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 12px;\n          \"\n        >\n          <!-- Images -->\n          <button\n            (click)=\"triggerFileInput('image')\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #dbeafe;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-image\"\n                style=\"color: #3b82f6; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Images</span\n            >\n          </button>\n\n          <!-- Documents -->\n          <button\n            (click)=\"triggerFileInput('document')\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #fef3c7;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-file-alt\"\n                style=\"color: #f59e0b; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Documents</span\n            >\n          </button>\n\n          <!-- Camera -->\n          <button\n            (click)=\"openCamera()\"\n            style=\"\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              gap: 8px;\n              padding: 16px;\n              border: none;\n              background: transparent;\n              border-radius: 12px;\n              cursor: pointer;\n              transition: all 0.2s;\n            \"\n            onmouseover=\"this.style.background='#f3f4f6'\"\n            onmouseout=\"this.style.background='transparent'\"\n          >\n            <div\n              style=\"\n                width: 48px;\n                height: 48px;\n                background: #dcfce7;\n                border-radius: 50%;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <i\n                class=\"fas fa-camera\"\n                style=\"color: #10b981; font-size: 20px\"\n              ></i>\n            </div>\n            <span style=\"font-size: 14px; font-weight: 500; color: #374151\"\n              >Caméra</span\n            >\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Hidden File Input -->\n    <input\n      #fileInput\n      type=\"file\"\n      style=\"display: none\"\n      (change)=\"onFileSelected($event)\"\n      [accept]=\"getFileAcceptTypes()\"\n      multiple\n    />\n  </footer>\n\n  <!-- Overlay to close menus -->\n  <div\n    *ngIf=\"showEmojiPicker || showAttachmentMenu || showMainMenu\"\n    style=\"\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.25);\n      z-index: 40;\n    \"\n    (click)=\"closeAllMenus()\"\n  ></div>\n\n  <!-- Interface d'enregistrement vocal -->\n  <div\n    *ngIf=\"isRecordingVoice\"\n    style=\"\n      position: fixed;\n      bottom: 100px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: linear-gradient(135deg, #f59e0b, #d97706);\n      color: white;\n      padding: 20px 24px;\n      border-radius: 20px;\n      box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);\n      z-index: 60;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      min-width: 280px;\n      animation: slideInUp 0.3s ease-out;\n    \"\n  >\n    <!-- Icône microphone animée -->\n    <div\n      style=\"\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        animation: pulse 1s infinite;\n      \"\n    >\n      <i class=\"fas fa-microphone\" style=\"font-size: 20px\"></i>\n    </div>\n\n    <!-- Contenu central -->\n    <div style=\"flex: 1\">\n      <!-- Durée d'enregistrement -->\n      <div style=\"font-size: 18px; font-weight: bold; margin-bottom: 4px\">\n        {{ formatRecordingDuration(voiceRecordingDuration) }}\n      </div>\n\n      <!-- Visualisation des ondes sonores -->\n      <div style=\"display: flex; align-items: center; gap: 2px; height: 20px\">\n        <div\n          *ngFor=\"let wave of voiceWaves; let i = index\"\n          style=\"\n            width: 3px;\n            background: rgba(255, 255, 255, 0.8);\n            border-radius: 2px;\n            transition: height 0.3s ease;\n          \"\n          [style.height.px]=\"wave\"\n          [style.animation]=\"'bounce 1s infinite'\"\n          [style.animation-delay]=\"i * 0.1 + 's'\"\n        ></div>\n      </div>\n\n      <!-- Format d'enregistrement -->\n      <div style=\"font-size: 12px; opacity: 0.8; margin-top: 4px\">\n        Format: {{ getRecordingFormat() }}\n      </div>\n    </div>\n\n    <!-- Boutons d'action -->\n    <div style=\"display: flex; gap: 8px\">\n      <!-- Bouton Annuler -->\n      <button\n        (click)=\"onRecordCancel($event)\"\n        style=\"\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          border: none;\n          background: rgba(239, 68, 68, 0.8);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n        \"\n        onmouseover=\"this.style.background='rgba(239, 68, 68, 1)'\"\n        onmouseout=\"this.style.background='rgba(239, 68, 68, 0.8)'\"\n        title=\"Annuler l'enregistrement\"\n      >\n        <i class=\"fas fa-times\" style=\"font-size: 16px\"></i>\n      </button>\n\n      <!-- Bouton Envoyer -->\n      <button\n        (click)=\"onRecordEnd($event)\"\n        style=\"\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          border: none;\n          background: rgba(34, 197, 94, 0.8);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n        \"\n        onmouseover=\"this.style.background='rgba(34, 197, 94, 1)'\"\n        onmouseout=\"this.style.background='rgba(34, 197, 94, 0.8)'\"\n        title=\"Envoyer le message vocal\"\n      >\n        <i class=\"fas fa-paper-plane\" style=\"font-size: 16px\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Interface d'appel gérée par les composants globaux app-incoming-call et app-active-call -->\n\n<!-- Éléments vidéo gérés par les composants globaux d'appel -->\n\n<!-- Interface d'appel gérée par les composants globaux app-incoming-call et app-active-call -->\n\n<!-- Les composants d'appel sont maintenant gérés globalement dans app.component.html -->\n", "import { Component, OnDestroy, OnInit } from '@angular/core';\nimport { map, Subscription, BehaviorSubject, Observable } from 'rxjs';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { Conversation, Message } from 'src/app/models/message.model';\nimport { User } from '@app/models/user.model';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from '@app/services/message.service';\nimport { LoggerService } from 'src/app/services/logger.service';\nimport { ThemeService } from '@app/services/theme.service';\n@Component({\n  selector: 'app-messages-list',\n  templateUrl: './messages-list.component.html',\n  styleUrls: ['./messages-list.component.css'],\n})\nexport class MessagesListComponent implements OnInit, OnDestroy {\n  conversations: Conversation[] = [];\n  filteredConversations: Conversation[] = [];\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  searchQuery = '';\n  selectedConversationId: string | null = null;\n  isDarkMode$: Observable<boolean>;\n\n  private unreadCount = new BehaviorSubject<number>(0);\n  public unreadCount$ = this.unreadCount.asObservable();\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private MessageService: MessageService,\n    private authService: AuthuserService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private themeService: ThemeService\n  ) {\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n    if (!this.currentUserId) {\n      this.handleError('User not authenticated');\n      return;\n    }\n\n    this.loadConversations();\n    this.subscribeToUserStatus();\n    this.subscribeToConversationUpdates();\n\n    // Check for active conversation from route\n    this.route.firstChild?.params.subscribe((params) => {\n      this.selectedConversationId = params['conversationId'] || null;\n    });\n  }\n\n  loadConversations(): void {\n    this.loading = true;\n    this.error = null;\n\n    const sub = this.MessageService.getConversations().subscribe({\n      next: (conversations) => {\n        this.conversations = Array.isArray(conversations)\n          ? [...conversations]\n          : [];\n\n        this.filterConversations();\n        this.updateUnreadCount();\n        this.sortConversations();\n        this.loading = false;\n      },\n      error: (error) => {\n        this.error = error;\n        this.loading = false;\n        this.toastService.showError('Failed to load conversations');\n      },\n    });\n    this.subscriptions.push(sub);\n  }\n\n  filterConversations(): void {\n    if (!this.searchQuery) {\n      this.filteredConversations = [...this.conversations];\n      return;\n    }\n\n    const query = this.searchQuery.toLowerCase();\n    this.filteredConversations = this.conversations.filter((conv) => {\n      const otherParticipant = conv.participants\n        ? this.getOtherParticipant(conv.participants)\n        : undefined;\n      return (\n        otherParticipant?.username.toLowerCase().includes(query) ||\n        conv.lastMessage?.content?.toLowerCase().includes(query) ||\n        false\n      );\n    });\n  }\n\n  private updateUnreadCount(): void {\n    const count = this.conversations.reduce(\n      (sum, conv) => sum + (conv.unreadCount || 0),\n      0\n    );\n    this.unreadCount.next(count);\n  }\n\n  sortConversations(): void {\n    this.conversations.sort((a, b) => {\n      const dateA = this.getConversationDate(a);\n      const dateB = this.getConversationDate(b);\n      return dateB.getTime() - dateA.getTime();\n    });\n    this.filterConversations();\n  }\n\n  private getConversationDate(conv: Conversation): Date {\n    // Utiliser une date par défaut si aucune date n'est disponible\n    const defaultDate = new Date(0); // 1970-01-01\n\n    if (conv.lastMessage?.timestamp) {\n      return typeof conv.lastMessage.timestamp === 'string'\n        ? new Date(conv.lastMessage.timestamp)\n        : conv.lastMessage.timestamp;\n    }\n\n    if (conv.updatedAt) {\n      return typeof conv.updatedAt === 'string'\n        ? new Date(conv.updatedAt)\n        : conv.updatedAt;\n    }\n\n    if (conv.createdAt) {\n      return typeof conv.createdAt === 'string'\n        ? new Date(conv.createdAt)\n        : conv.createdAt;\n    }\n\n    return defaultDate;\n  }\n\n  getOtherParticipant(participants: User[] | undefined): User | undefined {\n    if (!participants || !Array.isArray(participants)) {\n      return undefined;\n    }\n    return participants.find(\n      (p) => p._id !== this.currentUserId && p.id !== this.currentUserId\n    );\n  }\n\n  subscribeToUserStatus(): void {\n    const sub = this.MessageService.subscribeToUserStatus()\n      .pipe(map((user) => this.MessageService.normalizeUser(user)))\n      .subscribe({\n        next: (user: User) => {\n          if (user) {\n            this.updateUserStatus(user);\n          }\n        },\n        error: (error) => {\n          this.toastService.showError('Connection to status updates lost');\n        },\n      });\n    this.subscriptions.push(sub);\n  }\n\n  subscribeToConversationUpdates(): void {\n    const sub = this.MessageService.subscribeToConversationUpdates(\n      'global'\n    ).subscribe({\n      next: (updatedConv) => {\n        const index = this.conversations.findIndex(\n          (c) => c.id === updatedConv.id\n        );\n        if (index >= 0) {\n          this.conversations[index] = updatedConv;\n        } else {\n          this.conversations.unshift(updatedConv);\n        }\n        this.sortConversations();\n      },\n      error: (error) => {\n        // Handle error silently\n      },\n    });\n    this.subscriptions.push(sub);\n  }\n\n  updateUserStatus(updatedUser: User): void {\n    this.conversations = this.conversations.map((conv) => {\n      if (!conv.participants) {\n        return conv;\n      }\n      const participants = conv.participants.map((p) => {\n        const userIdMatches =\n          p._id === updatedUser._id || p.id === updatedUser._id;\n        return userIdMatches\n          ? {\n              ...p,\n              isOnline: updatedUser.isOnline,\n              lastActive: updatedUser.lastActive,\n            }\n          : p;\n      });\n      return { ...conv, participants };\n    });\n    this.filterConversations();\n  }\n\n  openConversation(conversationId: string | undefined): void {\n    if (!conversationId) {\n      return;\n    }\n\n    this.selectedConversationId = conversationId;\n    this.router.navigate(['chat', conversationId], { relativeTo: this.route });\n  }\n\n  startNewConversation(): void {\n    this.router.navigate(['/messages/users']);\n  }\n\n  formatLastActive(lastActive: string): string {\n    return this.MessageService.formatLastActive(lastActive);\n  }\n\n  private handleError(message: string, error?: any): void {\n    this.logger.error('MessagesListComponent', message, error);\n    this.error = message;\n    this.loading = false;\n    this.toastService.showError(message);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n}\n", "<div\n  class=\"flex h-screen futuristic-messages-page relative\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Sidebar -->\n  <div\n    class=\"w-full md:w-80 lg:w-96 futuristic-sidebar flex flex-col relative z-10 backdrop-blur-sm\"\n  >\n    <!-- Header -->\n    <div class=\"futuristic-header sticky top-0 z-10 backdrop-blur-sm\">\n      <!-- Decorative top border with gradient and glow -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\n      ></div>\n\n      <div class=\"flex justify-between items-center mb-4\">\n        <h1 class=\"futuristic-title text-xl font-bold\">Messages</h1>\n        <div class=\"flex items-center space-x-2\">\n          <button\n            (click)=\"startNewConversation()\"\n            class=\"p-2 rounded-full text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-all relative group overflow-hidden\"\n            title=\"Nouvelle conversation\"\n          >\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-full blur-md\"\n            ></div>\n            <i\n              class=\"fas fa-edit relative z-10 group-hover:scale-110 transition-transform\"\n            ></i>\n          </button>\n          <div *ngIf=\"unreadCount$ | async as count\" class=\"relative\">\n            <span class=\"futuristic-badge\">\n              {{ count }}\n            </span>\n            <!-- Glow effect -->\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30 rounded-full blur-md transform scale-150 -z-10\"\n            ></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Recherche -->\n      <div class=\"relative group\">\n        <input\n          [(ngModel)]=\"searchQuery\"\n          (ngModelChange)=\"filterConversations()\"\n          type=\"text\"\n          placeholder=\"Rechercher des conversations...\"\n          class=\"w-full pl-10 pr-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n        />\n        <div\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n        >\n          <i\n            class=\"fas fa-search text-[#bdc6cc] dark:text-[#6d6870] group-focus-within:text-[#4f5fad] dark:group-focus-within:text-[#6d78c9] transition-colors\"\n          ></i>\n        </div>\n        <div\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n        >\n          <div\n            class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n          ></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Liste des conversations -->\n    <div class=\"flex-1 overflow-y-auto futuristic-conversations-list\">\n      <!-- État de chargement -->\n      <div\n        *ngIf=\"loading\"\n        class=\"flex flex-col items-center justify-center h-full p-4\"\n      >\n        <div class=\"futuristic-loading-circle\"></div>\n        <p class=\"futuristic-loading-text\">Chargement des conversations...</p>\n      </div>\n\n      <!-- État d'erreur -->\n      <div *ngIf=\"error\" class=\"futuristic-error-container\">\n        <div class=\"futuristic-error-icon\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n        </div>\n        <div class=\"flex-1\">\n          <h3 class=\"futuristic-error-title\">\n            Erreur de chargement des conversations\n          </h3>\n          <p class=\"futuristic-error-message\">\n            {{ error }}\n          </p>\n          <button (click)=\"loadConversations()\" class=\"futuristic-retry-button\">\n            <i class=\"fas fa-sync-alt mr-1.5\"></i> Réessayer\n          </button>\n        </div>\n      </div>\n\n      <!-- État vide -->\n      <div\n        *ngIf=\"!loading && filteredConversations.length === 0 && !searchQuery\"\n        class=\"futuristic-empty-state\"\n      >\n        <div class=\"futuristic-empty-icon\">\n          <i class=\"fas fa-comments\"></i>\n        </div>\n\n        <h3 class=\"futuristic-empty-title\">Aucune conversation</h3>\n\n        <p class=\"futuristic-empty-text\">\n          Démarrez une nouvelle conversation pour communiquer\n        </p>\n\n        <button\n          (click)=\"startNewConversation()\"\n          class=\"futuristic-start-button\"\n        >\n          <i class=\"fas fa-plus-circle mr-2\"></i>\n          Nouvelle Conversation\n        </button>\n      </div>\n\n      <!-- État sans résultats -->\n      <div\n        *ngIf=\"!loading && filteredConversations.length === 0 && searchQuery\"\n        class=\"futuristic-no-results\"\n      >\n        <div class=\"futuristic-empty-icon\">\n          <i class=\"fas fa-search\"></i>\n        </div>\n\n        <h3 class=\"futuristic-empty-title\">Aucun résultat trouvé</h3>\n\n        <p class=\"futuristic-empty-text\">Essayez un autre terme de recherche</p>\n      </div>\n\n      <!-- Conversations -->\n      <ul\n        *ngIf=\"!loading && filteredConversations.length > 0\"\n        class=\"futuristic-conversations\"\n      >\n        <li\n          *ngFor=\"let conv of filteredConversations\"\n          (click)=\"openConversation(conv.id)\"\n          class=\"futuristic-conversation-item\"\n          [ngClass]=\"{\n            'futuristic-conversation-selected':\n              selectedConversationId === conv.id\n          }\"\n        >\n          <div class=\"flex items-center\">\n            <!-- Avatar avec indicateur de statut -->\n            <div class=\"futuristic-avatar\">\n              <img\n                [src]=\"\n                  (conv.participants\n                    ? getOtherParticipant(conv.participants)?.image\n                    : null) || 'assets/images/default-avatar.png'\n                \"\n                alt=\"User avatar\"\n              />\n\n              <!-- Online indicator with glow -->\n              <div\n                *ngIf=\"\n                  conv.participants &&\n                  getOtherParticipant(conv.participants)?.isOnline\n                \"\n                class=\"futuristic-online-indicator\"\n              ></div>\n            </div>\n\n            <!-- Détails de la conversation -->\n            <div class=\"futuristic-conversation-details\">\n              <div class=\"futuristic-conversation-header\">\n                <h3 class=\"futuristic-conversation-name\">\n                  {{\n                    (conv.participants\n                      ? getOtherParticipant(conv.participants)?.username\n                      : null) || \"Utilisateur inconnu\"\n                  }}\n                </h3>\n                <span class=\"futuristic-conversation-time\">\n                  {{ (conv.lastMessage?.timestamp | date : \"shortTime\") || \"\" }}\n                </span>\n              </div>\n\n              <div class=\"futuristic-conversation-preview\">\n                <p class=\"futuristic-conversation-message\">\n                  <span\n                    *ngIf=\"conv.lastMessage?.sender?.id === currentUserId\"\n                    class=\"futuristic-you-prefix\"\n                    >Vous:\n                  </span>\n                  {{ conv.lastMessage?.content || \"Pas encore de messages\" }}\n                </p>\n                <div\n                  *ngIf=\"conv.unreadCount && conv.unreadCount > 0\"\n                  class=\"futuristic-unread-badge\"\n                >\n                  {{ conv.unreadCount }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </li>\n      </ul>\n    </div>\n  </div>\n\n  <!-- Zone de contenu principal -->\n  <div class=\"flex-1 hidden md:flex flex-col futuristic-main-area\">\n    <!-- Router outlet pour le composant de chat -->\n    <router-outlet></router-outlet>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: MessageLayoutComponent,\n    children: [\n      { path: '', redirectTo: 'conversations', pathMatch: 'full' },\n      {\n        path: 'conversations',\n        component: MessagesListComponent,\n        data: { title: 'Conversations' },\n      },\n      {\n        path: 'conversations/chat/:id',\n        component: MessageChatComponent,\n        data: { title: 'Chat' },\n      },\n      {\n        path: 'chat/:id',\n        component: MessageChatComponent,\n        data: { title: 'Chat' },\n      },\n      {\n        path: 'users',\n        component: UserListComponent,\n        data: { title: 'Utilisateurs' },\n      },\n\n      {\n        path: 'new',\n        redirectTo: 'users',\n        pathMatch: 'full',\n      },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class MessagesRoutingModule {}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\n\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { VoiceMessageModule } from 'src/app/components/voice-message/voice-message.module';\n\n@NgModule({\n  declarations: [\n    MessageChatComponent,\n    MessagesListComponent,\n    UserListComponent,\n    MessageLayoutComponent,\n  ],\n  imports: [\n    CommonModule,\n    MessagesRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ApolloModule,\n    RouterModule,\n    VoiceMessageModule,\n  ],\n  providers: [UserStatusService, MessageService],\n})\nexport class MessagesModule {}\n", "// user-list.component.ts\nimport { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Subscription, interval, Observable } from 'rxjs';\nimport { User } from 'src/app/models/user.model';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { CallService } from 'src/app/services/call.service';\nimport { CallType, Call } from 'src/app/models/message.model';\nimport { LoggerService } from 'src/app/services/logger.service';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { ThemeService } from '@app/services/theme.service';\n\n@Component({\n  selector: 'app-user-list',\n  templateUrl: './user-list.component.html',\n  styleUrls: ['./user-list.component.css'],\n})\nexport class UserListComponent implements OnInit, OnDestroy {\n  users: User[] = [];\n  loading = true;\n  currentUserId: string | null = null;\n  isDarkMode$: Observable<boolean>;\n\n  // Pagination\n  currentPage = 1;\n  pageSize = 10;\n  totalUsers = 0;\n  totalPages = 0;\n  hasNextPage = false;\n  hasPreviousPage = false;\n\n  // Sorting and filtering\n  sortBy = 'username';\n  sortOrder = 'asc';\n  filterForm = new FormGroup({\n    searchQuery: new FormControl(''),\n    isOnline: new FormControl<boolean | null>(null),\n  });\n\n  // Auto-refresh\n  autoRefreshEnabled = true;\n  autoRefreshInterval = 30000; // 30 seconds\n  private autoRefreshSubscription?: Subscription;\n\n  private loadingMore = false;\n  private subscriptions: Subscription = new Subscription();\n\n  constructor(\n    private MessageService: MessageService,\n    private callService: CallService,\n    public router: Router,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private themeService: ThemeService\n  ) {\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n    this.setupFilterListeners();\n    this.setupAutoRefresh();\n    this.loadUsers();\n  }\n\n  private setupFilterListeners(): void {\n    // Subscribe to search query changes\n    const searchSub = this.filterForm\n      .get('searchQuery')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(searchSub);\n\n    // Subscribe to online status filter changes\n    const onlineSub = this.filterForm\n      .get('isOnline')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(onlineSub);\n  }\n\n  private setupAutoRefresh(): void {\n    if (this.autoRefreshEnabled) {\n      this.autoRefreshSubscription = interval(\n        this.autoRefreshInterval\n      ).subscribe(() => {\n        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\n          this.loadUsers(true);\n        }\n      });\n    }\n  }\n\n  toggleAutoRefresh(): void {\n    this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n    if (this.autoRefreshEnabled) {\n      this.setupAutoRefresh();\n    } else if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n      this.autoRefreshSubscription = undefined;\n    }\n  }\n\n  resetPagination(): void {\n    this.currentPage = 1;\n  }\n\n  // Get searchQuery from the form\n  get searchQuery(): string {\n    return this.filterForm.get('searchQuery')?.value || '';\n  }\n\n  // Set searchQuery in the form\n  set searchQuery(value: string) {\n    this.filterForm.get('searchQuery')?.setValue(value);\n  }\n\n  // Helper function for template type casting\n  $any(item: any): any {\n    return item;\n  }\n\n  loadUsers(forceRefresh = false): void {\n    if (this.loadingMore) return;\n\n    this.loading = true;\n\n    const searchQuery = this.filterForm.get('searchQuery')?.value || '';\n    const isOnline = this.filterForm.get('isOnline')?.value;\n\n    const sub = this.MessageService.getAllUsers(\n      forceRefresh,\n      searchQuery,\n      this.currentPage,\n      this.pageSize,\n      this.sortBy,\n      this.sortOrder,\n      isOnline === true ? true : undefined\n    ).subscribe({\n      next: (users) => {\n        if (!Array.isArray(users)) {\n          this.users = [];\n          this.loading = false;\n          this.loadingMore = false;\n          this.toastService.showError('Failed to load users: Invalid data');\n          return;\n        }\n\n        // If first page, replace users array; otherwise append\n        if (this.currentPage === 1) {\n          // Filter out current user\n          this.users = users.filter((user) => {\n            if (!user) return false;\n            const userId = user.id || user._id;\n            return userId !== this.currentUserId;\n          });\n        } else {\n          // Append new users to existing array, avoiding duplicates and filtering out current user\n          const newUsers = users.filter((newUser) => {\n            if (!newUser) return false;\n            const userId = newUser.id || newUser._id;\n            return (\n              userId !== this.currentUserId &&\n              !this.users.some(\n                (existingUser) =>\n                  (existingUser.id || existingUser._id) === userId\n              )\n            );\n          });\n\n          this.users = [...this.users, ...newUsers];\n        }\n\n        // Update pagination metadata from service\n        const pagination = this.MessageService.currentUserPagination;\n        this.totalUsers = pagination.totalCount;\n        this.totalPages = pagination.totalPages;\n        this.hasNextPage = pagination.hasNextPage;\n        this.hasPreviousPage = pagination.hasPreviousPage;\n\n        this.loading = false;\n        this.loadingMore = false;\n      },\n      error: (error) => {\n        this.loading = false;\n        this.loadingMore = false;\n        this.toastService.showError(\n          `Failed to load users: ${error.message || 'Unknown error'}`\n        );\n\n        if (this.currentPage === 1) {\n          this.users = [];\n        }\n      },\n      complete: () => {\n        this.loading = false;\n        this.loadingMore = false;\n      },\n    });\n\n    this.subscriptions.add(sub);\n  }\n\n  startConversation(userId: string | undefined) {\n    if (!userId) {\n      this.toastService.showError(\n        'Cannot start conversation with undefined user'\n      );\n      return;\n    }\n\n    this.toastService.showInfo('Creating conversation...');\n\n    this.MessageService.createConversation(userId).subscribe({\n      next: (conversation) => {\n        if (!conversation || !conversation.id) {\n          this.toastService.showError(\n            'Failed to create conversation: Invalid response'\n          );\n          return;\n        }\n\n        this.router\n          .navigate(['/messages/conversations/chat', conversation.id])\n          .then((success) => {\n            if (!success) {\n              this.toastService.showError('Failed to open conversation');\n            }\n          });\n      },\n      error: (error) => {\n        this.toastService.showError(\n          `Failed to create conversation: ${error.message || 'Unknown error'}`\n        );\n      },\n    });\n  }\n\n  startAudioCall(userId: string): void {\n    if (!userId) return;\n\n    this.callService.initiateCall(userId, CallType.AUDIO).subscribe({\n      next: (call: Call) => {\n        this.toastService.showSuccess('Audio call initiated');\n      },\n      error: (error: any) => {\n        this.toastService.showError('Failed to initiate audio call');\n      },\n    });\n  }\n\n  startVideoCall(userId: string): void {\n    if (!userId) return;\n\n    this.callService.initiateCall(userId, CallType.VIDEO).subscribe({\n      next: (call: Call) => {\n        this.toastService.showSuccess('Video call initiated');\n      },\n      error: (error: any) => {\n        this.toastService.showError('Failed to initiate video call');\n      },\n    });\n  }\n\n  loadNextPage(): void {\n    if (this.hasNextPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage++;\n      this.loadUsers();\n    }\n  }\n\n  loadPreviousPage(): void {\n    if (this.hasPreviousPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage--;\n      this.loadUsers();\n    }\n  }\n\n  refreshUsers(): void {\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  clearFilters(): void {\n    this.filterForm.reset({\n      searchQuery: '',\n      isOnline: null,\n    });\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  changeSortOrder(field: string): void {\n    if (this.sortBy === field) {\n      // Toggle sort order if clicking the same field\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Set new sort field with default ascending order\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n    if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n    }\n  }\n}\n", "<div\n  class=\"flex flex-col h-full futuristic-users-container\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <!-- Gradient orbs -->\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Additional glow effects for dark mode -->\n    <div\n      class=\"absolute top-[40%] right-[30%] w-40 h-40 rounded-full bg-gradient-to-br from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n    <div\n      class=\"absolute bottom-[60%] left-[25%] w-32 h-32 rounded-full bg-gradient-to-tl from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n\n    <!-- Grid pattern for light mode -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-0\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n      </div>\n    </div>\n\n    <!-- Horizontal scan line effect for dark mode -->\n    <div class=\"absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden\">\n      <div class=\"h-px w-full bg-[#00f7ff]/20 absolute animate-scan\"></div>\n    </div>\n  </div>\n  <!-- En-tête -->\n  <div class=\"futuristic-users-header\">\n    <div class=\"flex justify-between items-center mb-4\">\n      <h1 class=\"futuristic-title\">Nouvelle Conversation</h1>\n      <div class=\"flex space-x-2\">\n        <button\n          (click)=\"refreshUsers()\"\n          class=\"futuristic-action-button\"\n          title=\"Rafraîchir la liste\"\n        >\n          <i class=\"fas fa-sync-alt\"></i>\n        </button>\n        <button\n          (click)=\"goBackToConversations()\"\n          class=\"futuristic-action-button\"\n        >\n          <i class=\"fas fa-arrow-left\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Recherche et filtres -->\n    <div class=\"space-y-3\">\n      <!-- Recherche -->\n      <div class=\"relative\">\n        <input\n          [ngModel]=\"searchQuery\"\n          (ngModelChange)=\"searchQuery = $event\"\n          type=\"text\"\n          placeholder=\"Rechercher des utilisateurs...\"\n          class=\"w-full pl-10 pr-4 py-2 rounded-lg futuristic-input-field\"\n        />\n        <i\n          class=\"fas fa-search absolute left-3 top-3 text-[#6d6870] dark:text-[#a0a0a0]\"\n        ></i>\n      </div>\n\n      <!-- Filtres -->\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-4\">\n          <!-- Filtre en ligne -->\n          <div class=\"flex items-center space-x-2\">\n            <label class=\"futuristic-checkbox-container\">\n              <input\n                type=\"checkbox\"\n                id=\"onlineFilter\"\n                class=\"futuristic-checkbox\"\n                [checked]=\"filterForm.get('isOnline')?.value === true\"\n                (change)=\"\n                  filterForm\n                    .get('isOnline')\n                    ?.setValue($any($event.target).checked ? true : null)\n                \"\n              />\n              <span class=\"futuristic-checkbox-checkmark\"></span>\n            </label>\n            <label for=\"onlineFilter\" class=\"futuristic-label\"\n              >En ligne uniquement</label\n            >\n          </div>\n\n          <!-- Options de tri -->\n          <div class=\"flex items-center space-x-2\">\n            <span class=\"futuristic-label\">Trier par:</span>\n            <select\n              (change)=\"changeSortOrder($any($event.target).value)\"\n              class=\"futuristic-select\"\n            >\n              <option [selected]=\"sortBy === 'username'\" value=\"username\">\n                Nom\n              </option>\n              <option [selected]=\"sortBy === 'email'\" value=\"email\">\n                Email\n              </option>\n              <option [selected]=\"sortBy === 'lastActive'\" value=\"lastActive\">\n                Dernière activité\n              </option>\n            </select>\n            <button\n              (click)=\"\n                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';\n                loadUsers(true)\n              \"\n              class=\"futuristic-sort-button\"\n              [title]=\"\n                sortOrder === 'asc' ? 'Ordre croissant' : 'Ordre décroissant'\n              \"\n            >\n              <i\n                [class]=\"\n                  sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'\n                \"\n              ></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Effacer les filtres -->\n        <button (click)=\"clearFilters()\" class=\"futuristic-clear-button\">\n          Effacer les filtres\n        </button>\n      </div>\n\n      <!-- Info pagination -->\n      <div\n        *ngIf=\"totalUsers > 0\"\n        class=\"flex justify-between items-center futuristic-pagination-info\"\n      >\n        <span\n          >Affichage de {{ users.length }} sur\n          {{ totalUsers }} utilisateurs</span\n        >\n        <span>Page {{ currentPage }} sur {{ totalPages }}</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Liste des utilisateurs -->\n  <div\n    class=\"futuristic-users-list\"\n    (scroll)=\"\n      $any($event.target).scrollTop + $any($event.target).clientHeight >=\n        $any($event.target).scrollHeight - 200 && loadNextPage()\n    \"\n  >\n    <!-- État de chargement -->\n    <div *ngIf=\"loading && !users.length\" class=\"futuristic-loading-container\">\n      <div class=\"futuristic-loading-circle\"></div>\n      <div class=\"futuristic-loading-text\">Chargement des utilisateurs...</div>\n    </div>\n\n    <!-- État vide -->\n    <div *ngIf=\"!loading && users.length === 0\" class=\"futuristic-empty-state\">\n      <div class=\"futuristic-empty-icon\">\n        <i class=\"fas fa-users\"></i>\n      </div>\n      <h3 class=\"futuristic-empty-title\">Aucun utilisateur trouvé</h3>\n      <p class=\"futuristic-empty-text\">\n        Essayez un autre terme de recherche ou effacez les filtres\n      </p>\n    </div>\n\n    <!-- Liste des utilisateurs -->\n    <ul *ngIf=\"users.length > 0\" class=\"futuristic-users-grid\">\n      <li *ngFor=\"let user of users\" class=\"futuristic-user-card\">\n        <div\n          class=\"futuristic-user-content\"\n          (click)=\"startConversation(user.id || user._id)\"\n        >\n          <div class=\"futuristic-avatar\">\n            <img\n              [src]=\"user.image || 'assets/images/default-avatar.png'\"\n              alt=\"User avatar\"\n            />\n            <span\n              *ngIf=\"user.isOnline\"\n              class=\"futuristic-online-indicator\"\n            ></span>\n          </div>\n          <div class=\"futuristic-user-info\">\n            <h3 class=\"futuristic-username\">\n              {{ user.username }}\n            </h3>\n            <p class=\"futuristic-user-email\">{{ user.email }}</p>\n          </div>\n        </div>\n\n        <!-- Boutons d'appel -->\n        <div class=\"futuristic-call-buttons\">\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startAudioCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel audio\"\n          >\n            <i class=\"fas fa-phone\"></i>\n          </button>\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startVideoCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel vidéo\"\n          >\n            <i class=\"fas fa-video\"></i>\n          </button>\n        </div>\n      </li>\n    </ul>\n\n    <!-- Indicateur de chargement supplémentaire -->\n    <div *ngIf=\"loading && users.length > 0\" class=\"futuristic-loading-more\">\n      <div class=\"futuristic-loading-dots\">\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.2s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.4s\"></div>\n      </div>\n      <div class=\"futuristic-loading-text\">\n        Chargement de plus d'utilisateurs...\n      </div>\n    </div>\n\n    <!-- Bouton de chargement supplémentaire -->\n    <div *ngIf=\"hasNextPage && !loading\" class=\"futuristic-load-more-container\">\n      <button (click)=\"loadNextPage()\" class=\"futuristic-load-more-button\">\n        <i class=\"fas fa-chevron-down mr-2\"></i>\n        Charger plus d'utilisateurs\n      </button>\n    </div>\n  </div>\n</div>\n", "import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n    if (period < 0) {\n        period = 0;\n    }\n    return timer(period, period, scheduler);\n}\n"], "names": ["BehaviorSubject", "ToastService", "constructor", "toastsSubject", "toasts$", "asObservable", "currentId", "generateId", "Math", "random", "toString", "substr", "addToast", "toast", "newToast", "id", "duration", "currentToasts", "value", "next", "setTimeout", "removeToast", "show", "message", "type", "title", "dismiss", "showSuccess", "showError", "showWarning", "showInfo", "filter", "t", "success", "icon", "error", "action", "warning", "accessDenied", "code", "codeText", "label", "handler", "console", "log", "ownershipRequired", "resource", "clear", "factory", "ɵfac", "providedIn", "Validators", "Subscription", "CallType", "environment", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r4", "otherParticipant", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_29_Template_button_click_2_listener", "ɵɵrestoreView", "_r20", "ctx_r19", "ɵɵnextContext", "toggleSearch", "ɵɵresetView", "showMainMenu", "ctx_r9", "username", "ctx_r25", "formatDateSeparator", "message_r23", "timestamp", "MessageChatComponent_div_35_ng_container_1_div_3_Template_img_click_1_listener", "_r35", "$implicit", "ctx_r33", "openUserProfile", "sender", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "ɵɵstyleProp", "ctx_r27", "getUserColor", "ctx_r28", "formatMessageContent", "content", "ɵɵsanitizeHtml", "ctx_r39", "currentUserId", "MessageChatComponent_div_35_ng_container_1_div_7_Template_img_click_1_listener", "_r43", "ctx_r41", "openImageViewer", "MessageChatComponent_div_35_ng_container_1_div_7_Template_img_load_1_listener", "$event", "ctx_r44", "onImageLoad", "MessageChatComponent_div_35_ng_container_1_div_7_Template_img_error_1_listener", "ctx_r46", "onImageError", "ɵɵtemplate", "MessageChatComponent_div_35_ng_container_1_div_7_div_2_Template", "ctx_r29", "getImageUrl", "ctx_r49", "isVoicePlaying", "wave_r51", "i_r52", "MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template_button_click_0_listener", "_r56", "ctx_r54", "changeVoiceSpeed", "ctx_r50", "getVoiceSpeed", "MessageChatComponent_div_35_ng_container_1_div_8_Template_button_click_1_listener", "_r60", "ctx_r58", "toggleVoicePlayback", "MessageChatComponent_div_35_ng_container_1_div_8_div_4_Template", "MessageChatComponent_div_35_ng_container_1_div_8_button_8_Template", "ɵɵclassMap", "ctx_r30", "voiceWaves", "getVoiceDuration", "MessageChatComponent_div_35_ng_container_1_div_12_i_1_Template", "MessageChatComponent_div_35_ng_container_1_div_12_i_2_Template", "MessageChatComponent_div_35_ng_container_1_div_12_i_3_Template", "MessageChatComponent_div_35_ng_container_1_div_12_i_4_Template", "status", "ɵɵelementContainerStart", "MessageChatComponent_div_35_ng_container_1_div_1_Template", "MessageChatComponent_div_35_ng_container_1_Template_div_click_2_listener", "restoredCtx", "_r68", "ctx_r67", "onMessageClick", "MessageChatComponent_div_35_ng_container_1_Template_div_contextmenu_2_listener", "ctx_r69", "onMessageContextMenu", "MessageChatComponent_div_35_ng_container_1_div_3_Template", "MessageChatComponent_div_35_ng_container_1_div_5_Template", "MessageChatComponent_div_35_ng_container_1_div_6_Template", "MessageChatComponent_div_35_ng_container_1_div_7_Template", "MessageChatComponent_div_35_ng_container_1_div_8_Template", "MessageChatComponent_div_35_ng_container_1_div_12_Template", "ɵɵelementContainerEnd", "ctx_r21", "shouldShowDateSeparator", "i_r24", "shouldShowAvatar", "isGroupConversation", "shouldShowSenderName", "getMessageType", "hasImage", "ɵɵtextInterpolate", "formatMessageTime", "ctx_r22", "MessageChatComponent_div_35_ng_container_1_Template", "MessageChatComponent_div_35_div_2_Template", "ctx_r10", "messages", "trackByMessageId", "otherUserIsTyping", "MessageChatComponent_div_51_button_5_Template_button_click_0_listener", "_r73", "emoji_r71", "ctx_r72", "insert<PERSON><PERSON><PERSON>", "name", "emoji", "MessageChatComponent_div_51_button_5_Template", "ctx_r14", "getEmojisForCategory", "selectedEmojiCategory", "MessageChatComponent_div_52_Template_button_click_5_listener", "_r75", "ctx_r74", "triggerFileInput", "MessageChatComponent_div_52_Template_button_click_10_listener", "ctx_r76", "MessageChatComponent_div_52_Template_button_click_15_listener", "ctx_r77", "openCamera", "MessageChatComponent_div_55_Template_div_click_0_listener", "_r79", "ctx_r78", "closeAllMenus", "wave_r81", "i_r82", "MessageChatComponent_div_56_div_7_Template", "MessageChatComponent_div_56_Template_button_click_11_listener", "_r84", "ctx_r83", "onRecordCancel", "MessageChatComponent_div_56_Template_button_click_13_listener", "ctx_r85", "onRecordEnd", "ctx_r18", "formatRecordingDuration", "voiceRecordingDuration", "getRecordingFormat", "MessageChatComponent", "fb", "route", "router", "MessageService", "callService", "toastService", "cdr", "conversation", "currentUsername", "isLoading", "isLoadingMore", "hasMoreMessages", "showEmojiPicker", "showAttachmentMenu", "showSearch", "searchQuery", "searchResults", "searchMode", "isSendingMessage", "showMessageContextMenu", "selectedMessage", "contextMenuPosition", "x", "y", "showReactionPicker", "reactionPickerMessage", "showImageViewer", "selectedImage", "uploadProgress", "isUploading", "isDragOver", "isRecordingVoice", "voiceRecordingState", "mediaRecorder", "audioChunks", "recordingTimer", "currentAudio", "playingMessageId", "voicePlayback", "isInCall", "callType", "callDuration", "callTimer", "emojiCategories", "emojis", "MAX_MESSAGES_TO_LOAD", "currentPage", "isTyping", "isUserTyping", "typingTimeout", "subscriptions", "messageForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "isInputDisabled", "updateInputState", "contentControl", "get", "disable", "enable", "ngOnInit", "initializeComponent", "enableSoundsOnFirstInteraction", "ngAfterViewInit", "setupVideoElements", "localVideo", "remoteVideo", "setVideoElements", "nativeElement", "localVideoHidden", "remoteVideoHidden", "createVideoElementsManually", "localVideoEl", "document", "getElementById", "remoteVideoEl", "warn", "createElement", "autoplay", "muted", "playsInline", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "enableSounds", "removeEventListener", "addEventListener", "once", "loadCurrentUser", "loadConversation", "setupCallSubscriptions", "handleIncomingCall", "incomingCall", "caller", "play", "userString", "localStorage", "getItem", "user", "JSON", "parse", "userId", "_id", "conversationId", "snapshot", "paramMap", "cleanupSubscriptions", "getConversation", "subscribe", "setOtherParticipant", "loadMessages", "setupSubscriptions", "participants", "length", "isGroup", "find", "p", "participantId", "String", "firstParticipantId", "sort", "a", "b", "dateA", "Date", "createdAt", "getTime", "dateB", "total", "first", "last", "scrollToBottom", "loadMoreMessages", "offset", "getMessages", "newMessages", "reverse", "unsubscribe", "slice", "reloadConversation", "add", "subscribeToNewMessages", "newMessage", "production", "messageExists", "some", "msg", "push", "detectChanges", "senderId", "shouldMarkAsRead", "markMessageAsRead", "subscribeToTypingIndicator", "typingData", "subscribeToConversationUpdates", "conversationUpdate", "messageId", "sendMessage", "valid", "trim", "receiverId", "undefined", "reset", "messagesContainer", "element", "scrollTop", "scrollHeight", "diffMins", "floor", "now", "getVoicePlaybackData", "progress", "currentTime", "speed", "setVoicePlaybackData", "data", "startVideoCall", "initiateCall", "VIDEO", "startVoiceCall", "AUDIO", "formatFileSize", "bytes", "round", "downloadFile", "fileAttachment", "attachments", "att", "startsWith", "url", "link", "href", "download", "target", "click", "<PERSON><PERSON><PERSON><PERSON>", "toggleMainMenu", "goBackToConversations", "navigate", "then", "catch", "window", "location", "event", "preventDefault", "clientX", "clientY", "showQuickReactions", "stopPropagation", "quickReact", "toggleReaction", "reactToMessage", "result", "messageIndex", "findIndex", "hasUserReacted", "reaction", "replyToMessage", "forwardMessage", "deleteMessage", "canDelete", "confirm", "toggleEmojiPicker", "selectEmojiCategory", "category", "currentC<PERSON>nt", "newContent", "patchValue", "toggleAttachmentMenu", "index", "testAddMessage", "testMessage", "toLocaleTimeString", "toISOString", "isRead", "zoomImage", "factor", "imageElement", "querySelector", "currentTransform", "transform", "currentScale", "parseFloat", "match", "newScale", "max", "min", "classList", "remove", "resetZoom", "input", "fileInput", "accept", "date", "hour", "minute", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "urlRegex", "replace", "currentMessage", "previousMessage", "currentDate", "previousDate", "nextMessage", "attachment", "voiceUrl", "audioUrl", "voice", "hasImageAttachment", "hasImageUrl", "imageUrl", "hasFile", "hasFileAttachment", "imageAttachment", "path", "getFileName", "getFileSize", "size", "getFileIcon", "includes", "colors", "charCodeAt", "onInputChange", "handleTypingIndicator", "onInputKeyDown", "key", "shift<PERSON>ey", "onInputFocus", "onInputBlur", "onScroll", "src", "closeImageViewer", "downloadImage", "searchMessages", "toLowerCase", "onSearchQueryChange", "clearSearch", "jumpToMessage", "messageElement", "scrollIntoView", "behavior", "block", "closeContextMenu", "recipientId", "<PERSON><PERSON><PERSON>", "startCallTimer", "call", "acceptCall", "rejectCall", "setInterval", "resetCallState", "clearInterval", "toggleMute", "toggleVideo", "endCall", "formatCallDuration", "hours", "minutes", "seconds", "padStart", "startVoiceRecording", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "Error", "MediaRecorder", "stream", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "channelCount", "mimeType", "isTypeSupported", "animateVoice<PERSON>aves", "ondataavailable", "onstop", "processRecordedAudio", "onerror", "cancelVoiceRecording", "start", "errorMessage", "stopVoiceRecording", "state", "stop", "getTracks", "for<PERSON>ach", "track", "_this2", "audioBlob", "Blob", "extension", "audioFile", "File", "sendVoiceMessage", "_this3", "Promise", "resolve", "reject", "onRecordStart", "map", "onFileSelected", "files", "file", "uploadFile", "maxSize", "compressImage", "compressedFile", "sendFileToServer", "messageType", "getFileMessageType", "progressInterval", "resetUploadState", "getFileAcceptTypes", "onDragOver", "onDragLeave", "rect", "currentTarget", "getBoundingClientRect", "left", "right", "top", "bottom", "onDrop", "dataTransfer", "Array", "from", "quality", "canvas", "ctx", "getContext", "img", "Image", "onload", "max<PERSON><PERSON><PERSON>", "maxHeight", "width", "height", "ratio", "drawImage", "toBlob", "blob", "lastModified", "URL", "createObjectURL", "sendTypingIndicator", "clearTimeout", "onCallAccepted", "onCallRejected", "playVoiceMessage", "getVoiceUrl", "stopVoicePlayback", "startVoicePlayback", "Audio", "currentData", "playbackRate", "pause", "audioAttachment", "getVoiceWaves", "seed", "split", "reduce", "acc", "char", "waves", "i", "getVoiceProgress", "totalWaves", "getVoiceCurrentTime", "formatAudioTime", "metadata", "remainingSeconds", "seekVoiceMessage", "waveIndex", "seekPercentage", "seekTime", "toggleVoiceSpeed", "newSpeed", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "i4", "CallService", "i5", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "MessageChatComponent_Template_button_click_6_listener", "MessageChatComponent_Template_img_click_10_listener", "MessageChatComponent_div_11_Template", "MessageChatComponent_div_16_Template", "MessageChatComponent_span_17_Template", "MessageChatComponent_Template_button_click_19_listener", "MessageChatComponent_Template_button_click_21_listener", "MessageChatComponent_Template_button_click_23_listener", "MessageChatComponent_Template_button_click_25_listener", "MessageChatComponent_Template_button_click_27_listener", "MessageChatComponent_div_29_Template", "MessageChatComponent_Template_main_scroll_30_listener", "MessageChatComponent_Template_main_dragover_30_listener", "MessageChatComponent_Template_main_dragleave_30_listener", "MessageChatComponent_Template_main_drop_30_listener", "MessageChatComponent_div_32_Template", "MessageChatComponent_div_33_Template", "MessageChatComponent_div_34_Template", "MessageChatComponent_div_35_Template", "MessageChatComponent_Template_form_ngSubmit_37_listener", "MessageChatComponent_Template_button_click_39_listener", "MessageChatComponent_Template_button_click_41_listener", "MessageChatComponent_Template_button_mousedown_43_listener", "MessageChatComponent_Template_button_mouseup_43_listener", "MessageChatComponent_Template_button_mouseleave_43_listener", "MessageChatComponent_Template_button_touchstart_43_listener", "MessageChatComponent_Template_button_touchend_43_listener", "MessageChatComponent_Template_button_touchcancel_43_listener", "MessageChatComponent_div_45_Template", "MessageChatComponent_Template_textarea_keydown_47_listener", "MessageChatComponent_Template_textarea_input_47_listener", "MessageChatComponent_Template_textarea_focus_47_listener", "MessageChatComponent_i_49_Template", "MessageChatComponent_div_50_Template", "MessageChatComponent_div_51_Template", "MessageChatComponent_div_52_Template", "MessageChatComponent_Template_input_change_53_listener", "MessageChatComponent_div_55_Template", "MessageChatComponent_div_56_Template", "count_r6", "MessagesListComponent_div_39_Template_button_click_8_listener", "_r8", "ctx_r7", "loadConversations", "ctx_r2", "MessagesListComponent_div_40_Template_button_click_7_listener", "_r10", "startNewConversation", "conv_r12", "unreadCount", "MessagesListComponent_ul_42_li_1_Template_li_click_0_listener", "_r18", "ctx_r17", "openConversation", "MessagesListComponent_ul_42_li_1_div_4_Template", "MessagesListComponent_ul_42_li_1_span_14_Template", "MessagesListComponent_ul_42_li_1_div_16_Template", "ɵɵpureFunction1", "_c0", "ctx_r11", "selectedConversationId", "tmp_1_0", "getOtherParticipant", "tmp_2_0", "tmp_3_0", "ɵɵpipeBind2", "lastMessage", "MessagesListComponent_ul_42_li_1_Template", "ctx_r5", "filteredConversations", "MessagesListComponent", "authService", "logger", "themeService", "conversations", "loading", "unreadCount$", "isDarkMode$", "darkMode$", "getCurrentUserId", "handleError", "subscribeToUserStatus", "<PERSON><PERSON><PERSON><PERSON>", "params", "sub", "getConversations", "isArray", "filterConversations", "updateUnreadCount", "sortConversations", "query", "conv", "count", "sum", "getConversationDate", "defaultDate", "updatedAt", "pipe", "normalizeUser", "updateUserStatus", "updatedConv", "c", "unshift", "updatedUser", "userIdMatches", "relativeTo", "AuthuserService", "LoggerService", "i6", "ThemeService", "decls", "vars", "consts", "template", "MessagesListComponent_Template", "MessagesListComponent_Template_button_click_26_listener", "MessagesListComponent_div_29_Template", "MessagesListComponent_Template_input_ngModelChange_32_listener", "MessagesListComponent_div_38_Template", "MessagesListComponent_div_39_Template", "MessagesListComponent_div_40_Template", "MessagesListComponent_div_41_Template", "MessagesListComponent_ul_42_Template", "ɵɵclassProp", "ɵɵpipeBind1", "RouterModule", "UserListComponent", "MessageLayoutComponent", "routes", "component", "children", "redirectTo", "pathMatch", "MessagesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "FormsModule", "ReactiveFormsModule", "ApolloModule", "UserStatusService", "VoiceMessageModule", "MessagesModule", "declarations", "interval", "FormControl", "FormGroup", "ɵɵtextInterpolate2", "ctx_r0", "users", "totalUsers", "totalPages", "UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener", "_r13", "user_r7", "startAudioCall", "UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener", "_r16", "UserListComponent_ul_61_li_1_Template_div_click_1_listener", "startConversation", "UserListComponent_ul_61_li_1_span_4_Template", "UserListComponent_ul_61_li_1_button_11_Template", "UserListComponent_ul_61_li_1_button_12_Template", "email", "UserListComponent_ul_61_li_1_Template", "ctx_r3", "UserListComponent_div_63_Template_button_click_1_listener", "loadNextPage", "pageSize", "hasNextPage", "hasPreviousPage", "sortBy", "sortOrder", "filterForm", "autoRefreshEnabled", "autoRefreshInterval", "loadingMore", "setupFilterListeners", "setupAutoRefresh", "loadUsers", "searchSub", "valueChanges", "resetPagination", "onlineSub", "autoRefreshSubscription", "toggleAutoRefresh", "setValue", "$any", "item", "forceRefresh", "getAllUsers", "newUsers", "newUser", "existingUser", "pagination", "currentUserPagination", "totalCount", "complete", "createConversation", "loadPreviousPage", "refreshUsers", "clearFilters", "changeSortOrder", "field", "i7", "UserListComponent_Template", "UserListComponent_Template_button_click_27_listener", "UserListComponent_Template_button_click_29_listener", "UserListComponent_Template_input_ngModelChange_33_listener", "UserListComponent_Template_input_change_39_listener", "tmp_b_0", "checked", "UserListComponent_Template_select_change_46_listener", "UserListComponent_Template_button_click_53_listener", "UserListComponent_Template_button_click_55_listener", "UserListComponent_div_57_Template", "UserListComponent_Template_div_scroll_58_listener", "clientHeight", "UserListComponent_div_59_Template", "UserListComponent_div_60_Template", "UserListComponent_ul_61_Template", "UserListComponent_div_62_Template", "UserListComponent_div_63_Template", "asyncScheduler", "timer", "period", "scheduler"], "sourceRoot": "webpack:///", "x_google_ignoreList": [9]}