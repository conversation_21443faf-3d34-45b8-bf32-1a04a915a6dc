"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_notifications_notifications_module_ts"],{

/***/ 63226:
/*!********************************************************************************************!*\
  !*** ./src/app/views/front/notifications/notification-list/notification-list.component.ts ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationListComponent: () => (/* binding */ NotificationListComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 10819);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 75797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rxjs */ 59452);
/* harmony import */ var src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/models/message.model */ 15293);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs/operators */ 64334);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs/operators */ 33900);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs/operators */ 52575);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs/operators */ 91817);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs/operators */ 51567);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs/operators */ 70271);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rxjs/operators */ 61318);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/message.service */ 54537);
/* harmony import */ var _app_services_theme_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/services/theme.service */ 70487);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/common */ 60316);








const _c0 = ["notificationContainer"];
function NotificationListComponent_div_8_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 33)(1, "label", 34)(2, "input", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_div_3_Template_input_click_2_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r14);
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r13.toggleSelectAll($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "span", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("checked", ctx_r10.allSelected);
  }
}
function NotificationListComponent_div_8_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_button_9_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r16);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r15.markAllAsRead());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, " Tout marquer comme lu ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_8_button_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_button_11_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r18);
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r17.deleteAllNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, " Tout supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 23)(1, "button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r20);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r19.loadNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, NotificationListComponent_div_8_div_3_Template, 4, 1, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](4, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "button", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_Template_button_click_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r20);
      const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r21.toggleUnreadFilter());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "i", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "button", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r20);
      const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r22.toggleSound());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](8, "i", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, NotificationListComponent_div_8_button_9_Template, 3, 0, "button", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](10, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](11, NotificationListComponent_div_8_button_11_Template, 3, 0, "button", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](12, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](4, 9, ctx_r0.hasNotifications()));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("active", ctx_r0.showOnlyUnread);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("active", !ctx_r0.isSoundMuted);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate"]("title", ctx_r0.isSoundMuted ? "Activer le son" : "D\u00E9sactiver le son");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", ctx_r0.isSoundMuted ? "fa-volume-mute" : "fa-volume-up");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](10, 11, ctx_r0.unreadCount$) || 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](12, 13, ctx_r0.hasNotifications()));
  }
}
function NotificationListComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 41)(1, "span", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_9_Template_button_click_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r23.markSelectedAsRead());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](4, "i", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, " Marquer comme lu ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "button", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_9_Template_button_click_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r25.deleteSelectedNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](7, "i", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "button", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_9_Template_button_click_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      ctx_r26.selectedNotifications.clear();
      ctx_r26.showSelectionBar = false;
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r26.allSelected = false);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](10, "i", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, " Annuler ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("", ctx_r1.selectedNotifications.size, " s\u00E9lectionn\u00E9(s)");
  }
}
function NotificationListComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "p", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Chargement des notifications...");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 50)(1, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div")(4, "h3", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, "Erreur de chargement");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "p", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "button", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_11_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r28);
      const ctx_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r27.loadNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, " R\u00E9essayer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r3.getErrorMessage());
  }
}
function NotificationListComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 56)(1, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "h3", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Aucune notification");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "p", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, "Vous \u00EAtes \u00E0 jour !");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "button", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_12_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r30);
      const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r29.loadNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, " V\u00E9rifier les nouvelles notifications ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_14_ng_container_2_div_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", notification_r34.message == null ? null : notification_r34.message.content, " ");
  }
}
function NotificationListComponent_div_14_ng_container_2_div_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length, " pi\u00E8ce(s) jointe(s) ");
  }
}
function NotificationListComponent_div_14_ng_container_2_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "div", 92);
  }
}
function NotificationListComponent_div_14_ng_container_2_button_24_Template(rf, ctx) {
  if (rf & 1) {
    const _r45 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r45);
      const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r43 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r43.getNotificationAttachments(notification_r34.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 97);
  }
}
function NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 98);
  }
}
function NotificationListComponent_div_14_ng_container_2_button_25_Template(rf, ctx) {
  if (rf & 1) {
    const _r50 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r50);
      const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r48 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r48.joinConversation(notification_r34);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template, 1, 0, "i", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template, 1, 0, "i", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r39 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx_r39.loading);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r39.loading);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r39.loading);
  }
}
function NotificationListComponent_div_14_ng_container_2_button_28_Template(rf, ctx) {
  if (rf & 1) {
    const _r53 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r53);
      const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r51 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r51.markAsRead(notification_r34.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 100);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_14_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r55 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](1, "div", 66)(2, "div", 67)(3, "label", 34)(4, "input", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r55);
      const notification_r34 = restoredCtx.$implicit;
      const ctx_r54 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r54.toggleSelection(notification_r34.id, $event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "span", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](7, "img", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "div", 70)(9, "div", 71)(10, "div", 72)(11, "div", 73)(12, "span", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "div", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](16, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "div", 76)(18, "span", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](20, NotificationListComponent_div_14_ng_container_2_div_20_Template, 2, 1, "div", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](21, NotificationListComponent_div_14_ng_container_2_div_21_Template, 3, 1, "div", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](22, NotificationListComponent_div_14_ng_container_2_div_22_Template, 1, 0, "div", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](23, "div", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](24, NotificationListComponent_div_14_ng_container_2_button_24_Template, 2, 0, "button", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](25, NotificationListComponent_div_14_ng_container_2_button_25_Template, 3, 3, "button", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](26, "button", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r55);
      const notification_r34 = restoredCtx.$implicit;
      const ctx_r56 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r56.openNotificationDetails(notification_r34);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](27, "i", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](28, NotificationListComponent_div_14_ng_container_2_button_28_Template, 2, 0, "button", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](29, "button", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r55);
      const notification_r34 = restoredCtx.$implicit;
      const ctx_r57 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r57.deleteNotification(notification_r34.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](30, "i", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const notification_r34 = ctx.$implicit;
    const ctx_r32 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("futuristic-notification-unread", !notification_r34.isRead)("futuristic-notification-read", notification_r34.isRead)("futuristic-notification-selected", ctx_r32.isSelected(notification_r34.id));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("checked", ctx_r32.isSelected(notification_r34.id));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", (notification_r34.senderId == null ? null : notification_r34.senderId.image) || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"]((notification_r34.senderId == null ? null : notification_r34.senderId.username) || "Syst\u00E8me");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](16, 17, notification_r34.timestamp, "shortTime"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](notification_r34.content);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", notification_r34.message == null ? null : notification_r34.message.content);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !notification_r34.isRead);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", notification_r34.type === "NEW_MESSAGE" || notification_r34.type === "GROUP_INVITE" || notification_r34.type === "MESSAGE_REACTION");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !notification_r34.isRead);
  }
}
function NotificationListComponent_div_14_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "p", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Chargement des notifications plus anciennes... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r59 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 62, 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("scroll", function NotificationListComponent_div_14_Template_div_scroll_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r59);
      const _r31 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](1);
      const ctx_r58 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r58.onScroll(_r31));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_14_ng_container_2_Template, 31, 20, "ng-container", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](3, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](4, NotificationListComponent_div_14_div_4_Template, 4, 0, "div", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](3, 2, ctx_r5.filteredNotifications$));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r5.loadingMore);
  }
}
function NotificationListComponent_div_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "p", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Chargement des pi\u00E8ces jointes...");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 56)(1, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "h3", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Aucune pi\u00E8ce jointe");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "p", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, " Aucune pi\u00E8ce jointe n'a \u00E9t\u00E9 trouv\u00E9e pour cette notification. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_27_div_1_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r67 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 120)(1, "img", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r67);
      const attachment_r61 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r65 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r65.openAttachment(attachment_r61.url));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const attachment_r61 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", attachment_r61.url, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
  }
}
function NotificationListComponent_div_27_div_1_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 122);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const attachment_r61 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    const ctx_r63 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassMap"](ctx_r63.getFileIcon(attachment_r61.type));
  }
}
function NotificationListComponent_div_27_div_1_span_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span", 123);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const attachment_r61 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    const ctx_r64 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r64.formatFileSize(attachment_r61.size));
  }
}
function NotificationListComponent_div_27_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r72 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_27_div_1_div_1_Template, 2, 1, "div", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_27_div_1_div_2_Template, 2, 2, "div", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 110)(4, "div", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 112)(7, "span", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, NotificationListComponent_div_27_div_1_span_9_Template, 2, 1, "span", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "div", 115)(11, "button", 116);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_27_div_1_Template_button_click_11_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r72);
      const attachment_r61 = restoredCtx.$implicit;
      const ctx_r71 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r71.openAttachment(attachment_r61.url));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](12, "i", 117);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "button", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_27_div_1_Template_button_click_13_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r72);
      const attachment_r61 = restoredCtx.$implicit;
      const ctx_r73 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r73.downloadAttachment(attachment_r61));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](14, "i", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const attachment_r61 = ctx.$implicit;
    const ctx_r60 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r60.isImage(attachment_r61.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r60.isImage(attachment_r61.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", attachment_r61.name || "Pi\u00E8ce jointe", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r60.getFileTypeLabel(attachment_r61.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", attachment_r61.size);
  }
}
function NotificationListComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_27_div_1_Template, 15, 5, "div", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r8.currentAttachments);
  }
}
function NotificationListComponent_div_36_div_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 147)(1, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "Message original :");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r74 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r74.currentNotification.message == null ? null : ctx_r74.currentNotification.message.content, " ");
  }
}
function NotificationListComponent_div_36_div_35_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 137)(1, "span", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "Lu le :");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "span", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](5, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r75 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](5, 1, ctx_r75.currentNotification.readAt, "medium"));
  }
}
function NotificationListComponent_div_36_div_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 148)(1, "span", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 149);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Note : ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "span", 150);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, " Ouvrir les d\u00E9tails ne marque pas automatiquement comme lu ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_36_div_37_div_5_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r87 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 164)(1, "img", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_div_37_div_5_div_1_Template_img_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r87);
      const attachment_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r85 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r85.openAttachment(attachment_r81.url));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const attachment_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", attachment_r81.url, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
  }
}
function NotificationListComponent_div_36_div_37_div_5_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 165);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const attachment_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    const ctx_r83 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassMap"](ctx_r83.getFileIcon(attachment_r81.type));
  }
}
function NotificationListComponent_div_36_div_37_div_5_span_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span", 166);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const attachment_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    const ctx_r84 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r84.formatFileSize(attachment_r81.size));
  }
}
function NotificationListComponent_div_36_div_37_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r92 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 153);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_36_div_37_div_5_div_1_Template, 2, 1, "div", 154);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_36_div_37_div_5_div_2_Template, 2, 2, "div", 155);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 156)(4, "div", 157);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 158)(7, "span", 159);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, NotificationListComponent_div_36_div_37_div_5_span_9_Template, 2, 1, "span", 160);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "div", 161)(11, "button", 162);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_11_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r92);
      const attachment_r81 = restoredCtx.$implicit;
      const ctx_r91 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r91.openAttachment(attachment_r81.url));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](12, "i", 117);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "button", 163);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_13_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r92);
      const attachment_r81 = restoredCtx.$implicit;
      const ctx_r93 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r93.downloadAttachment(attachment_r81));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](14, "i", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const attachment_r81 = ctx.$implicit;
    const ctx_r80 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r80.isImage(attachment_r81.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r80.isImage(attachment_r81.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", attachment_r81.name || "Pi\u00E8ce jointe", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r80.getFileTypeLabel(attachment_r81.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", attachment_r81.size);
  }
}
function NotificationListComponent_div_36_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 124)(1, "h4", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 151);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, NotificationListComponent_div_36_div_37_div_5_Template, 15, 5, "div", 152);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r77 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" Pi\u00E8ces jointes (", ctx_r77.currentAttachments.length, ") ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r77.currentAttachments);
  }
}
function NotificationListComponent_div_36_button_39_i_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 170);
  }
}
function NotificationListComponent_div_36_button_39_i_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 171);
  }
}
function NotificationListComponent_div_36_button_39_Template(rf, ctx) {
  if (rf & 1) {
    const _r97 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 167);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_button_39_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r97);
      const ctx_r96 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r96.joinConversation(ctx_r96.currentNotification);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r96.closeNotificationDetailsModal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_36_button_39_i_1_Template, 1, 0, "i", 168);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_36_button_39_i_2_Template, 1, 0, "i", 169);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Rejoindre la conversation ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r78 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx_r78.loading);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r78.loading);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r78.loading);
  }
}
function NotificationListComponent_div_36_button_40_Template(rf, ctx) {
  if (rf & 1) {
    const _r99 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 172);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_button_40_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r99);
      const ctx_r98 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r98.markAsRead(ctx_r98.currentNotification.id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 173);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, " Marquer comme lu ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    const _r101 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 19)(1, "div", 124)(2, "h4", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "i", 126);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, " Exp\u00E9diteur ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "div", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "img", 128);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "div", 129)(8, "span", 130);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "span", 131);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](12, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "div", 124)(14, "h4", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](15, "i", 132);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](16, " Message ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "div", 133);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](19, NotificationListComponent_div_36_div_19_Template, 4, 1, "div", 134);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](20, "div", 124)(21, "h4", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](22, "i", 135);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](23, " Informations ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](24, "div", 136)(25, "div", 137)(26, "span", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](27, "Type :");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "span", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](29);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](30, "div", 137)(31, "span", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](32, "Statut :");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](33, "span", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](34);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](35, NotificationListComponent_div_36_div_35_Template, 6, 4, "div", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](36, NotificationListComponent_div_36_div_36_Template, 6, 0, "div", 141);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](37, NotificationListComponent_div_36_div_37_Template, 6, 2, "div", 142);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](38, "div", 143);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](39, NotificationListComponent_div_36_button_39_Template, 4, 3, "button", 144);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](40, NotificationListComponent_div_36_button_40_Template, 3, 0, "button", 145);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](41, "button", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_Template_button_click_41_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r101);
      const ctx_r100 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      ctx_r100.deleteNotification(ctx_r100.currentNotification.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r100.closeNotificationDetailsModal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](42, "i", 146);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](43, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.image) || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.username) || "Syst\u00E8me", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](12, 16, ctx_r9.currentNotification.timestamp, "medium"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r9.currentNotification.content, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r9.currentNotification.message == null ? null : ctx_r9.currentNotification.message.content);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r9.currentNotification.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("text-green-500", ctx_r9.currentNotification.isRead)("text-orange-500", !ctx_r9.currentNotification.isRead);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r9.currentNotification.isRead ? "Lu" : "Non lu", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r9.currentNotification.readAt);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r9.currentNotification.isRead);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r9.currentAttachments.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r9.currentNotification.type === "NEW_MESSAGE" || ctx_r9.currentNotification.type === "GROUP_INVITE" || ctx_r9.currentNotification.type === "MESSAGE_REACTION");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r9.currentNotification.isRead);
  }
}
class NotificationListComponent {
  constructor(messageService, themeService, router) {
    this.messageService = messageService;
    this.themeService = themeService;
    this.router = router;
    this.loading = true;
    this.loadingMore = false;
    this.hasMoreNotifications = true;
    this.error = null;
    this.showOnlyUnread = false;
    this.isSoundMuted = false;
    // Propriétés pour la sélection multiple
    this.selectedNotifications = new Set();
    this.allSelected = false;
    this.showSelectionBar = false;
    // Propriétés pour le modal des pièces jointes
    this.showAttachmentsModal = false;
    this.loadingAttachments = false;
    this.currentAttachments = [];
    // Propriétés pour le modal des détails de notification
    this.showNotificationDetailsModal = false;
    this.currentNotification = null;
    this.destroy$ = new rxjs__WEBPACK_IMPORTED_MODULE_4__.Subject();
    this.scrollPosition$ = new rxjs__WEBPACK_IMPORTED_MODULE_5__.BehaviorSubject(0);
    this.notifications$ = this.messageService.notifications$;
    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications
    this.unreadCount$ = this.messageService.notificationCount$;
    this.isDarkMode$ = this.themeService.darkMode$;
    // Vérifier l'état du son
    this.isSoundMuted = this.messageService.isMuted();
  }
  /**
   * Rejoint une conversation ou un groupe à partir d'une notification
   * @param notification Notification contenant les informations de la conversation ou du groupe
   */
  joinConversation(notification) {
    // Marquer la notification comme lue d'abord
    this.markAsRead(notification.id);
    // Extraire les informations pertinentes de la notification
    const conversationId = notification.conversationId || notification.metadata && notification.metadata['conversationId'] || (notification.relatedEntity && notification.relatedEntity.includes('conversation') ? notification.relatedEntity : null);
    const groupId = notification.groupId || notification.metadata && notification.metadata['groupId'] || (notification.relatedEntity && notification.relatedEntity.includes('group') ? notification.relatedEntity : null);
    // Déterminer où rediriger l'utilisateur
    if (conversationId) {
      this.router.navigate(['/messages/conversations/chat', conversationId]);
    } else if (groupId) {
      this.router.navigate(['/messages/group', groupId]);
    } else if (notification.senderId && notification.senderId.id) {
      this.loading = true;
      this.messageService.getOrCreateConversation(notification.senderId.id).subscribe({
        next: conversation => {
          this.loading = false;
          if (conversation && conversation.id) {
            this.router.navigate(['/messages/conversations/chat', conversation.id]);
          } else {
            this.router.navigate(['/messages']);
          }
        },
        error: error => {
          this.loading = false;
          this.error = error;
          this.router.navigate(['/messages']);
        }
      });
    } else {
      this.router.navigate(['/messages']);
    }
  }
  onScroll(target) {
    if (!target) return;
    const scrollPosition = target.scrollTop;
    const scrollHeight = target.scrollHeight;
    const clientHeight = target.clientHeight;
    // Si on est proche du bas (à 200px du bas)
    if (scrollHeight - scrollPosition - clientHeight < 200) {
      this.scrollPosition$.next(scrollPosition);
    }
  }
  ngOnInit() {
    // Charger la préférence de son depuis le localStorage
    const savedMutePreference = localStorage.getItem('notificationSoundMuted');
    if (savedMutePreference !== null) {
      this.isSoundMuted = savedMutePreference === 'true';
      this.messageService.setMuted(this.isSoundMuted);
    }
    this.loadNotifications();
    this.setupSubscriptions();
    this.setupInfiniteScroll();
    this.filterDeletedNotifications();
  }
  /**
   * Filtre les notifications supprimées lors du chargement initial
   */
  filterDeletedNotifications() {
    const deletedNotificationIds = this.getDeletedNotificationIds();
    if (deletedNotificationIds.size > 0) {
      this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
        const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));
        this.messageService.notifications.next(filteredNotifications);
        const unreadCount = filteredNotifications.filter(n => !n.isRead).length;
        this.messageService.notificationCount.next(unreadCount);
        this.updateNotificationCache(filteredNotifications);
      });
    }
  }
  setupInfiniteScroll() {
    // Configurer le chargement des anciennes notifications lors du défilement
    this.scrollPosition$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.debounceTime)(200),
    // Attendre 200ms après le dernier événement de défilement
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.distinctUntilChanged)(),
    // Ne déclencher que si la position de défilement a changé
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.filter)(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger
    ).subscribe(() => {
      this.loadMoreNotifications();
    });
  }
  loadNotifications() {
    this.loading = true;
    this.loadingMore = false;
    this.error = null;
    this.hasMoreNotifications = true;
    const deletedNotificationIds = this.getDeletedNotificationIds();
    this.messageService.getNotifications(true).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.map)(notifications => {
      if (deletedNotificationIds.size > 0) {
        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));
      }
      return notifications;
    })).subscribe({
      next: notifications => {
        this.messageService.notifications.next(notifications);
        const unreadCount = notifications.filter(n => !n.isRead).length;
        this.messageService.notificationCount.next(unreadCount);
        this.loading = false;
        this.hasMoreNotifications = this.messageService.hasMoreNotifications();
      },
      error: err => {
        this.error = err;
        this.loading = false;
        this.hasMoreNotifications = false;
      }
    });
  }
  loadMoreNotifications() {
    if (this.loadingMore || !this.hasMoreNotifications) return;
    this.loadingMore = true;
    const deletedNotificationIds = this.getDeletedNotificationIds();
    this.messageService.loadMoreNotifications().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.map)(notifications => {
      if (deletedNotificationIds.size > 0) {
        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));
      }
      return notifications;
    })).subscribe({
      next: notifications => {
        this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(existingNotifications => {
          const allNotifications = [...existingNotifications, ...notifications];
          this.messageService.notifications.next(allNotifications);
          const unreadCount = allNotifications.filter(n => !n.isRead).length;
          this.messageService.notificationCount.next(unreadCount);
          this.updateNotificationCache(allNotifications);
        });
        this.loadingMore = false;
        this.hasMoreNotifications = this.messageService.hasMoreNotifications();
      },
      error: err => {
        this.loadingMore = false;
        this.hasMoreNotifications = false;
      }
    });
  }
  setupSubscriptions() {
    this.messageService.subscribeToNewNotifications().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_12__.catchError)(error => {
      console.log('Notification stream error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_13__.of)(null);
    })).subscribe();
    this.messageService.subscribeToNotificationsRead().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_12__.catchError)(error => {
      console.log('Notifications read stream error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_13__.of)(null);
    })).subscribe();
  }
  markAsRead(notificationId) {
    if (!notificationId) {
      this.error = new Error('ID de notification invalide');
      return;
    }
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
      const notification = notifications.find(n => n.id === notificationId);
      if (notification) {
        if (notification.isRead) return;
        const updatedNotifications = notifications.map(n => n.id === notificationId ? {
          ...n,
          isRead: true,
          readAt: new Date().toISOString()
        } : n);
        this.updateUIWithNotifications(updatedNotifications);
        this.messageService.markAsRead([notificationId]).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe({
          next: result => {
            if (result && result.success) {
              if (this.error && this.error.message.includes('mark')) {
                this.error = null;
              }
            }
          },
          error: err => {
            const revertedNotifications = notifications.map(n => n.id === notificationId ? {
              ...n,
              isRead: false,
              readAt: undefined
            } : n);
            this.messageService.notifications.next(revertedNotifications);
            const revertedUnreadCount = revertedNotifications.filter(n => !n.isRead).length;
            this.messageService.notificationCount.next(revertedUnreadCount);
          }
        });
      } else {
        this.loadNotifications();
      }
    });
  }
  /**
   * Met à jour l'interface utilisateur avec les nouvelles notifications
   * @param notifications Notifications à afficher
   */
  updateUIWithNotifications(notifications) {
    // Mettre à jour l'interface utilisateur immédiatement
    this.messageService.notifications.next(notifications);
    // Mettre à jour le compteur de notifications non lues
    const unreadCount = notifications.filter(n => !n.isRead).length;
    this.messageService.notificationCount.next(unreadCount);
    // Mettre à jour le cache de notifications dans le service
    this.updateNotificationCache(notifications);
  }
  /**
   * Met à jour le cache de notifications dans le service
   * @param notifications Notifications à mettre à jour
   */
  updateNotificationCache(notifications) {
    notifications.forEach(notification => {
      this.messageService.updateNotificationCache?.(notification);
    });
  }
  /**
   * Réinitialise la sélection des notifications
   */
  resetSelection() {
    this.selectedNotifications.clear();
    this.allSelected = false;
    this.showSelectionBar = false;
  }
  markAllAsRead() {
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
      const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);
      if (unreadIds.length === 0) return;
      const validIds = unreadIds.filter(id => id && typeof id === 'string' && id.trim() !== '');
      if (validIds.length !== unreadIds.length) {
        this.error = new Error('Invalid notification IDs');
        return;
      }
      const updatedNotifications = notifications.map(n => validIds.includes(n.id) ? {
        ...n,
        isRead: true,
        readAt: new Date().toISOString()
      } : n);
      this.updateUIWithNotifications(updatedNotifications);
      this.messageService.markAsRead(validIds).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe({
        next: result => {
          if (result && result.success) {
            if (this.error && this.error.message.includes('mark')) {
              this.error = null;
            }
          }
        },
        error: err => {
          // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur
        }
      });
    });
  }
  hasNotifications() {
    return this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.map)(notifications => notifications?.length > 0));
  }
  hasUnreadNotifications() {
    return this.unreadCount$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.map)(count => count > 0));
  }
  /**
   * Active/désactive le filtre pour n'afficher que les notifications non lues
   */
  toggleUnreadFilter() {
    this.showOnlyUnread = !this.showOnlyUnread;
    if (this.showOnlyUnread) {
      this.filteredNotifications$ = this.messageService.getUnreadNotifications();
    } else {
      this.filteredNotifications$ = this.notifications$;
    }
  }
  /**
   * Active/désactive le son des notifications
   */
  toggleSound() {
    this.isSoundMuted = !this.isSoundMuted;
    this.messageService.setMuted(this.isSoundMuted);
    if (!this.isSoundMuted) {
      setTimeout(() => {
        this.messageService.playNotificationSound();
        setTimeout(() => {
          this.messageService.playNotificationSound();
        }, 1000);
      }, 100);
    }
    localStorage.setItem('notificationSoundMuted', this.isSoundMuted.toString());
  }
  /**
   * Récupère les pièces jointes d'une notification et ouvre le modal
   * @param notificationId ID de la notification
   */
  getNotificationAttachments(notificationId) {
    if (!notificationId) return;
    this.currentAttachments = [];
    this.loadingAttachments = true;
    this.showAttachmentsModal = true;
    let notification;
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
      notification = notifications.find(n => n.id === notificationId);
    });
    if (notification && notification.message && notification.message.attachments && notification.message.attachments.length > 0) {
      this.loadingAttachments = false;
      this.currentAttachments = notification.message.attachments.map(attachment => ({
        id: '',
        url: attachment.url || '',
        type: this.convertAttachmentTypeToMessageType(attachment.type),
        name: attachment.name || '',
        size: attachment.size || 0,
        duration: 0
      }));
      return;
    }
    this.messageService.getNotificationAttachments(notificationId).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe({
      next: attachments => {
        this.loadingAttachments = false;
        this.currentAttachments = attachments;
      },
      error: err => {
        this.loadingAttachments = false;
      }
    });
  }
  /**
   * Ferme le modal des pièces jointes
   */
  closeAttachmentsModal() {
    this.showAttachmentsModal = false;
  }
  /**
   * Ouvre le modal des détails de notification
   * @param notification Notification à afficher
   */
  openNotificationDetails(notification) {
    this.currentNotification = notification;
    this.showNotificationDetailsModal = true;
    if (notification.message?.attachments?.length) {
      this.getNotificationAttachmentsForModal(notification.id);
    }
  }
  /**
   * Ferme le modal des détails de notification
   */
  closeNotificationDetailsModal() {
    this.showNotificationDetailsModal = false;
    this.currentNotification = null;
    this.currentAttachments = [];
  }
  /**
   * Récupère les pièces jointes d'une notification pour le modal de détails
   */
  getNotificationAttachmentsForModal(notificationId) {
    this.currentAttachments = [];
    if (this.currentNotification?.message?.attachments?.length) {
      this.currentAttachments = this.currentNotification.message.attachments.map(attachment => ({
        id: '',
        url: attachment.url || '',
        type: this.convertAttachmentTypeToMessageType(attachment.type),
        name: attachment.name || '',
        size: attachment.size || 0,
        duration: 0
      }));
    }
  }
  /**
   * Convertit AttachmentType en MessageType
   */
  convertAttachmentTypeToMessageType(type) {
    switch (type) {
      case 'IMAGE':
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.IMAGE;
      case 'VIDEO':
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.VIDEO;
      case 'AUDIO':
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.AUDIO;
      case 'FILE':
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.FILE;
      default:
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.FILE;
    }
  }
  /**
   * Vérifie si un type de fichier est une image
   */
  isImage(type) {
    return type?.startsWith('image/') || false;
  }
  /**
   * Obtient l'icône FontAwesome correspondant au type de fichier
   * @param type Type MIME du fichier
   * @returns Classe CSS de l'icône
   */
  getFileIcon(type) {
    if (!type) return 'fas fa-file';
    if (type.startsWith('image/')) return 'fas fa-file-image';
    if (type.startsWith('video/')) return 'fas fa-file-video';
    if (type.startsWith('audio/')) return 'fas fa-file-audio';
    if (type.startsWith('text/')) return 'fas fa-file-alt';
    if (type.includes('pdf')) return 'fas fa-file-pdf';
    if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';
    if (type.includes('excel') || type.includes('sheet')) return 'fas fa-file-excel';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'fas fa-file-powerpoint';
    if (type.includes('zip') || type.includes('compressed')) return 'fas fa-file-archive';
    return 'fas fa-file';
  }
  /**
   * Obtient le libellé du type de fichier
   * @param type Type MIME du fichier
   * @returns Libellé du type de fichier
   */
  getFileTypeLabel(type) {
    if (!type) return 'Fichier';
    if (type.startsWith('image/')) return 'Image';
    if (type.startsWith('video/')) return 'Vidéo';
    if (type.startsWith('audio/')) return 'Audio';
    if (type.startsWith('text/')) return 'Texte';
    if (type.includes('pdf')) return 'PDF';
    if (type.includes('word') || type.includes('document')) return 'Document';
    if (type.includes('excel') || type.includes('sheet')) return 'Feuille de calcul';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'Présentation';
    if (type.includes('zip') || type.includes('compressed')) return 'Archive';
    return 'Fichier';
  }
  /**
   * Formate la taille du fichier en unités lisibles
   * @param size Taille en octets
   * @returns Taille formatée (ex: "1.5 MB")
   */
  formatFileSize(size) {
    if (!size) return '';
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let i = 0;
    let formattedSize = size;
    while (formattedSize >= 1024 && i < units.length - 1) {
      formattedSize /= 1024;
      i++;
    }
    return `${formattedSize.toFixed(1)} ${units[i]}`;
  }
  /**
   * Ouvre une pièce jointe dans un nouvel onglet
   * @param url URL de la pièce jointe
   */
  openAttachment(url) {
    if (!url) return;
    window.open(url, '_blank');
  }
  /**
   * Télécharge une pièce jointe
   * @param attachment Pièce jointe à télécharger
   */
  downloadAttachment(attachment) {
    if (!attachment?.url) return;
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.name || 'attachment';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  acceptFriendRequest(notification) {
    this.markAsRead(notification.id);
  }
  /**
   * Supprime une notification et la stocke dans le localStorage
   * @param notificationId ID de la notification à supprimer
   */
  deleteNotification(notificationId) {
    if (!notificationId) {
      this.error = new Error('ID de notification invalide');
      return;
    }
    const deletedNotificationIds = this.getDeletedNotificationIds();
    deletedNotificationIds.add(notificationId);
    this.saveDeletedNotificationIds(deletedNotificationIds);
    this.messageService.deleteNotification(notificationId).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe({
      next: result => {
        if (result && result.success) {
          if (this.error && this.error.message.includes('suppression')) {
            this.error = null;
          }
        }
      },
      error: err => {
        this.error = err;
      }
    });
  }
  /**
   * Supprime toutes les notifications et les stocke dans le localStorage
   */
  deleteAllNotifications() {
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
      const deletedNotificationIds = this.getDeletedNotificationIds();
      notifications.forEach(notification => {
        deletedNotificationIds.add(notification.id);
      });
      this.saveDeletedNotificationIds(deletedNotificationIds);
      this.messageService.deleteAllNotifications().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe({
        next: result => {
          if (result && result.success) {
            if (this.error && this.error.message.includes('suppression')) {
              this.error = null;
            }
          }
        },
        error: err => {
          this.error = err;
        }
      });
    });
  }
  getErrorMessage() {
    return this.error?.message || 'Unknown error occurred';
  }
  /**
   * Récupère les IDs des notifications supprimées du localStorage
   * @returns Set contenant les IDs des notifications supprimées
   */
  getDeletedNotificationIds() {
    try {
      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');
      if (deletedIdsJson) {
        return new Set(JSON.parse(deletedIdsJson));
      }
      return new Set();
    } catch (error) {
      return new Set();
    }
  }
  /**
   * Sauvegarde les IDs des notifications supprimées dans le localStorage
   * @param deletedIds Set contenant les IDs des notifications supprimées
   */
  saveDeletedNotificationIds(deletedIds) {
    try {
      localStorage.setItem('deletedNotificationIds', JSON.stringify(Array.from(deletedIds)));
    } catch (error) {
      // Ignore silently
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  /**
   * Sélectionne ou désélectionne une notification
   * @param notificationId ID de la notification
   * @param event Événement de la case à cocher
   */
  toggleSelection(notificationId, event) {
    event.stopPropagation(); // Empêcher la propagation de l'événement
    if (this.selectedNotifications.has(notificationId)) {
      this.selectedNotifications.delete(notificationId);
    } else {
      this.selectedNotifications.add(notificationId);
    }
    // Mettre à jour l'état de sélection globale
    this.updateSelectionState();
    // Afficher ou masquer la barre de sélection
    this.showSelectionBar = this.selectedNotifications.size > 0;
  }
  /**
   * Sélectionne ou désélectionne toutes les notifications
   * @param event Événement de la case à cocher
   */
  toggleSelectAll(event) {
    event.stopPropagation(); // Empêcher la propagation de l'événement
    this.allSelected = !this.allSelected;
    this.filteredNotifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
      if (this.allSelected) {
        // Sélectionner toutes les notifications
        notifications.forEach(notification => {
          this.selectedNotifications.add(notification.id);
        });
      } else {
        // Désélectionner toutes les notifications
        this.selectedNotifications.clear();
      }
      // Afficher ou masquer la barre de sélection
      this.showSelectionBar = this.selectedNotifications.size > 0;
    });
  }
  /**
   * Met à jour l'état de sélection globale
   */
  updateSelectionState() {
    this.filteredNotifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
      this.allSelected = notifications.length > 0 && this.selectedNotifications.size === notifications.length;
    });
  }
  /**
   * Supprime les notifications sélectionnées
   */
  deleteSelectedNotifications() {
    if (this.selectedNotifications.size === 0) return;
    const selectedIds = Array.from(this.selectedNotifications);
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
      const updatedNotifications = notifications.filter(notification => !this.selectedNotifications.has(notification.id));
      this.updateUIWithNotifications(updatedNotifications);
      this.resetSelection();
    });
    this.messageService.deleteMultipleNotifications(selectedIds).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe({
      next: result => {
        // Success handled silently
      },
      error: err => {
        // Error handled silently
      }
    });
  }
  /**
   * Marque les notifications sélectionnées comme lues
   */
  markSelectedAsRead() {
    if (this.selectedNotifications.size === 0) return;
    const selectedIds = Array.from(this.selectedNotifications);
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.take)(1)).subscribe(notifications => {
      const updatedNotifications = notifications.map(notification => this.selectedNotifications.has(notification.id) ? {
        ...notification,
        isRead: true,
        readAt: new Date().toISOString()
      } : notification);
      this.updateUIWithNotifications(updatedNotifications);
      this.resetSelection();
    });
    this.messageService.markAsRead(selectedIds).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe({
      next: result => {
        // Success handled silently
      },
      error: err => {
        // Error handled silently
      }
    });
  }
  /**
   * Vérifie si une notification est sélectionnée
   * @param notificationId ID de la notification
   * @returns true si la notification est sélectionnée, false sinon
   */
  isSelected(notificationId) {
    return this.selectedNotifications.has(notificationId);
  }
  static {
    this.ɵfac = function NotificationListComponent_Factory(t) {
      return new (t || NotificationListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_services_message_service__WEBPACK_IMPORTED_MODULE_1__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_app_services_theme_service__WEBPACK_IMPORTED_MODULE_2__.ThemeService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_14__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: NotificationListComponent,
      selectors: [["app-notification-list"]],
      viewQuery: function NotificationListComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵviewQuery"](_c0, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵloadQuery"]()) && (ctx.notificationContainer = _t.first);
        }
      },
      hostBindings: function NotificationListComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("scroll", function NotificationListComponent_scroll_HostBindingHandler($event) {
            return ctx.onScroll($event.target);
          });
        }
      },
      decls: 37,
      vars: 22,
      consts: [[1, "futuristic-notifications-container", "main-grid-container"], [1, "background-elements", "background-grid"], [1, "futuristic-notifications-card", "content-card", "relative", "z-10"], [1, "futuristic-notifications-header"], [1, "futuristic-title"], [1, "fas", "fa-bell", "mr-2"], ["class", "flex space-x-2", 4, "ngIf"], ["class", "flex space-x-2 selection-actions", 4, "ngIf"], ["class", "futuristic-loading-container", 4, "ngIf"], ["class", "futuristic-error-message", 4, "ngIf"], ["class", "futuristic-empty-state", 4, "ngIf"], ["class", "futuristic-notifications-list", 3, "scroll", 4, "ngIf"], [1, "futuristic-modal-overlay", 3, "click"], [1, "futuristic-modal-container", 3, "click"], [1, "futuristic-modal-header"], [1, "futuristic-modal-title"], [1, "fas", "fa-paperclip", "mr-2"], [1, "futuristic-modal-close", 3, "click"], [1, "fas", "fa-times"], [1, "futuristic-modal-body"], ["class", "futuristic-attachments-list", 4, "ngIf"], [1, "fas", "fa-info-circle", "mr-2"], ["class", "futuristic-modal-body", 4, "ngIf"], [1, "flex", "space-x-2"], ["title", "Rafra\u00EEchir", 1, "futuristic-action-button", 3, "click"], [1, "fas", "fa-sync-alt"], ["class", "select-all-checkbox", 4, "ngIf"], ["title", "Filtrer les non lues", 1, "futuristic-action-button", 3, "click"], [1, "fas", "fa-filter"], [1, "futuristic-action-button", 3, "title", "click"], [1, "fas", 3, "ngClass"], ["class", "futuristic-primary-button", 3, "click", 4, "ngIf"], ["class", "futuristic-danger-button", "title", "Supprimer toutes les notifications", 3, "click", 4, "ngIf"], [1, "select-all-checkbox"], [1, "futuristic-checkbox"], ["type", "checkbox", 3, "checked", "click"], [1, "checkmark"], [1, "futuristic-primary-button", 3, "click"], [1, "fas", "fa-check-double", "mr-1"], ["title", "Supprimer toutes les notifications", 1, "futuristic-danger-button", 3, "click"], [1, "fas", "fa-trash-alt", "mr-1"], [1, "flex", "space-x-2", "selection-actions"], [1, "selection-count"], [1, "fas", "fa-check", "mr-1"], [1, "futuristic-danger-button", 3, "click"], [1, "futuristic-cancel-button", 3, "click"], [1, "fas", "fa-times", "mr-1"], [1, "futuristic-loading-container"], [1, "futuristic-loading-circle"], [1, "futuristic-loading-text"], [1, "futuristic-error-message"], [1, "flex", "items-center"], [1, "fas", "fa-exclamation-triangle", "futuristic-error-icon"], [1, "futuristic-error-title"], [1, "futuristic-error-text"], [1, "futuristic-retry-button", "ml-auto", 3, "click"], [1, "futuristic-empty-state"], [1, "futuristic-empty-icon"], [1, "fas", "fa-bell-slash"], [1, "futuristic-empty-title"], [1, "futuristic-empty-text"], [1, "futuristic-check-button", 3, "click"], [1, "futuristic-notifications-list", 3, "scroll"], ["notificationContainer", ""], [4, "ngFor", "ngForOf"], ["class", "futuristic-loading-more", 4, "ngIf"], [1, "futuristic-notification-card"], [1, "notification-checkbox"], [1, "notification-avatar"], ["alt", "Avatar", "onerror", "this.src='assets/images/default-avatar.png'", 3, "src"], [1, "notification-main-content"], [1, "notification-content"], [1, "notification-header"], [1, "notification-header-top"], [1, "notification-sender"], [1, "notification-time"], [1, "notification-text-container"], [1, "notification-text"], ["class", "notification-message-preview", 4, "ngIf"], ["class", "notification-attachments-indicator", 4, "ngIf"], ["class", "unread-indicator", 4, "ngIf"], [1, "notification-actions"], ["class", "notification-action-button notification-attachment-button", "title", "Voir les pi\u00E8ces jointes", 3, "click", 4, "ngIf"], ["class", "notification-action-button notification-join-button", "title", "Rejoindre la conversation", 3, "disabled", "click", 4, "ngIf"], ["title", "Voir les d\u00E9tails (ne marque PAS comme lu automatiquement)", 1, "notification-action-button", "notification-details-button", 3, "click"], [1, "fas", "fa-info-circle"], ["class", "notification-action-button notification-read-button", "title", "Marquer cette notification comme lue", 3, "click", 4, "ngIf"], ["title", "Supprimer cette notification", 1, "notification-action-button", "notification-delete-button", 3, "click"], [1, "fas", "fa-trash-alt"], [1, "notification-message-preview"], [1, "notification-attachments-indicator"], [1, "fas", "fa-paperclip"], [1, "unread-indicator"], ["title", "Voir les pi\u00E8ces jointes", 1, "notification-action-button", "notification-attachment-button", 3, "click"], ["title", "Rejoindre la conversation", 1, "notification-action-button", "notification-join-button", 3, "disabled", "click"], ["class", "fas fa-comments", 4, "ngIf"], ["class", "fas fa-spinner fa-spin", 4, "ngIf"], [1, "fas", "fa-comments"], [1, "fas", "fa-spinner", "fa-spin"], ["title", "Marquer cette notification comme lue", 1, "notification-action-button", "notification-read-button", 3, "click"], [1, "fas", "fa-check"], [1, "futuristic-loading-more"], [1, "futuristic-loading-circle-small"], [1, "futuristic-loading-text-small"], [1, "fas", "fa-file-alt"], [1, "futuristic-attachments-list"], ["class", "futuristic-attachment-item", 4, "ngFor", "ngForOf"], [1, "futuristic-attachment-item"], ["class", "futuristic-attachment-preview", 4, "ngIf"], ["class", "futuristic-attachment-icon", 4, "ngIf"], [1, "futuristic-attachment-info"], [1, "futuristic-attachment-name"], [1, "futuristic-attachment-meta"], [1, "futuristic-attachment-type"], ["class", "futuristic-attachment-size", 4, "ngIf"], [1, "futuristic-attachment-actions"], ["title", "Ouvrir", 1, "futuristic-attachment-button", 3, "click"], [1, "fas", "fa-external-link-alt"], ["title", "T\u00E9l\u00E9charger", 1, "futuristic-attachment-button", 3, "click"], [1, "fas", "fa-download"], [1, "futuristic-attachment-preview"], ["alt", "Image", 3, "src", "click"], [1, "futuristic-attachment-icon"], [1, "futuristic-attachment-size"], [1, "notification-detail-section"], [1, "notification-detail-title"], [1, "fas", "fa-user", "mr-2"], [1, "notification-sender-info"], ["alt", "Avatar", "onerror", "this.src='assets/images/default-avatar.png'", 1, "notification-sender-avatar", 3, "src"], [1, "notification-sender-details"], [1, "notification-sender-name"], [1, "notification-timestamp"], [1, "fas", "fa-message", "mr-2"], [1, "notification-content-detail"], ["class", "notification-message-detail", 4, "ngIf"], [1, "fas", "fa-tag", "mr-2"], [1, "notification-info-grid"], [1, "notification-info-item"], [1, "notification-info-label"], [1, "notification-info-value"], ["class", "notification-info-item", 4, "ngIf"], ["class", "notification-info-item", "style", "\n              background: rgba(255, 140, 0, 0.1);\n              border: 1px solid rgba(255, 140, 0, 0.3);\n            ", 4, "ngIf"], ["class", "notification-detail-section", 4, "ngIf"], [1, "notification-detail-actions"], ["class", "futuristic-primary-button", 3, "disabled", "click", 4, "ngIf"], ["class", "futuristic-secondary-button", 3, "click", 4, "ngIf"], [1, "fas", "fa-trash-alt", "mr-2"], [1, "notification-message-detail"], [1, "notification-info-item", 2, "background", "rgba(255, 140, 0, 0.1)", "border", "1px solid rgba(255, 140, 0, 0.3)"], [1, "fas", "fa-info-circle", "mr-1"], [1, "notification-info-value", 2, "color", "#ff8c00", "font-style", "italic"], [1, "notification-attachments-grid"], ["class", "notification-attachment-item", 4, "ngFor", "ngForOf"], [1, "notification-attachment-item"], ["class", "notification-attachment-preview", 4, "ngIf"], ["class", "notification-attachment-icon", 4, "ngIf"], [1, "notification-attachment-info"], [1, "notification-attachment-name"], [1, "notification-attachment-meta"], [1, "notification-attachment-type"], ["class", "notification-attachment-size", 4, "ngIf"], [1, "notification-attachment-actions"], ["title", "Ouvrir", 1, "notification-attachment-button", 3, "click"], ["title", "T\u00E9l\u00E9charger", 1, "notification-attachment-button", 3, "click"], [1, "notification-attachment-preview"], [1, "notification-attachment-icon"], [1, "notification-attachment-size"], [1, "futuristic-primary-button", 3, "disabled", "click"], ["class", "fas fa-comments mr-2", 4, "ngIf"], ["class", "fas fa-spinner fa-spin mr-2", 4, "ngIf"], [1, "fas", "fa-comments", "mr-2"], [1, "fas", "fa-spinner", "fa-spin", "mr-2"], [1, "futuristic-secondary-button", 3, "click"], [1, "fas", "fa-check", "mr-2"]],
      template: function NotificationListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](1, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 2)(4, "div", 3)(5, "h2", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "i", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, " Notifications ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, NotificationListComponent_div_8_Template, 13, 15, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, NotificationListComponent_div_9_Template, 12, 1, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](10, NotificationListComponent_div_10_Template, 4, 0, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](11, NotificationListComponent_div_11_Template, 10, 1, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](12, NotificationListComponent_div_12_Template, 9, 0, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](13, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](14, NotificationListComponent_div_14_Template, 5, 4, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](15, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_div_click_16_listener() {
            return ctx.closeAttachmentsModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_div_click_17_listener($event) {
            return $event.stopPropagation();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](18, "div", 14)(19, "h3", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](20, "i", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](21, " Pi\u00E8ces jointes ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](22, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_button_click_22_listener() {
            return ctx.closeAttachmentsModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](23, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](24, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](25, NotificationListComponent_div_25_Template, 4, 0, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](26, NotificationListComponent_div_26_Template, 7, 0, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](27, NotificationListComponent_div_27_Template, 2, 1, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_div_click_28_listener() {
            return ctx.closeNotificationDetailsModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](29, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_div_click_29_listener($event) {
            return $event.stopPropagation();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](30, "div", 14)(31, "h3", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](32, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](33, " D\u00E9tails de la notification ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](34, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_button_click_34_listener() {
            return ctx.closeNotificationDetailsModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](35, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](36, NotificationListComponent_div_36_Template, 44, 19, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("dark", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](1, 16, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.showSelectionBar);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.showSelectionBar);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loading && !_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](13, 18, ctx.hasNotifications()));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loading && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](15, 20, ctx.hasNotifications()));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵstyleProp"]("display", ctx.showAttachmentsModal ? "flex" : "none");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.loadingAttachments);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loadingAttachments && ctx.currentAttachments.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loadingAttachments && ctx.currentAttachments.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵstyleProp"]("display", ctx.showNotificationDetailsModal ? "flex" : "none");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.currentNotification);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_15__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_15__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_15__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_15__.AsyncPipe, _angular_common__WEBPACK_IMPORTED_MODULE_15__.DatePipe],
      styles: ["@charset \"UTF-8\";\n\n\n.futuristic-notifications-container[_ngcontent-%COMP%] {\n  padding: 0;\n  min-height: calc(100vh - 4rem);\n  position: relative;\n  overflow: hidden;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  padding-top: 1rem; \n\n  margin-bottom: 0;\n  height: 100vh; \n\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%] {\n  background-color: #edf1f4; \n\n  color: #6d6870;\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%] {\n  background-color: #121212; \n\n  color: #a0a0a0;\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n.futuristic-notifications-container[_ngcontent-%COMP%]   .background-elements[_ngcontent-%COMP%] {\n  position: absolute;\n  inset: 0;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: 0;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  inset: 0;\n  opacity: 0.05;\n  background-image: linear-gradient(to right, #4f5fad 1px, transparent 1px),\n    linear-gradient(to bottom, #4f5fad 1px, transparent 1px);\n  background-size: calc(100% / 12) 100%, 100% calc(100% / 12);\n  z-index: 0;\n}\n\n\n\n.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  inset: 0;\n  opacity: 0.05; \n\n  background-image: linear-gradient(\n      to right,\n      rgba(255, 140, 0, 0.3) 1px,\n      transparent 1px\n    ),\n    linear-gradient(to bottom, rgba(255, 140, 0, 0.3) 1px, transparent 1px); \n\n  background-size: calc(100% / 20) 100%, 100% calc(100% / 20); \n\n  z-index: 0;\n  animation: _ngcontent-%COMP%_grid-pulse 4s ease-in-out infinite; \n\n}\n\n\n\n.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: linear-gradient(\n    to right,\n    transparent 0%,\n    rgba(255, 140, 0, 0.5) 50%,\n    transparent 100%\n  );\n  box-shadow: 0 0 10px rgba(255, 140, 0, 0.5);\n  z-index: 1;\n  animation: _ngcontent-%COMP%_scan 8s linear infinite; \n\n}\n\n\n\n@keyframes _ngcontent-%COMP%_grid-pulse {\n  0% {\n    opacity: 0.03;\n  }\n  50% {\n    opacity: 0.07;\n  }\n  100% {\n    opacity: 0.03;\n  }\n}\n\n\n\n@keyframes _ngcontent-%COMP%_scan {\n  0% {\n    top: -10%;\n    opacity: 0.5;\n  }\n  50% {\n    opacity: 0.8;\n  }\n  100% {\n    top: 110%;\n    opacity: 0.5;\n  }\n}\n\n\n\n.futuristic-notifications-card[_ngcontent-%COMP%] {\n  border-radius: 0.5rem;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n  margin: 0.5rem auto; \n\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  max-width: 1200px; \n\n  height: calc(100vh - 1rem); \n\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(79, 95, 173, 0.1);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%] {\n  background-color: #1e1e1e; \n\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(109, 120, 201, 0.1); \n\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n}\n\n\n\n.futuristic-notifications-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  margin-bottom: 1.5rem; \n\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%] {\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\n  background-color: rgba(240, 244, 248, 0.5);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%] {\n  border-bottom: 1px solid rgba(0, 247, 255, 0.1);\n  background-color: rgba(0, 0, 0, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #4f5fad;\n  display: flex;\n  align-items: center;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--accent-color);\n  display: flex;\n  align-items: center;\n}\n\n\n\n.futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  margin-right: 0.5rem;\n  \n\n  position: relative;\n  top: 0px; \n\n  \n\n  width: 22px;\n  height: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n\n  border-radius: 50%;\n  background: rgba(0, 247, 255, 0.1);\n  border: 1px solid rgba(0, 247, 255, 0.3);\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.3);\n  transition: all 0.3s ease;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  background: rgba(79, 95, 173, 0.1);\n  border: 1px solid rgba(79, 95, 173, 0.3);\n  box-shadow: 0 0 8px rgba(79, 95, 173, 0.3);\n  color: #4f5fad;\n}\n\n\n\n.futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\n  transform: scale(1.05);\n  box-shadow: 0 0 12px rgba(0, 247, 255, 0.5);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\n  box-shadow: 0 0 12px rgba(79, 95, 173, 0.5);\n}\n\n\n\n.futuristic-loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n}\n\n.futuristic-loading-circle[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  border: 2px solid transparent;\n  border-top-color: var(--accent-color);\n  border-bottom-color: var(--secondary-color);\n  animation: _ngcontent-%COMP%_futuristic-spin 1.2s linear infinite;\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);\n}\n\n@keyframes _ngcontent-%COMP%_futuristic-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.futuristic-loading-text[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  color: var(--text-dim);\n  font-size: 0.875rem;\n  text-align: center;\n}\n\n\n\n.futuristic-error-message[_ngcontent-%COMP%] {\n  margin: 1rem;\n  padding: 1rem;\n  background-color: rgba(255, 0, 76, 0.1);\n  border-left: 4px solid var(--error-color);\n  border-radius: var(--border-radius-md);\n}\n\n.futuristic-error-icon[_ngcontent-%COMP%] {\n  color: var(--error-color);\n  font-size: 1.25rem;\n  margin-right: 0.75rem;\n}\n\n.futuristic-error-title[_ngcontent-%COMP%] {\n  color: var(--error-color);\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.futuristic-error-text[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n  font-size: 0.75rem;\n  margin-top: 0.25rem;\n}\n\n.futuristic-retry-button[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.75rem;\n  background-color: rgba(255, 0, 76, 0.1);\n  color: var(--error-color);\n  border: 1px solid var(--error-color);\n  border-radius: var(--border-radius-sm);\n  font-size: 0.75rem;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n.futuristic-retry-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(255, 0, 76, 0.2);\n  box-shadow: 0 0 10px rgba(255, 0, 76, 0.3);\n}\n\n\n\n.futuristic-empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.futuristic-empty-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  color: var(--accent-color);\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n\n.futuristic-empty-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--text-light);\n  margin-bottom: 0.5rem;\n}\n\n.futuristic-empty-text[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n  font-size: 0.875rem;\n  margin-bottom: 1rem;\n}\n\n.futuristic-check-button[_ngcontent-%COMP%] {\n  padding: 0.5rem 1rem;\n  background-color: rgba(0, 247, 255, 0.1);\n  color: var(--accent-color);\n  border: 1px solid rgba(0, 247, 255, 0.3);\n  border-radius: var(--border-radius-md);\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n.futuristic-check-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.2);\n  transform: translateY(-2px);\n  box-shadow: var(--glow-effect);\n}\n\n\n\n.futuristic-notifications-list[_ngcontent-%COMP%] {\n  padding: 0;\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  overflow-y: auto;\n  position: relative;\n  scrollbar-width: thin;\n  z-index: 1;\n  width: 100%;\n}\n\n.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 4px;\n}\n\n.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%] {\n  scrollbar-color: #4f5fad transparent;\n  background-color: white;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background-color: #4f5fad;\n  border-radius: 10px;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%] {\n  scrollbar-color: var(--accent-color) transparent;\n  background-color: transparent;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background-color: var(--accent-color);\n  border-radius: 10px;\n}\n\n\n\n.futuristic-notification-card[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 30px 20px 16px 20px; \n\n  position: relative;\n  transition: all 0.2s ease;\n  margin: 0.5rem 1rem; \n\n  border-radius: 8px; \n\n  flex-wrap: nowrap; \n\n  justify-content: space-between; \n\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%] {\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\n  background-color: white;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n  border-radius: 15px; \n\n  transition: all 0.3s ease;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover {\n  background-color: rgba(79, 95, 173, 0.05);\n  transform: translateY(-1px);\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(79, 95, 173, 0.1),\n    rgba(61, 74, 133, 0.2)\n  ); \n\n  border: 1px solid rgba(79, 95, 173, 0.3); \n\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); \n\n  border-bottom-right-radius: 0; \n\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border-radius: inherit;\n  pointer-events: none;\n  z-index: -1;\n  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2); \n\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  background: linear-gradient(\n    135deg,\n    rgba(79, 95, 173, 0.15),\n    rgba(61, 74, 133, 0.25)\n  ); \n\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.2); \n\n}\n\n\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%] {\n  border-bottom: none;\n  background-color: var(\n    --dark-medium-bg,\n    #252740\n  ); \n\n  border: 1px solid rgba(255, 255, 255, 0.1); \n\n  border-radius: 15px; \n\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); \n\n  margin-bottom: 15px; \n\n  margin-left: 15px; \n\n  margin-right: 15px; \n\n  transition: all 0.3s ease; \n\n  color: var(\n    --text-light,\n    #ffffff\n  ); \n\n}\n\n.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(\n    -2px\n  ); \n\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); \n\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n  background: linear-gradient(\n    135deg,\n    #00f7ff20,\n    #00c3ff30\n  ); \n\n  border: 1px solid rgba(0, 247, 255, 0.3); \n\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); \n\n  border-bottom-right-radius: 0; \n\n  transition: all 0.3s ease;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border-radius: inherit;\n  pointer-events: none;\n  z-index: -1;\n  box-shadow: inset 0 0 0 1px rgba(0, 247, 255, 0.3); \n\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.2); \n\n  background: linear-gradient(\n    135deg,\n    #00f7ff30,\n    #00c3ff40\n  ); \n\n}\n\n\n\n.futuristic-notification-unread[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes _ngcontent-%COMP%_scaleIn {\n  from {\n    transform: scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n@keyframes _ngcontent-%COMP%_borderFlow {\n  0% {\n    background-position: 0% 0%;\n  }\n  100% {\n    background-position: 200% 0%;\n  }\n}\n\n\n\n.futuristic-modal-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  -webkit-backdrop-filter: blur(5px);\n          backdrop-filter: blur(5px);\n  display: none; \n\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\n  opacity: 0; \n\n  transition: opacity 0.3s ease;\n}\n\n\n\n.futuristic-modal-overlay[style*=\"display: flex\"][_ngcontent-%COMP%] {\n  opacity: 1;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%] {\n  background-color: rgba(0, 0, 0, 0.8);\n  -webkit-backdrop-filter: blur(8px);\n          backdrop-filter: blur(8px);\n  animation: _ngcontent-%COMP%_modalBackdropFadeIn 0.3s ease-out;\n}\n\n@keyframes _ngcontent-%COMP%_modalBackdropFadeIn {\n  from {\n    background-color: rgba(0, 0, 0, 0);\n    -webkit-backdrop-filter: blur(0px);\n            backdrop-filter: blur(0px);\n  }\n  to {\n    background-color: rgba(0, 0, 0, 0.8);\n    -webkit-backdrop-filter: blur(8px);\n            backdrop-filter: blur(8px);\n  }\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%] {\n  width: 90%;\n  max-width: 600px;\n  max-height: 80vh;\n  background-color: #ffffff;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  display: flex;\n  flex-direction: column;\n  animation: _ngcontent-%COMP%_scaleIn 0.3s ease;\n  border: 1px solid rgba(79, 95, 173, 0.2);\n  position: relative;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(\n    90deg,\n    transparent 0%,\n    rgba(79, 95, 173, 0.2) 20%,\n    rgba(79, 95, 173, 0.8) 50%,\n    rgba(79, 95, 173, 0.2) 80%,\n    transparent 100%\n  );\n  background-size: 200% 100%;\n  animation: _ngcontent-%COMP%_borderFlow 3s infinite linear;\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.4);\n  z-index: 1;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%] {\n  width: 90%;\n  max-width: 600px;\n  max-height: 80vh;\n  background-color: rgba(18, 18, 18, 0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 0 40px rgba(0, 247, 255, 0.4),\n    inset 0 0 20px rgba(0, 247, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n  animation: _ngcontent-%COMP%_modalFadeIn 0.4s cubic-bezier(0.19, 1, 0.22, 1);\n  border: 1px solid rgba(0, 247, 255, 0.3);\n  position: relative;\n}\n\n@keyframes _ngcontent-%COMP%_modalFadeIn {\n  0% {\n    opacity: 0;\n    transform: scale(0.9) translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(\n    90deg,\n    transparent 0%,\n    rgba(0, 247, 255, 0.2) 20%,\n    rgba(0, 247, 255, 0.8) 50%,\n    rgba(0, 247, 255, 0.2) 80%,\n    transparent 100%\n  );\n  background-size: 200% 100%;\n  animation: _ngcontent-%COMP%_borderFlow 3s infinite linear;\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\n  z-index: 1;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%] {\n  padding: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\n  background-color: rgba(79, 95, 173, 0.05);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #4f5fad;\n  margin: 0;\n  display: flex;\n  align-items: center;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: rgba(79, 95, 173, 0.1);\n  color: #4f5fad;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover {\n  background-color: rgba(79, 95, 173, 0.2);\n  transform: scale(1.1);\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.3);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%] {\n  padding: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom: 1px solid rgba(0, 247, 255, 0.1);\n  background-color: rgba(0, 0, 0, 0.2);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #00f7ff;\n  margin: 0;\n  display: flex;\n  align-items: center;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: rgba(0, 247, 255, 0.1);\n  color: #00f7ff;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.2);\n  transform: scale(1.1);\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\n}\n\n\n\n.futuristic-modal-body[_ngcontent-%COMP%] {\n  padding: 16px;\n  overflow-y: auto;\n  max-height: calc(80vh - 70px);\n}\n\n\n\n.futuristic-attachments-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n  border-radius: 8px;\n  background-color: rgba(79, 95, 173, 0.05);\n  border: 1px solid rgba(79, 95, 173, 0.1);\n  transition: all 0.2s ease;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover {\n  background-color: rgba(79, 95, 173, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-right: 12px;\n  flex-shrink: 0;\n  border: 1px solid rgba(79, 95, 173, 0.2);\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 4px;\n  background-color: rgba(79, 95, 173, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 24px;\n  color: #4f5fad;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: rgba(79, 95, 173, 0.1);\n  color: #4f5fad;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(79, 95, 173, 0.2);\n  transform: scale(1.1);\n  box-shadow: 0 0 10px rgba(79, 95, 173, 0.3);\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n  border-radius: 8px;\n  background-color: rgba(0, 247, 255, 0.05);\n  border: 1px solid rgba(0, 247, 255, 0.1);\n  transition: all 0.2s ease;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.1);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-right: 12px;\n  flex-shrink: 0;\n  border: 1px solid rgba(0, 247, 255, 0.2);\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.2);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 4px;\n  background-color: rgba(0, 247, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 24px;\n  color: #00f7ff;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: rgba(0, 247, 255, 0.1);\n  color: #00f7ff;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.2);\n  transform: scale(1.1);\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\n}\n\n\n\n.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  cursor: pointer;\n  transition: transform 0.2s ease;\n}\n\n.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\n  transform: scale(1.1);\n}\n\n.futuristic-attachment-info[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n\n.futuristic-attachment-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  margin-bottom: 4px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.futuristic-attachment-meta[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 0.8rem;\n  color: var(--text-dim);\n}\n\n.futuristic-attachment-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n  margin-left: 12px;\n}\n\n\n\n.futuristic-loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n}\n\n.futuristic-loading-circle[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 3px solid transparent;\n  margin-bottom: 16px;\n  animation: _ngcontent-%COMP%_spin 1.2s linear infinite;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\n  border-top-color: #4f5fad;\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.3);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\n  border-top-color: #00f7ff;\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\n}\n\n.futuristic-loading-text[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: var(--text-dim);\n}\n\n.futuristic-empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n  text-align: center;\n}\n\n.futuristic-empty-icon[_ngcontent-%COMP%] {\n  font-size: 48px;\n  margin-bottom: 16px;\n  opacity: 0.5;\n}\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\n  color: #4f5fad;\n}\n\n.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\n  color: #00f7ff;\n}\n\n.futuristic-empty-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: 500;\n  margin-bottom: 8px;\n}\n\n.futuristic-empty-text[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: var(--text-dim);\n  max-width: 300px;\n}\n\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n\n\n.notification-avatar[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  flex-shrink: 0;\n  margin-right: 12px;\n  margin-left: 10px; \n\n}\n\n.notification-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  object-fit: cover;\n}\n\n\n\n.notification-content[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n  padding-right: 16px;\n}\n\n.notification-header[_ngcontent-%COMP%] {\n  margin-bottom: 6px; \n\n}\n\n.notification-header-top[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between; \n\n  align-items: center; \n\n  width: 100%; \n\n}\n\n.notification-sender[_ngcontent-%COMP%] {\n  font-weight: 600;\n  font-size: 0.95rem; \n\n  color: #4f5fad;\n  padding: 2px 0; \n\n  transition: all 0.3s ease; \n\n}\n\n.notification-sender[_ngcontent-%COMP%]:hover {\n  color: #3d4a85; \n\n  text-shadow: 0 0 1px rgba(79, 95, 173, 0.3); \n\n}\n\n.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%] {\n  color: #ff8c00; \n\n  text-shadow: 0 0 5px rgba(255, 140, 0, 0.3); \n\n}\n\n.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover {\n  color: #ffa040; \n\n  text-shadow: 0 0 8px rgba(255, 140, 0, 0.5); \n\n}\n\n.notification-text[_ngcontent-%COMP%] {\n  color: #333;\n  font-size: 0.9rem;\n  position: relative;\n  z-index: 1;\n}\n\n.dark[_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%] {\n  color: #ffffff; \n\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%] {\n  color: var(\n    --light-text,\n    #333333\n  ); \n\n  font-weight: 400; \n\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%] {\n  color: var(\n    --dark-text,\n    #ffffff\n  ); \n\n  font-weight: 400; \n\n}\n\n.notification-message-preview[_ngcontent-%COMP%] {\n  font-size: 0.85rem;\n  color: #666;\n  margin-top: 4px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n}\n\n.dark[_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%] {\n  color: #cccccc; \n\n}\n\n\n\n.notification-attachments-indicator[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #ff8c00;\n  margin-top: 0.25rem;\n  display: flex;\n  align-items: center;\n}\n\n.dark[_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%] {\n  color: rgba(0, 247, 255, 0.9);\n}\n\n.notification-attachments-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  margin-right: 0.25rem;\n}\n\n\n\n\n\n\n.notification-action-button[_ngcontent-%COMP%] {\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n  margin: 6px; \n\n  border: none;\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n.notification-time[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 10px; \n\n  right: 10px; \n\n  font-size: 0.75rem; \n\n  color: rgba(0, 247, 255, 0.9);\n  font-weight: 600;\n  padding: 5px 10px; \n\n  border-radius: 8px;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 247, 255, 0.15),\n    rgba(0, 200, 255, 0.1)\n  );\n  border: 1px solid rgba(0, 247, 255, 0.3);\n  box-shadow: 0 2px 8px rgba(0, 247, 255, 0.2);\n  z-index: 15;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n  \n\n  letter-spacing: 0.5px;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n}\n\n\n\n.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%] {\n  \n\n  color: #00ffff !important; \n\n  background: linear-gradient(\n    135deg,\n    rgba(0, 255, 255, 0.4),\n    rgba(0, 255, 200, 0.35),\n    rgba(0, 200, 255, 0.3)\n  );\n  border: 1px solid rgba(0, 255, 255, 0.8);\n  \n\n  text-shadow: 0 0 5px rgba(0, 255, 255, 0.8), 0 0 10px rgba(0, 255, 255, 0.6),\n    0 0 15px rgba(0, 255, 255, 0.4);\n  box-shadow: 0 2px 10px rgba(0, 255, 255, 0.6), 0 0 20px rgba(0, 255, 255, 0.3),\n    inset 0 0 10px rgba(0, 255, 255, 0.2);\n  -webkit-backdrop-filter: none;\n          backdrop-filter: none; \n\n  \n\n  animation: _ngcontent-%COMP%_fluoro-pulse 2s ease-in-out infinite alternate;\n}\n\n\n\n:not(.dark)[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%] {\n  color: #4f5fad;\n  background: linear-gradient(\n    135deg,\n    rgba(79, 95, 173, 0.1),\n    rgba(79, 95, 173, 0.05)\n  );\n  border: 1px solid rgba(79, 95, 173, 0.2);\n  box-shadow: 0 2px 8px rgba(79, 95, 173, 0.15);\n}\n\n\n\n.notification-time[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px) scale(1.05);\n  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.4);\n}\n\n.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover {\n  \n\n  color: #ffffff !important; \n\n  background: linear-gradient(\n    135deg,\n    rgba(0, 255, 255, 0.6),\n    rgba(0, 255, 200, 0.5),\n    rgba(0, 200, 255, 0.45)\n  );\n  border-color: rgba(0, 255, 255, 1); \n\n  \n\n  text-shadow: 0 0 8px rgba(255, 255, 255, 1), 0 0 15px rgba(0, 255, 255, 0.9),\n    0 0 25px rgba(0, 255, 255, 0.7);\n  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.8), 0 0 30px rgba(0, 255, 255, 0.5),\n    inset 0 0 15px rgba(0, 255, 255, 0.3);\n  \n\n  animation: _ngcontent-%COMP%_fluoro-pulse-intense 1s ease-in-out infinite alternate;\n}\n\n:not(.dark)[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(\n    135deg,\n    rgba(79, 95, 173, 0.15),\n    rgba(79, 95, 173, 0.1)\n  );\n  box-shadow: 0 4px 15px rgba(79, 95, 173, 0.25);\n}\n\n\n\n.notification-join-button[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #00c853, #00a843);\n  color: white;\n  border: 2px solid #00c853;\n  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);\n  position: relative;\n}\n\n.notification-join-button[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(135deg, #00e676, #00c853);\n  transform: scale(1.15) rotate(5deg);\n  box-shadow: 0 0 15px rgba(0, 200, 83, 0.6);\n  border-color: #00e676;\n}\n\n.notification-join-button[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  background: linear-gradient(45deg, #00c853, #00e676);\n  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);\n  z-index: -1;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.notification-join-button[_ngcontent-%COMP%]:hover::before {\n  opacity: 1;\n}\n\n\n\n.notification-details-button[_ngcontent-%COMP%] {\n  width: 38px;\n  height: 38px;\n  background: linear-gradient(135deg, #2196f3, #1976d2);\n  color: white;\n  border: 2px solid #2196f3;\n  border-radius: 8px;\n  position: relative;\n}\n\n.notification-details-button[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(135deg, #42a5f5, #2196f3);\n  transform: scale(1.1) rotateY(15deg);\n  box-shadow: 0 0 15px rgba(33, 150, 243, 0.6);\n  border-color: #42a5f5;\n}\n\n.notification-details-button[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 2px;\n  left: 2px;\n  right: 2px;\n  bottom: 2px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.notification-details-button[_ngcontent-%COMP%]:hover::after {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n\n\n.notification-read-button[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  background: linear-gradient(135deg, #ffc107, #ff9800);\n  color: white;\n  border: 2px solid #ffc107;\n  border-radius: 50%;\n  transform: rotate(45deg);\n  position: relative;\n}\n\n.notification-read-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  transform: rotate(-45deg);\n}\n\n.notification-read-button[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(135deg, #ffca28, #ffc107);\n  transform: rotate(45deg) scale(1.15);\n  box-shadow: 0 0 15px rgba(255, 193, 7, 0.6);\n  border-color: #ffca28;\n}\n\n.notification-read-button[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 8px;\n  height: 8px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  transform: translate(-50%, -50%) rotate(-45deg);\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n}\n\n\n\n.notification-delete-button[_ngcontent-%COMP%] {\n  width: 38px;\n  height: 38px;\n  background: linear-gradient(135deg, #f44336, #d32f2f);\n  color: white;\n  border: 2px solid #f44336;\n  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);\n  position: relative;\n}\n\n.notification-delete-button[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(135deg, #ef5350, #f44336);\n  transform: scale(1.15) rotate(-5deg);\n  box-shadow: 0 0 15px rgba(244, 67, 54, 0.6);\n  border-color: #ef5350;\n}\n\n.notification-delete-button[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  background: linear-gradient(45deg, #f44336, #ef5350);\n  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);\n  z-index: -1;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.notification-delete-button[_ngcontent-%COMP%]:hover::before {\n  opacity: 1;\n}\n\n\n\n.notification-attachment-button[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #9c27b0, #7b1fa2);\n  color: white;\n  border: 2px solid #9c27b0;\n  clip-path: polygon(\n    30% 0%,\n    70% 0%,\n    100% 30%,\n    100% 70%,\n    70% 100%,\n    30% 100%,\n    0% 70%,\n    0% 30%\n  );\n  position: relative;\n}\n\n.notification-attachment-button[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(135deg, #ab47bc, #9c27b0);\n  transform: scale(1.1) rotate(10deg);\n  box-shadow: 0 0 15px rgba(156, 39, 176, 0.6);\n  border-color: #ab47bc;\n}\n\n.notification-attachment-button[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 6px;\n  height: 6px;\n  background: rgba(255, 255, 255, 0.4);\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  animation: _ngcontent-%COMP%_bounce 1.5s infinite;\n}\n\n\n\n.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #00c853, #00a843);\n  border-color: #00e676;\n  box-shadow: 0 0 10px rgba(0, 200, 83, 0.4);\n}\n\n.dark[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #2196f3, #1976d2);\n  border-color: #42a5f5;\n  box-shadow: 0 0 10px rgba(33, 150, 243, 0.4);\n}\n\n.dark[_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #ffc107, #ff9800);\n  border-color: #ffca28;\n  box-shadow: 0 0 10px rgba(255, 193, 7, 0.4);\n}\n\n.dark[_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #f44336, #d32f2f);\n  border-color: #ef5350;\n  box-shadow: 0 0 10px rgba(244, 67, 54, 0.4);\n}\n\n.dark[_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #9c27b0, #7b1fa2);\n  border-color: #ab47bc;\n  box-shadow: 0 0 10px rgba(156, 39, 176, 0.4);\n}\n\n\n\n@keyframes _ngcontent-%COMP%_pulse {\n  0%,\n  100% {\n    opacity: 1;\n    transform: translate(-50%, -50%) rotate(-45deg) scale(1);\n  }\n  50% {\n    opacity: 0.5;\n    transform: translate(-50%, -50%) rotate(-45deg) scale(1.2);\n  }\n}\n\n@keyframes _ngcontent-%COMP%_bounce {\n  0%,\n  100% {\n    transform: translate(-50%, -50%) scale(1);\n  }\n  50% {\n    transform: translate(-50%, -50%) scale(1.3);\n  }\n}\n\n\n\n.notification-join-button[_ngcontent-%COMP%]:hover::after {\n  content: \"\uD83D\uDCAC\";\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  font-size: 10px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 1;\n  transition: opacity 0.3s ease;\n  z-index: 10;\n}\n\n.notification-details-button[_ngcontent-%COMP%]:hover::after {\n  content: \"\u2139\uFE0F\";\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  font-size: 10px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 1;\n  transition: opacity 0.3s ease;\n  z-index: 10;\n}\n\n.notification-delete-button[_ngcontent-%COMP%]:hover::after {\n  content: \"\uD83D\uDDD1\uFE0F\";\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  font-size: 10px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 1;\n  transition: opacity 0.3s ease;\n  z-index: 10;\n}\n\n\n\n.futuristic-checkbox[_ngcontent-%COMP%] {\n  position: relative;\n  display: inline-block;\n  width: 22px; \n\n  height: 22px; \n\n  cursor: pointer;\n  transition: all 0.2s ease; \n\n}\n\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\n  position: absolute;\n  opacity: 0;\n  cursor: pointer;\n  height: 0;\n  width: 0;\n}\n\n.checkmark[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 22px; \n\n  width: 22px; \n\n  background: linear-gradient(\n    135deg,\n    rgba(0, 255, 200, 0.1),\n    rgba(0, 200, 255, 0.1)\n  ); \n\n  border: 2px solid transparent; \n\n  border-radius: 50%; \n\n  transition: all 0.3s ease;\n  box-shadow: 0 0 10px rgba(0, 255, 200, 0.4); \n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  animation: _ngcontent-%COMP%_glow-pulse 2s infinite alternate; \n\n  \n\n  position: relative;\n  z-index: 1;\n  overflow: hidden;\n}\n\n\n\n@keyframes _ngcontent-%COMP%_glow-pulse {\n  0% {\n    box-shadow: 0 0 8px rgba(0, 255, 200, 0.3);\n  }\n  100% {\n    box-shadow: 0 0 15px rgba(0, 200, 255, 0.6);\n  }\n}\n\n.dark[_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%] {\n  background: rgba(\n    0,\n    247,\n    255,\n    0.1\n  ); \n\n  border: 1px solid rgba(0, 247, 255, 0.3); \n\n  box-shadow: 0 0 12px rgba(0, 247, 255, 0.4); \n\n  animation: _ngcontent-%COMP%_glow-pulse 2s infinite alternate; \n\n}\n\n.futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\n  background: rgba(\n    0,\n    247,\n    255,\n    0.2\n  ); \n\n  border: 1px solid rgba(0, 247, 255, 0.3);\n  box-shadow: var(\n    --glow-effect\n  ); \n\n  transform: scale(\n    1.05\n  ); \n\n}\n\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\n  background: rgba(\n    0,\n    247,\n    255,\n    0.2\n  ); \n\n  border: 1px solid rgba(0, 247, 255, 0.3);\n  box-shadow: var(\n    --glow-effect\n  ); \n\n  transform: scale(\n    1.05\n  ); \n\n}\n\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(0, 255, 200, 0.8),\n    rgba(0, 200, 255, 0.8)\n  ); \n\n  border: 2px solid transparent; \n\n  box-shadow: 0 0 20px rgba(0, 255, 200, 0.8); \n\n  animation: _ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate; \n\n}\n\n\n\n@keyframes _ngcontent-%COMP%_checkbox-glow {\n  0% {\n    box-shadow: 0 0 15px rgba(0, 255, 200, 0.6);\n    transform: scale(1);\n  }\n  100% {\n    box-shadow: 0 0 25px rgba(0, 200, 255, 0.9);\n    transform: scale(1.15);\n  }\n}\n\n\n\n@keyframes _ngcontent-%COMP%_checkbox-pulse {\n  0% {\n    transform: scale(1);\n    box-shadow: 0 0 15px rgba(0, 247, 255, 0.4);\n  }\n  100% {\n    transform: scale(1.15);\n    box-shadow: 0 0 20px rgba(0, 247, 255, 0.8);\n  }\n}\n\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(0, 255, 200, 0.8),\n    rgba(0, 200, 255, 0.8)\n  ); \n\n  border: 2px solid transparent; \n\n  box-shadow: 0 0 20px rgba(0, 255, 200, 0.8); \n\n  animation: _ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate; \n\n}\n\n.checkmark[_ngcontent-%COMP%]:after {\n  content: \"\";\n  position: absolute;\n  display: none;\n}\n\n.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%]:after {\n  display: block;\n}\n\n.futuristic-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after {\n  left: 7px; \n\n  top: 3px; \n\n  width: 6px; \n\n  height: 12px; \n\n  border: solid white;\n  border-width: 0 2px 2px 0; \n\n  transform: rotate(45deg);\n  box-shadow: 0 0 5px rgba(255, 255, 255, 0.8); \n\n  animation: _ngcontent-%COMP%_pulse-check 1.5s infinite alternate; \n\n}\n\n\n\n@keyframes _ngcontent-%COMP%_pulse-check {\n  0% {\n    opacity: 0.8;\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);\n  }\n  100% {\n    opacity: 1;\n    box-shadow: 0 0 10px rgba(255, 255, 255, 1);\n  }\n}\n\n\n\n.select-all-checkbox[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 5px;\n}\n\n\n\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n}\n\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%; \n\n  background: rgba(\n    0,\n    247,\n    255,\n    0.1\n  ); \n\n  border: 1px solid rgba(0, 247, 255, 0.3); \n\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.4); \n\n}\n\n\n\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\n  background: rgba(0, 247, 255, 0.2);\n  box-shadow: var(--glow-effect);\n  transform: scale(1.05);\n}\n\n\n\n.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after {\n  left: 13px; \n\n  top: 7px; \n\n  width: 8px; \n\n  height: 16px; \n\n}\n\n\n\n.selection-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  background-color: rgba(255, 140, 0, 0.1);\n  border-radius: 8px;\n  padding: 8px 12px;\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.2);\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\n}\n\n.dark[_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%] {\n  background-color: rgba(255, 140, 0, 0.1);\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.2);\n}\n\n.selection-count[_ngcontent-%COMP%] {\n  font-weight: 500;\n  margin-right: 15px;\n  color: #ff8c00;\n}\n\n.dark[_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%] {\n  color: rgba(255, 140, 0, 0.9);\n}\n\n\n\n\n\n.futuristic-notification-selected[_ngcontent-%COMP%] {\n  border: 1px solid rgba(255, 140, 0, 0.5) !important;\n  background-color: rgba(255, 140, 0, 0.05) !important;\n  box-shadow: 0 5px 15px rgba(255, 140, 0, 0.1) !important;\n  transform: translateY(-2px);\n}\n\n\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%] {\n  border: 1px solid rgba(255, 140, 0, 0.3) !important; \n\n  background: linear-gradient(\n    135deg,\n    rgba(255, 140, 0, 0.15),\n    rgba(255, 0, 128, 0.15),\n    rgba(128, 0, 255, 0.15)\n  ) !important; \n\n  box-shadow: 0 5px 15px rgba(255, 140, 0, 0.2),\n    inset 0 0 20px rgba(255, 0, 128, 0.1) !important; \n\n  transform: translateY(-2px);\n  padding: 18px 22px !important; \n\n  margin-bottom: 18px !important; \n\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(\n    circle,\n    rgba(255, 140, 0, 0.1) 0%,\n    transparent 70%\n  );\n  animation: _ngcontent-%COMP%_rotate-gradient 8s linear infinite;\n  pointer-events: none;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  inset: 0;\n  background-image: radial-gradient(\n      circle at 10% 10%,\n      rgba(255, 255, 255, 0.8) 0%,\n      rgba(255, 255, 255, 0) 2%\n    ),\n    radial-gradient(\n      circle at 20% 30%,\n      rgba(255, 140, 0, 0.8) 0%,\n      rgba(255, 140, 0, 0) 2%\n    ),\n    radial-gradient(\n      circle at 30% 70%,\n      rgba(255, 0, 128, 0.8) 0%,\n      rgba(255, 0, 128, 0) 2%\n    ),\n    radial-gradient(\n      circle at 70% 40%,\n      rgba(128, 0, 255, 0.8) 0%,\n      rgba(128, 0, 255, 0) 2%\n    ),\n    radial-gradient(\n      circle at 80% 80%,\n      rgba(255, 255, 255, 0.8) 0%,\n      rgba(255, 255, 255, 0) 2%\n    ),\n    radial-gradient(\n      circle at 90% 10%,\n      rgba(255, 140, 0, 0.8) 0%,\n      rgba(255, 140, 0, 0) 2%\n    ),\n    radial-gradient(\n      circle at 50% 50%,\n      rgba(255, 0, 128, 0.8) 0%,\n      rgba(255, 0, 128, 0) 2%\n    );\n  opacity: 0;\n  animation: _ngcontent-%COMP%_sparkle-effect 4s ease-in-out infinite;\n  pointer-events: none;\n}\n\n@keyframes _ngcontent-%COMP%_sparkle-effect {\n  0% {\n    opacity: 0;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n\n@keyframes _ngcontent-%COMP%_rotate-gradient {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n\n\n\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%] {\n  color: rgba(\n    255,\n    255,\n    255,\n    0.9\n  ) !important; \n\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%] {\n  background-color: rgba(\n    0,\n    0,\n    0,\n    0.5\n  ) !important; \n\n  color: rgba(255, 255, 255, 0.9) !important; \n\n  border-left: 2px solid rgba(255, 140, 0, 0.5) !important; \n\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%] {\n  color: #ff8c00 !important; \n\n  font-weight: 600;\n}\n\n\n\n\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(255, 140, 0, 0.2),\n    rgba(255, 94, 98, 0.2)\n  );\n  color: #ff8c00; \n\n  border: 1px solid rgba(255, 140, 0, 0.4);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(\n    135deg,\n    rgba(255, 140, 0, 0.3),\n    rgba(255, 94, 98, 0.3)\n  );\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.5); \n\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%] {\n  color: rgba(255, 255, 255, 0.7);\n  background-color: rgba(\n    255,\n    140,\n    0,\n    0.1\n  ); \n\n  border: 1px solid rgba(255, 140, 0, 0.3);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(\n    255,\n    140,\n    0,\n    0.2\n  ); \n\n  color: #ff8c00; \n\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.4); \n\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%] {\n  background-color: rgba(\n    255,\n    140,\n    0,\n    0.1\n  ); \n\n  color: #ff8c00; \n\n  border: 1px solid rgba(255, 140, 0, 0.3);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(\n    255,\n    140,\n    0,\n    0.2\n  ); \n\n  color: #ff8c00; \n\n  box-shadow: 0 0 8px rgba(255, 140, 0, 0.4); \n\n}\n\n\n\n.notification-separator-dot[_ngcontent-%COMP%] {\n  width: 4px;\n  height: 4px;\n  border-radius: 50%;\n  background-color: rgba(\n    0,\n    247,\n    255,\n    0.6\n  ); \n\n  margin: 0 8px;\n  box-shadow: 0 0 5px rgba(0, 247, 255, 0.4); \n\n  animation: _ngcontent-%COMP%_dot-pulse 2s infinite alternate; \n\n  transition: all 0.5s ease; \n\n}\n\n\n\n.notification-separator-dot.fade-out[_ngcontent-%COMP%] {\n  opacity: 0;\n  transform: scale(0);\n  width: 0;\n  margin: 0;\n}\n\n\n\n.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%] {\n  background-color: rgba(\n    0,\n    247,\n    255,\n    0.8\n  ); \n\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.6); \n\n  animation: _ngcontent-%COMP%_dot-pulse-selected 1.5s infinite alternate; \n\n}\n\n\n\n@keyframes _ngcontent-%COMP%_dot-pulse-selected {\n  0% {\n    opacity: 0.6;\n    transform: scale(1);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1.5);\n  }\n}\n\n\n\n@keyframes _ngcontent-%COMP%_dot-pulse {\n  0% {\n    opacity: 0.4;\n    transform: scale(0.8);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1.2);\n  }\n}\n\n\n\n.notification-checkbox[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 10px; \n\n  left: 10px; \n\n  z-index: 10; \n\n}\n\n\n\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n\n\n.futuristic-cancel-button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  background-color: rgba(150, 150, 150, 0.2);\n  color: #6d6870;\n  border: none;\n  border-radius: var(--border-radius-md);\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 0 8px rgba(150, 150, 150, 0.2);\n}\n\n.futuristic-cancel-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(150, 150, 150, 0.3);\n  box-shadow: 0 0 12px rgba(150, 150, 150, 0.3);\n  transform: translateY(-2px);\n}\n\n.dark[_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%] {\n  background-color: rgba(100, 100, 100, 0.2);\n  color: #e0e0e0;\n  box-shadow: 0 0 8px rgba(100, 100, 100, 0.2);\n}\n\n\n\n\n\n\n\n\n.notification-detail-section[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 12px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.dark[_nghost-%COMP%]   .notification-detail-section[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-detail-section[_ngcontent-%COMP%] {\n  background: rgba(0, 247, 255, 0.05);\n  border: 1px solid rgba(0, 247, 255, 0.1);\n}\n\n.notification-detail-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  font-size: 1rem;\n  font-weight: 600;\n  color: #4f5fad;\n  margin-bottom: 0.75rem;\n}\n\n.dark[_nghost-%COMP%]   .notification-detail-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-detail-title[_ngcontent-%COMP%] {\n  color: #00f7ff;\n  text-shadow: 0 0 6px rgba(0, 247, 255, 0.3);\n}\n\n\n\n.notification-sender-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.notification-sender-avatar[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid rgba(79, 95, 173, 0.3);\n}\n\n.dark[_nghost-%COMP%]   .notification-sender-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender-avatar[_ngcontent-%COMP%] {\n  border: 2px solid rgba(0, 247, 255, 0.3);\n}\n\n.notification-sender-details[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n\n.notification-sender-name[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #4f5fad;\n  font-size: 1rem;\n}\n\n.dark[_nghost-%COMP%]   .notification-sender-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender-name[_ngcontent-%COMP%] {\n  color: #00f7ff;\n}\n\n.notification-timestamp[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #a0a0a0;\n}\n\n\n\n.notification-content-detail[_ngcontent-%COMP%] {\n  background: rgba(79, 95, 173, 0.1);\n  padding: 0.75rem;\n  border-radius: 8px;\n  color: #333;\n  line-height: 1.5;\n  border-left: 3px solid #4f5fad;\n}\n\n.dark[_nghost-%COMP%]   .notification-content-detail[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-content-detail[_ngcontent-%COMP%] {\n  background: rgba(0, 0, 0, 0.2);\n  color: #e0e0e0;\n  border-left: 3px solid #00f7ff;\n}\n\n.notification-message-detail[_ngcontent-%COMP%] {\n  margin-top: 0.75rem;\n  padding: 0.75rem;\n  background: rgba(79, 95, 173, 0.05);\n  border-radius: 8px;\n  color: #333;\n  font-size: 0.9rem;\n}\n\n.dark[_nghost-%COMP%]   .notification-message-detail[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-detail[_ngcontent-%COMP%] {\n  background: rgba(0, 247, 255, 0.1);\n  color: #e0e0e0;\n}\n\n\n\n.notification-info-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 0.5rem;\n}\n\n.notification-info-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem;\n  background: rgba(79, 95, 173, 0.05);\n  border-radius: 6px;\n}\n\n.dark[_nghost-%COMP%]   .notification-info-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-item[_ngcontent-%COMP%] {\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.notification-info-label[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #666;\n}\n\n.dark[_nghost-%COMP%]   .notification-info-label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-label[_ngcontent-%COMP%] {\n  color: #a0a0a0;\n}\n\n.notification-info-value[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #333;\n}\n\n.dark[_nghost-%COMP%]   .notification-info-value[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-info-value[_ngcontent-%COMP%] {\n  color: #e0e0e0;\n}\n\n\n\n.notification-attachments-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 0.75rem;\n}\n\n.notification-attachment-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 0.75rem;\n  background: rgba(79, 95, 173, 0.05);\n  border-radius: 8px;\n  border: 1px solid rgba(79, 95, 173, 0.1);\n  transition: all 0.3s ease;\n}\n\n.dark[_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%] {\n  background: rgba(0, 0, 0, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.notification-attachment-item[_ngcontent-%COMP%]:hover {\n  background: rgba(79, 95, 173, 0.1);\n  border-color: rgba(79, 95, 173, 0.3);\n}\n\n.dark[_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-attachment-item[_ngcontent-%COMP%]:hover {\n  background: rgba(0, 247, 255, 0.1);\n  border-color: rgba(0, 247, 255, 0.3);\n}\n\n.notification-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  object-fit: cover;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.notification-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\n  transform: scale(1.1);\n}\n\n.notification-attachment-icon[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(79, 95, 173, 0.2);\n  border-radius: 6px;\n  font-size: 1.5rem;\n  color: #4f5fad;\n}\n\n.dark[_nghost-%COMP%]   .notification-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-icon[_ngcontent-%COMP%] {\n  background: rgba(0, 247, 255, 0.2);\n  color: #00f7ff;\n}\n\n.notification-attachment-info[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n\n.notification-attachment-name[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.25rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.dark[_nghost-%COMP%]   .notification-attachment-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-name[_ngcontent-%COMP%] {\n  color: #e0e0e0;\n}\n\n.notification-attachment-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  font-size: 0.8rem;\n  color: #666;\n}\n\n.dark[_nghost-%COMP%]   .notification-attachment-meta[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-meta[_ngcontent-%COMP%] {\n  color: #a0a0a0;\n}\n\n.notification-attachment-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n}\n\n\n\n\n\n\n\n\n@keyframes _ngcontent-%COMP%_fluoro-pulse {\n  0% {\n    text-shadow: 0 0 5px rgba(0, 255, 255, 0.8), 0 0 10px rgba(0, 255, 255, 0.6),\n      0 0 15px rgba(0, 255, 255, 0.4);\n    box-shadow: 0 2px 10px rgba(0, 255, 255, 0.6),\n      0 0 20px rgba(0, 255, 255, 0.3), inset 0 0 10px rgba(0, 255, 255, 0.2);\n    border-color: rgba(0, 255, 255, 0.8);\n  }\n  100% {\n    text-shadow: 0 0 8px rgba(0, 255, 255, 1), 0 0 15px rgba(0, 255, 255, 0.8),\n      0 0 25px rgba(0, 255, 255, 0.6);\n    box-shadow: 0 2px 15px rgba(0, 255, 255, 0.8),\n      0 0 30px rgba(0, 255, 255, 0.5), inset 0 0 15px rgba(0, 255, 255, 0.3);\n    border-color: rgba(0, 255, 255, 1);\n  }\n}\n\n\n\n@keyframes _ngcontent-%COMP%_fluoro-pulse-intense {\n  0% {\n    text-shadow: 0 0 8px rgba(255, 255, 255, 1), 0 0 15px rgba(0, 255, 255, 0.9),\n      0 0 25px rgba(0, 255, 255, 0.7);\n    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.8),\n      0 0 30px rgba(0, 255, 255, 0.5), inset 0 0 15px rgba(0, 255, 255, 0.3);\n    transform: translateY(-2px) scale(1.05);\n  }\n  100% {\n    text-shadow: 0 0 12px rgba(255, 255, 255, 1), 0 0 20px rgba(0, 255, 255, 1),\n      0 0 35px rgba(0, 255, 255, 0.9);\n    box-shadow: 0 6px 25px rgba(0, 255, 255, 1), 0 0 40px rgba(0, 255, 255, 0.7),\n      inset 0 0 20px rgba(0, 255, 255, 0.4);\n    transform: translateY(-3px) scale(1.08);\n  }\n}\n\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm5vdGlmaWNhdGlvbi1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsZ0JBQWdCO0FBQ2hCLG1GQUFtRjtBQUNuRjtFQUNFLFVBQVU7RUFDViw4QkFBOEI7RUFDOUIsa0JBQWtCO0VBQ2xCLGdCQUFnQjtFQUNoQixXQUFXO0VBQ1gsYUFBYTtFQUNiLHVCQUF1QjtFQUN2QixpQkFBaUIsRUFBRSw0QkFBNEI7RUFDL0MsZ0JBQWdCO0VBQ2hCLGFBQWEsRUFBRSw0Q0FBNEM7QUFDN0Q7O0FBRUEscUNBQXFDO0FBQ3JDO0VBQ0UseUJBQXlCLEVBQUUseUNBQXlDO0VBQ3BFLGNBQWM7RUFDZCxrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCOztBQUVBLHNDQUFzQztBQUN0QztFQUNFLHlCQUF5QixFQUFFLHlDQUF5QztFQUNwRSxjQUFjO0VBQ2Qsa0JBQWtCO0VBQ2xCLGdCQUFnQjtBQUNsQjs7QUFFQSxzREFBc0Q7QUFDdEQ7RUFDRSxrQkFBa0I7RUFDbEIsUUFBUTtFQUNSLGdCQUFnQjtFQUNoQixvQkFBb0I7RUFDcEIsVUFBVTtBQUNaOztBQUVBLHVDQUF1QztBQUN2QztFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsUUFBUTtFQUNSLGFBQWE7RUFDYjs0REFDMEQ7RUFDMUQsMkRBQTJEO0VBQzNELFVBQVU7QUFDWjs7QUFFQSx3Q0FBd0M7QUFDeEM7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLFFBQVE7RUFDUixhQUFhLEVBQUUsNEJBQTRCO0VBQzNDOzs7OzsyRUFLeUUsRUFBRSwrREFBK0Q7RUFDMUksMkRBQTJELEVBQUUsa0VBQWtFO0VBQy9ILFVBQVU7RUFDViw2Q0FBNkMsRUFBRSwwQ0FBMEM7QUFDM0Y7O0FBRUEsc0NBQXNDO0FBQ3RDO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixPQUFPO0VBQ1AsUUFBUTtFQUNSLFdBQVc7RUFDWDs7Ozs7R0FLQztFQUNELDJDQUEyQztFQUMzQyxVQUFVO0VBQ1Ysa0NBQWtDLEVBQUUsb0NBQW9DO0FBQzFFOztBQUVBLHlEQUF5RDtBQUN6RDtFQUNFO0lBQ0UsYUFBYTtFQUNmO0VBQ0E7SUFDRSxhQUFhO0VBQ2Y7RUFDQTtJQUNFLGFBQWE7RUFDZjtBQUNGOztBQUVBLDBDQUEwQztBQUMxQztFQUNFO0lBQ0UsU0FBUztJQUNULFlBQVk7RUFDZDtFQUNBO0lBQ0UsWUFBWTtFQUNkO0VBQ0E7SUFDRSxTQUFTO0lBQ1QsWUFBWTtFQUNkO0FBQ0Y7O0FBRUEsd0ZBQXdGO0FBQ3hGO0VBQ0UscUJBQXFCO0VBQ3JCLGdCQUFnQjtFQUNoQixrQkFBa0I7RUFDbEIsVUFBVTtFQUNWLG1CQUFtQixFQUFFLHNDQUFzQztFQUMzRCxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFdBQVc7RUFDWCxpQkFBaUIsRUFBRSw2REFBNkQ7RUFDaEYsMEJBQTBCLEVBQUUsK0NBQStDO0FBQzdFOztBQUVBLDhCQUE4QjtBQUM5QjtFQUNFLHlCQUF5QjtFQUN6Qix5Q0FBeUM7RUFDekMsd0NBQXdDO0FBQzFDOztBQUVBLCtCQUErQjtBQUMvQjtFQUNFLHlCQUF5QixFQUFFLG9EQUFvRDtFQUMvRSx5Q0FBeUM7RUFDekMsMENBQTBDLEVBQUUsZ0RBQWdEO0VBQzVGLG1DQUEyQjtVQUEzQiwyQkFBMkI7QUFDN0I7O0FBRUEsa0NBQWtDO0FBQ2xDO0VBQ0UsYUFBYTtFQUNiLDhCQUE4QjtFQUM5QixtQkFBbUI7RUFDbkIsYUFBYTtFQUNiLGdCQUFnQjtFQUNoQixNQUFNO0VBQ04sV0FBVztFQUNYLHFCQUFxQixFQUFFLHFEQUFxRDtBQUM5RTs7QUFFQSx5QkFBeUI7QUFDekI7RUFDRSwrQ0FBK0M7RUFDL0MsMENBQTBDO0VBQzFDLHlDQUF5QztBQUMzQzs7QUFFQSwwQkFBMEI7QUFDMUI7RUFDRSwrQ0FBK0M7RUFDL0Msb0NBQW9DO0VBQ3BDLHlDQUF5QztBQUMzQzs7QUFFQSx1QkFBdUI7QUFDdkI7RUFDRSxrQkFBa0I7RUFDbEIsZ0JBQWdCO0VBQ2hCLGNBQWM7RUFDZCxhQUFhO0VBQ2IsbUJBQW1CO0FBQ3JCOztBQUVBLHdCQUF3QjtBQUN4QjtFQUNFLGtCQUFrQjtFQUNsQixnQkFBZ0I7RUFDaEIsMEJBQTBCO0VBQzFCLGFBQWE7RUFDYixtQkFBbUI7QUFDckI7O0FBRUEsa0VBQWtFO0FBQ2xFO0VBQ0Usb0JBQW9CO0VBQ3BCLDBFQUEwRTtFQUMxRSxrQkFBa0I7RUFDbEIsUUFBUSxFQUFFLDZDQUE2QztFQUN2RCwrRkFBK0Y7RUFDL0YsV0FBVztFQUNYLFlBQVk7RUFDWixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixpRUFBaUU7RUFDakUsa0JBQWtCO0VBQ2xCLGtDQUFrQztFQUNsQyx3Q0FBd0M7RUFDeEMsMENBQTBDO0VBQzFDLHlCQUF5QjtBQUMzQjs7QUFFQSw0QkFBNEI7QUFDNUI7RUFDRSxrQ0FBa0M7RUFDbEMsd0NBQXdDO0VBQ3hDLDBDQUEwQztFQUMxQyxjQUFjO0FBQ2hCOztBQUVBLGlDQUFpQztBQUNqQztFQUNFLHNCQUFzQjtFQUN0QiwyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRSwyQ0FBMkM7QUFDN0M7O0FBRUEsaUNBQWlDO0FBQ2pDO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLDZCQUE2QjtFQUM3QixxQ0FBcUM7RUFDckMsMkNBQTJDO0VBQzNDLCtDQUErQztFQUMvQywyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRTtJQUNFLHVCQUF1QjtFQUN6QjtFQUNBO0lBQ0UseUJBQXlCO0VBQzNCO0FBQ0Y7QUFDQTtFQUNFLGdCQUFnQjtFQUNoQixzQkFBc0I7RUFDdEIsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQSw0QkFBNEI7QUFDNUI7RUFDRSxZQUFZO0VBQ1osYUFBYTtFQUNiLHVDQUF1QztFQUN2Qyx5Q0FBeUM7RUFDekMsc0NBQXNDO0FBQ3hDOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLGtCQUFrQjtFQUNsQixxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSx5QkFBeUI7RUFDekIsZ0JBQWdCO0VBQ2hCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLHNCQUFzQjtFQUN0QixrQkFBa0I7RUFDbEIsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0Usd0JBQXdCO0VBQ3hCLHVDQUF1QztFQUN2Qyx5QkFBeUI7RUFDekIsb0NBQW9DO0VBQ3BDLHNDQUFzQztFQUN0QyxrQkFBa0I7RUFDbEIsZUFBZTtFQUNmLHNDQUFzQztBQUN4Qzs7QUFFQTtFQUNFLHVDQUF1QztFQUN2QywwQ0FBMEM7QUFDNUM7O0FBRUEsd0JBQXdCO0FBQ3hCO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGtCQUFrQjtFQUNsQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxlQUFlO0VBQ2YsMEJBQTBCO0VBQzFCLG1CQUFtQjtFQUNuQixZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsZ0JBQWdCO0VBQ2hCLHdCQUF3QjtFQUN4QixxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxzQkFBc0I7RUFDdEIsbUJBQW1CO0VBQ25CLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLG9CQUFvQjtFQUNwQix3Q0FBd0M7RUFDeEMsMEJBQTBCO0VBQzFCLHdDQUF3QztFQUN4QyxzQ0FBc0M7RUFDdEMsbUJBQW1CO0VBQ25CLGVBQWU7RUFDZixzQ0FBc0M7QUFDeEM7O0FBRUE7RUFDRSx3Q0FBd0M7RUFDeEMsMkJBQTJCO0VBQzNCLDhCQUE4QjtBQUNoQzs7QUFFQSx1REFBdUQ7QUFDdkQ7RUFDRSxVQUFVO0VBQ1YsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixPQUFPO0VBQ1AsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtFQUNsQixxQkFBcUI7RUFDckIsVUFBVTtFQUNWLFdBQVc7QUFDYjs7QUFFQTtFQUNFLFVBQVU7QUFDWjs7QUFFQTtFQUNFLHVCQUF1QjtBQUN6Qjs7QUFFQSx5Q0FBeUM7QUFDekM7RUFDRSxvQ0FBb0M7RUFDcEMsdUJBQXVCO0FBQ3pCOztBQUVBOztFQUVFLHlCQUF5QjtFQUN6QixtQkFBbUI7QUFDckI7O0FBRUEsMENBQTBDO0FBQzFDO0VBQ0UsZ0RBQWdEO0VBQ2hELDZCQUE2QjtBQUMvQjs7QUFFQTtFQUNFLHFDQUFxQztFQUNyQyxtQkFBbUI7QUFDckI7O0FBRUEsc0RBQXNEO0FBQ3REO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQiw0QkFBNEIsRUFBRSxtREFBbUQ7RUFDakYsa0JBQWtCO0VBQ2xCLHlCQUF5QjtFQUN6QixtQkFBbUIsRUFBRSxnRUFBZ0U7RUFDckYsa0JBQWtCLEVBQUUsd0JBQXdCO0VBQzVDLGlCQUFpQixFQUFFLDhDQUE4QztFQUNqRSw4QkFBOEIsRUFBRSxxQ0FBcUM7QUFDdkU7O0FBRUEsdUNBQXVDO0FBQ3ZDO0VBQ0UsK0NBQStDO0VBQy9DLHVCQUF1QjtFQUN2Qix5Q0FBeUM7RUFDekMsbUJBQW1CLEVBQUUsbURBQW1EO0VBQ3hFLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLHlDQUF5QztFQUN6QywyQkFBMkI7RUFDM0Isd0NBQXdDO0FBQzFDOztBQUVBLG9EQUFvRDtBQUNwRDs7RUFFRTs7OztHQUlDLEVBQUUsaURBQWlEO0VBQ3BELHdDQUF3QyxFQUFFLGtEQUFrRDtFQUM1Rix5Q0FBeUMsRUFBRSwwQ0FBMEM7RUFDckYsNkJBQTZCLEVBQUUsZ0VBQWdFO0VBQy9GLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUEseUVBQXlFO0FBQ3pFOztFQUVFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNULHNCQUFzQjtFQUN0QixvQkFBb0I7RUFDcEIsV0FBVztFQUNYLG9EQUFvRCxFQUFFLGlDQUFpQztBQUN6Rjs7QUFFQSw4REFBOEQ7QUFDOUQ7O0VBRUUsMkJBQTJCO0VBQzNCOzs7O0dBSUMsRUFBRSxtQ0FBbUM7RUFDdEMsNkNBQTZDLEVBQUUsMEJBQTBCO0FBQzNFOztBQUVBOzsyQ0FFMkM7QUFDM0M7RUFDRSxtQkFBbUI7RUFDbkI7OztHQUdDLEVBQUUsMkNBQTJDO0VBQzlDLDBDQUEwQyxFQUFFLG9EQUFvRDtFQUNoRyxtQkFBbUIsRUFBRSxtREFBbUQ7RUFDeEUseUNBQXlDLEVBQUUsMENBQTBDO0VBQ3JGLG1CQUFtQixFQUFFLHVDQUF1QztFQUM1RCxpQkFBaUIsRUFBRSxtQkFBbUI7RUFDdEMsa0JBQWtCLEVBQUUsbUJBQW1CO0VBQ3ZDLHlCQUF5QixFQUFFLDJDQUEyQztFQUN0RTs7O0dBR0MsRUFBRSxnREFBZ0Q7QUFDckQ7O0FBRUE7RUFDRTs7R0FFQyxFQUFFLHdEQUF3RDtFQUMzRCx5Q0FBeUMsRUFBRSxtQ0FBbUM7QUFDaEY7O0FBRUEsbUVBQW1FO0FBQ25FOztFQUVFLGtCQUFrQjtFQUNsQixnQkFBZ0I7RUFDaEI7Ozs7R0FJQyxFQUFFLHNEQUFzRDtFQUN6RCx3Q0FBd0MsRUFBRSxpREFBaUQ7RUFDM0YseUNBQXlDLEVBQUUsMENBQTBDO0VBQ3JGLDZCQUE2QixFQUFFLGdFQUFnRTtFQUMvRix5QkFBeUI7QUFDM0I7O0FBRUEsMkRBQTJEO0FBQzNEOztFQUVFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNULHNCQUFzQjtFQUN0QixvQkFBb0I7RUFDcEIsV0FBVztFQUNYLGtEQUFrRCxFQUFFLGlDQUFpQztBQUN2Rjs7QUFFQSxnREFBZ0Q7QUFDaEQ7O0VBRUUsMkJBQTJCO0VBQzNCLDZDQUE2QyxFQUFFLHlCQUF5QjtFQUN4RTs7OztHQUlDLEVBQUUsbUNBQW1DO0FBQ3hDOztBQUVBLDBDQUEwQztBQUMxQztFQUNFLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUEsNkJBQTZCO0FBQzdCO0VBQ0U7SUFDRSxVQUFVO0VBQ1o7RUFDQTtJQUNFLFVBQVU7RUFDWjtBQUNGOztBQUVBO0VBQ0U7SUFDRSxxQkFBcUI7SUFDckIsVUFBVTtFQUNaO0VBQ0E7SUFDRSxtQkFBbUI7SUFDbkIsVUFBVTtFQUNaO0FBQ0Y7O0FBRUE7RUFDRTtJQUNFLDBCQUEwQjtFQUM1QjtFQUNBO0lBQ0UsNEJBQTRCO0VBQzlCO0FBQ0Y7O0FBRUEsNENBQTRDO0FBQzVDO0VBQ0UsZUFBZTtFQUNmLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFNBQVM7RUFDVCxvQ0FBb0M7RUFDcEMsa0NBQTBCO1VBQTFCLDBCQUEwQjtFQUMxQixhQUFhLEVBQUUscUJBQXFCO0VBQ3BDLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsYUFBYTtFQUNiLDJCQUEyQjtFQUMzQixVQUFVLEVBQUUseUJBQXlCO0VBQ3JDLDZCQUE2QjtBQUMvQjs7QUFFQSwrQkFBK0I7QUFDL0I7RUFDRSxVQUFVO0FBQ1o7O0FBRUE7RUFDRSxvQ0FBb0M7RUFDcEMsa0NBQTBCO1VBQTFCLDBCQUEwQjtFQUMxQiw0Q0FBNEM7QUFDOUM7O0FBRUE7RUFDRTtJQUNFLGtDQUFrQztJQUNsQyxrQ0FBMEI7WUFBMUIsMEJBQTBCO0VBQzVCO0VBQ0E7SUFDRSxvQ0FBb0M7SUFDcEMsa0NBQTBCO1lBQTFCLDBCQUEwQjtFQUM1QjtBQUNGOztBQUVBLGlDQUFpQztBQUNqQztFQUNFLFVBQVU7RUFDVixnQkFBZ0I7RUFDaEIsZ0JBQWdCO0VBQ2hCLHlCQUF5QjtFQUN6QixtQkFBbUI7RUFDbkIsZ0JBQWdCO0VBQ2hCLDBDQUEwQztFQUMxQyxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLDRCQUE0QjtFQUM1Qix3Q0FBd0M7RUFDeEMsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sT0FBTztFQUNQLFFBQVE7RUFDUixXQUFXO0VBQ1g7Ozs7Ozs7R0FPQztFQUNELDBCQUEwQjtFQUMxQix3Q0FBd0M7RUFDeEMsMkNBQTJDO0VBQzNDLFVBQVU7QUFDWjs7QUFFQSxrQ0FBa0M7QUFDbEM7RUFDRSxVQUFVO0VBQ1YsZ0JBQWdCO0VBQ2hCLGdCQUFnQjtFQUNoQix3Q0FBd0M7RUFDeEMsbUJBQW1CO0VBQ25CLGdCQUFnQjtFQUNoQjt5Q0FDdUM7RUFDdkMsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QiwwREFBMEQ7RUFDMUQsd0NBQXdDO0VBQ3hDLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFO0lBQ0UsVUFBVTtJQUNWLHNDQUFzQztFQUN4QztFQUNBO0lBQ0UsVUFBVTtJQUNWLGlDQUFpQztFQUNuQztBQUNGOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sT0FBTztFQUNQLFFBQVE7RUFDUixXQUFXO0VBQ1g7Ozs7Ozs7R0FPQztFQUNELDBCQUEwQjtFQUMxQix3Q0FBd0M7RUFDeEMsMkNBQTJDO0VBQzNDLFVBQVU7QUFDWjs7QUFFQSxrQ0FBa0M7QUFDbEM7RUFDRSxhQUFhO0VBQ2IsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQiw4QkFBOEI7RUFDOUIsK0NBQStDO0VBQy9DLHlDQUF5QztBQUMzQzs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixnQkFBZ0I7RUFDaEIsY0FBYztFQUNkLFNBQVM7RUFDVCxhQUFhO0VBQ2IsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsd0NBQXdDO0VBQ3hDLGNBQWM7RUFDZCxZQUFZO0VBQ1osYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsZUFBZTtFQUNmLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLHdDQUF3QztFQUN4QyxxQkFBcUI7RUFDckIsMkNBQTJDO0FBQzdDOztBQUVBLG1DQUFtQztBQUNuQztFQUNFLGFBQWE7RUFDYixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLDhCQUE4QjtFQUM5QiwrQ0FBK0M7RUFDL0Msb0NBQW9DO0FBQ3RDOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLGdCQUFnQjtFQUNoQixjQUFjO0VBQ2QsU0FBUztFQUNULGFBQWE7RUFDYixtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQix3Q0FBd0M7RUFDeEMsY0FBYztFQUNkLFlBQVk7RUFDWixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixlQUFlO0VBQ2YseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0Usd0NBQXdDO0VBQ3hDLHFCQUFxQjtFQUNyQiwyQ0FBMkM7QUFDN0M7O0FBRUEsbUJBQW1CO0FBQ25CO0VBQ0UsYUFBYTtFQUNiLGdCQUFnQjtFQUNoQiw2QkFBNkI7QUFDL0I7O0FBRUEsNkJBQTZCO0FBQzdCO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixTQUFTO0FBQ1g7O0FBRUEseUNBQXlDO0FBQ3pDO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixhQUFhO0VBQ2Isa0JBQWtCO0VBQ2xCLHlDQUF5QztFQUN6Qyx3Q0FBd0M7RUFDeEMseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0Usd0NBQXdDO0VBQ3hDLDJCQUEyQjtFQUMzQix5Q0FBeUM7QUFDM0M7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQixnQkFBZ0I7RUFDaEIsa0JBQWtCO0VBQ2xCLGNBQWM7RUFDZCx3Q0FBd0M7QUFDMUM7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQix3Q0FBd0M7RUFDeEMsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsa0JBQWtCO0VBQ2xCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxlQUFlO0VBQ2YsY0FBYztBQUNoQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLHdDQUF3QztFQUN4QyxjQUFjO0VBQ2QsWUFBWTtFQUNaLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGVBQWU7RUFDZix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSx3Q0FBd0M7RUFDeEMscUJBQXFCO0VBQ3JCLDJDQUEyQztBQUM3Qzs7QUFFQSwwQ0FBMEM7QUFDMUM7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLGFBQWE7RUFDYixrQkFBa0I7RUFDbEIseUNBQXlDO0VBQ3pDLHdDQUF3QztFQUN4Qyx5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSx3Q0FBd0M7RUFDeEMsMkJBQTJCO0VBQzNCLHlDQUF5QztBQUMzQzs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLGdCQUFnQjtFQUNoQixrQkFBa0I7RUFDbEIsY0FBYztFQUNkLHdDQUF3QztFQUN4QywyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQix3Q0FBd0M7RUFDeEMsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsa0JBQWtCO0VBQ2xCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxlQUFlO0VBQ2YsY0FBYztBQUNoQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLHdDQUF3QztFQUN4QyxjQUFjO0VBQ2QsWUFBWTtFQUNaLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGVBQWU7RUFDZix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSx3Q0FBd0M7RUFDeEMscUJBQXFCO0VBQ3JCLDJDQUEyQztBQUM3Qzs7QUFFQSwyQ0FBMkM7QUFDM0M7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGlCQUFpQjtFQUNqQixlQUFlO0VBQ2YsK0JBQStCO0FBQ2pDOztBQUVBO0VBQ0UscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UsT0FBTztFQUNQLFlBQVk7QUFDZDs7QUFFQTtFQUNFLGdCQUFnQjtFQUNoQixrQkFBa0I7RUFDbEIsbUJBQW1CO0VBQ25CLGdCQUFnQjtFQUNoQix1QkFBdUI7QUFDekI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFFBQVE7RUFDUixpQkFBaUI7RUFDakIsc0JBQXNCO0FBQ3hCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLFFBQVE7RUFDUixpQkFBaUI7QUFDbkI7O0FBRUEsNkNBQTZDO0FBQzdDO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGVBQWU7QUFDakI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQiw2QkFBNkI7RUFDN0IsbUJBQW1CO0VBQ25CLG9DQUFvQztBQUN0Qzs7QUFFQTtFQUNFLHlCQUF5QjtFQUN6QiwyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRSx5QkFBeUI7RUFDekIsMkNBQTJDO0FBQzdDOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixlQUFlO0VBQ2Ysa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsZUFBZTtFQUNmLG1CQUFtQjtFQUNuQixZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixnQkFBZ0I7RUFDaEIsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLHNCQUFzQjtFQUN0QixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRTtJQUNFLHVCQUF1QjtFQUN6QjtFQUNBO0lBQ0UseUJBQXlCO0VBQzNCO0FBQ0Y7O0FBRUEscUJBQXFCO0FBQ3JCO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixjQUFjO0VBQ2Qsa0JBQWtCO0VBQ2xCLGlCQUFpQixFQUFFLDRFQUE0RTtBQUNqRzs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLGlCQUFpQjtBQUNuQjs7QUFFQSwrQkFBK0I7QUFDL0I7RUFDRSxPQUFPO0VBQ1AsWUFBWTtFQUNaLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGtCQUFrQixFQUFFLCtCQUErQjtBQUNyRDs7QUFFQTtFQUNFLGFBQWE7RUFDYiw4QkFBOEIsRUFBRSxzQ0FBc0M7RUFDdEUsbUJBQW1CLEVBQUUseUJBQXlCO0VBQzlDLFdBQVcsRUFBRSx3Q0FBd0M7QUFDdkQ7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsa0JBQWtCLEVBQUUsOENBQThDO0VBQ2xFLGNBQWM7RUFDZCxjQUFjLEVBQUUscURBQXFEO0VBQ3JFLHlCQUF5QixFQUFFLGdEQUFnRDtBQUM3RTs7QUFFQTtFQUNFLGNBQWMsRUFBRSw2Q0FBNkM7RUFDN0QsMkNBQTJDLEVBQUUsbUNBQW1DO0FBQ2xGOztBQUVBO0VBQ0UsY0FBYyxFQUFFLHVDQUF1QztFQUN2RCwyQ0FBMkMsRUFBRSx5QkFBeUI7QUFDeEU7O0FBRUE7RUFDRSxjQUFjLEVBQUUsZ0NBQWdDO0VBQ2hELDJDQUEyQyxFQUFFLDJDQUEyQztBQUMxRjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxpQkFBaUI7RUFDakIsa0JBQWtCO0VBQ2xCLFVBQVU7QUFDWjs7QUFFQTtFQUNFLGNBQWMsRUFBRSxxRUFBcUU7QUFDdkY7O0FBRUEsNkRBQTZEO0FBQzdEO0VBQ0U7OztHQUdDLEVBQUUscURBQXFEO0VBQ3hELGdCQUFnQixFQUFFLG9EQUFvRDtBQUN4RTs7QUFFQSw4REFBOEQ7QUFDOUQ7RUFDRTs7O0dBR0MsRUFBRSxxREFBcUQ7RUFDeEQsZ0JBQWdCLEVBQUUsb0RBQW9EO0FBQ3hFOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxlQUFlO0VBQ2YsbUJBQW1CO0VBQ25CLGdCQUFnQjtFQUNoQix1QkFBdUI7RUFDdkIsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLGNBQWMsRUFBRSwwRUFBMEU7QUFDNUY7O0FBRUEsaUNBQWlDO0FBQ2pDO0VBQ0Usa0JBQWtCO0VBQ2xCLGNBQWM7RUFDZCxtQkFBbUI7RUFDbkIsYUFBYTtFQUNiLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLDZCQUE2QjtBQUMvQjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQSwrREFBK0Q7O0FBRS9ELHlCQUF5QjtBQUN6QjtFQUNFLGVBQWU7RUFDZixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixpQkFBaUI7RUFDakIseUJBQXlCO0VBQ3pCLFdBQVcsRUFBRSx5REFBeUQ7RUFDdEUsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUEsMERBQTBEO0FBQzFEO0VBQ0Usa0JBQWtCO0VBQ2xCLFNBQVMsRUFBRSxzQ0FBc0M7RUFDakQsV0FBVyxFQUFFLCtDQUErQztFQUM1RCxrQkFBa0IsRUFBRSwwREFBMEQ7RUFDOUUsNkJBQTZCO0VBQzdCLGdCQUFnQjtFQUNoQixpQkFBaUIsRUFBRSxnQ0FBZ0M7RUFDbkQsa0JBQWtCO0VBQ2xCOzs7O0dBSUM7RUFDRCx3Q0FBd0M7RUFDeEMsNENBQTRDO0VBQzVDLFdBQVc7RUFDWCx5QkFBeUI7RUFDekIsbUJBQW1CO0VBQ25CLGtDQUFrQztFQUNsQyxxQkFBcUI7RUFDckIsa0NBQWtDO0VBQ2xDLG1DQUFtQztBQUNyQzs7QUFFQSwrREFBK0Q7QUFDL0Q7RUFDRSxvQ0FBb0M7RUFDcEMseUJBQXlCLEVBQUUseUJBQXlCO0VBQ3BEOzs7OztHQUtDO0VBQ0Qsd0NBQXdDO0VBQ3hDLHdDQUF3QztFQUN4QzttQ0FDaUM7RUFDakM7eUNBQ3VDO0VBQ3ZDLDZCQUFxQjtVQUFyQixxQkFBcUIsRUFBRSxrREFBa0Q7RUFDekUsd0NBQXdDO0VBQ3hDLHlEQUF5RDtBQUMzRDs7QUFFQSx5Q0FBeUM7QUFDekM7RUFDRSxjQUFjO0VBQ2Q7Ozs7R0FJQztFQUNELHdDQUF3QztFQUN4Qyw2Q0FBNkM7QUFDL0M7O0FBRUEsdUJBQXVCO0FBQ3ZCO0VBQ0UsdUNBQXVDO0VBQ3ZDLDZDQUE2QztBQUMvQzs7QUFFQTtFQUNFLHNDQUFzQztFQUN0Qyx5QkFBeUIsRUFBRSx3QkFBd0I7RUFDbkQ7Ozs7O0dBS0M7RUFDRCxrQ0FBa0MsRUFBRSxzQkFBc0I7RUFDMUQsNEJBQTRCO0VBQzVCO21DQUNpQztFQUNqQzt5Q0FDdUM7RUFDdkMscUNBQXFDO0VBQ3JDLGlFQUFpRTtBQUNuRTs7QUFFQTtFQUNFOzs7O0dBSUM7RUFDRCw4Q0FBOEM7QUFDaEQ7O0FBRUEsaURBQWlEO0FBQ2pEO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixxREFBcUQ7RUFDckQsWUFBWTtFQUNaLHlCQUF5QjtFQUN6Qix3RUFBd0U7RUFDeEUsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UscURBQXFEO0VBQ3JELG1DQUFtQztFQUNuQywwQ0FBMEM7RUFDMUMscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixTQUFTO0VBQ1QsVUFBVTtFQUNWLFdBQVc7RUFDWCxZQUFZO0VBQ1osb0RBQW9EO0VBQ3BELHdFQUF3RTtFQUN4RSxXQUFXO0VBQ1gsVUFBVTtFQUNWLDZCQUE2QjtBQUMvQjs7QUFFQTtFQUNFLFVBQVU7QUFDWjs7QUFFQSwyQ0FBMkM7QUFDM0M7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLHFEQUFxRDtFQUNyRCxZQUFZO0VBQ1oseUJBQXlCO0VBQ3pCLGtCQUFrQjtFQUNsQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxxREFBcUQ7RUFDckQsb0NBQW9DO0VBQ3BDLDRDQUE0QztFQUM1QyxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLFFBQVE7RUFDUixTQUFTO0VBQ1QsVUFBVTtFQUNWLFdBQVc7RUFDWCxvQ0FBb0M7RUFDcEMsa0JBQWtCO0VBQ2xCLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLG9DQUFvQztBQUN0Qzs7QUFFQSxvREFBb0Q7QUFDcEQ7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLHFEQUFxRDtFQUNyRCxZQUFZO0VBQ1oseUJBQXlCO0VBQ3pCLGtCQUFrQjtFQUNsQix3QkFBd0I7RUFDeEIsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UscURBQXFEO0VBQ3JELG9DQUFvQztFQUNwQywyQ0FBMkM7RUFDM0MscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixRQUFRO0VBQ1IsU0FBUztFQUNULFVBQVU7RUFDVixXQUFXO0VBQ1gsb0NBQW9DO0VBQ3BDLGtCQUFrQjtFQUNsQiwrQ0FBK0M7RUFDL0MsNEJBQTRCO0FBQzlCOztBQUVBLG9EQUFvRDtBQUNwRDtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1oscURBQXFEO0VBQ3JELFlBQVk7RUFDWix5QkFBeUI7RUFDekIsOENBQThDO0VBQzlDLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLHFEQUFxRDtFQUNyRCxvQ0FBb0M7RUFDcEMsMkNBQTJDO0VBQzNDLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsU0FBUztFQUNULFVBQVU7RUFDVixXQUFXO0VBQ1gsWUFBWTtFQUNaLG9EQUFvRDtFQUNwRCw4Q0FBOEM7RUFDOUMsV0FBVztFQUNYLFVBQVU7RUFDViw2QkFBNkI7QUFDL0I7O0FBRUE7RUFDRSxVQUFVO0FBQ1o7O0FBRUEseURBQXlEO0FBQ3pEO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixxREFBcUQ7RUFDckQsWUFBWTtFQUNaLHlCQUF5QjtFQUN6Qjs7Ozs7Ozs7O0dBU0M7RUFDRCxrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxxREFBcUQ7RUFDckQsbUNBQW1DO0VBQ25DLDRDQUE0QztFQUM1QyxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLFFBQVE7RUFDUixTQUFTO0VBQ1QsVUFBVTtFQUNWLFdBQVc7RUFDWCxvQ0FBb0M7RUFDcEMsa0JBQWtCO0VBQ2xCLGdDQUFnQztFQUNoQywrQkFBK0I7QUFDakM7O0FBRUEsdUNBQXVDO0FBQ3ZDO0VBQ0UscURBQXFEO0VBQ3JELHFCQUFxQjtFQUNyQiwwQ0FBMEM7QUFDNUM7O0FBRUE7RUFDRSxxREFBcUQ7RUFDckQscUJBQXFCO0VBQ3JCLDRDQUE0QztBQUM5Qzs7QUFFQTtFQUNFLHFEQUFxRDtFQUNyRCxxQkFBcUI7RUFDckIsMkNBQTJDO0FBQzdDOztBQUVBO0VBQ0UscURBQXFEO0VBQ3JELHFCQUFxQjtFQUNyQiwyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRSxxREFBcUQ7RUFDckQscUJBQXFCO0VBQ3JCLDRDQUE0QztBQUM5Qzs7QUFFQSxxQ0FBcUM7QUFDckM7RUFDRTs7SUFFRSxVQUFVO0lBQ1Ysd0RBQXdEO0VBQzFEO0VBQ0E7SUFDRSxZQUFZO0lBQ1osMERBQTBEO0VBQzVEO0FBQ0Y7O0FBRUE7RUFDRTs7SUFFRSx5Q0FBeUM7RUFDM0M7RUFDQTtJQUNFLDJDQUEyQztFQUM3QztBQUNGOztBQUVBLG9EQUFvRDtBQUNwRDtFQUNFLGFBQWE7RUFDYixrQkFBa0I7RUFDbEIsU0FBUztFQUNULFdBQVc7RUFDWCxlQUFlO0VBQ2Ysb0NBQW9DO0VBQ3BDLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsWUFBWTtFQUNaLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLFVBQVU7RUFDViw2QkFBNkI7RUFDN0IsV0FBVztBQUNiOztBQUVBO0VBQ0UsYUFBYTtFQUNiLGtCQUFrQjtFQUNsQixTQUFTO0VBQ1QsV0FBVztFQUNYLGVBQWU7RUFDZixvQ0FBb0M7RUFDcEMsa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxZQUFZO0VBQ1osYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsVUFBVTtFQUNWLDZCQUE2QjtFQUM3QixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxjQUFjO0VBQ2Qsa0JBQWtCO0VBQ2xCLFNBQVM7RUFDVCxXQUFXO0VBQ1gsZUFBZTtFQUNmLG9DQUFvQztFQUNwQyxrQkFBa0I7RUFDbEIsV0FBVztFQUNYLFlBQVk7RUFDWixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixVQUFVO0VBQ1YsNkJBQTZCO0VBQzdCLFdBQVc7QUFDYjs7QUFFQSw4Q0FBOEM7QUFDOUM7RUFDRSxrQkFBa0I7RUFDbEIscUJBQXFCO0VBQ3JCLFdBQVcsRUFBRSxtQkFBbUI7RUFDaEMsWUFBWSxFQUFFLG1CQUFtQjtFQUNqQyxlQUFlO0VBQ2YseUJBQXlCLEVBQUUsMkNBQTJDO0FBQ3hFOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFVBQVU7RUFDVixlQUFlO0VBQ2YsU0FBUztFQUNULFFBQVE7QUFDVjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sT0FBTztFQUNQLFlBQVksRUFBRSxtQkFBbUI7RUFDakMsV0FBVyxFQUFFLG1CQUFtQjtFQUNoQzs7OztHQUlDLEVBQUUsaUJBQWlCO0VBQ3BCLDZCQUE2QixFQUFFLGlEQUFpRDtFQUNoRixrQkFBa0IsRUFBRSwyQ0FBMkM7RUFDL0QseUJBQXlCO0VBQ3pCLDJDQUEyQyxFQUFFLHdDQUF3QztFQUNyRixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QiwyQ0FBMkMsRUFBRSx5Q0FBeUM7RUFDdEYsa0VBQWtFO0VBQ2xFLGtCQUFrQjtFQUNsQixVQUFVO0VBQ1YsZ0JBQWdCO0FBQ2xCOztBQUVBLDhDQUE4QztBQUM5QztFQUNFO0lBQ0UsMENBQTBDO0VBQzVDO0VBQ0E7SUFDRSwyQ0FBMkM7RUFDN0M7QUFDRjs7QUFFQTtFQUNFOzs7OztHQUtDLEVBQUUseURBQXlEO0VBQzVELHdDQUF3QyxFQUFFLHlEQUF5RDtFQUNuRywyQ0FBMkMsRUFBRSx5Q0FBeUM7RUFDdEYsMkNBQTJDLEVBQUUseUNBQXlDO0FBQ3hGOztBQUVBO0VBQ0U7Ozs7O0dBS0MsRUFBRSw4Q0FBOEM7RUFDakQsd0NBQXdDO0VBQ3hDOztHQUVDLEVBQUUsaURBQWlEO0VBQ3BEOztHQUVDLEVBQUUseURBQXlEO0FBQzlEOztBQUVBO0VBQ0U7Ozs7O0dBS0MsRUFBRSw2REFBNkQ7RUFDaEUsd0NBQXdDO0VBQ3hDOztHQUVDLEVBQUUsaURBQWlEO0VBQ3BEOztHQUVDLEVBQUUseURBQXlEO0FBQzlEOztBQUVBO0VBQ0U7Ozs7R0FJQyxFQUFFLGlDQUFpQztFQUNwQyw2QkFBNkIsRUFBRSxpREFBaUQ7RUFDaEYsMkNBQTJDLEVBQUUsMENBQTBDO0VBQ3ZGLGdEQUFnRCxFQUFFLG9EQUFvRDtBQUN4Rzs7QUFFQSw0REFBNEQ7QUFDNUQ7RUFDRTtJQUNFLDJDQUEyQztJQUMzQyxtQkFBbUI7RUFDckI7RUFDQTtJQUNFLDJDQUEyQztJQUMzQyxzQkFBc0I7RUFDeEI7QUFDRjs7QUFFQSx3REFBd0Q7QUFDeEQ7RUFDRTtJQUNFLG1CQUFtQjtJQUNuQiwyQ0FBMkM7RUFDN0M7RUFDQTtJQUNFLHNCQUFzQjtJQUN0QiwyQ0FBMkM7RUFDN0M7QUFDRjs7QUFFQTtFQUNFOzs7O0dBSUMsRUFBRSxnREFBZ0Q7RUFDbkQsNkJBQTZCLEVBQUUsaURBQWlEO0VBQ2hGLDJDQUEyQyxFQUFFLHlEQUF5RDtFQUN0RyxnREFBZ0QsRUFBRSxtRUFBbUU7QUFDdkg7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLGFBQWE7QUFDZjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxTQUFTLEVBQUUsK0JBQStCO0VBQzFDLFFBQVEsRUFBRSwrQkFBK0I7RUFDekMsVUFBVSxFQUFFLG1CQUFtQjtFQUMvQixZQUFZLEVBQUUsbUJBQW1CO0VBQ2pDLG1CQUFtQjtFQUNuQix5QkFBeUIsRUFBRSxtQ0FBbUM7RUFDOUQsd0JBQXdCO0VBQ3hCLDRDQUE0QyxFQUFFLGdDQUFnQztFQUM5RSw4Q0FBOEMsRUFBRSx5Q0FBeUM7QUFDM0Y7O0FBRUEseUNBQXlDO0FBQ3pDO0VBQ0U7SUFDRSxZQUFZO0lBQ1osNENBQTRDO0VBQzlDO0VBQ0E7SUFDRSxVQUFVO0lBQ1YsMkNBQTJDO0VBQzdDO0FBQ0Y7O0FBRUEscURBQXFEO0FBQ3JEO0VBQ0Usb0JBQW9CO0VBQ3BCLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsYUFBYTtBQUNmOztBQUVBLG1FQUFtRTtBQUNuRTtFQUNFLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQixFQUFFLDJDQUEyQztFQUMvRDs7Ozs7R0FLQyxFQUFFLDBDQUEwQztFQUM3Qyx3Q0FBd0MsRUFBRSwwQ0FBMEM7RUFDcEYsMkNBQTJDLEVBQUUseUNBQXlDO0FBQ3hGOztBQUVBLCtDQUErQztBQUMvQzs7Ozs7RUFLRSxrQ0FBa0M7RUFDbEMsOEJBQThCO0VBQzlCLHNCQUFzQjtBQUN4Qjs7QUFFQSx3RkFBd0Y7QUFDeEY7RUFDRSxVQUFVLEVBQUUsK0JBQStCO0VBQzNDLFFBQVEsRUFBRSwrQkFBK0I7RUFDekMsVUFBVSxFQUFFLG1CQUFtQjtFQUMvQixZQUFZLEVBQUUsbUJBQW1CO0FBQ25DOztBQUVBLHNDQUFzQztBQUN0QztFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsd0NBQXdDO0VBQ3hDLGtCQUFrQjtFQUNsQixpQkFBaUI7RUFDakIsMkNBQTJDO0VBQzNDLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLHdDQUF3QztFQUN4QywyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsa0JBQWtCO0VBQ2xCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSw2QkFBNkI7QUFDL0I7O0FBRUE7OzJDQUUyQztBQUMzQztFQUNFLG1EQUFtRDtFQUNuRCxvREFBb0Q7RUFDcEQsd0RBQXdEO0VBQ3hELDJCQUEyQjtBQUM3Qjs7QUFFQTs7MkNBRTJDO0FBQzNDO0VBQ0UsbURBQW1ELEVBQUUsbUJBQW1CO0VBQ3hFOzs7OztjQUtZLEVBQUUsb0NBQW9DO0VBQ2xEO29EQUNrRCxFQUFFLG1CQUFtQjtFQUN2RSwyQkFBMkI7RUFDM0IsNkJBQTZCLEVBQUUsdUVBQXVFO0VBQ3RHLDhCQUE4QixFQUFFLDJEQUEyRDtFQUMzRixrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCOztBQUVBLDREQUE0RDtBQUM1RDtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsU0FBUztFQUNULFVBQVU7RUFDVixXQUFXO0VBQ1gsWUFBWTtFQUNaOzs7O0dBSUM7RUFDRCw2Q0FBNkM7RUFDN0Msb0JBQW9CO0FBQ3RCOztBQUVBLDJFQUEyRTtBQUMzRTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsUUFBUTtFQUNSOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0tBa0NHO0VBQ0gsVUFBVTtFQUNWLGlEQUFpRDtFQUNqRCxvQkFBb0I7QUFDdEI7O0FBRUE7RUFDRTtJQUNFLFVBQVU7RUFDWjtFQUNBO0lBQ0UsVUFBVTtFQUNaO0VBQ0E7SUFDRSxVQUFVO0VBQ1o7QUFDRjs7QUFFQTtFQUNFO0lBQ0UsdUJBQXVCO0VBQ3pCO0VBQ0E7SUFDRSx5QkFBeUI7RUFDM0I7QUFDRjs7QUFFQTs7MkNBRTJDO0FBQzNDLGtDQUFrQztBQUNsQzs7Ozs7O0VBTUU7Ozs7O2NBS1ksRUFBRSx5RUFBeUU7QUFDekY7O0FBRUEsd0JBQXdCO0FBQ3hCOzs7RUFHRTs7Ozs7Y0FLWSxFQUFFLCtCQUErQjtFQUM3QywwQ0FBMEMsRUFBRSxnQkFBZ0I7RUFDNUQsd0RBQXdELEVBQUUsbUJBQW1CO0FBQy9FOztBQUVBLHdCQUF3QjtBQUN4Qjs7O0VBR0UseUJBQXlCLEVBQUUsMkNBQTJDO0VBQ3RFLGdCQUFnQjtBQUNsQjs7QUFFQTs7MkNBRTJDO0FBQzNDLDhCQUE4QjtBQUM5QjtFQUNFOzs7O0dBSUM7RUFDRCxjQUFjLEVBQUUsNERBQTREO0VBQzVFLHdDQUF3QztBQUMxQzs7QUFFQTs7O0VBR0U7Ozs7R0FJQztFQUNELDJDQUEyQyxFQUFFLDJCQUEyQjtBQUMxRTs7QUFFQSxnQ0FBZ0M7QUFDaEM7OztFQUdFLCtCQUErQjtFQUMvQjs7Ozs7R0FLQyxFQUFFLDZEQUE2RDtFQUNoRSx3Q0FBd0M7QUFDMUM7O0FBRUE7OztFQUdFOzs7OztHQUtDLEVBQUUscUNBQXFDO0VBQ3hDLGNBQWMsRUFBRSw4REFBOEQ7RUFDOUUsMkNBQTJDLEVBQUUsMkJBQTJCO0FBQzFFOztBQUVBLDBCQUEwQjtBQUMxQjs7O0VBR0U7Ozs7O0dBS0MsRUFBRSw2REFBNkQ7RUFDaEUsY0FBYyxFQUFFLDhEQUE4RDtFQUM5RSx3Q0FBd0M7QUFDMUM7O0FBRUE7OztFQUdFOzs7OztHQUtDLEVBQUUscUNBQXFDO0VBQ3hDLGNBQWMsRUFBRSw4REFBOEQ7RUFDOUUsMENBQTBDLEVBQUUsMkJBQTJCO0FBQ3pFOztBQUVBLDhFQUE4RTtBQUM5RTtFQUNFLFVBQVU7RUFDVixXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCOzs7OztHQUtDLEVBQUUsd0NBQXdDO0VBQzNDLGFBQWE7RUFDYiwwQ0FBMEMsRUFBRSxlQUFlO0VBQzNELDBDQUEwQyxFQUFFLDJCQUEyQjtFQUN2RSx5QkFBeUIsRUFBRSwrQ0FBK0M7QUFDNUU7O0FBRUEsaURBQWlEO0FBQ2pEO0VBQ0UsVUFBVTtFQUNWLG1CQUFtQjtFQUNuQixRQUFRO0VBQ1IsU0FBUztBQUNYOztBQUVBLHlFQUF5RTtBQUN6RTs7O0VBR0U7Ozs7O0dBS0MsRUFBRSxnRUFBZ0U7RUFDbkUsMENBQTBDLEVBQUUsOEJBQThCO0VBQzFFLHFEQUFxRCxFQUFFLDBCQUEwQjtBQUNuRjs7QUFFQSxvRkFBb0Y7QUFDcEY7RUFDRTtJQUNFLFlBQVk7SUFDWixtQkFBbUI7RUFDckI7RUFDQTtJQUNFLFVBQVU7SUFDVixxQkFBcUI7RUFDdkI7QUFDRjs7QUFFQSxvREFBb0Q7QUFDcEQ7RUFDRTtJQUNFLFlBQVk7SUFDWixxQkFBcUI7RUFDdkI7RUFDQTtJQUNFLFVBQVU7SUFDVixxQkFBcUI7RUFDdkI7QUFDRjs7QUFFQSx3REFBd0Q7QUFDeEQ7RUFDRSxrQkFBa0I7RUFDbEIsU0FBUyxFQUFFLDBEQUEwRDtFQUNyRSxVQUFVLEVBQUUsc0JBQXNCO0VBQ2xDLFdBQVcsRUFBRSx3REFBd0Q7QUFDdkU7O0FBRUEsaURBQWlEO0FBQ2pEO0VBQ0U7SUFDRSxtQkFBbUI7RUFDckI7RUFDQTtJQUNFLHFCQUFxQjtFQUN2QjtFQUNBO0lBQ0UsbUJBQW1CO0VBQ3JCO0FBQ0Y7O0FBRUEsd0JBQXdCO0FBQ3hCO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixvQkFBb0I7RUFDcEIsMENBQTBDO0VBQzFDLGNBQWM7RUFDZCxZQUFZO0VBQ1osc0NBQXNDO0VBQ3RDLG1CQUFtQjtFQUNuQixnQkFBZ0I7RUFDaEIsZUFBZTtFQUNmLHlCQUF5QjtFQUN6Qiw0Q0FBNEM7QUFDOUM7O0FBRUE7RUFDRSwwQ0FBMEM7RUFDMUMsNkNBQTZDO0VBQzdDLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLDBDQUEwQztFQUMxQyxjQUFjO0VBQ2QsNENBQTRDO0FBQzlDOztBQUVBOzs2Q0FFNkM7O0FBRTdDLGlDQUFpQztBQUNqQztFQUNFLHFCQUFxQjtFQUNyQixhQUFhO0VBQ2IscUNBQXFDO0VBQ3JDLG1CQUFtQjtFQUNuQiwwQ0FBMEM7QUFDNUM7O0FBRUE7RUFDRSxtQ0FBbUM7RUFDbkMsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixlQUFlO0VBQ2YsZ0JBQWdCO0VBQ2hCLGNBQWM7RUFDZCxzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsMkNBQTJDO0FBQzdDOztBQUVBLGlDQUFpQztBQUNqQztFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsWUFBWTtBQUNkOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsaUJBQWlCO0VBQ2pCLHdDQUF3QztBQUMxQzs7QUFFQTtFQUNFLHdDQUF3QztBQUMxQzs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsY0FBYztFQUNkLGVBQWU7QUFDakI7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7QUFDaEI7O0FBRUEsK0JBQStCO0FBQy9CO0VBQ0Usa0NBQWtDO0VBQ2xDLGdCQUFnQjtFQUNoQixrQkFBa0I7RUFDbEIsV0FBVztFQUNYLGdCQUFnQjtFQUNoQiw4QkFBOEI7QUFDaEM7O0FBRUE7RUFDRSw4QkFBOEI7RUFDOUIsY0FBYztFQUNkLDhCQUE4QjtBQUNoQzs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixnQkFBZ0I7RUFDaEIsbUNBQW1DO0VBQ25DLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0Usa0NBQWtDO0VBQ2xDLGNBQWM7QUFDaEI7O0FBRUEsMEJBQTBCO0FBQzFCO0VBQ0UsYUFBYTtFQUNiLDBCQUEwQjtFQUMxQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsOEJBQThCO0VBQzlCLG1CQUFtQjtFQUNuQixlQUFlO0VBQ2YsbUNBQW1DO0VBQ25DLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLDhCQUE4QjtBQUNoQzs7QUFFQTtFQUNFLGdCQUFnQjtFQUNoQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUEsOEJBQThCO0FBQzlCO0VBQ0UsYUFBYTtFQUNiLDBCQUEwQjtFQUMxQixZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFlBQVk7RUFDWixnQkFBZ0I7RUFDaEIsbUNBQW1DO0VBQ25DLGtCQUFrQjtFQUNsQix3Q0FBd0M7RUFDeEMseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsOEJBQThCO0VBQzlCLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFLGtDQUFrQztFQUNsQyxvQ0FBb0M7QUFDdEM7O0FBRUE7RUFDRSxrQ0FBa0M7RUFDbEMsb0NBQW9DO0FBQ3RDOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixpQkFBaUI7RUFDakIsa0JBQWtCO0VBQ2xCLGVBQWU7RUFDZiwrQkFBK0I7QUFDakM7O0FBRUE7RUFDRSxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGtDQUFrQztFQUNsQyxrQkFBa0I7RUFDbEIsaUJBQWlCO0VBQ2pCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxrQ0FBa0M7RUFDbEMsY0FBYztBQUNoQjs7QUFFQTtFQUNFLE9BQU87RUFDUCxZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsV0FBVztFQUNYLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsZ0JBQWdCO0VBQ2hCLHVCQUF1QjtBQUN6Qjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsV0FBVztFQUNYLGlCQUFpQjtFQUNqQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLFlBQVk7QUFDZDs7QUFFQTs7NkNBRTZDOztBQUU3QyxnREFBZ0Q7QUFDaEQ7RUFDRTtJQUNFO3FDQUNpQztJQUNqQzs0RUFDd0U7SUFDeEUsb0NBQW9DO0VBQ3RDO0VBQ0E7SUFDRTtxQ0FDaUM7SUFDakM7NEVBQ3dFO0lBQ3hFLGtDQUFrQztFQUNwQztBQUNGOztBQUVBLCtEQUErRDtBQUMvRDtFQUNFO0lBQ0U7cUNBQ2lDO0lBQ2pDOzRFQUN3RTtJQUN4RSx1Q0FBdUM7RUFDekM7RUFDQTtJQUNFO3FDQUNpQztJQUNqQzsyQ0FDdUM7SUFDdkMsdUNBQXVDO0VBQ3pDO0FBQ0YiLCJmaWxlIjoibm90aWZpY2F0aW9uLWxpc3QuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIkBjaGFyc2V0IFwiVVRGLThcIjtcbi8qIFN0eWxlcyBmdXR1cmlzdGVzIHBvdXIgbGVzIG5vdGlmaWNhdGlvbnMgLSBIYXJtb25pc8OpcyBhdmVjIGxlIGxheW91dCBwcmluY2lwYWwgKi9cbi5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbnMtY29udGFpbmVyIHtcbiAgcGFkZGluZzogMDtcbiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDRyZW0pO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHdpZHRoOiAxMDAlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgcGFkZGluZy10b3A6IDFyZW07IC8qIFLDqWR1aXQgbCdlc3BhY2UgZW4gaGF1dCAqL1xuICBtYXJnaW4tYm90dG9tOiAwO1xuICBoZWlnaHQ6IDEwMHZoOyAvKiBIYXV0ZXVyIGZpeGUgcG91ciDDqXZpdGVyIGxlIGTDqWJvcmRlbWVudCAqL1xufVxuXG4vKiBDb250ZW5ldXIgcHJpbmNpcGFsIC0gTW9kZSBjbGFpciAqL1xuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9ucy1jb250YWluZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWRmMWY0OyAvKiBNw6ptZSBjb3VsZXVyIHF1ZSBsZSBsYXlvdXQgcHJpbmNpcGFsICovXG4gIGNvbG9yOiAjNmQ2ODcwO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi8qIENvbnRlbmV1ciBwcmluY2lwYWwgLSBNb2RlIHNvbWJyZSAqL1xuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9ucy1jb250YWluZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTIxMjEyOyAvKiBNw6ptZSBjb3VsZXVyIHF1ZSBsZSBsYXlvdXQgcHJpbmNpcGFsICovXG4gIGNvbG9yOiAjYTBhMGEwO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi8qIEdyaWxsZSBkJ2FycmnDqHJlLXBsYW4gY29tbXVuZSBwb3VyIGxlcyBkZXV4IG1vZGVzICovXG4uZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWNvbnRhaW5lciAuYmFja2dyb3VuZC1lbGVtZW50cyB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgaW5zZXQ6IDA7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xuICB6LWluZGV4OiAwO1xufVxuXG4vKiBHcmlsbGUgZCdhcnJpw6hyZS1wbGFuIC0gTW9kZSBjbGFpciAqL1xuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmJhY2tncm91bmQtZWxlbWVudHM6OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgaW5zZXQ6IDA7XG4gIG9wYWNpdHk6IDAuMDU7XG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgIzRmNWZhZCAxcHgsIHRyYW5zcGFyZW50IDFweCksXG4gICAgbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgIzRmNWZhZCAxcHgsIHRyYW5zcGFyZW50IDFweCk7XG4gIGJhY2tncm91bmQtc2l6ZTogY2FsYygxMDAlIC8gMTIpIDEwMCUsIDEwMCUgY2FsYygxMDAlIC8gMTIpO1xuICB6LWluZGV4OiAwO1xufVxuXG4vKiBHcmlsbGUgZCdhcnJpw6hyZS1wbGFuIC0gTW9kZSBzb21icmUgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspIC5iYWNrZ3JvdW5kLWVsZW1lbnRzOjpiZWZvcmUge1xuICBjb250ZW50OiBcIlwiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGluc2V0OiAwO1xuICBvcGFjaXR5OiAwLjA1OyAvKiBMw6lnw6hyZW1lbnQgcGx1cyB2aXNpYmxlICovXG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudChcbiAgICAgIHRvIHJpZ2h0LFxuICAgICAgcmdiYSgyNTUsIDE0MCwgMCwgMC4zKSAxcHgsXG4gICAgICB0cmFuc3BhcmVudCAxcHhcbiAgICApLFxuICAgIGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sIHJnYmEoMjU1LCAxNDAsIDAsIDAuMykgMXB4LCB0cmFuc3BhcmVudCAxcHgpOyAvKiBHcmlsbGUgb3JhbmdlIGNvbW1lIGRhbnMgbGEgcGFnZSBkZXMgbWVzc2FnZXMvdXRpbGlzYXRldXJzICovXG4gIGJhY2tncm91bmQtc2l6ZTogY2FsYygxMDAlIC8gMjApIDEwMCUsIDEwMCUgY2FsYygxMDAlIC8gMjApOyAvKiBHcmlsbGUgcGx1cyBmaW5lIGNvbW1lIGRhbnMgbGEgcGFnZSBkZXMgbWVzc2FnZXMvdXRpbGlzYXRldXJzICovXG4gIHotaW5kZXg6IDA7XG4gIGFuaW1hdGlvbjogZ3JpZC1wdWxzZSA0cyBlYXNlLWluLW91dCBpbmZpbml0ZTsgLyogQW5pbWF0aW9uIGRlIHB1bHNhdGlvbiBwb3VyIGxhIGdyaWxsZSAqL1xufVxuXG4vKiBMaWduZSBkZSBzY2FuIHBvdXIgbGUgbW9kZSBzb21icmUgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspIC5iYWNrZ3JvdW5kLWVsZW1lbnRzOjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGhlaWdodDogMnB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXG4gICAgdG8gcmlnaHQsXG4gICAgdHJhbnNwYXJlbnQgMCUsXG4gICAgcmdiYSgyNTUsIDE0MCwgMCwgMC41KSA1MCUsXG4gICAgdHJhbnNwYXJlbnQgMTAwJVxuICApO1xuICBib3gtc2hhZG93OiAwIDAgMTBweCByZ2JhKDI1NSwgMTQwLCAwLCAwLjUpO1xuICB6LWluZGV4OiAxO1xuICBhbmltYXRpb246IHNjYW4gOHMgbGluZWFyIGluZmluaXRlOyAvKiBBbmltYXRpb24gZGUgc2NhbiBwb3VyIGxhIGxpZ25lICovXG59XG5cbi8qIEFuaW1hdGlvbiBkZSBwdWxzYXRpb24gcG91ciBsYSBncmlsbGUgZW4gbW9kZSBzb21icmUgKi9cbkBrZXlmcmFtZXMgZ3JpZC1wdWxzZSB7XG4gIDAlIHtcbiAgICBvcGFjaXR5OiAwLjAzO1xuICB9XG4gIDUwJSB7XG4gICAgb3BhY2l0eTogMC4wNztcbiAgfVxuICAxMDAlIHtcbiAgICBvcGFjaXR5OiAwLjAzO1xuICB9XG59XG5cbi8qIEFuaW1hdGlvbiBkZSBzY2FuIHBvdXIgbGUgbW9kZSBzb21icmUgKi9cbkBrZXlmcmFtZXMgc2NhbiB7XG4gIDAlIHtcbiAgICB0b3A6IC0xMCU7XG4gICAgb3BhY2l0eTogMC41O1xuICB9XG4gIDUwJSB7XG4gICAgb3BhY2l0eTogMC44O1xuICB9XG4gIDEwMCUge1xuICAgIHRvcDogMTEwJTtcbiAgICBvcGFjaXR5OiAwLjU7XG4gIH1cbn1cblxuLyogU3R5bGVzIGNvbW11bnMgcG91ciBsYSBjYXJ0ZSBkZSBub3RpZmljYXRpb25zIC0gSGFybW9uaXPDqXMgYXZlYyBsZSBsYXlvdXQgcHJpbmNpcGFsICovXG4uZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWNhcmQge1xuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMTtcbiAgbWFyZ2luOiAwLjVyZW0gYXV0bzsgLyogUsOpZHVpdCBsYSBtYXJnZSBlbiBoYXV0IGV0IGVuIGJhcyAqL1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICB3aWR0aDogMTAwJTtcbiAgbWF4LXdpZHRoOiAxMjAwcHg7IC8qIExhcmdldXIgbWF4aW1hbGUgcG91ciBzJ2FsaWduZXIgYXZlYyBsZSBsYXlvdXQgcHJpbmNpcGFsICovXG4gIGhlaWdodDogY2FsYygxMDB2aCAtIDFyZW0pOyAvKiBIYXV0ZXVyIGFkYXB0w6llIHBvdXIgw6l2aXRlciBsZSBkw6lib3JkZW1lbnQgKi9cbn1cblxuLyogU3R5bGVzIHBvdXIgbGUgbW9kZSBjbGFpciAqL1xuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9ucy1jYXJkIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjtcbiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoNzksIDk1LCAxNzMsIDAuMSk7XG59XG5cbi8qIFN0eWxlcyBwb3VyIGxlIG1vZGUgc29tYnJlICovXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWNhcmQge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWUxZTFlOyAvKiBNw6ptZSBjb3VsZXVyIHF1ZSBsZXMgY2FydGVzIGR1IGxheW91dCBwcmluY2lwYWwgKi9cbiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMyk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMTA5LCAxMjAsIDIwMSwgMC4xKTsgLyogQ291bGV1ciBoYXJtb25pc8OpZSBhdmVjIGxlIGxheW91dCBwcmluY2lwYWwgKi9cbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xufVxuXG4vKiBTdHlsZXMgY29tbXVucyBwb3VyIGwnZW4tdMOqdGUgKi9cbi5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbnMtaGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiAxcmVtO1xuICBwb3NpdGlvbjogc3RpY2t5O1xuICB0b3A6IDA7XG4gIHotaW5kZXg6IDEwO1xuICBtYXJnaW4tYm90dG9tOiAxLjVyZW07IC8qIEVzcGFjZSBwb3VyIGxhIGNhc2Ugw6AgY29jaGVyIFwiU8OpbGVjdGlvbm5lciB0b3V0XCIgKi9cbn1cblxuLyogRW4tdMOqdGUgLSBNb2RlIGNsYWlyICovXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWhlYWRlciB7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDc5LCA5NSwgMTczLCAwLjEpO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI0MCwgMjQ0LCAyNDgsIDAuNSk7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4vKiBFbi10w6p0ZSAtIE1vZGUgc29tYnJlICovXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWhlYWRlciB7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDAsIDI0NywgMjU1LCAwLjEpO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMik7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjMpO1xufVxuXG4vKiBUaXRyZSAtIE1vZGUgY2xhaXIgKi9cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5mdXR1cmlzdGljLXRpdGxlIHtcbiAgZm9udC1zaXplOiAxLjI1cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzRmNWZhZDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbn1cblxuLyogVGl0cmUgLSBNb2RlIHNvbWJyZSAqL1xuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtdGl0bGUge1xuICBmb250LXNpemU6IDEuMjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiB2YXIoLS1hY2NlbnQtY29sb3IpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4vKiBBbGlnbmVtZW50IGRlIGwnaWPDtG5lIGRlIG5vdGlmaWNhdGlvbiBhdmVjIGxlcyBjYXNlcyDDoCBjb2NoZXIgKi9cbi5mdXR1cmlzdGljLXRpdGxlIGkge1xuICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcbiAgLyogQWxpZ25lbWVudCB2ZXJ0aWNhbCBwYXJmYWl0IGF2ZWMgbGVzIGNhc2VzIMOgIGNvY2hlciBkZXMgbm90aWZpY2F0aW9ucyAqL1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHRvcDogMHB4OyAvKiBBanVzdGVtZW50IGZpbiBwb3VyIGwnYWxpZ25lbWVudCBwYXJmYWl0ICovXG4gIC8qIE3Dqm1lIHRhaWxsZSBxdWUgbGVzIGNhc2VzIMOgIGNvY2hlciBpbmRpdmlkdWVsbGVzICgyMnB4KSBwb3VyIHVuIGFsaWduZW1lbnQgdmlzdWVsIGNvaMOpcmVudCAqL1xuICB3aWR0aDogMjJweDtcbiAgaGVpZ2h0OiAyMnB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgLyogTcOqbWUgc3R5bGUgcXVlIGxlcyBjYXNlcyDDoCBjb2NoZXIgcG91ciBsYSBjb2jDqXJlbmNlIHZpc3VlbGxlICovXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcbiAgYm94LXNoYWRvdzogMCAwIDhweCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuXG4vKiBNb2RlIGNsYWlyIHBvdXIgbCdpY8O0bmUgKi9cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5mdXR1cmlzdGljLXRpdGxlIGkge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDc5LCA5NSwgMTczLCAwLjEpO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDc5LCA5NSwgMTczLCAwLjMpO1xuICBib3gtc2hhZG93OiAwIDAgOHB4IHJnYmEoNzksIDk1LCAxNzMsIDAuMyk7XG4gIGNvbG9yOiAjNGY1ZmFkO1xufVxuXG4vKiBFZmZldCBkZSBzdXJ2b2wgcG91ciBsJ2ljw7RuZSAqL1xuLmZ1dHVyaXN0aWMtdGl0bGU6aG92ZXIgaSB7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XG4gIGJveC1zaGFkb3c6IDAgMCAxMnB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuNSk7XG59XG5cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5mdXR1cmlzdGljLXRpdGxlOmhvdmVyIGkge1xuICBib3gtc2hhZG93OiAwIDAgMTJweCByZ2JhKDc5LCA5NSwgMTczLCAwLjUpO1xufVxuXG4vKiDDiXRhdCBkZSBjaGFyZ2VtZW50IGZ1dHVyaXN0ZSAqL1xuLmZ1dHVyaXN0aWMtbG9hZGluZy1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgcGFkZGluZzogNHJlbSAycmVtO1xufVxuXG4uZnV0dXJpc3RpYy1sb2FkaW5nLWNpcmNsZSB7XG4gIHdpZHRoOiA2MHB4O1xuICBoZWlnaHQ6IDYwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgYm9yZGVyOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XG4gIGJvcmRlci10b3AtY29sb3I6IHZhcigtLWFjY2VudC1jb2xvcik7XG4gIGJvcmRlci1ib3R0b20tY29sb3I6IHZhcigtLXNlY29uZGFyeS1jb2xvcik7XG4gIGFuaW1hdGlvbjogZnV0dXJpc3RpYy1zcGluIDEuMnMgbGluZWFyIGluZmluaXRlO1xuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xufVxuXG5Aa2V5ZnJhbWVzIGZ1dHVyaXN0aWMtc3BpbiB7XG4gIDAlIHtcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTtcbiAgfVxuICAxMDAlIHtcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpO1xuICB9XG59XG4uZnV0dXJpc3RpYy1sb2FkaW5nLXRleHQge1xuICBtYXJnaW4tdG9wOiAxcmVtO1xuICBjb2xvcjogdmFyKC0tdGV4dC1kaW0pO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi8qIMOJdGF0IGQnZXJyZXVyIGZ1dHVyaXN0ZSAqL1xuLmZ1dHVyaXN0aWMtZXJyb3ItbWVzc2FnZSB7XG4gIG1hcmdpbjogMXJlbTtcbiAgcGFkZGluZzogMXJlbTtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDAsIDc2LCAwLjEpO1xuICBib3JkZXItbGVmdDogNHB4IHNvbGlkIHZhcigtLWVycm9yLWNvbG9yKTtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cy1tZCk7XG59XG5cbi5mdXR1cmlzdGljLWVycm9yLWljb24ge1xuICBjb2xvcjogdmFyKC0tZXJyb3ItY29sb3IpO1xuICBmb250LXNpemU6IDEuMjVyZW07XG4gIG1hcmdpbi1yaWdodDogMC43NXJlbTtcbn1cblxuLmZ1dHVyaXN0aWMtZXJyb3ItdGl0bGUge1xuICBjb2xvcjogdmFyKC0tZXJyb3ItY29sb3IpO1xuICBmb250LXdlaWdodDogNjAwO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xufVxuXG4uZnV0dXJpc3RpYy1lcnJvci10ZXh0IHtcbiAgY29sb3I6IHZhcigtLXRleHQtZGltKTtcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xuICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xufVxuXG4uZnV0dXJpc3RpYy1yZXRyeS1idXR0b24ge1xuICBwYWRkaW5nOiAwLjI1cmVtIDAuNzVyZW07XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAwLCA3NiwgMC4xKTtcbiAgY29sb3I6IHZhcigtLWVycm9yLWNvbG9yKTtcbiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tZXJyb3ItY29sb3IpO1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzLXNtKTtcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCB2YXIoLS10cmFuc2l0aW9uLWZhc3QpO1xufVxuXG4uZnV0dXJpc3RpYy1yZXRyeS1idXR0b246aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMCwgNzYsIDAuMik7XG4gIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoMjU1LCAwLCA3NiwgMC4zKTtcbn1cblxuLyogw4l0YXQgdmlkZSBmdXR1cmlzdGUgKi9cbi5mdXR1cmlzdGljLWVtcHR5LXN0YXRlIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHBhZGRpbmc6IDRyZW0gMnJlbTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4uZnV0dXJpc3RpYy1lbXB0eS1pY29uIHtcbiAgZm9udC1zaXplOiAzcmVtO1xuICBjb2xvcjogdmFyKC0tYWNjZW50LWNvbG9yKTtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgb3BhY2l0eTogMC41O1xufVxuXG4uZnV0dXJpc3RpYy1lbXB0eS10aXRsZSB7XG4gIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6IHZhcigtLXRleHQtbGlnaHQpO1xuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG59XG5cbi5mdXR1cmlzdGljLWVtcHR5LXRleHQge1xuICBjb2xvcjogdmFyKC0tdGV4dC1kaW0pO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xufVxuXG4uZnV0dXJpc3RpYy1jaGVjay1idXR0b24ge1xuICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbiAgY29sb3I6IHZhcigtLWFjY2VudC1jb2xvcik7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjQ3LCAyNTUsIDAuMyk7XG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMtbWQpO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCB2YXIoLS10cmFuc2l0aW9uLWZhc3QpO1xufVxuXG4uZnV0dXJpc3RpYy1jaGVjay1idXR0b246aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDI0NywgMjU1LCAwLjIpO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gIGJveC1zaGFkb3c6IHZhcigtLWdsb3ctZWZmZWN0KTtcbn1cblxuLyogTGlzdGUgZGVzIG5vdGlmaWNhdGlvbnMgZnV0dXJpc3RlIC0gU3R5bGVzIGNvbW11bnMgKi9cbi5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbnMtbGlzdCB7XG4gIHBhZGRpbmc6IDA7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGZsZXg6IDE7XG4gIG92ZXJmbG93LXk6IGF1dG87XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgc2Nyb2xsYmFyLXdpZHRoOiB0aGluO1xuICB6LWluZGV4OiAxO1xuICB3aWR0aDogMTAwJTtcbn1cblxuLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9ucy1saXN0Ojotd2Via2l0LXNjcm9sbGJhciB7XG4gIHdpZHRoOiA0cHg7XG59XG5cbi5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbnMtbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbn1cblxuLyogTGlzdGUgZGVzIG5vdGlmaWNhdGlvbnMgLSBNb2RlIGNsYWlyICovXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWxpc3Qge1xuICBzY3JvbGxiYXItY29sb3I6ICM0ZjVmYWQgdHJhbnNwYXJlbnQ7XG4gIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xufVxuXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKVxuICAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb25zLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzRmNWZhZDtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbn1cblxuLyogTGlzdGUgZGVzIG5vdGlmaWNhdGlvbnMgLSBNb2RlIHNvbWJyZSAqL1xuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9ucy1saXN0IHtcbiAgc2Nyb2xsYmFyLWNvbG9yOiB2YXIoLS1hY2NlbnQtY29sb3IpIHRyYW5zcGFyZW50O1xuICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9ucy1saXN0Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWFjY2VudC1jb2xvcik7XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG59XG5cbi8qIENhcnRlIGRlIG5vdGlmaWNhdGlvbiBzaW1wbGlmacOpZSAtIFN0eWxlcyBjb21tdW5zICovXG4uZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tY2FyZCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDMwcHggMjBweCAxNnB4IDIwcHg7IC8qIFBhZGRpbmcgZW4gaGF1dCBhdWdtZW50w6kgcG91ciBsYSBjYXNlIMOgIGNvY2hlciAqL1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG4gIG1hcmdpbjogMC41cmVtIDFyZW07IC8qIEF1Z21lbnRhdGlvbiBkZXMgbWFyZ2VzIHBvdXIgcGx1cyBkJ2VzcGFjZSBlbnRyZSBsZXMgY2FydGVzICovXG4gIGJvcmRlci1yYWRpdXM6IDhweDsgLyogQ29pbnMgcGx1cyBhcnJvbmRpcyAqL1xuICBmbGV4LXdyYXA6IG5vd3JhcDsgLyogRW1ww6pjaGUgbGUgcmV0b3VyIMOgIGxhIGxpZ25lIGRlcyDDqWzDqW1lbnRzICovXG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsgLyogRXNwYWNlIGxlcyDDqWzDqW1lbnRzIHVuaWZvcm3DqW1lbnQgKi9cbn1cblxuLyogQ2FydGUgZGUgbm90aWZpY2F0aW9uIC0gTW9kZSBjbGFpciAqL1xuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLWNhcmQge1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSg3OSwgOTUsIDE3MywgMC4xKTtcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XG4gIGJveC1zaGFkb3c6IDAgMXB4IDNweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAxNXB4OyAvKiBDb2lucyBhcnJvbmRpcyBjb21tZSBkYW5zIGxlcyBtZXNzYWdlcyBkZSBjaGF0ICovXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG5cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1jYXJkOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg3OSwgOTUsIDE3MywgMC4wNSk7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgYm94LXNoYWRvdzogMCAzcHggNnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLyogU3R5bGUgcG91ciBsZXMgbm90aWZpY2F0aW9ucyBsdWVzIGVuIG1vZGUgY2xhaXIgKi9cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpXG4gIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1jYXJkLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLXJlYWQge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXG4gICAgMTM1ZGVnLFxuICAgIHJnYmEoNzksIDk1LCAxNzMsIDAuMSksXG4gICAgcmdiYSg2MSwgNzQsIDEzMywgMC4yKVxuICApOyAvKiBEw6lncmFkw6kgYmxldSBjb21tZSBkYW5zIGxlcyBtZXNzYWdlcyBkZSBjaGF0ICovXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoNzksIDk1LCAxNzMsIDAuMyk7IC8qIEJvcmR1cmUgYmxldWUgY29tbWUgZGFucyBsZXMgbWVzc2FnZXMgZGUgY2hhdCAqL1xuICBib3gtc2hhZG93OiAwIDJweCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsgLyogT21icmUgY29tbWUgZGFucyBsZXMgbWVzc2FnZXMgZGUgY2hhdCAqL1xuICBib3JkZXItYm90dG9tLXJpZ2h0LXJhZGl1czogMDsgLyogQ29pbiBpbmbDqXJpZXVyIGRyb2l0IGNhcnLDqSBjb21tZSBkYW5zIGxlcyBidWxsZXMgZGUgbWVzc2FnZSAqL1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi8qIEVmZmV0IGRlIGJvcmR1cmUgYnJpbGxhbnRlIHBvdXIgbGVzIG5vdGlmaWNhdGlvbnMgbHVlcyBlbiBtb2RlIGNsYWlyICovXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKVxuICAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tY2FyZC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1yZWFkOjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBib3JkZXItcmFkaXVzOiBpbmhlcml0O1xuICBwb2ludGVyLWV2ZW50czogbm9uZTtcbiAgei1pbmRleDogLTE7XG4gIGJveC1zaGFkb3c6IGluc2V0IDAgMCAwIDFweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7IC8qIEJvcmR1cmUgaW50w6lyaWV1cmUgYnJpbGxhbnRlICovXG59XG5cbi8qIEVmZmV0IGRlIHN1cnZvbCBwb3VyIGxlcyBub3RpZmljYXRpb25zIGx1ZXMgZW4gbW9kZSBjbGFpciAqL1xuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSlcbiAgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLWNhcmQuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tcmVhZDpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KFxuICAgIDEzNWRlZyxcbiAgICByZ2JhKDc5LCA5NSwgMTczLCAwLjE1KSxcbiAgICByZ2JhKDYxLCA3NCwgMTMzLCAwLjI1KVxuICApOyAvKiBEw6lncmFkw6kgcGx1cyBpbnRlbnNlIGF1IHN1cnZvbCAqL1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoNzksIDk1LCAxNzMsIDAuMik7IC8qIE9tYnJlIGJsZXVlIGF1IHN1cnZvbCAqL1xufVxuXG4vKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgTk9USUZJQ0FUSU9OUyBFTiBNT0RFIFNPTUJSRSAtIE5PTiBTw4lMRUNUSU9OTsOJRVNcbiAgID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tY2FyZCB7XG4gIGJvcmRlci1ib3R0b206IG5vbmU7XG4gIGJhY2tncm91bmQtY29sb3I6IHZhcihcbiAgICAtLWRhcmstbWVkaXVtLWJnLFxuICAgICMyNTI3NDBcbiAgKTsgLyogRm9uZCBzb21icmUgY29tbWUgZGFucyBsYSBwYWdlIGRlIGNoYXQgKi9cbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOyAvKiBCb3JkdXJlIHN1YnRpbGUgY29tbWUgZGFucyBsZXMgbWVzc2FnZXMgZGUgY2hhdCAqL1xuICBib3JkZXItcmFkaXVzOiAxNXB4OyAvKiBDb2lucyBhcnJvbmRpcyBjb21tZSBkYW5zIGxlcyBtZXNzYWdlcyBkZSBjaGF0ICovXG4gIGJveC1zaGFkb3c6IDAgMnB4IDEwcHggcmdiYSgwLCAwLCAwLCAwLjIpOyAvKiBPbWJyZSBjb21tZSBkYW5zIGxlcyBtZXNzYWdlcyBkZSBjaGF0ICovXG4gIG1hcmdpbi1ib3R0b206IDE1cHg7IC8qIFBsdXMgZCdlc3BhY2VtZW50IGVudHJlIGxlcyBjYXJ0ZXMgKi9cbiAgbWFyZ2luLWxlZnQ6IDE1cHg7IC8qIE1hcmdlIMOgIGdhdWNoZSAqL1xuICBtYXJnaW4tcmlnaHQ6IDE1cHg7IC8qIE1hcmdlIMOgIGRyb2l0ZSAqL1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOyAvKiBUcmFuc2l0aW9uIGZsdWlkZSBwb3VyIHRvdXMgbGVzIGVmZmV0cyAqL1xuICBjb2xvcjogdmFyKFxuICAgIC0tdGV4dC1saWdodCxcbiAgICAjZmZmZmZmXG4gICk7IC8qIFRleHRlIGJsYW5jIGNvbW1lIGRhbnMgbGVzIG1lc3NhZ2VzIGRlIGNoYXQgKi9cbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoXG4gICAgLTJweFxuICApOyAvKiBFZmZldCBkZSBmbG90dGVtZW50IGNvbW1lIGRhbnMgbGVzIG1lc3NhZ2VzIGRlIGNoYXQgKi9cbiAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMyk7IC8qIE9tYnJlIHBsdXMgcHJvbm9uY8OpZSBhdSBzdXJ2b2wgKi9cbn1cblxuLyogU3R5bGUgcG91ciBsZXMgbm90aWZpY2F0aW9ucyBsdWVzIChjb21tZSBkYW5zIGxhIHBhZ2UgZGUgY2hhdCkgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspXG4gIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1jYXJkLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLXJlYWQge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgIzAwZjdmZjIwLFxuICAgICMwMGMzZmYzMFxuICApOyAvKiBEw6lncmFkw6kgYmxldS9jeWFuIGNvbW1lIGRhbnMgbGVzIG1lc3NhZ2VzIGRlIGNoYXQgKi9cbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTsgLyogQm9yZHVyZSBjeWFuIGNvbW1lIGRhbnMgbGVzIG1lc3NhZ2VzIGRlIGNoYXQgKi9cbiAgYm94LXNoYWRvdzogMCAycHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMik7IC8qIE9tYnJlIGNvbW1lIGRhbnMgbGVzIG1lc3NhZ2VzIGRlIGNoYXQgKi9cbiAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDA7IC8qIENvaW4gaW5mw6lyaWV1ciBkcm9pdCBjYXJyw6kgY29tbWUgZGFucyBsZXMgYnVsbGVzIGRlIG1lc3NhZ2UgKi9cbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cblxuLyogRWZmZXQgZGUgYm9yZHVyZSBicmlsbGFudGUgcG91ciBsZXMgbm90aWZpY2F0aW9ucyBsdWVzICovXG46aG9zdC1jb250ZXh0KC5kYXJrKVxuICAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tY2FyZC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1yZWFkOjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBib3JkZXItcmFkaXVzOiBpbmhlcml0O1xuICBwb2ludGVyLWV2ZW50czogbm9uZTtcbiAgei1pbmRleDogLTE7XG4gIGJveC1zaGFkb3c6IGluc2V0IDAgMCAwIDFweCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpOyAvKiBCb3JkdXJlIGludMOpcmlldXJlIGJyaWxsYW50ZSAqL1xufVxuXG4vKiBFZmZldCBkZSBzdXJ2b2wgcG91ciBsZXMgbm90aWZpY2F0aW9ucyBsdWVzICovXG46aG9zdC1jb250ZXh0KC5kYXJrKVxuICAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tY2FyZC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1yZWFkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuMik7IC8qIE9tYnJlIGN5YW4gYXUgc3Vydm9sICovXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgIzAwZjdmZjMwLFxuICAgICMwMGMzZmY0MFxuICApOyAvKiBEw6lncmFkw6kgcGx1cyBpbnRlbnNlIGF1IHN1cnZvbCAqL1xufVxuXG4vKiBOb3RpZmljYXRpb24gbm9uIGx1ZSAtIFN0eWxlcyBjb21tdW5zICovXG4uZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tdW5yZWFkIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuXG4vKiBBbmltYXRpb25zIHBvdXIgbGUgbW9kYWwgKi9cbkBrZXlmcmFtZXMgZmFkZUluIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgfVxuICB0byB7XG4gICAgb3BhY2l0eTogMTtcbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIHNjYWxlSW4ge1xuICBmcm9tIHtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDAuOSk7XG4gICAgb3BhY2l0eTogMDtcbiAgfVxuICB0byB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcbiAgICBvcGFjaXR5OiAxO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgYm9yZGVyRmxvdyB7XG4gIDAlIHtcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSAwJTtcbiAgfVxuICAxMDAlIHtcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAyMDAlIDAlO1xuICB9XG59XG5cbi8qIFN0eWxlcyBwb3VyIGxlIG1vZGFsIGRlcyBwacOoY2VzIGpvaW50ZXMgKi9cbi5mdXR1cmlzdGljLW1vZGFsLW92ZXJsYXkge1xuICBwb3NpdGlvbjogZml4ZWQ7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGJvdHRvbTogMDtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjcpO1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNXB4KTtcbiAgZGlzcGxheTogbm9uZTsgLyogQ2FjaMOpIHBhciBkw6lmYXV0ICovXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICB6LWluZGV4OiAxMDAwO1xuICBhbmltYXRpb246IGZhZGVJbiAwLjNzIGVhc2U7XG4gIG9wYWNpdHk6IDA7IC8qIEludmlzaWJsZSBwYXIgZMOpZmF1dCAqL1xuICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgZWFzZTtcbn1cblxuLyogUXVhbmQgbGUgbW9kYWwgZXN0IGFmZmljaMOpICovXG4uZnV0dXJpc3RpYy1tb2RhbC1vdmVybGF5W3N0eWxlKj1cImRpc3BsYXk6IGZsZXhcIl0ge1xuICBvcGFjaXR5OiAxO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1tb2RhbC1vdmVybGF5IHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjgpO1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcbiAgYW5pbWF0aW9uOiBtb2RhbEJhY2tkcm9wRmFkZUluIDAuM3MgZWFzZS1vdXQ7XG59XG5cbkBrZXlmcmFtZXMgbW9kYWxCYWNrZHJvcEZhZGVJbiB7XG4gIGZyb20ge1xuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMCk7XG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDBweCk7XG4gIH1cbiAgdG8ge1xuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC44KTtcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcbiAgfVxufVxuXG4vKiBDb250ZW5ldXIgbW9kYWwgLSBNb2RlIGNsYWlyICovXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAuZnV0dXJpc3RpYy1tb2RhbC1jb250YWluZXIge1xuICB3aWR0aDogOTAlO1xuICBtYXgtd2lkdGg6IDYwMHB4O1xuICBtYXgtaGVpZ2h0OiA4MHZoO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3gtc2hhZG93OiAwIDEwcHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMik7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFuaW1hdGlvbjogc2NhbGVJbiAwLjNzIGVhc2U7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoNzksIDk1LCAxNzMsIDAuMik7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtbW9kYWwtY29udGFpbmVyOjpiZWZvcmUge1xuICBjb250ZW50OiBcIlwiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGhlaWdodDogMXB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXG4gICAgOTBkZWcsXG4gICAgdHJhbnNwYXJlbnQgMCUsXG4gICAgcmdiYSg3OSwgOTUsIDE3MywgMC4yKSAyMCUsXG4gICAgcmdiYSg3OSwgOTUsIDE3MywgMC44KSA1MCUsXG4gICAgcmdiYSg3OSwgOTUsIDE3MywgMC4yKSA4MCUsXG4gICAgdHJhbnNwYXJlbnQgMTAwJVxuICApO1xuICBiYWNrZ3JvdW5kLXNpemU6IDIwMCUgMTAwJTtcbiAgYW5pbWF0aW9uOiBib3JkZXJGbG93IDNzIGluZmluaXRlIGxpbmVhcjtcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSg3OSwgOTUsIDE3MywgMC40KTtcbiAgei1pbmRleDogMTtcbn1cblxuLyogQ29udGVuZXVyIG1vZGFsIC0gTW9kZSBzb21icmUgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspIC5mdXR1cmlzdGljLW1vZGFsLWNvbnRhaW5lciB7XG4gIHdpZHRoOiA5MCU7XG4gIG1heC13aWR0aDogNjAwcHg7XG4gIG1heC1oZWlnaHQ6IDgwdmg7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTgsIDE4LCAxOCwgMC45NSk7XG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGJveC1zaGFkb3c6IDAgMCA0MHB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuNCksXG4gICAgaW5zZXQgMCAwIDIwcHggcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYW5pbWF0aW9uOiBtb2RhbEZhZGVJbiAwLjRzIGN1YmljLWJlemllcigwLjE5LCAxLCAwLjIyLCAxKTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG5Aa2V5ZnJhbWVzIG1vZGFsRmFkZUluIHtcbiAgMCUge1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgwLjkpIHRyYW5zbGF0ZVkoMjBweCk7XG4gIH1cbiAgMTAwJSB7XG4gICAgb3BhY2l0eTogMTtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEpIHRyYW5zbGF0ZVkoMCk7XG4gIH1cbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtbW9kYWwtY29udGFpbmVyOjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgaGVpZ2h0OiAxcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICA5MGRlZyxcbiAgICB0cmFuc3BhcmVudCAwJSxcbiAgICByZ2JhKDAsIDI0NywgMjU1LCAwLjIpIDIwJSxcbiAgICByZ2JhKDAsIDI0NywgMjU1LCAwLjgpIDUwJSxcbiAgICByZ2JhKDAsIDI0NywgMjU1LCAwLjIpIDgwJSxcbiAgICB0cmFuc3BhcmVudCAxMDAlXG4gICk7XG4gIGJhY2tncm91bmQtc2l6ZTogMjAwJSAxMDAlO1xuICBhbmltYXRpb246IGJvcmRlckZsb3cgM3MgaW5maW5pdGUgbGluZWFyO1xuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDI0NywgMjU1LCAwLjcpO1xuICB6LWluZGV4OiAxO1xufVxuXG4vKiBFbi10w6p0ZSBkdSBtb2RhbCAtIE1vZGUgY2xhaXIgKi9cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5mdXR1cmlzdGljLW1vZGFsLWhlYWRlciB7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoNzksIDk1LCAxNzMsIDAuMSk7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNzksIDk1LCAxNzMsIDAuMDUpO1xufVxuXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAuZnV0dXJpc3RpYy1tb2RhbC10aXRsZSB7XG4gIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6ICM0ZjVmYWQ7XG4gIG1hcmdpbjogMDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbn1cblxuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtbW9kYWwtY2xvc2Uge1xuICB3aWR0aDogMzJweDtcbiAgaGVpZ2h0OiAzMnB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNzksIDk1LCAxNzMsIDAuMSk7XG4gIGNvbG9yOiAjNGY1ZmFkO1xuICBib3JkZXI6IG5vbmU7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG59XG5cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5mdXR1cmlzdGljLW1vZGFsLWNsb3NlOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg3OSwgOTUsIDE3MywgMC4yKTtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xuICBib3gtc2hhZG93OiAwIDAgMTBweCByZ2JhKDc5LCA5NSwgMTczLCAwLjMpO1xufVxuXG4vKiBFbi10w6p0ZSBkdSBtb2RhbCAtIE1vZGUgc29tYnJlICovXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1tb2RhbC1oZWFkZXIge1xuICBwYWRkaW5nOiAxNnB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDAsIDI0NywgMjU1LCAwLjEpO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMik7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5mdXR1cmlzdGljLW1vZGFsLXRpdGxlIHtcbiAgZm9udC1zaXplOiAxLjI1cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzAwZjdmZjtcbiAgbWFyZ2luOiAwO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1tb2RhbC1jbG9zZSB7XG4gIHdpZHRoOiAzMnB4O1xuICBoZWlnaHQ6IDMycHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbiAgY29sb3I6ICMwMGY3ZmY7XG4gIGJvcmRlcjogbm9uZTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtbW9kYWwtY2xvc2U6aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDI0NywgMjU1LCAwLjIpO1xuICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XG4gIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuNSk7XG59XG5cbi8qIENvcnBzIGR1IG1vZGFsICovXG4uZnV0dXJpc3RpYy1tb2RhbC1ib2R5IHtcbiAgcGFkZGluZzogMTZweDtcbiAgb3ZlcmZsb3cteTogYXV0bztcbiAgbWF4LWhlaWdodDogY2FsYyg4MHZoIC0gNzBweCk7XG59XG5cbi8qIExpc3RlIGRlcyBwacOoY2VzIGpvaW50ZXMgKi9cbi5mdXR1cmlzdGljLWF0dGFjaG1lbnRzLWxpc3Qge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDEycHg7XG59XG5cbi8qIMOJbMOpbWVudCBkZSBwacOoY2Ugam9pbnRlIC0gTW9kZSBjbGFpciAqL1xuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtYXR0YWNobWVudC1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMTJweDtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDc5LCA5NSwgMTczLCAwLjA1KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSg3OSwgOTUsIDE3MywgMC4xKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbn1cblxuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtYXR0YWNobWVudC1pdGVtOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg3OSwgOTUsIDE3MywgMC4xKTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDVweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtYXR0YWNobWVudC1wcmV2aWV3IHtcbiAgd2lkdGg6IDYwcHg7XG4gIGhlaWdodDogNjBweDtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBtYXJnaW4tcmlnaHQ6IDEycHg7XG4gIGZsZXgtc2hyaW5rOiAwO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDc5LCA5NSwgMTczLCAwLjIpO1xufVxuXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAuZnV0dXJpc3RpYy1hdHRhY2htZW50LWljb24ge1xuICB3aWR0aDogNjBweDtcbiAgaGVpZ2h0OiA2MHB4O1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNzksIDk1LCAxNzMsIDAuMSk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBtYXJnaW4tcmlnaHQ6IDEycHg7XG4gIGZsZXgtc2hyaW5rOiAwO1xufVxuXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAuZnV0dXJpc3RpYy1hdHRhY2htZW50LWljb24gaSB7XG4gIGZvbnQtc2l6ZTogMjRweDtcbiAgY29sb3I6ICM0ZjVmYWQ7XG59XG5cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5mdXR1cmlzdGljLWF0dGFjaG1lbnQtYnV0dG9uIHtcbiAgd2lkdGg6IDMycHg7XG4gIGhlaWdodDogMzJweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDc5LCA5NSwgMTczLCAwLjEpO1xuICBjb2xvcjogIzRmNWZhZDtcbiAgYm9yZGVyOiBub25lO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xufVxuXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAuZnV0dXJpc3RpYy1hdHRhY2htZW50LWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNzksIDk1LCAxNzMsIDAuMik7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSg3OSwgOTUsIDE3MywgMC4zKTtcbn1cblxuLyogw4lsw6ltZW50IGRlIHBpw6hjZSBqb2ludGUgLSBNb2RlIHNvbWJyZSAqL1xuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtYXR0YWNobWVudC1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMTJweDtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDI0NywgMjU1LCAwLjA1KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtYXR0YWNobWVudC1pdGVtOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDVweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4yKTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtYXR0YWNobWVudC1wcmV2aWV3IHtcbiAgd2lkdGg6IDYwcHg7XG4gIGhlaWdodDogNjBweDtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBtYXJnaW4tcmlnaHQ6IDEycHg7XG4gIGZsZXgtc2hyaW5rOiAwO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDI0NywgMjU1LCAwLjIpO1xuICBib3gtc2hhZG93OiAwIDAgMTBweCByZ2JhKDAsIDI0NywgMjU1LCAwLjIpO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1hdHRhY2htZW50LWljb24ge1xuICB3aWR0aDogNjBweDtcbiAgaGVpZ2h0OiA2MHB4O1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMjQ3LCAyNTUsIDAuMSk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBtYXJnaW4tcmlnaHQ6IDEycHg7XG4gIGZsZXgtc2hyaW5rOiAwO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1hdHRhY2htZW50LWljb24gaSB7XG4gIGZvbnQtc2l6ZTogMjRweDtcbiAgY29sb3I6ICMwMGY3ZmY7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5mdXR1cmlzdGljLWF0dGFjaG1lbnQtYnV0dG9uIHtcbiAgd2lkdGg6IDMycHg7XG4gIGhlaWdodDogMzJweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDI0NywgMjU1LCAwLjEpO1xuICBjb2xvcjogIzAwZjdmZjtcbiAgYm9yZGVyOiBub25lO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1hdHRhY2htZW50LWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMjQ3LCAyNTUsIDAuMik7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgwLCAyNDcsIDI1NSwgMC41KTtcbn1cblxuLyogU3R5bGVzIGNvbW11bnMgcG91ciBsZXMgcGnDqGNlcyBqb2ludGVzICovXG4uZnV0dXJpc3RpYy1hdHRhY2htZW50LXByZXZpZXcgaW1nIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgb2JqZWN0LWZpdDogY292ZXI7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcbn1cblxuLmZ1dHVyaXN0aWMtYXR0YWNobWVudC1wcmV2aWV3IGltZzpob3ZlciB7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbn1cblxuLmZ1dHVyaXN0aWMtYXR0YWNobWVudC1pbmZvIHtcbiAgZmxleDogMTtcbiAgbWluLXdpZHRoOiAwO1xufVxuXG4uZnV0dXJpc3RpYy1hdHRhY2htZW50LW5hbWUge1xuICBmb250LXdlaWdodDogNTAwO1xuICBtYXJnaW4tYm90dG9tOiA0cHg7XG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xufVxuXG4uZnV0dXJpc3RpYy1hdHRhY2htZW50LW1ldGEge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGNvbG9yOiB2YXIoLS10ZXh0LWRpbSk7XG59XG5cbi5mdXR1cmlzdGljLWF0dGFjaG1lbnQtYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogOHB4O1xuICBtYXJnaW4tbGVmdDogMTJweDtcbn1cblxuLyogU3R5bGVzIHBvdXIgbCfDqXRhdCB2aWRlIGV0IGxlIGNoYXJnZW1lbnQgKi9cbi5mdXR1cmlzdGljLWxvYWRpbmctY29udGFpbmVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHBhZGRpbmc6IDQwcHggMDtcbn1cblxuLmZ1dHVyaXN0aWMtbG9hZGluZy1jaXJjbGUge1xuICB3aWR0aDogNDBweDtcbiAgaGVpZ2h0OiA0MHB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGJvcmRlcjogM3B4IHNvbGlkIHRyYW5zcGFyZW50O1xuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuICBhbmltYXRpb246IHNwaW4gMS4ycyBsaW5lYXIgaW5maW5pdGU7XG59XG5cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5mdXR1cmlzdGljLWxvYWRpbmctY2lyY2xlIHtcbiAgYm9yZGVyLXRvcC1jb2xvcjogIzRmNWZhZDtcbiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSg3OSwgOTUsIDE3MywgMC4zKTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtbG9hZGluZy1jaXJjbGUge1xuICBib3JkZXItdG9wLWNvbG9yOiAjMDBmN2ZmO1xuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDI0NywgMjU1LCAwLjUpO1xufVxuXG4uZnV0dXJpc3RpYy1sb2FkaW5nLXRleHQge1xuICBmb250LXNpemU6IDAuOXJlbTtcbiAgY29sb3I6IHZhcigtLXRleHQtZGltKTtcbn1cblxuLmZ1dHVyaXN0aWMtZW1wdHktc3RhdGUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgcGFkZGluZzogNDBweCAwO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5mdXR1cmlzdGljLWVtcHR5LWljb24ge1xuICBmb250LXNpemU6IDQ4cHg7XG4gIG1hcmdpbi1ib3R0b206IDE2cHg7XG4gIG9wYWNpdHk6IDAuNTtcbn1cblxuOmhvc3QtY29udGV4dCg6bm90KC5kYXJrKSkgLmZ1dHVyaXN0aWMtZW1wdHktaWNvbiB7XG4gIGNvbG9yOiAjNGY1ZmFkO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1lbXB0eS1pY29uIHtcbiAgY29sb3I6ICMwMGY3ZmY7XG59XG5cbi5mdXR1cmlzdGljLWVtcHR5LXRpdGxlIHtcbiAgZm9udC1zaXplOiAxLjJyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIG1hcmdpbi1ib3R0b206IDhweDtcbn1cblxuLmZ1dHVyaXN0aWMtZW1wdHktdGV4dCB7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICBjb2xvcjogdmFyKC0tdGV4dC1kaW0pO1xuICBtYXgtd2lkdGg6IDMwMHB4O1xufVxuXG5Aa2V5ZnJhbWVzIHNwaW4ge1xuICAwJSB7XG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7XG4gIH1cbiAgMTAwJSB7XG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcbiAgfVxufVxuXG4vKiBBdmF0YXIgc2ltcGxpZmnDqSAqL1xuLm5vdGlmaWNhdGlvbi1hdmF0YXIge1xuICB3aWR0aDogNDBweDtcbiAgaGVpZ2h0OiA0MHB4O1xuICBmbGV4LXNocmluazogMDtcbiAgbWFyZ2luLXJpZ2h0OiAxMnB4O1xuICBtYXJnaW4tbGVmdDogMTBweDsgLyogQWpvdXQgZCd1bmUgbWFyZ2Ugw6AgZ2F1Y2hlIHBvdXIgcydhbGlnbmVyIGF2ZWMgbGEgY2FzZSDDoCBjb2NoZXIgZW4gaGF1dCAqL1xufVxuXG4ubm90aWZpY2F0aW9uLWF2YXRhciBpbWcge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xufVxuXG4vKiBDb250ZW51IGRlIGxhIG5vdGlmaWNhdGlvbiAqL1xuLm5vdGlmaWNhdGlvbi1jb250ZW50IHtcbiAgZmxleDogMTtcbiAgbWluLXdpZHRoOiAwO1xuICBwYWRkaW5nLXJpZ2h0OiAxNnB4O1xufVxuXG4ubm90aWZpY2F0aW9uLWhlYWRlciB7XG4gIG1hcmdpbi1ib3R0b206IDZweDsgLyogRXNwYWNlIGVuIGJhcyBkZSBsJ2VuLXTDqnRlICovXG59XG5cbi5ub3RpZmljYXRpb24taGVhZGVyLXRvcCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsgLyogUGxhY2UgbGVzIMOpbMOpbWVudHMgYXV4IGV4dHLDqW1pdMOpcyAqL1xuICBhbGlnbi1pdGVtczogY2VudGVyOyAvKiBDZW50cmUgdmVydGljYWxlbWVudCAqL1xuICB3aWR0aDogMTAwJTsgLyogVXRpbGlzZSB0b3V0ZSBsYSBsYXJnZXVyIGRpc3BvbmlibGUgKi9cbn1cblxuLm5vdGlmaWNhdGlvbi1zZW5kZXIge1xuICBmb250LXdlaWdodDogNjAwO1xuICBmb250LXNpemU6IDAuOTVyZW07IC8qIFRhaWxsZSBsw6lnw6hyZW1lbnQgcGx1cyBncmFuZGUgcG91ciBsZSBub20gKi9cbiAgY29sb3I6ICM0ZjVmYWQ7XG4gIHBhZGRpbmc6IDJweCAwOyAvKiBMw6lnZXIgcGFkZGluZyB2ZXJ0aWNhbCBwb3VyIGFsaWduZXIgYXZlYyBsJ2hldXJlICovXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7IC8qIFRyYW5zaXRpb24gZmx1aWRlIHBvdXIgbGVzIGVmZmV0cyBkZSBzdXJ2b2wgKi9cbn1cblxuLm5vdGlmaWNhdGlvbi1zZW5kZXI6aG92ZXIge1xuICBjb2xvcjogIzNkNGE4NTsgLyogQ291bGV1ciBsw6lnw6hyZW1lbnQgcGx1cyBmb25jw6llIGF1IHN1cnZvbCAqL1xuICB0ZXh0LXNoYWRvdzogMCAwIDFweCByZ2JhKDc5LCA5NSwgMTczLCAwLjMpOyAvKiBMw6lnZXIgZWZmZXQgZGUgbHVldXIgYXUgc3Vydm9sICovXG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24tc2VuZGVyIHtcbiAgY29sb3I6ICNmZjhjMDA7IC8qIE9yYW5nZSBwb3VyIGxlIG5vbSBkZSBsJ2V4cMOpZGl0ZXVyICovXG4gIHRleHQtc2hhZG93OiAwIDAgNXB4IHJnYmEoMjU1LCAxNDAsIDAsIDAuMyk7IC8qIEzDqWdlciBlZmZldCBkZSBsdWV1ciAqL1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLXNlbmRlcjpob3ZlciB7XG4gIGNvbG9yOiAjZmZhMDQwOyAvKiBPcmFuZ2UgcGx1cyBjbGFpciBhdSBzdXJ2b2wgKi9cbiAgdGV4dC1zaGFkb3c6IDAgMCA4cHggcmdiYSgyNTUsIDE0MCwgMCwgMC41KTsgLyogRWZmZXQgZGUgbHVldXIgcGx1cyBwcm9ub25jw6kgYXUgc3Vydm9sICovXG59XG5cbi5ub3RpZmljYXRpb24tdGV4dCB7XG4gIGNvbG9yOiAjMzMzO1xuICBmb250LXNpemU6IDAuOXJlbTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLXRleHQge1xuICBjb2xvcjogI2ZmZmZmZjsgLyogVGV4dGUgYmxhbmMgcG91ciBsZXMgbm90aWZpY2F0aW9ucyBub24gc8OpbGVjdGlvbm7DqWVzIChmb25kIG5vaXIpICovXG59XG5cbi8qIFN0eWxlIGR1IHRleHRlIHBvdXIgbGVzIG5vdGlmaWNhdGlvbnMgbHVlcyBlbiBtb2RlIGNsYWlyICovXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tcmVhZCAubm90aWZpY2F0aW9uLXRleHQge1xuICBjb2xvcjogdmFyKFxuICAgIC0tbGlnaHQtdGV4dCxcbiAgICAjMzMzMzMzXG4gICk7IC8qIENvdWxldXIgZGUgdGV4dGUgY29tbWUgZGFucyBsZXMgbWVzc2FnZXMgZGUgY2hhdCAqL1xuICBmb250LXdlaWdodDogNDAwOyAvKiBQb2lkcyBkZSBwb2xpY2UgY29tbWUgZGFucyBsZXMgbWVzc2FnZXMgZGUgY2hhdCAqL1xufVxuXG4vKiBTdHlsZSBkdSB0ZXh0ZSBwb3VyIGxlcyBub3RpZmljYXRpb25zIGx1ZXMgZW4gbW9kZSBzb21icmUgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1yZWFkIC5ub3RpZmljYXRpb24tdGV4dCB7XG4gIGNvbG9yOiB2YXIoXG4gICAgLS1kYXJrLXRleHQsXG4gICAgI2ZmZmZmZlxuICApOyAvKiBDb3VsZXVyIGRlIHRleHRlIGNvbW1lIGRhbnMgbGVzIG1lc3NhZ2VzIGRlIGNoYXQgKi9cbiAgZm9udC13ZWlnaHQ6IDQwMDsgLyogUG9pZHMgZGUgcG9saWNlIGNvbW1lIGRhbnMgbGVzIG1lc3NhZ2VzIGRlIGNoYXQgKi9cbn1cblxuLm5vdGlmaWNhdGlvbi1tZXNzYWdlLXByZXZpZXcge1xuICBmb250LXNpemU6IDAuODVyZW07XG4gIGNvbG9yOiAjNjY2O1xuICBtYXJnaW4tdG9wOiA0cHg7XG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICBtYXgtd2lkdGg6IDEwMCU7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24tbWVzc2FnZS1wcmV2aWV3IHtcbiAgY29sb3I6ICNjY2NjY2M7IC8qIFRleHRlIGdyaXMgY2xhaXIgcG91ciBsZXMgbm90aWZpY2F0aW9ucyBub24gc8OpbGVjdGlvbm7DqWVzIChmb25kIG5vaXIpICovXG59XG5cbi8qIEluZGljYXRldXIgZGUgcGnDqGNlcyBqb2ludGVzICovXG4ubm90aWZpY2F0aW9uLWF0dGFjaG1lbnRzLWluZGljYXRvciB7XG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgY29sb3I6ICNmZjhjMDA7XG4gIG1hcmdpbi10b3A6IDAuMjVyZW07XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24tYXR0YWNobWVudHMtaW5kaWNhdG9yIHtcbiAgY29sb3I6IHJnYmEoMCwgMjQ3LCAyNTUsIDAuOSk7XG59XG5cbi5ub3RpZmljYXRpb24tYXR0YWNobWVudHMtaW5kaWNhdG9yIGkge1xuICBtYXJnaW4tcmlnaHQ6IDAuMjVyZW07XG59XG5cbi8qIPCfmoAgU1RZTEVTIFVOSVFVRVMgUE9VUiBDSEFRVUUgQk9VVE9OIC0gw4lWSVRFUiBMQSBDT05GVVNJT04gKi9cblxuLyogU3R5bGUgZGUgYmFzZSBjb21tdW4gKi9cbi5ub3RpZmljYXRpb24tYWN0aW9uLWJ1dHRvbiB7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBtYXJnaW46IDZweDsgLyogTWFyZ2UgYXVnbWVudMOpZSBwb3VyIHBsdXMgZCdlc3BhY2UgZW50cmUgbGVzIGJvdXRvbnMgKi9cbiAgYm9yZGVyOiBub25lO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi8qIPCfk4UgU1RZTEUgUE9VUiBMJ0hFVVJFIC0gQUxJR07DiUUgQVZFQyBMQSBDQVNFIMOAIENPQ0hFUiAqL1xuLm5vdGlmaWNhdGlvbi10aW1lIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDEwcHg7IC8qIE3Dqm1lIGhhdXRldXIgcXVlIGxhIGNhc2Ugw6AgY29jaGVyICovXG4gIHJpZ2h0OiAxMHB4OyAvKiBNw6ptZSBkaXN0YW5jZSBkdSBib3JkIHF1ZSBsYSBjYXNlIMOgIGNvY2hlciAqL1xuICBmb250LXNpemU6IDAuNzVyZW07IC8qIFRhaWxsZSBsw6lnw6hyZW1lbnQgYXVnbWVudMOpZSBwb3VyIG1laWxsZXVyZSBsaXNpYmlsaXTDqSAqL1xuICBjb2xvcjogcmdiYSgwLCAyNDcsIDI1NSwgMC45KTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgcGFkZGluZzogNXB4IDEwcHg7IC8qIFBhZGRpbmcgbMOpZ8OocmVtZW50IGF1Z21lbnTDqSAqL1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgcmdiYSgwLCAyNDcsIDI1NSwgMC4xNSksXG4gICAgcmdiYSgwLCAyMDAsIDI1NSwgMC4xKVxuICApO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xuICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAyNDcsIDI1NSwgMC4yKTtcbiAgei1pbmRleDogMTU7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gIC8qIEFtw6lsaW9yYXRpb24gZGUgbGEgbGlzaWJpbGl0w6kgKi9cbiAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xuICB0ZXh0LXJlbmRlcmluZzogb3B0aW1pemVMZWdpYmlsaXR5O1xuICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDtcbn1cblxuLyog8J+MmSBNT0RFIFNPTUJSRSAtIERBVEUgQ09JTiBEUk9JVCBIQVVUIC0gRkxVTyBVTFRSQSBWSVNJQkxFICovXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLXRpbWUge1xuICAvKiBDb3VsZXVyIGZsdW9yZXNjZW50ZSB1bHRyYS12aXZlICovXG4gIGNvbG9yOiAjMDBmZmZmICFpbXBvcnRhbnQ7IC8qIEN5YW4gcHVyIGZsdW9yZXNjZW50ICovXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgcmdiYSgwLCAyNTUsIDI1NSwgMC40KSxcbiAgICByZ2JhKDAsIDI1NSwgMjAwLCAwLjM1KSxcbiAgICByZ2JhKDAsIDIwMCwgMjU1LCAwLjMpXG4gICk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjU1LCAyNTUsIDAuOCk7XG4gIC8qIEVmZmV0IGRlIGx1ZXVyIGZsdW9yZXNjZW50ZSBpbnRlbnNlICovXG4gIHRleHQtc2hhZG93OiAwIDAgNXB4IHJnYmEoMCwgMjU1LCAyNTUsIDAuOCksIDAgMCAxMHB4IHJnYmEoMCwgMjU1LCAyNTUsIDAuNiksXG4gICAgMCAwIDE1cHggcmdiYSgwLCAyNTUsIDI1NSwgMC40KTtcbiAgYm94LXNoYWRvdzogMCAycHggMTBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjYpLCAwIDAgMjBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjMpLFxuICAgIGluc2V0IDAgMCAxMHB4IHJnYmEoMCwgMjU1LCAyNTUsIDAuMik7XG4gIGJhY2tkcm9wLWZpbHRlcjogbm9uZTsgLyogU3VwcHJlc3Npb24gZHUgZmxvdSBwb3VyIMOpdml0ZXIgbGUgdGV4dGUgZmxvdSAqL1xuICAvKiBBbmltYXRpb24gZGUgcHVsc2F0aW9uIGZsdW9yZXNjZW50ZSAqL1xuICBhbmltYXRpb246IGZsdW9yby1wdWxzZSAycyBlYXNlLWluLW91dCBpbmZpbml0ZSBhbHRlcm5hdGU7XG59XG5cbi8qIOKYgO+4jyBNT0RFIENMQUlSIC0gREFURSBDT0lOIERST0lUIEhBVVQgKi9cbjpob3N0LWNvbnRleHQoOm5vdCguZGFyaykpIC5ub3RpZmljYXRpb24tdGltZSB7XG4gIGNvbG9yOiAjNGY1ZmFkO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXG4gICAgMTM1ZGVnLFxuICAgIHJnYmEoNzksIDk1LCAxNzMsIDAuMSksXG4gICAgcmdiYSg3OSwgOTUsIDE3MywgMC4wNSlcbiAgKTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSg3OSwgOTUsIDE3MywgMC4yKTtcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNzksIDk1LCAxNzMsIDAuMTUpO1xufVxuXG4vKiDinKggRUZGRVRTIERFIFNVUlZPTCAqL1xuLm5vdGlmaWNhdGlvbi10aW1lOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpIHNjYWxlKDEuMDUpO1xuICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuNCk7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24tdGltZTpob3ZlciB7XG4gIC8qIEVmZmV0IGRlIHN1cnZvbCB1bHRyYS1mbHVvcmVzY2VudCAqL1xuICBjb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50OyAvKiBCbGFuYyBwdXIgYXUgc3Vydm9sICovXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgcmdiYSgwLCAyNTUsIDI1NSwgMC42KSxcbiAgICByZ2JhKDAsIDI1NSwgMjAwLCAwLjUpLFxuICAgIHJnYmEoMCwgMjAwLCAyNTUsIDAuNDUpXG4gICk7XG4gIGJvcmRlci1jb2xvcjogcmdiYSgwLCAyNTUsIDI1NSwgMSk7IC8qIEJvcmR1cmUgY3lhbiBwdXJlICovXG4gIC8qIEx1ZXVyIGludGVuc2UgYXUgc3Vydm9sICovXG4gIHRleHQtc2hhZG93OiAwIDAgOHB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSksIDAgMCAxNXB4IHJnYmEoMCwgMjU1LCAyNTUsIDAuOSksXG4gICAgMCAwIDI1cHggcmdiYSgwLCAyNTUsIDI1NSwgMC43KTtcbiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjgpLCAwIDAgMzBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjUpLFxuICAgIGluc2V0IDAgMCAxNXB4IHJnYmEoMCwgMjU1LCAyNTUsIDAuMyk7XG4gIC8qIEFuaW1hdGlvbiBwbHVzIGludGVuc2UgYXUgc3Vydm9sICovXG4gIGFuaW1hdGlvbjogZmx1b3JvLXB1bHNlLWludGVuc2UgMXMgZWFzZS1pbi1vdXQgaW5maW5pdGUgYWx0ZXJuYXRlO1xufVxuXG46aG9zdC1jb250ZXh0KDpub3QoLmRhcmspKSAubm90aWZpY2F0aW9uLXRpbWU6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXG4gICAgMTM1ZGVnLFxuICAgIHJnYmEoNzksIDk1LCAxNzMsIDAuMTUpLFxuICAgIHJnYmEoNzksIDk1LCAxNzMsIDAuMSlcbiAgKTtcbiAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDc5LCA5NSwgMTczLCAwLjI1KTtcbn1cblxuLyog8J+OryBCT1VUT04gUkVKT0lORFJFIC0gRk9STUUgSEVYQUdPTkFMRSBWRVJURSAqL1xuLm5vdGlmaWNhdGlvbi1qb2luLWJ1dHRvbiB7XG4gIHdpZHRoOiA0MHB4O1xuICBoZWlnaHQ6IDQwcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMwMGM4NTMsICMwMGE4NDMpO1xuICBjb2xvcjogd2hpdGU7XG4gIGJvcmRlcjogMnB4IHNvbGlkICMwMGM4NTM7XG4gIGNsaXAtcGF0aDogcG9seWdvbigyNSUgMCUsIDc1JSAwJSwgMTAwJSA1MCUsIDc1JSAxMDAlLCAyNSUgMTAwJSwgMCUgNTAlKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG4ubm90aWZpY2F0aW9uLWpvaW4tYnV0dG9uOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzAwZTY3NiwgIzAwYzg1Myk7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xNSkgcm90YXRlKDVkZWcpO1xuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDIwMCwgODMsIDAuNik7XG4gIGJvcmRlci1jb2xvcjogIzAwZTY3Njtcbn1cblxuLm5vdGlmaWNhdGlvbi1qb2luLWJ1dHRvbjo6YmVmb3JlIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IC0ycHg7XG4gIGxlZnQ6IC0ycHg7XG4gIHJpZ2h0OiAtMnB4O1xuICBib3R0b206IC0ycHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgIzAwYzg1MywgIzAwZTY3Nik7XG4gIGNsaXAtcGF0aDogcG9seWdvbigyNSUgMCUsIDc1JSAwJSwgMTAwJSA1MCUsIDc1JSAxMDAlLCAyNSUgMTAwJSwgMCUgNTAlKTtcbiAgei1pbmRleDogLTE7XG4gIG9wYWNpdHk6IDA7XG4gIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlO1xufVxuXG4ubm90aWZpY2F0aW9uLWpvaW4tYnV0dG9uOmhvdmVyOjpiZWZvcmUge1xuICBvcGFjaXR5OiAxO1xufVxuXG4vKiDwn5OWIEJPVVRPTiBEw4lUQUlMUyAtIEZPUk1FIENBUlLDiUUgQkxFVUUgKi9cbi5ub3RpZmljYXRpb24tZGV0YWlscy1idXR0b24ge1xuICB3aWR0aDogMzhweDtcbiAgaGVpZ2h0OiAzOHB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjE5NmYzLCAjMTk3NmQyKTtcbiAgY29sb3I6IHdoaXRlO1xuICBib3JkZXI6IDJweCBzb2xpZCAjMjE5NmYzO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLm5vdGlmaWNhdGlvbi1kZXRhaWxzLWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0MmE1ZjUsICMyMTk2ZjMpO1xuICB0cmFuc2Zvcm06IHNjYWxlKDEuMSkgcm90YXRlWSgxNWRlZyk7XG4gIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMzMsIDE1MCwgMjQzLCAwLjYpO1xuICBib3JkZXItY29sb3I6ICM0MmE1ZjU7XG59XG5cbi5ub3RpZmljYXRpb24tZGV0YWlscy1idXR0b246OmFmdGVyIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDJweDtcbiAgbGVmdDogMnB4O1xuICByaWdodDogMnB4O1xuICBib3R0b206IDJweDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBib3JkZXItcmFkaXVzOiA2cHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG5cbi5ub3RpZmljYXRpb24tZGV0YWlscy1idXR0b246aG92ZXI6OmFmdGVyIHtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xufVxuXG4vKiDinIUgQk9VVE9OIE1BUlFVRVIgQ09NTUUgTFUgLSBGT1JNRSBESUFNQU5UIEpBVU5FICovXG4ubm90aWZpY2F0aW9uLXJlYWQtYnV0dG9uIHtcbiAgd2lkdGg6IDM2cHg7XG4gIGhlaWdodDogMzZweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmYzEwNywgI2ZmOTgwMCk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgYm9yZGVyOiAycHggc29saWQgI2ZmYzEwNztcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB0cmFuc2Zvcm06IHJvdGF0ZSg0NWRlZyk7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLm5vdGlmaWNhdGlvbi1yZWFkLWJ1dHRvbiBpIHtcbiAgdHJhbnNmb3JtOiByb3RhdGUoLTQ1ZGVnKTtcbn1cblxuLm5vdGlmaWNhdGlvbi1yZWFkLWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmNhMjgsICNmZmMxMDcpO1xuICB0cmFuc2Zvcm06IHJvdGF0ZSg0NWRlZykgc2NhbGUoMS4xNSk7XG4gIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMjU1LCAxOTMsIDcsIDAuNik7XG4gIGJvcmRlci1jb2xvcjogI2ZmY2EyODtcbn1cblxuLm5vdGlmaWNhdGlvbi1yZWFkLWJ1dHRvbjo6YmVmb3JlIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDUwJTtcbiAgbGVmdDogNTAlO1xuICB3aWR0aDogOHB4O1xuICBoZWlnaHQ6IDhweDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpIHJvdGF0ZSgtNDVkZWcpO1xuICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlO1xufVxuXG4vKiDwn5eR77iPIEJPVVRPTiBTVVBQUklNRVIgLSBGT1JNRSBUUklBTkdVTEFJUkUgUk9VR0UgKi9cbi5ub3RpZmljYXRpb24tZGVsZXRlLWJ1dHRvbiB7XG4gIHdpZHRoOiAzOHB4O1xuICBoZWlnaHQ6IDM4cHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNDQzMzYsICNkMzJmMmYpO1xuICBjb2xvcjogd2hpdGU7XG4gIGJvcmRlcjogMnB4IHNvbGlkICNmNDQzMzY7XG4gIGNsaXAtcGF0aDogcG9seWdvbig1MCUgMCUsIDAlIDEwMCUsIDEwMCUgMTAwJSk7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLm5vdGlmaWNhdGlvbi1kZWxldGUtYnV0dG9uOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2VmNTM1MCwgI2Y0NDMzNik7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xNSkgcm90YXRlKC01ZGVnKTtcbiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgyNDQsIDY3LCA1NCwgMC42KTtcbiAgYm9yZGVyLWNvbG9yOiAjZWY1MzUwO1xufVxuXG4ubm90aWZpY2F0aW9uLWRlbGV0ZS1idXR0b246OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAtMnB4O1xuICBsZWZ0OiAtMnB4O1xuICByaWdodDogLTJweDtcbiAgYm90dG9tOiAtMnB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsICNmNDQzMzYsICNlZjUzNTApO1xuICBjbGlwLXBhdGg6IHBvbHlnb24oNTAlIDAlLCAwJSAxMDAlLCAxMDAlIDEwMCUpO1xuICB6LWluZGV4OiAtMTtcbiAgb3BhY2l0eTogMDtcbiAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzIGVhc2U7XG59XG5cbi5ub3RpZmljYXRpb24tZGVsZXRlLWJ1dHRvbjpob3Zlcjo6YmVmb3JlIHtcbiAgb3BhY2l0eTogMTtcbn1cblxuLyog8J+TjiBCT1VUT04gUEnDiENFUyBKT0lOVEVTIC0gRk9STUUgT0NUT0dPTkFMRSBWSU9MRVRURSAqL1xuLm5vdGlmaWNhdGlvbi1hdHRhY2htZW50LWJ1dHRvbiB7XG4gIHdpZHRoOiA0MHB4O1xuICBoZWlnaHQ6IDQwcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM5YzI3YjAsICM3YjFmYTIpO1xuICBjb2xvcjogd2hpdGU7XG4gIGJvcmRlcjogMnB4IHNvbGlkICM5YzI3YjA7XG4gIGNsaXAtcGF0aDogcG9seWdvbihcbiAgICAzMCUgMCUsXG4gICAgNzAlIDAlLFxuICAgIDEwMCUgMzAlLFxuICAgIDEwMCUgNzAlLFxuICAgIDcwJSAxMDAlLFxuICAgIDMwJSAxMDAlLFxuICAgIDAlIDcwJSxcbiAgICAwJSAzMCVcbiAgKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG4ubm90aWZpY2F0aW9uLWF0dGFjaG1lbnQtYnV0dG9uOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2FiNDdiYywgIzljMjdiMCk7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKSByb3RhdGUoMTBkZWcpO1xuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDE1NiwgMzksIDE3NiwgMC42KTtcbiAgYm9yZGVyLWNvbG9yOiAjYWI0N2JjO1xufVxuXG4ubm90aWZpY2F0aW9uLWF0dGFjaG1lbnQtYnV0dG9uOjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiA1MCU7XG4gIGxlZnQ6IDUwJTtcbiAgd2lkdGg6IDZweDtcbiAgaGVpZ2h0OiA2cHg7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC40KTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTtcbiAgYW5pbWF0aW9uOiBib3VuY2UgMS41cyBpbmZpbml0ZTtcbn1cblxuLyog8J+MmSBBREFQVEFUSU9OUyBQT1VSIExFIE1PREUgU09NQlJFICovXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLWpvaW4tYnV0dG9uIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzAwYzg1MywgIzAwYTg0Myk7XG4gIGJvcmRlci1jb2xvcjogIzAwZTY3NjtcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSgwLCAyMDAsIDgzLCAwLjQpO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLWRldGFpbHMtYnV0dG9uIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzIxOTZmMywgIzE5NzZkMik7XG4gIGJvcmRlci1jb2xvcjogIzQyYTVmNTtcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSgzMywgMTUwLCAyNDMsIDAuNCk7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24tcmVhZC1idXR0b24ge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmZjMTA3LCAjZmY5ODAwKTtcbiAgYm9yZGVyLWNvbG9yOiAjZmZjYTI4O1xuICBib3gtc2hhZG93OiAwIDAgMTBweCByZ2JhKDI1NSwgMTkzLCA3LCAwLjQpO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLWRlbGV0ZS1idXR0b24ge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjQ0MzM2LCAjZDMyZjJmKTtcbiAgYm9yZGVyLWNvbG9yOiAjZWY1MzUwO1xuICBib3gtc2hhZG93OiAwIDAgMTBweCByZ2JhKDI0NCwgNjcsIDU0LCAwLjQpO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLWF0dGFjaG1lbnQtYnV0dG9uIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzljMjdiMCwgIzdiMWZhMik7XG4gIGJvcmRlci1jb2xvcjogI2FiNDdiYztcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSgxNTYsIDM5LCAxNzYsIDAuNCk7XG59XG5cbi8qIPCfjJkgQU5JTUFUSU9OUyBFVCBFRkZFVFMgU1DDiUNJQVVYICovXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcbiAgMCUsXG4gIDEwMCUge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSkgcm90YXRlKC00NWRlZykgc2NhbGUoMSk7XG4gIH1cbiAgNTAlIHtcbiAgICBvcGFjaXR5OiAwLjU7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSkgcm90YXRlKC00NWRlZykgc2NhbGUoMS4yKTtcbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIGJvdW5jZSB7XG4gIDAlLFxuICAxMDAlIHtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKSBzY2FsZSgxKTtcbiAgfVxuICA1MCUge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpIHNjYWxlKDEuMyk7XG4gIH1cbn1cblxuLyog8J+OryBJTkRJQ0FURVVSUyBWSVNVRUxTIFBPVVIgw4lWSVRFUiBMQSBDT05GVVNJT04gKi9cbi5ub3RpZmljYXRpb24tam9pbi1idXR0b246aG92ZXI6OmFmdGVyIHtcbiAgY29udGVudDogXCLwn5KsXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAtOHB4O1xuICByaWdodDogLThweDtcbiAgZm9udC1zaXplOiAxMHB4O1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgd2lkdGg6IDE2cHg7XG4gIGhlaWdodDogMTZweDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIG9wYWNpdHk6IDE7XG4gIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlO1xuICB6LWluZGV4OiAxMDtcbn1cblxuLm5vdGlmaWNhdGlvbi1kZXRhaWxzLWJ1dHRvbjpob3Zlcjo6YWZ0ZXIge1xuICBjb250ZW50OiBcIuKEue+4j1wiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogLThweDtcbiAgcmlnaHQ6IC04cHg7XG4gIGZvbnQtc2l6ZTogMTBweDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIHdpZHRoOiAxNnB4O1xuICBoZWlnaHQ6IDE2cHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBvcGFjaXR5OiAxO1xuICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgZWFzZTtcbiAgei1pbmRleDogMTA7XG59XG5cbi5ub3RpZmljYXRpb24tZGVsZXRlLWJ1dHRvbjpob3Zlcjo6YWZ0ZXIge1xuICBjb250ZW50OiBcIvCfl5HvuI9cIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IC04cHg7XG4gIHJpZ2h0OiAtOHB4O1xuICBmb250LXNpemU6IDEwcHg7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB3aWR0aDogMTZweDtcbiAgaGVpZ2h0OiAxNnB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgb3BhY2l0eTogMTtcbiAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzIGVhc2U7XG4gIHotaW5kZXg6IDEwO1xufVxuXG4vKiBTdHlsZXMgcG91ciBsZXMgY2FzZXMgw6AgY29jaGVyIGZ1dHVyaXN0ZXMgKi9cbi5mdXR1cmlzdGljLWNoZWNrYm94IHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG4gIHdpZHRoOiAyMnB4OyAvKiBUYWlsbGUgcsOpZHVpdGUgKi9cbiAgaGVpZ2h0OiAyMnB4OyAvKiBUYWlsbGUgcsOpZHVpdGUgKi9cbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlOyAvKiBNw6ptZSB0cmFuc2l0aW9uIHF1ZSBsZXMgYXV0cmVzIGJvdXRvbnMgKi9cbn1cblxuLmZ1dHVyaXN0aWMtY2hlY2tib3ggaW5wdXQge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIG9wYWNpdHk6IDA7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgaGVpZ2h0OiAwO1xuICB3aWR0aDogMDtcbn1cblxuLmNoZWNrbWFyayB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICBoZWlnaHQ6IDIycHg7IC8qIFRhaWxsZSByw6lkdWl0ZSAqL1xuICB3aWR0aDogMjJweDsgLyogVGFpbGxlIHLDqWR1aXRlICovXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgcmdiYSgwLCAyNTUsIDIwMCwgMC4xKSxcbiAgICByZ2JhKDAsIDIwMCwgMjU1LCAwLjEpXG4gICk7IC8qIETDqWdyYWTDqSBmbHVvICovXG4gIGJvcmRlcjogMnB4IHNvbGlkIHRyYW5zcGFyZW50OyAvKiBCb3JkdXJlIHRyYW5zcGFyZW50ZSBwb3VyIGwnZWZmZXQgZGUgZMOpZ3JhZMOpICovXG4gIGJvcmRlci1yYWRpdXM6IDUwJTsgLyogRm9ybWUgcm9uZGUgY29tbWUgbGUgYm91dG9uIHJhZnJhw65jaGlyICovXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoMCwgMjU1LCAyMDAsIDAuNCk7IC8qIEx1ZXVyIGZsdW8gcGx1cyBwcm9ub25jw6llIChyw6lkdWl0ZSkgKi9cbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFuaW1hdGlvbjogZ2xvdy1wdWxzZSAycyBpbmZpbml0ZSBhbHRlcm5hdGU7IC8qIEFuaW1hdGlvbiBkZSBwdWxzYXRpb24gcG91ciBsYSBsdWV1ciAqL1xuICAvKiBVdGlsaXNhdGlvbiBkJ3VuIHBzZXVkby3DqWzDqW1lbnQgcG91ciBjcsOpZXIgbGUgY29udG91ciBkw6lncmFkw6kgKi9cbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxO1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuXG4vKiBBbmltYXRpb24gZGUgcHVsc2F0aW9uIHBvdXIgbGEgbHVldXIgZmx1byAqL1xuQGtleWZyYW1lcyBnbG93LXB1bHNlIHtcbiAgMCUge1xuICAgIGJveC1zaGFkb3c6IDAgMCA4cHggcmdiYSgwLCAyNTUsIDIwMCwgMC4zKTtcbiAgfVxuICAxMDAlIHtcbiAgICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDIwMCwgMjU1LCAwLjYpO1xuICB9XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5jaGVja21hcmsge1xuICBiYWNrZ3JvdW5kOiByZ2JhKFxuICAgIDAsXG4gICAgMjQ3LFxuICAgIDI1NSxcbiAgICAwLjFcbiAgKTsgLyogTcOqbWUgY291bGV1ciBxdWUgbGUgYm91dG9uIHJhZnJhw65jaGlyIGVuIG1vZGUgc29tYnJlICovXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjQ3LCAyNTUsIDAuMyk7IC8qIE3Dqm1lIGJvcmR1cmUgcXVlIGxlIGJvdXRvbiByYWZyYcOuY2hpciBlbiBtb2RlIHNvbWJyZSAqL1xuICBib3gtc2hhZG93OiAwIDAgMTJweCByZ2JhKDAsIDI0NywgMjU1LCAwLjQpOyAvKiBMdWV1ciBzaW1pbGFpcmUgYXUgYm91dG9uIHJhZnJhw65jaGlyICovXG4gIGFuaW1hdGlvbjogZ2xvdy1wdWxzZSAycyBpbmZpbml0ZSBhbHRlcm5hdGU7IC8qIEFuaW1hdGlvbiBkZSBwdWxzYXRpb24gcG91ciBsYSBsdWV1ciAqL1xufVxuXG4uZnV0dXJpc3RpYy1jaGVja2JveDpob3ZlciBpbnB1dCB+IC5jaGVja21hcmsge1xuICBiYWNrZ3JvdW5kOiByZ2JhKFxuICAgIDAsXG4gICAgMjQ3LFxuICAgIDI1NSxcbiAgICAwLjJcbiAgKTsgLyogTcOqbWUgZWZmZXQgaG92ZXIgcXVlIGxlIGJvdXRvbiByYWZyYcOuY2hpciAqL1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xuICBib3gtc2hhZG93OiB2YXIoXG4gICAgLS1nbG93LWVmZmVjdFxuICApOyAvKiBNw6ptZSBlZmZldCBkZSBsdWV1ciBxdWUgbGUgYm91dG9uIHJhZnJhw65jaGlyICovXG4gIHRyYW5zZm9ybTogc2NhbGUoXG4gICAgMS4wNVxuICApOyAvKiBNw6ptZSBlZmZldCBkJ2FncmFuZGlzc2VtZW50IHF1ZSBsZSBib3V0b24gcmFmcmHDrmNoaXIgKi9cbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtY2hlY2tib3g6aG92ZXIgaW5wdXQgfiAuY2hlY2ttYXJrIHtcbiAgYmFja2dyb3VuZDogcmdiYShcbiAgICAwLFxuICAgIDI0NyxcbiAgICAyNTUsXG4gICAgMC4yXG4gICk7IC8qIE3Dqm1lIGVmZmV0IGhvdmVyIHF1ZSBsZSBib3V0b24gcmFmcmHDrmNoaXIgZW4gbW9kZSBzb21icmUgKi9cbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcbiAgYm94LXNoYWRvdzogdmFyKFxuICAgIC0tZ2xvdy1lZmZlY3RcbiAgKTsgLyogTcOqbWUgZWZmZXQgZGUgbHVldXIgcXVlIGxlIGJvdXRvbiByYWZyYcOuY2hpciAqL1xuICB0cmFuc2Zvcm06IHNjYWxlKFxuICAgIDEuMDVcbiAgKTsgLyogTcOqbWUgZWZmZXQgZCdhZ3JhbmRpc3NlbWVudCBxdWUgbGUgYm91dG9uIHJhZnJhw65jaGlyICovXG59XG5cbi5mdXR1cmlzdGljLWNoZWNrYm94IGlucHV0OmNoZWNrZWQgfiAuY2hlY2ttYXJrIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KFxuICAgIDEzNWRlZyxcbiAgICByZ2JhKDAsIDI1NSwgMjAwLCAwLjgpLFxuICAgIHJnYmEoMCwgMjAwLCAyNTUsIDAuOClcbiAgKTsgLyogRMOpZ3JhZMOpIGZsdW8gdmlmIHF1YW5kIGNvY2jDqSAqL1xuICBib3JkZXI6IDJweCBzb2xpZCB0cmFuc3BhcmVudDsgLyogQm9yZHVyZSB0cmFuc3BhcmVudGUgcG91ciBsJ2VmZmV0IGRlIGTDqWdyYWTDqSAqL1xuICBib3gtc2hhZG93OiAwIDAgMjBweCByZ2JhKDAsIDI1NSwgMjAwLCAwLjgpOyAvKiBMdWV1ciBmbHVvIHBsdXMgcHJvbm9uY8OpZSBxdWFuZCBjb2Now6kgKi9cbiAgYW5pbWF0aW9uOiBjaGVja2JveC1nbG93IDEuNXMgaW5maW5pdGUgYWx0ZXJuYXRlOyAvKiBBbmltYXRpb24gZGUgcHVsc2F0aW9uIHBsdXMgaW50ZW5zZSBxdWFuZCBjb2Now6kgKi9cbn1cblxuLyogQW5pbWF0aW9uIGRlIHB1bHNhdGlvbiBwbHVzIGludGVuc2UgcG91ciBsYSBjYXNlIGNvY2jDqWUgKi9cbkBrZXlmcmFtZXMgY2hlY2tib3gtZ2xvdyB7XG4gIDAlIHtcbiAgICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDI1NSwgMjAwLCAwLjYpO1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMSk7XG4gIH1cbiAgMTAwJSB7XG4gICAgYm94LXNoYWRvdzogMCAwIDI1cHggcmdiYSgwLCAyMDAsIDI1NSwgMC45KTtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMTUpO1xuICB9XG59XG5cbi8qIEFuaW1hdGlvbiBkZSBwdWxzYXRpb24gcG91ciBsYSBjYXNlIMOgIGNvY2hlciBjb2Now6llICovXG5Aa2V5ZnJhbWVzIGNoZWNrYm94LXB1bHNlIHtcbiAgMCUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMSk7XG4gICAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgwLCAyNDcsIDI1NSwgMC40KTtcbiAgfVxuICAxMDAlIHtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMTUpO1xuICAgIGJveC1zaGFkb3c6IDAgMCAyMHB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuOCk7XG4gIH1cbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtY2hlY2tib3ggaW5wdXQ6Y2hlY2tlZCB+IC5jaGVja21hcmsge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXG4gICAgMTM1ZGVnLFxuICAgIHJnYmEoMCwgMjU1LCAyMDAsIDAuOCksXG4gICAgcmdiYSgwLCAyMDAsIDI1NSwgMC44KVxuICApOyAvKiBEw6lncmFkw6kgZmx1byB2aWYgcXVhbmQgY29jaMOpIGVuIG1vZGUgc29tYnJlICovXG4gIGJvcmRlcjogMnB4IHNvbGlkIHRyYW5zcGFyZW50OyAvKiBCb3JkdXJlIHRyYW5zcGFyZW50ZSBwb3VyIGwnZWZmZXQgZGUgZMOpZ3JhZMOpICovXG4gIGJveC1zaGFkb3c6IDAgMCAyMHB4IHJnYmEoMCwgMjU1LCAyMDAsIDAuOCk7IC8qIEx1ZXVyIGZsdW8gcGx1cyBwcm9ub25jw6llIHF1YW5kIGNvY2jDqSBlbiBtb2RlIHNvbWJyZSAqL1xuICBhbmltYXRpb246IGNoZWNrYm94LWdsb3cgMS41cyBpbmZpbml0ZSBhbHRlcm5hdGU7IC8qIEFuaW1hdGlvbiBkZSBwdWxzYXRpb24gcGx1cyBpbnRlbnNlIHF1YW5kIGNvY2jDqSBlbiBtb2RlIHNvbWJyZSAqL1xufVxuXG4uY2hlY2ttYXJrOmFmdGVyIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBkaXNwbGF5OiBub25lO1xufVxuXG4uZnV0dXJpc3RpYy1jaGVja2JveCBpbnB1dDpjaGVja2VkIH4gLmNoZWNrbWFyazphZnRlciB7XG4gIGRpc3BsYXk6IGJsb2NrO1xufVxuXG4uZnV0dXJpc3RpYy1jaGVja2JveCAuY2hlY2ttYXJrOmFmdGVyIHtcbiAgbGVmdDogN3B4OyAvKiBBanVzdMOpIHBvdXIgbGEgZm9ybWUgcm9uZGUgKi9cbiAgdG9wOiAzcHg7IC8qIEFqdXN0w6kgcG91ciBsYSBmb3JtZSByb25kZSAqL1xuICB3aWR0aDogNnB4OyAvKiBUYWlsbGUgcsOpZHVpdGUgKi9cbiAgaGVpZ2h0OiAxMnB4OyAvKiBUYWlsbGUgcsOpZHVpdGUgKi9cbiAgYm9yZGVyOiBzb2xpZCB3aGl0ZTtcbiAgYm9yZGVyLXdpZHRoOiAwIDJweCAycHggMDsgLyogQm9yZHVyZSBwbHVzIGZpbmUgbWFpcyB2aXNpYmxlICovXG4gIHRyYW5zZm9ybTogcm90YXRlKDQ1ZGVnKTtcbiAgYm94LXNoYWRvdzogMCAwIDVweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7IC8qIEx1ZXVyIGJsYW5jaGUgcG91ciBsYSBjb2NoZSAqL1xuICBhbmltYXRpb246IHB1bHNlLWNoZWNrIDEuNXMgaW5maW5pdGUgYWx0ZXJuYXRlOyAvKiBBbmltYXRpb24gZGUgcHVsc2F0aW9uIHBvdXIgbGEgY29jaGUgKi9cbn1cblxuLyogQW5pbWF0aW9uIGRlIHB1bHNhdGlvbiBwb3VyIGxhIGNvY2hlICovXG5Aa2V5ZnJhbWVzIHB1bHNlLWNoZWNrIHtcbiAgMCUge1xuICAgIG9wYWNpdHk6IDAuODtcbiAgICBib3gtc2hhZG93OiAwIDAgNXB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTtcbiAgfVxuICAxMDAlIHtcbiAgICBvcGFjaXR5OiAxO1xuICAgIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSk7XG4gIH1cbn1cblxuLyogU3R5bGVzIHBvdXIgbGEgY2FzZSDDoCBjb2NoZXIgXCJUb3V0IHPDqWxlY3Rpb25uZXJcIiAqL1xuLnNlbGVjdC1hbGwtY2hlY2tib3gge1xuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIG1hcmdpbjogMCA1cHg7XG59XG5cbi8qIFN0eWxlIHNww6ljaWZpcXVlIHBvdXIgbGEgY2FzZSDDoCBjb2NoZXIgZGFucyBsYSBiYXJyZSBkJ2FjdGlvbnMgKi9cbi5mbGV4LnNwYWNlLXgtMiAuc2VsZWN0LWFsbC1jaGVja2JveCAuZnV0dXJpc3RpYy1jaGVja2JveCB7XG4gIHdpZHRoOiAzNnB4O1xuICBoZWlnaHQ6IDM2cHg7XG59XG5cbi5mbGV4LnNwYWNlLXgtMiAuc2VsZWN0LWFsbC1jaGVja2JveCAuY2hlY2ttYXJrIHtcbiAgd2lkdGg6IDM2cHg7XG4gIGhlaWdodDogMzZweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlOyAvKiBGb3JtZSByb25kZSBjb21tZSBsZSBib3V0b24gcmFmcmHDrmNoaXIgKi9cbiAgYmFja2dyb3VuZDogcmdiYShcbiAgICAwLFxuICAgIDI0NyxcbiAgICAyNTUsXG4gICAgMC4xXG4gICk7IC8qIE3Dqm1lIGNvdWxldXIgcXVlIGxlIGJvdXRvbiByYWZyYcOuY2hpciAqL1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpOyAvKiBNw6ptZSBib3JkdXJlIHF1ZSBsZSBib3V0b24gcmFmcmHDrmNoaXIgKi9cbiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSgwLCAyNDcsIDI1NSwgMC40KTsgLyogTHVldXIgc2ltaWxhaXJlIGF1IGJvdXRvbiByYWZyYcOuY2hpciAqL1xufVxuXG4vKiBFZmZldCBob3ZlciBzaW1pbGFpcmUgYXUgYm91dG9uIHJhZnJhw65jaGlyICovXG4uZmxleC5zcGFjZS14LTJcbiAgLnNlbGVjdC1hbGwtY2hlY2tib3hcbiAgLmZ1dHVyaXN0aWMtY2hlY2tib3g6aG92ZXJcbiAgaW5wdXRcbiAgfiAuY2hlY2ttYXJrIHtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAyNDcsIDI1NSwgMC4yKTtcbiAgYm94LXNoYWRvdzogdmFyKC0tZ2xvdy1lZmZlY3QpO1xuICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xufVxuXG4vKiBBanVzdGVtZW50IGRlIGxhIHBvc2l0aW9uIGRlIGxhIGNvY2hlIHBvdXIgbGEgY2FzZSDDoCBjb2NoZXIgZGFucyBsYSBiYXJyZSBkJ2FjdGlvbnMgKi9cbi5mbGV4LnNwYWNlLXgtMiAuc2VsZWN0LWFsbC1jaGVja2JveCAuY2hlY2ttYXJrOmFmdGVyIHtcbiAgbGVmdDogMTNweDsgLyogQWp1c3TDqSBwb3VyIGxhIGZvcm1lIHJvbmRlICovXG4gIHRvcDogN3B4OyAvKiBBanVzdMOpIHBvdXIgbGEgZm9ybWUgcm9uZGUgKi9cbiAgd2lkdGg6IDhweDsgLyogVGFpbGxlIGFqdXN0w6llICovXG4gIGhlaWdodDogMTZweDsgLyogVGFpbGxlIGFqdXN0w6llICovXG59XG5cbi8qIFN0eWxlcyBwb3VyIGxhIGJhcnJlIGRlIHPDqWxlY3Rpb24gKi9cbi5zZWxlY3Rpb24tYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAxNDAsIDAsIDAuMSk7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgcGFkZGluZzogOHB4IDEycHg7XG4gIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMjU1LCAxNDAsIDAsIDAuMik7XG4gIGFuaW1hdGlvbjogZmFkZUluIDAuM3MgZWFzZTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLnNlbGVjdGlvbi1hY3Rpb25zIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDE0MCwgMCwgMC4xKTtcbiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgyNTUsIDE0MCwgMCwgMC4yKTtcbn1cblxuLnNlbGVjdGlvbi1jb3VudCB7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIG1hcmdpbi1yaWdodDogMTVweDtcbiAgY29sb3I6ICNmZjhjMDA7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5zZWxlY3Rpb24tY291bnQge1xuICBjb2xvcjogcmdiYSgyNTUsIDE0MCwgMCwgMC45KTtcbn1cblxuLyogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgIE5PVElGSUNBVElPTlMgU8OJTEVDVElPTk7DiUVTIC0gTU9ERSBDTEFJUlxuICAgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gKi9cbi5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1zZWxlY3RlZCB7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAxNDAsIDAsIDAuNSkgIWltcG9ydGFudDtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDE0MCwgMCwgMC4wNSkgIWltcG9ydGFudDtcbiAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDI1NSwgMTQwLCAwLCAwLjEpICFpbXBvcnRhbnQ7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbn1cblxuLyogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgIE5PVElGSUNBVElPTlMgU8OJTEVDVElPTk7DiUVTIC0gTU9ERSBTT01CUkVcbiAgID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tc2VsZWN0ZWQge1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMTQwLCAwLCAwLjMpICFpbXBvcnRhbnQ7IC8qIEJvcmR1cmUgb3JhbmdlICovXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgcmdiYSgyNTUsIDE0MCwgMCwgMC4xNSksXG4gICAgcmdiYSgyNTUsIDAsIDEyOCwgMC4xNSksXG4gICAgcmdiYSgxMjgsIDAsIDI1NSwgMC4xNSlcbiAgKSAhaW1wb3J0YW50OyAvKiBEw6lncmFkw6kgaW5ub3ZhbnQgZXh0cmFvcmRpbmFpcmUgKi9cbiAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDI1NSwgMTQwLCAwLCAwLjIpLFxuICAgIGluc2V0IDAgMCAyMHB4IHJnYmEoMjU1LCAwLCAxMjgsIDAuMSkgIWltcG9ydGFudDsgLyogT21icmUgY29tcGxleGUgKi9cbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBwYWRkaW5nOiAxOHB4IDIycHggIWltcG9ydGFudDsgLyogUGFkZGluZyBsw6lnw6hyZW1lbnQgcGx1cyBncmFuZCBwb3VyIGxlcyBub3RpZmljYXRpb25zIHPDqWxlY3Rpb25uw6llcyAqL1xuICBtYXJnaW4tYm90dG9tOiAxOHB4ICFpbXBvcnRhbnQ7IC8qIFBsdXMgZCdlc3BhY2VtZW50IHBvdXIgbGVzIG5vdGlmaWNhdGlvbnMgc8OpbGVjdGlvbm7DqWVzICovXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLyogRWZmZXQgZGUgYnJpbGxhbmNlIHBvdXIgbGVzIG5vdGlmaWNhdGlvbnMgc8OpbGVjdGlvbm7DqWVzICovXG46aG9zdC1jb250ZXh0KC5kYXJrKSAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tc2VsZWN0ZWQ6OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAtNTAlO1xuICBsZWZ0OiAtNTAlO1xuICB3aWR0aDogMjAwJTtcbiAgaGVpZ2h0OiAyMDAlO1xuICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoXG4gICAgY2lyY2xlLFxuICAgIHJnYmEoMjU1LCAxNDAsIDAsIDAuMSkgMCUsXG4gICAgdHJhbnNwYXJlbnQgNzAlXG4gICk7XG4gIGFuaW1hdGlvbjogcm90YXRlLWdyYWRpZW50IDhzIGxpbmVhciBpbmZpbml0ZTtcbiAgcG9pbnRlci1ldmVudHM6IG5vbmU7XG59XG5cbi8qIEVmZmV0IGRlIHBhcnRpY3VsZXMgc2NpbnRpbGxhbnRlcyBwb3VyIGxlcyBub3RpZmljYXRpb25zIHPDqWxlY3Rpb25uw6llcyAqL1xuOmhvc3QtY29udGV4dCguZGFyaykgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLXNlbGVjdGVkOjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgaW5zZXQ6IDA7XG4gIGJhY2tncm91bmQtaW1hZ2U6IHJhZGlhbC1ncmFkaWVudChcbiAgICAgIGNpcmNsZSBhdCAxMCUgMTAlLFxuICAgICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpIDAlLFxuICAgICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwKSAyJVxuICAgICksXG4gICAgcmFkaWFsLWdyYWRpZW50KFxuICAgICAgY2lyY2xlIGF0IDIwJSAzMCUsXG4gICAgICByZ2JhKDI1NSwgMTQwLCAwLCAwLjgpIDAlLFxuICAgICAgcmdiYSgyNTUsIDE0MCwgMCwgMCkgMiVcbiAgICApLFxuICAgIHJhZGlhbC1ncmFkaWVudChcbiAgICAgIGNpcmNsZSBhdCAzMCUgNzAlLFxuICAgICAgcmdiYSgyNTUsIDAsIDEyOCwgMC44KSAwJSxcbiAgICAgIHJnYmEoMjU1LCAwLCAxMjgsIDApIDIlXG4gICAgKSxcbiAgICByYWRpYWwtZ3JhZGllbnQoXG4gICAgICBjaXJjbGUgYXQgNzAlIDQwJSxcbiAgICAgIHJnYmEoMTI4LCAwLCAyNTUsIDAuOCkgMCUsXG4gICAgICByZ2JhKDEyOCwgMCwgMjU1LCAwKSAyJVxuICAgICksXG4gICAgcmFkaWFsLWdyYWRpZW50KFxuICAgICAgY2lyY2xlIGF0IDgwJSA4MCUsXG4gICAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCkgMCUsXG4gICAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDApIDIlXG4gICAgKSxcbiAgICByYWRpYWwtZ3JhZGllbnQoXG4gICAgICBjaXJjbGUgYXQgOTAlIDEwJSxcbiAgICAgIHJnYmEoMjU1LCAxNDAsIDAsIDAuOCkgMCUsXG4gICAgICByZ2JhKDI1NSwgMTQwLCAwLCAwKSAyJVxuICAgICksXG4gICAgcmFkaWFsLWdyYWRpZW50KFxuICAgICAgY2lyY2xlIGF0IDUwJSA1MCUsXG4gICAgICByZ2JhKDI1NSwgMCwgMTI4LCAwLjgpIDAlLFxuICAgICAgcmdiYSgyNTUsIDAsIDEyOCwgMCkgMiVcbiAgICApO1xuICBvcGFjaXR5OiAwO1xuICBhbmltYXRpb246IHNwYXJrbGUtZWZmZWN0IDRzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICBwb2ludGVyLWV2ZW50czogbm9uZTtcbn1cblxuQGtleWZyYW1lcyBzcGFya2xlLWVmZmVjdCB7XG4gIDAlIHtcbiAgICBvcGFjaXR5OiAwO1xuICB9XG4gIDUwJSB7XG4gICAgb3BhY2l0eTogMTtcbiAgfVxuICAxMDAlIHtcbiAgICBvcGFjaXR5OiAwO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgcm90YXRlLWdyYWRpZW50IHtcbiAgMCUge1xuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xuICB9XG4gIDEwMCUge1xuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XG4gIH1cbn1cblxuLyogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgIFRFWFRFIERFUyBOT1RJRklDQVRJT05TIFPDiUxFQ1RJT05Ow4lFUyAtIE1PREUgU09NQlJFXG4gICA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSAqL1xuLyogVGV4dGUgcHJpbmNpcGFsIGV0IGhvcm9kYXRhZ2UgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspXG4gIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1zZWxlY3RlZFxuICAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tdGV4dCxcbjpob3N0LWNvbnRleHQoLmRhcmspXG4gIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1zZWxlY3RlZFxuICAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tdGltZSB7XG4gIGNvbG9yOiByZ2JhKFxuICAgIDI1NSxcbiAgICAyNTUsXG4gICAgMjU1LFxuICAgIDAuOVxuICApICFpbXBvcnRhbnQ7IC8qIFRleHRlIGJsYW5jIHBvdXIgbGVzIG5vdGlmaWNhdGlvbnMgc8OpbGVjdGlvbm7DqWVzIChmb25kIG9yYW5nZSBjbGFpcikgKi9cbn1cblxuLyogQXBlcsOndSBkZXMgbWVzc2FnZXMgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspXG4gIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1zZWxlY3RlZFxuICAuZnV0dXJpc3RpYy1tZXNzYWdlLXByZXZpZXcge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKFxuICAgIDAsXG4gICAgMCxcbiAgICAwLFxuICAgIDAuNVxuICApICFpbXBvcnRhbnQ7IC8qIEZvbmQgbm9pciBzZW1pLXRyYW5zcGFyZW50ICovXG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSkgIWltcG9ydGFudDsgLyogVGV4dGUgYmxhbmMgKi9cbiAgYm9yZGVyLWxlZnQ6IDJweCBzb2xpZCByZ2JhKDI1NSwgMTQwLCAwLCAwLjUpICFpbXBvcnRhbnQ7IC8qIEJvcmR1cmUgb3JhbmdlICovXG59XG5cbi8qIE5vbSBkZSBsJ2V4cMOpZGl0ZXVyICovXG46aG9zdC1jb250ZXh0KC5kYXJrKVxuICAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tc2VsZWN0ZWRcbiAgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLXNlbmRlciB7XG4gIGNvbG9yOiAjZmY4YzAwICFpbXBvcnRhbnQ7IC8qIE9yYW5nZSB2aWYgcG91ciBsZSBub20gZGUgbCdleHDDqWRpdGV1ciAqL1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4vKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgQk9VVE9OUyBEJ0FDVElPTiAtIE5PVElGSUNBVElPTlMgU8OJTEVDVElPTk7DiUVTIC0gTU9ERSBTT01CUkVcbiAgID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG4vKiBCb3V0b24gXCJNYXJxdWVyIGNvbW1lIGx1XCIgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1zZWxlY3RlZCAuZnV0dXJpc3RpYy1yZWFkLWJ1dHRvbiB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgcmdiYSgyNTUsIDE0MCwgMCwgMC4yKSxcbiAgICByZ2JhKDI1NSwgOTQsIDk4LCAwLjIpXG4gICk7XG4gIGNvbG9yOiAjZmY4YzAwOyAvKiBPcmFuZ2UgdmlmIGNvbW1lIGRhbnMgbGEgcGFnZSBkZXMgbWVzc2FnZXMvdXRpbGlzYXRldXJzICovXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAxNDAsIDAsIDAuNCk7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspXG4gIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1zZWxlY3RlZFxuICAuZnV0dXJpc3RpYy1yZWFkLWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAxMzVkZWcsXG4gICAgcmdiYSgyNTUsIDE0MCwgMCwgMC4zKSxcbiAgICByZ2JhKDI1NSwgOTQsIDk4LCAwLjMpXG4gICk7XG4gIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMjU1LCAxNDAsIDAsIDAuNSk7IC8qIEx1ZXVyIG9yYW5nZSBhdSBzdXJ2b2wgKi9cbn1cblxuLyogQm91dG9ucyBkJ2FjdGlvbiBnw6luw6lyaXF1ZXMgKi9cbjpob3N0LWNvbnRleHQoLmRhcmspXG4gIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1zZWxlY3RlZFxuICAubm90aWZpY2F0aW9uLWFjdGlvbi1idXR0b24ge1xuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKFxuICAgIDI1NSxcbiAgICAxNDAsXG4gICAgMCxcbiAgICAwLjFcbiAgKTsgLyogRm9uZCBvcmFuZ2UgY29tbWUgZGFucyBsYSBwYWdlIGRlcyBtZXNzYWdlcy91dGlsaXNhdGV1cnMgKi9cbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDE0MCwgMCwgMC4zKTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaylcbiAgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLXNlbGVjdGVkXG4gIC5ub3RpZmljYXRpb24tYWN0aW9uLWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoXG4gICAgMjU1LFxuICAgIDE0MCxcbiAgICAwLFxuICAgIDAuMlxuICApOyAvKiBGb25kIG9yYW5nZSBwbHVzIGZvbmPDqSBhdSBzdXJ2b2wgKi9cbiAgY29sb3I6ICNmZjhjMDA7IC8qIFRleHRlIG9yYW5nZSBjb21tZSBkYW5zIGxhIHBhZ2UgZGVzIG1lc3NhZ2VzL3V0aWxpc2F0ZXVycyAqL1xuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDI1NSwgMTQwLCAwLCAwLjQpOyAvKiBMdWV1ciBvcmFuZ2UgYXUgc3Vydm9sICovXG59XG5cbi8qIEJvdXRvbiBkZSBzdXBwcmVzc2lvbiAqL1xuOmhvc3QtY29udGV4dCguZGFyaylcbiAgLmZ1dHVyaXN0aWMtbm90aWZpY2F0aW9uLXNlbGVjdGVkXG4gIC5ub3RpZmljYXRpb24tZGVsZXRlLWJ1dHRvbiB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoXG4gICAgMjU1LFxuICAgIDE0MCxcbiAgICAwLFxuICAgIDAuMVxuICApOyAvKiBGb25kIG9yYW5nZSBjb21tZSBkYW5zIGxhIHBhZ2UgZGVzIG1lc3NhZ2VzL3V0aWxpc2F0ZXVycyAqL1xuICBjb2xvcjogI2ZmOGMwMDsgLyogVGV4dGUgb3JhbmdlIGNvbW1lIGRhbnMgbGEgcGFnZSBkZXMgbWVzc2FnZXMvdXRpbGlzYXRldXJzICovXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAxNDAsIDAsIDAuMyk7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspXG4gIC5mdXR1cmlzdGljLW5vdGlmaWNhdGlvbi1zZWxlY3RlZFxuICAubm90aWZpY2F0aW9uLWRlbGV0ZS1idXR0b246aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKFxuICAgIDI1NSxcbiAgICAxNDAsXG4gICAgMCxcbiAgICAwLjJcbiAgKTsgLyogRm9uZCBvcmFuZ2UgcGx1cyBmb25jw6kgYXUgc3Vydm9sICovXG4gIGNvbG9yOiAjZmY4YzAwOyAvKiBUZXh0ZSBvcmFuZ2UgY29tbWUgZGFucyBsYSBwYWdlIGRlcyBtZXNzYWdlcy91dGlsaXNhdGV1cnMgKi9cbiAgYm94LXNoYWRvdzogMCAwIDhweCByZ2JhKDI1NSwgMTQwLCAwLCAwLjQpOyAvKiBMdWV1ciBvcmFuZ2UgYXUgc3Vydm9sICovXG59XG5cbi8qIFBldGl0IHBvaW50IHPDqXBhcmF0ZXVyIGVudHJlIGxlIGJvdXRvbiBkZSBzdXBwcmVzc2lvbiBldCBsYSBjYXNlIMOgIGNvY2hlciAqL1xuLm5vdGlmaWNhdGlvbi1zZXBhcmF0b3ItZG90IHtcbiAgd2lkdGg6IDRweDtcbiAgaGVpZ2h0OiA0cHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYShcbiAgICAwLFxuICAgIDI0NyxcbiAgICAyNTUsXG4gICAgMC42XG4gICk7IC8qIENvdWxldXIgY3lhbiBjb21tZSBsYSBjYXNlIMOgIGNvY2hlciAqL1xuICBtYXJnaW46IDAgOHB4O1xuICBib3gtc2hhZG93OiAwIDAgNXB4IHJnYmEoMCwgMjQ3LCAyNTUsIDAuNCk7IC8qIEx1ZXVyIGN5YW4gKi9cbiAgYW5pbWF0aW9uOiBkb3QtcHVsc2UgMnMgaW5maW5pdGUgYWx0ZXJuYXRlOyAvKiBBbmltYXRpb24gZGUgcHVsc2F0aW9uICovXG4gIHRyYW5zaXRpb246IGFsbCAwLjVzIGVhc2U7IC8qIFRyYW5zaXRpb24gcG91ciBsJ2FuaW1hdGlvbiBkZSBkaXNwYXJpdGlvbiAqL1xufVxuXG4vKiBBbmltYXRpb24gZGUgZGlzcGFyaXRpb24gZHUgcG9pbnQgc8OpcGFyYXRldXIgKi9cbi5ub3RpZmljYXRpb24tc2VwYXJhdG9yLWRvdC5mYWRlLW91dCB7XG4gIG9wYWNpdHk6IDA7XG4gIHRyYW5zZm9ybTogc2NhbGUoMCk7XG4gIHdpZHRoOiAwO1xuICBtYXJnaW46IDA7XG59XG5cbi8qIFBvaW50IHPDqXBhcmF0ZXVyIHBvdXIgbGVzIG5vdGlmaWNhdGlvbnMgc8OpbGVjdGlvbm7DqWVzIGVuIG1vZGUgc29tYnJlICovXG46aG9zdC1jb250ZXh0KC5kYXJrKVxuICAuZnV0dXJpc3RpYy1ub3RpZmljYXRpb24tc2VsZWN0ZWRcbiAgLm5vdGlmaWNhdGlvbi1zZXBhcmF0b3ItZG90IHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYShcbiAgICAwLFxuICAgIDI0NyxcbiAgICAyNTUsXG4gICAgMC44XG4gICk7IC8qIENvdWxldXIgY3lhbiBwbHVzIHZpdmUgcG91ciBsZXMgbm90aWZpY2F0aW9ucyBzw6lsZWN0aW9ubsOpZXMgKi9cbiAgYm94LXNoYWRvdzogMCAwIDhweCByZ2JhKDAsIDI0NywgMjU1LCAwLjYpOyAvKiBMdWV1ciBjeWFuIHBsdXMgcHJvbm9uY8OpZSAqL1xuICBhbmltYXRpb246IGRvdC1wdWxzZS1zZWxlY3RlZCAxLjVzIGluZmluaXRlIGFsdGVybmF0ZTsgLyogQW5pbWF0aW9uIHBsdXMgcmFwaWRlICovXG59XG5cbi8qIEFuaW1hdGlvbiBkZSBwdWxzYXRpb24gcG91ciBsZSBwb2ludCBzw6lwYXJhdGV1ciBkZXMgbm90aWZpY2F0aW9ucyBzw6lsZWN0aW9ubsOpZXMgKi9cbkBrZXlmcmFtZXMgZG90LXB1bHNlLXNlbGVjdGVkIHtcbiAgMCUge1xuICAgIG9wYWNpdHk6IDAuNjtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEpO1xuICB9XG4gIDEwMCUge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjUpO1xuICB9XG59XG5cbi8qIEFuaW1hdGlvbiBkZSBwdWxzYXRpb24gcG91ciBsZSBwb2ludCBzw6lwYXJhdGV1ciAqL1xuQGtleWZyYW1lcyBkb3QtcHVsc2Uge1xuICAwJSB7XG4gICAgb3BhY2l0eTogMC40O1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMC44KTtcbiAgfVxuICAxMDAlIHtcbiAgICBvcGFjaXR5OiAxO1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMS4yKTtcbiAgfVxufVxuXG4vKiBTdHlsZXMgcG91ciBsYSBjYXNlIMOgIGNvY2hlciBkYW5zIGxlcyBub3RpZmljYXRpb25zICovXG4ubm90aWZpY2F0aW9uLWNoZWNrYm94IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDEwcHg7IC8qIFBvc2l0aW9uIGVuIGhhdXQgYXUgbGlldSBkJ8OqdHJlIGNlbnRyw6llIHZlcnRpY2FsZW1lbnQgKi9cbiAgbGVmdDogMTBweDsgLyogUG9zaXRpb24gw6AgZ2F1Y2hlICovXG4gIHotaW5kZXg6IDEwOyAvKiBTJ2Fzc3VyZXIgcXUnZWxsZSBlc3QgYXUtZGVzc3VzIGRlcyBhdXRyZXMgw6lsw6ltZW50cyAqL1xufVxuXG4vKiBBbmltYXRpb24gZGUgcHVsc2F0aW9uIHBvdXIgbGEgY2FzZSDDoCBjb2NoZXIgKi9cbkBrZXlmcmFtZXMgcHVsc2Uge1xuICAwJSB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcbiAgfVxuICA1MCUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbiAgfVxuICAxMDAlIHtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEpO1xuICB9XG59XG5cbi8qIEJvdXRvbiBkJ2FubnVsYXRpb24gKi9cbi5mdXR1cmlzdGljLWNhbmNlbC1idXR0b24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxNTAsIDE1MCwgMTUwLCAwLjIpO1xuICBjb2xvcjogIzZkNjg3MDtcbiAgYm9yZGVyOiBub25lO1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzLW1kKTtcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBib3gtc2hhZG93OiAwIDAgOHB4IHJnYmEoMTUwLCAxNTAsIDE1MCwgMC4yKTtcbn1cblxuLmZ1dHVyaXN0aWMtY2FuY2VsLWJ1dHRvbjpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTUwLCAxNTAsIDE1MCwgMC4zKTtcbiAgYm94LXNoYWRvdzogMCAwIDEycHggcmdiYSgxNTAsIDE1MCwgMTUwLCAwLjMpO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5mdXR1cmlzdGljLWNhbmNlbC1idXR0b24ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDEwMCwgMTAwLCAxMDAsIDAuMik7XG4gIGNvbG9yOiAjZTBlMGUwO1xuICBib3gtc2hhZG93OiAwIDAgOHB4IHJnYmEoMTAwLCAxMDAsIDEwMCwgMC4yKTtcbn1cblxuLyogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgU1RZTEVTIFBPVVIgTEUgTU9EQUwgREUgRMOJVEFJTFMgREUgTk9USUZJQ0FUSU9OXG4gICA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG5cbi8qIFNlY3Rpb25zIGR1IG1vZGFsIGRlIGTDqXRhaWxzICovXG4ubm90aWZpY2F0aW9uLWRldGFpbC1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICBwYWRkaW5nOiAxcmVtO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24tZGV0YWlsLXNlY3Rpb24ge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDI0NywgMjU1LCAwLjA1KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbn1cblxuLm5vdGlmaWNhdGlvbi1kZXRhaWwtdGl0bGUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBmb250LXNpemU6IDFyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjNGY1ZmFkO1xuICBtYXJnaW4tYm90dG9tOiAwLjc1cmVtO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLWRldGFpbC10aXRsZSB7XG4gIGNvbG9yOiAjMDBmN2ZmO1xuICB0ZXh0LXNoYWRvdzogMCAwIDZweCByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xufVxuXG4vKiBJbmZvcm1hdGlvbnMgZGUgbCdleHDDqWRpdGV1ciAqL1xuLm5vdGlmaWNhdGlvbi1zZW5kZXItaW5mbyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC43NXJlbTtcbn1cblxuLm5vdGlmaWNhdGlvbi1zZW5kZXItYXZhdGFyIHtcbiAgd2lkdGg6IDQ4cHg7XG4gIGhlaWdodDogNDhweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgYm9yZGVyOiAycHggc29saWQgcmdiYSg3OSwgOTUsIDE3MywgMC4zKTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLm5vdGlmaWNhdGlvbi1zZW5kZXItYXZhdGFyIHtcbiAgYm9yZGVyOiAycHggc29saWQgcmdiYSgwLCAyNDcsIDI1NSwgMC4zKTtcbn1cblxuLm5vdGlmaWNhdGlvbi1zZW5kZXItZGV0YWlscyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG59XG5cbi5ub3RpZmljYXRpb24tc2VuZGVyLW5hbWUge1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzRmNWZhZDtcbiAgZm9udC1zaXplOiAxcmVtO1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLXNlbmRlci1uYW1lIHtcbiAgY29sb3I6ICMwMGY3ZmY7XG59XG5cbi5ub3RpZmljYXRpb24tdGltZXN0YW1wIHtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGNvbG9yOiAjYTBhMGEwO1xufVxuXG4vKiBDb250ZW51IGRlIGxhIG5vdGlmaWNhdGlvbiAqL1xuLm5vdGlmaWNhdGlvbi1jb250ZW50LWRldGFpbCB7XG4gIGJhY2tncm91bmQ6IHJnYmEoNzksIDk1LCAxNzMsIDAuMSk7XG4gIHBhZGRpbmc6IDAuNzVyZW07XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgY29sb3I6ICMzMzM7XG4gIGxpbmUtaGVpZ2h0OiAxLjU7XG4gIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzRmNWZhZDtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLm5vdGlmaWNhdGlvbi1jb250ZW50LWRldGFpbCB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4yKTtcbiAgY29sb3I6ICNlMGUwZTA7XG4gIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzAwZjdmZjtcbn1cblxuLm5vdGlmaWNhdGlvbi1tZXNzYWdlLWRldGFpbCB7XG4gIG1hcmdpbi10b3A6IDAuNzVyZW07XG4gIHBhZGRpbmc6IDAuNzVyZW07XG4gIGJhY2tncm91bmQ6IHJnYmEoNzksIDk1LCAxNzMsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGNvbG9yOiAjMzMzO1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLm5vdGlmaWNhdGlvbi1tZXNzYWdlLWRldGFpbCB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMjQ3LCAyNTUsIDAuMSk7XG4gIGNvbG9yOiAjZTBlMGUwO1xufVxuXG4vKiBHcmlsbGUgZCdpbmZvcm1hdGlvbnMgKi9cbi5ub3RpZmljYXRpb24taW5mby1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gIGdhcDogMC41cmVtO1xufVxuXG4ubm90aWZpY2F0aW9uLWluZm8taXRlbSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMC41cmVtO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDc5LCA5NSwgMTczLCAwLjA1KTtcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLWluZm8taXRlbSB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4yKTtcbn1cblxuLm5vdGlmaWNhdGlvbi1pbmZvLWxhYmVsIHtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgY29sb3I6ICM2NjY7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24taW5mby1sYWJlbCB7XG4gIGNvbG9yOiAjYTBhMGEwO1xufVxuXG4ubm90aWZpY2F0aW9uLWluZm8tdmFsdWUge1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzMzMztcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLm5vdGlmaWNhdGlvbi1pbmZvLXZhbHVlIHtcbiAgY29sb3I6ICNlMGUwZTA7XG59XG5cbi8qIEdyaWxsZSBkZXMgcGnDqGNlcyBqb2ludGVzICovXG4ubm90aWZpY2F0aW9uLWF0dGFjaG1lbnRzLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgZ2FwOiAwLjc1cmVtO1xufVxuXG4ubm90aWZpY2F0aW9uLWF0dGFjaG1lbnQtaXRlbSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC43NXJlbTtcbiAgcGFkZGluZzogMC43NXJlbTtcbiAgYmFja2dyb3VuZDogcmdiYSg3OSwgOTUsIDE3MywgMC4wNSk7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSg3OSwgOTUsIDE3MywgMC4xKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLm5vdGlmaWNhdGlvbi1hdHRhY2htZW50LWl0ZW0ge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMik7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbn1cblxuLm5vdGlmaWNhdGlvbi1hdHRhY2htZW50LWl0ZW06aG92ZXIge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDc5LCA5NSwgMTczLCAwLjEpO1xuICBib3JkZXItY29sb3I6IHJnYmEoNzksIDk1LCAxNzMsIDAuMyk7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24tYXR0YWNobWVudC1pdGVtOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAyNDcsIDI1NSwgMC4xKTtcbiAgYm9yZGVyLWNvbG9yOiByZ2JhKDAsIDI0NywgMjU1LCAwLjMpO1xufVxuXG4ubm90aWZpY2F0aW9uLWF0dGFjaG1lbnQtcHJldmlldyBpbWcge1xuICB3aWR0aDogNDhweDtcbiAgaGVpZ2h0OiA0OHB4O1xuICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7XG59XG5cbi5ub3RpZmljYXRpb24tYXR0YWNobWVudC1wcmV2aWV3IGltZzpob3ZlciB7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbn1cblxuLm5vdGlmaWNhdGlvbi1hdHRhY2htZW50LWljb24ge1xuICB3aWR0aDogNDhweDtcbiAgaGVpZ2h0OiA0OHB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYmFja2dyb3VuZDogcmdiYSg3OSwgOTUsIDE3MywgMC4yKTtcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xuICBmb250LXNpemU6IDEuNXJlbTtcbiAgY29sb3I6ICM0ZjVmYWQ7XG59XG5cbjpob3N0LWNvbnRleHQoLmRhcmspIC5ub3RpZmljYXRpb24tYXR0YWNobWVudC1pY29uIHtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAyNDcsIDI1NSwgMC4yKTtcbiAgY29sb3I6ICMwMGY3ZmY7XG59XG5cbi5ub3RpZmljYXRpb24tYXR0YWNobWVudC1pbmZvIHtcbiAgZmxleDogMTtcbiAgbWluLXdpZHRoOiAwO1xufVxuXG4ubm90aWZpY2F0aW9uLWF0dGFjaG1lbnQtbmFtZSB7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjMzMzO1xuICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcbn1cblxuOmhvc3QtY29udGV4dCguZGFyaykgLm5vdGlmaWNhdGlvbi1hdHRhY2htZW50LW5hbWUge1xuICBjb2xvcjogI2UwZTBlMDtcbn1cblxuLm5vdGlmaWNhdGlvbi1hdHRhY2htZW50LW1ldGEge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDAuNXJlbTtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGNvbG9yOiAjNjY2O1xufVxuXG46aG9zdC1jb250ZXh0KC5kYXJrKSAubm90aWZpY2F0aW9uLWF0dGFjaG1lbnQtbWV0YSB7XG4gIGNvbG9yOiAjYTBhMGEwO1xufVxuXG4ubm90aWZpY2F0aW9uLWF0dGFjaG1lbnQtYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMC4yNXJlbTtcbn1cblxuLyogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgQU5JTUFUSU9OUyBGTFVPUkVTQ0VOVEVTIFBPVVIgTCdIRVVSRVxuICAgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSAqL1xuXG4vKiBBbmltYXRpb24gZGUgcHVsc2F0aW9uIGZsdW9yZXNjZW50ZSBub3JtYWxlICovXG5Aa2V5ZnJhbWVzIGZsdW9yby1wdWxzZSB7XG4gIDAlIHtcbiAgICB0ZXh0LXNoYWRvdzogMCAwIDVweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjgpLCAwIDAgMTBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjYpLFxuICAgICAgMCAwIDE1cHggcmdiYSgwLCAyNTUsIDI1NSwgMC40KTtcbiAgICBib3gtc2hhZG93OiAwIDJweCAxMHB4IHJnYmEoMCwgMjU1LCAyNTUsIDAuNiksXG4gICAgICAwIDAgMjBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjMpLCBpbnNldCAwIDAgMTBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjIpO1xuICAgIGJvcmRlci1jb2xvcjogcmdiYSgwLCAyNTUsIDI1NSwgMC44KTtcbiAgfVxuICAxMDAlIHtcbiAgICB0ZXh0LXNoYWRvdzogMCAwIDhweCByZ2JhKDAsIDI1NSwgMjU1LCAxKSwgMCAwIDE1cHggcmdiYSgwLCAyNTUsIDI1NSwgMC44KSxcbiAgICAgIDAgMCAyNXB4IHJnYmEoMCwgMjU1LCAyNTUsIDAuNik7XG4gICAgYm94LXNoYWRvdzogMCAycHggMTVweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjgpLFxuICAgICAgMCAwIDMwcHggcmdiYSgwLCAyNTUsIDI1NSwgMC41KSwgaW5zZXQgMCAwIDE1cHggcmdiYSgwLCAyNTUsIDI1NSwgMC4zKTtcbiAgICBib3JkZXItY29sb3I6IHJnYmEoMCwgMjU1LCAyNTUsIDEpO1xuICB9XG59XG5cbi8qIEFuaW1hdGlvbiBkZSBwdWxzYXRpb24gZmx1b3Jlc2NlbnRlIGludGVuc2UgcG91ciBsZSBzdXJ2b2wgKi9cbkBrZXlmcmFtZXMgZmx1b3JvLXB1bHNlLWludGVuc2Uge1xuICAwJSB7XG4gICAgdGV4dC1zaGFkb3c6IDAgMCA4cHggcmdiYSgyNTUsIDI1NSwgMjU1LCAxKSwgMCAwIDE1cHggcmdiYSgwLCAyNTUsIDI1NSwgMC45KSxcbiAgICAgIDAgMCAyNXB4IHJnYmEoMCwgMjU1LCAyNTUsIDAuNyk7XG4gICAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjgpLFxuICAgICAgMCAwIDMwcHggcmdiYSgwLCAyNTUsIDI1NSwgMC41KSwgaW5zZXQgMCAwIDE1cHggcmdiYSgwLCAyNTUsIDI1NSwgMC4zKTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCkgc2NhbGUoMS4wNSk7XG4gIH1cbiAgMTAwJSB7XG4gICAgdGV4dC1zaGFkb3c6IDAgMCAxMnB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSksIDAgMCAyMHB4IHJnYmEoMCwgMjU1LCAyNTUsIDEpLFxuICAgICAgMCAwIDM1cHggcmdiYSgwLCAyNTUsIDI1NSwgMC45KTtcbiAgICBib3gtc2hhZG93OiAwIDZweCAyNXB4IHJnYmEoMCwgMjU1LCAyNTUsIDEpLCAwIDAgNDBweCByZ2JhKDAsIDI1NSwgMjU1LCAwLjcpLFxuICAgICAgaW5zZXQgMCAwIDIwcHggcmdiYSgwLCAyNTUsIDI1NSwgMC40KTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTNweCkgc2NhbGUoMS4wOCk7XG4gIH1cbn1cbiJdfQ== */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 92285:
/*!***************************************************************************!*\
  !*** ./src/app/views/front/notifications/notifications-routing.module.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationsRoutingModule: () => (/* binding */ NotificationsRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _notification_list_notification_list_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./notification-list/notification-list.component */ 63226);
/* harmony import */ var _messages_message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../messages/message-layout/message-layout.component */ 88076);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);





const routes = [{
  path: '',
  component: _messages_message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_1__.MessageLayoutComponent,
  data: {
    context: 'notifications'
  },
  children: [{
    path: '',
    component: _notification_list_notification_list_component__WEBPACK_IMPORTED_MODULE_0__.NotificationListComponent,
    data: {
      title: 'Notifications'
    }
  }]
}];
class NotificationsRoutingModule {
  static {
    this.ɵfac = function NotificationsRoutingModule_Factory(t) {
      return new (t || NotificationsRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: NotificationsRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](NotificationsRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
  });
})();

/***/ }),

/***/ 46532:
/*!*******************************************************************!*\
  !*** ./src/app/views/front/notifications/notifications.module.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationsModule: () => (/* binding */ NotificationsModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _notifications_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./notifications-routing.module */ 92285);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _notification_list_notification_list_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./notification-list/notification-list.component */ 63226);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/message.service */ 54537);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);






class NotificationsModule {
  static {
    this.ɵfac = function NotificationsModule_Factory(t) {
      return new (t || NotificationsModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
      type: NotificationsModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
      providers: [src_app_services_message_service__WEBPACK_IMPORTED_MODULE_2__.MessageService],
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _notifications_routing_module__WEBPACK_IMPORTED_MODULE_0__.NotificationsRoutingModule, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](NotificationsModule, {
    declarations: [_notification_list_notification_list_component__WEBPACK_IMPORTED_MODULE_1__.NotificationListComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _notifications_routing_module__WEBPACK_IMPORTED_MODULE_0__.NotificationsRoutingModule, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_notifications_notifications_module_ts.js.map