{"version": 3, "file": "src_app_views_front_plannings_plannings_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAS0B;AAEuD;;;;;;;;;;;;;;ICA/EM,6DAAA,EAA8C;IAA9CA,4DAAA,aAA8C;IAC5CA,uDAAA,aAAmC;IACnCA,4DAAA,WAA4C;IAAAA,oDAAA,qCAAyB;IAAAA,0DAAA,EAAI;;;;;;IAI3EA,6DAAA,EAA6I;IAA7IA,4DAAA,cAA6I;IAEzIA,4DAAA,EAA6F;IAA7FA,4DAAA,cAA6F;IAC3FA,uDAAA,eAAiN;IACnNA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,GAAW;IAAAA,0DAAA,EAAO;;;;IAAlBA,uDAAA,GAAW;IAAXA,+DAAA,CAAAS,MAAA,CAAAC,KAAA,CAAW;;;;;IA+BjBV,4DAAA,cAA6C;IAC3CA,4DAAA,EAA2E;IAA3EA,4DAAA,cAA2E;IACzEA,uDAAA,eAA+J;IAEjKA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,GAAmB;IAAAA,0DAAA,EAAO;;;;IAA1BA,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAW,MAAA,CAAAC,QAAA,CAAAC,IAAA,CAAmB;;;;;IAazBb,4DAAA,cAE6C;IACrCA,oDAAA,GAA0B;IAAAA,0DAAA,EAAO;;;;;IADpCA,yDAAA,oBAAAe,IAAA,aAAuC;IACpCf,uDAAA,GAA0B;IAA1BA,+DAAA,CAAAgB,cAAA,CAAAC,QAAA,CAA0B;;;;;;IA2H1BjB,6DAAA,EAAwE;IAAxEA,4DAAA,cAAwE;IACtEA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAmB,QAAA,CAAAC,IAAA,CAAAC,WAAA,MACF;;;;;;IAdNrB,4DAAA,aAE4C;IAGtCA,uDAAA,iBAA+D;;IAC/DA,4DAAA,cAAkD;IAChDA,4DAAA,EAAgF;IAAhFA,4DAAA,cAAgF;IAC9EA,uDAAA,eAAwH;IAC1HA,0DAAA,EAAM;IACNA,oDAAA,GACF;;;IAAAA,0DAAA,EAAM;IACNA,wDAAA,KAAAuB,yDAAA,kBAEM;IACRvB,0DAAA,EAAM;IACNA,6DAAA,EAAiC;IAAjCA,4DAAA,eAAiC;IACvBA,wDAAA,mBAAAyB,4EAAA;MAAA,MAAAC,WAAA,GAAA1B,2DAAA,CAAA4B,IAAA;MAAA,MAAAT,QAAA,GAAAO,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA9B,2DAAA;MAAA,OAASA,yDAAA,CAAA8B,OAAA,CAAAG,WAAA,CAAAd,QAAA,CAAAC,IAAA,CAAAc,EAAA,CAA0B;IAAA,EAAC;IAG1ClC,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAAmM;IACrMA,0DAAA,EAAM;IAERA,6DAAA,EAEqC;IAFrCA,4DAAA,kBAEqC;IAF7BA,wDAAA,mBAAAmC,4EAAAC,MAAA;MAAA,MAAAV,WAAA,GAAA1B,2DAAA,CAAA4B,IAAA;MAAA,MAAAT,QAAA,GAAAO,WAAA,CAAAG,SAAA;MAAA,MAAAQ,OAAA,GAAArC,2DAAA;MAASqC,OAAA,CAAAC,aAAA,CAAAnB,QAAA,CAAAC,IAAA,CAAAc,EAAA,CAA4B;MAAA,OAAElC,yDAAA,CAAAoC,MAAA,CAAAG,eAAA,EAAwB;IAAA,EAAE;IAGvEvC,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAAyM;IAC3MA,0DAAA,EAAM;;;;;IA3BVA,yDAAA,oBAAAwC,KAAA,aAAuC;IAG7BxC,uDAAA,GAA6C;IAA7CA,wDAAA,cAAAA,yDAAA,OAAAmB,QAAA,CAAAwB,KAAA,GAAA3C,4DAAA,CAA6C;IAKnDA,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,OAAAmB,QAAA,CAAA4B,KAAA,uBAAA/C,yDAAA,SAAAmB,QAAA,CAAA6B,GAAA,oBACF;IACMhD,uDAAA,GAA6B;IAA7BA,wDAAA,SAAAmB,QAAA,CAAAC,IAAA,kBAAAD,QAAA,CAAAC,IAAA,CAAAC,WAAA,CAA6B;;;;;IAtB7CrB,4DAAA,cAAyE;IAGnEA,4DAAA,EAAgF;IAAhFA,4DAAA,cAAgF;IAC9EA,uDAAA,eAAmK;IACrKA,0DAAA,EAAM;IACNA,oDAAA,GACF;;IAAAA,0DAAA,EAAO;IAETA,6DAAA,EAAsB;IAAtBA,4DAAA,aAAsB;IACpBA,wDAAA,IAAAiD,kDAAA,mBAiCK;IACPjD,0DAAA,EAAK;;;;IA5CsDA,wDAAA,cAAAkD,SAAA,CAAW;IAMlElD,uDAAA,GACF;IADEA,gEAAA,2BAAAA,yDAAA,OAAAmD,MAAA,CAAAC,YAAA,mBACF;IAGsBpD,uDAAA,GAAsB;IAAtBA,wDAAA,YAAAmD,MAAA,CAAAE,iBAAA,CAAsB;;;;;;;IA9JpDrD,6DAAA,EACyE;IADzEA,4DAAA,cACyE;IAApEA,wDAAA,wBAAAsD,iEAAA;MAAAtD,2DAAA,CAAAuD,IAAA;MAAA,MAAAC,OAAA,GAAAxD,2DAAA;MAAA,OAAcA,yDAAA,CAAAwD,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC,wBAAAC,iEAAA;MAAA1D,2DAAA,CAAAuD,IAAA;MAAA,MAAAI,OAAA,GAAA3D,2DAAA;MAAA,OAAeA,yDAAA,CAAA2D,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAjC;IAEpC5D,4DAAA,cAAyC;IACtBA,oDAAA,GAAoB;IAAAA,0DAAA,EAAK;IAC1CA,uDAAA,YAAgF;;IAClFA,0DAAA,EAAM;IAGNA,4DAAA,cAA+D;IAE3DA,4DAAA,EAA2E;IAA3EA,4DAAA,cAA2E;IACzEA,uDAAA,eAAsI;IACxIA,0DAAA,EAAM;IACNA,oDAAA,sBACF;IAAAA,0DAAA,EAAK;IACLA,6DAAA,EAAuB;IAAvBA,4DAAA,eAAuB;IACrBA,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAAmK;IACrKA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IACJA,oDAAA,YAAG;IAAAA,4DAAA,cAAQ;IAAAA,oDAAA,IAA4C;;IAAAA,0DAAA,EAAS;IAChEA,oDAAA,YAAG;IAAAA,4DAAA,cAAQ;IAAAA,oDAAA,IAA0C;;IAAAA,0DAAA,EAAS;IAIlEA,wDAAA,KAAA6D,6CAAA,kBAMM;IACR7D,0DAAA,EAAM;IAGNA,4DAAA,eAAuE;IAEnEA,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAA0L;IAC5LA,0DAAA,EAAM;IACNA,oDAAA,sBACF;IAAAA,0DAAA,EAAK;IACLA,6DAAA,EAA+B;IAA/BA,4DAAA,eAA+B;IAC7BA,wDAAA,KAAA8D,6CAAA,kBAIM;IACR9D,0DAAA,EAAM;IAIRA,4DAAA,eAAmE;IAG7DA,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAAmK;IACrKA,0DAAA,EAAM;IACNA,oDAAA,sCACF;IAAAA,0DAAA,EAAK;IACLA,6DAAA,EAA4D;IAA5DA,4DAAA,kBAA4D;IAApDA,wDAAA,mBAAA+D,gEAAA;MAAA/D,2DAAA,CAAAuD,IAAA;MAAA,MAAAS,OAAA,GAAAhE,2DAAA;MAAA,OAASA,yDAAA,CAAAgE,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IACjCjE,4DAAA,gBAAgC;IAC9BA,4DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAC9EA,uDAAA,gBAAuG;IACzGA,0DAAA,EAAM;IACNA,oDAAA,+BACF;IAAAA,0DAAA,EAAO;IAKXA,6DAAA,EAA6C;IAA7CA,4DAAA,eAA6C;IACnCA,wDAAA,mBAAAkE,gEAAA;MAAAlE,2DAAA,CAAAuD,IAAA;MAAA,MAAAY,OAAA,GAAAnE,2DAAA;MAAA,OAASA,yDAAA,CAAAmE,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9BpE,4DAAA,gBAAgC;IAC9BA,4DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAC9EA,uDAAA,gBAAmM;IACrMA,0DAAA,EAAM;IACNA,oDAAA,2BACF;IAAAA,0DAAA,EAAO;IAETA,6DAAA,EAA0D;IAA1DA,4DAAA,kBAA0D;IAAlDA,wDAAA,mBAAAqE,gEAAA;MAAArE,2DAAA,CAAAuD,IAAA;MAAA,MAAAe,OAAA,GAAAtE,2DAAA;MAAA,OAASA,yDAAA,CAAAsE,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAChCvE,4DAAA,gBAAgC;IAC9BA,4DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAC9EA,uDAAA,gBAAyM;IAC3MA,0DAAA,EAAM;IACNA,oDAAA,4BACF;IAAAA,0DAAA,EAAO;IAKXA,6DAAA,EAAwD;IAAxDA,4DAAA,eAAwD;IAIfA,oDAAA,2BAAc;IAAAA,0DAAA,EAAI;IACnDA,4DAAA,aAA4C;IAAAA,oDAAA,IAAoC;IAAAA,0DAAA,EAAI;IAEtFA,4DAAA,eAA4C;IAC1CA,4DAAA,EAA2F;IAA3FA,4DAAA,eAA2F;IACzFA,uDAAA,gBAAmV;IACrVA,0DAAA,EAAM;IAKZA,6DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAGzCA,oDAAA,oBAAO;IAAAA,0DAAA,EAAI;IAC5CA,4DAAA,aAA2C;IACzCA,oDAAA,IACF;;;IAAAA,0DAAA,EAAI;IAENA,4DAAA,eAA0C;IACxCA,4DAAA,EAAyF;IAAzFA,4DAAA,eAAyF;IACvFA,uDAAA,gBAAmK;IACrKA,0DAAA,EAAM;IAKZA,6DAAA,EAAoF;IAApFA,4DAAA,eAAoF;IAG7CA,oDAAA,oBAAY;IAAAA,0DAAA,EAAI;IACjDA,4DAAA,aAA4C;IAAAA,oDAAA,IAAwC;IAAAA,0DAAA,EAAI;IAE1FA,4DAAA,eAA2C;IACzCA,4DAAA,EAA0F;IAA1FA,4DAAA,eAA0F;IACxFA,uDAAA,gBAA0L;IAC5LA,0DAAA,EAAM;IAOdA,6DAAA,EAAgC;IAAhCA,4DAAA,eAAgC;IAI5BA,wDAAA,wBAAAwE,sFAAApC,MAAA;MAAApC,2DAAA,CAAAuD,IAAA;MAAA,MAAAkB,OAAA,GAAAzE,2DAAA;MAAA,OAAcA,yDAAA,CAAAyE,OAAA,CAAAC,cAAA,CAAAtC,MAAA,CAAAuC,GAAA,CAA0B;IAAA,EAAC;IAC3C3E,0DAAA,EAA0B;IAI5BA,wDAAA,KAAA4E,6CAAA,kBA6CM;IACR5E,0DAAA,EAAM;;;;IAlMgDA,wDAAA,eAAA6E,MAAA,CAAAC,SAAA,CAAwB;IAGjD9E,uDAAA,GAAW;IAAXA,wDAAA,cAAAkD,SAAA,CAAW;IACrBlD,uDAAA,GAAoB;IAApBA,+DAAA,CAAA6E,MAAA,CAAAjE,QAAA,CAAAmE,KAAA,CAAoB;IAChB/E,uDAAA,GAAsD;IAAtDA,wDAAA,cAAAA,yDAAA,QAAA6E,MAAA,CAAAjE,QAAA,CAAAS,WAAA,GAAArB,4DAAA,CAAsD;IAI/CA,uDAAA,GAAgC;IAAhCA,wDAAA,cAAA6E,MAAA,CAAAG,aAAA,CAAAC,IAAA,CAAgC;IAY7CjF,uDAAA,IAA4C;IAA5CA,+DAAA,CAAAA,yDAAA,SAAA6E,MAAA,CAAAjE,QAAA,CAAAsE,SAAA,gBAA4C;IAC5ClF,uDAAA,GAA0C;IAA1CA,+DAAA,CAAAA,yDAAA,SAAA6E,MAAA,CAAAjE,QAAA,CAAAuE,OAAA,gBAA0C;IAInDnF,uDAAA,GAAmB;IAAnBA,wDAAA,SAAA6E,MAAA,CAAAjE,QAAA,CAAAC,IAAA,CAAmB;IAUGb,uDAAA,GAAwC;IAAxCA,wDAAA,cAAA6E,MAAA,CAAAG,aAAA,CAAAI,YAAA,CAAwC;IAQrCpF,uDAAA,GAA0B;IAA1BA,wDAAA,YAAA6E,MAAA,CAAAjE,QAAA,CAAAwE,YAAA,CAA0B;IAS7BpF,uDAAA,GAAoC;IAApCA,wDAAA,cAAA6E,MAAA,CAAAG,aAAA,CAAAK,QAAA,CAAoC;IA4CZrF,uDAAA,IAAoC;IAApCA,+DAAA,EAAA6E,MAAA,CAAAjE,QAAA,CAAAyE,QAAA,kBAAAR,MAAA,CAAAjE,QAAA,CAAAyE,QAAA,CAAAC,MAAA,OAAoC;IAe9EtF,uDAAA,IACF;IADEA,gEAAA,MAAAA,yDAAA,SAAA6E,MAAA,CAAAjE,QAAA,CAAAsE,SAAA,uBAAAlF,yDAAA,SAAA6E,MAAA,CAAAjE,QAAA,CAAAuE,OAAA,oBACF;IAc4CnF,uDAAA,IAAwC;IAAxCA,+DAAA,EAAA6E,MAAA,CAAAjE,QAAA,CAAAwE,YAAA,kBAAAP,MAAA,CAAAjE,QAAA,CAAAwE,YAAA,CAAAE,MAAA,OAAwC;IAcxFtF,uDAAA,GAAqB;IAArBA,wDAAA,aAAA6E,MAAA,CAAAU,QAAA,CAAqB,WAAAV,MAAA,CAAAW,MAAA;IAOAxF,uDAAA,GAAkC;IAAlCA,wDAAA,SAAA6E,MAAA,CAAAxB,iBAAA,CAAAiC,MAAA,KAAkC;;;ADjI3D,MAAOG,uBAAuB;EAsBlCC,YACSC,KAAqB,EACrBC,MAAc,EACbC,eAAgC,EAChCC,cAA8B,EAC/BC,WAA4B,EAC3BC,GAAsB,EACtBC,SAAuB,EACvBC,YAA0B;IAP3B,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,YAAY,GAAZA,YAAY;IA5BtB,KAAAtF,QAAQ,GAAe,IAAI;IAC3B,KAAAuF,OAAO,GAAG,IAAI;IACd,KAAAzF,KAAK,GAAkB,IAAI;IAC3B,KAAA0F,SAAS,GAAG,KAAK;IACjB,KAAA/C,iBAAiB,GAAoB,EAAE;IACvC,KAAAD,YAAY,GAAgB,IAAI;IAChC,KAAA0B,SAAS,GAAG,SAAS;IAErB;IACA,KAAAuB,IAAI,GAAiB3G,0DAAY,CAAC4G,KAAK;IACvC,KAAAf,QAAQ,GAAS,IAAIgB,IAAI,EAAE;IAC3B,KAAAf,MAAM,GAAoB,EAAE;IAE5B;IACA,KAAAR,aAAa,GAAG;MACdC,IAAI,EAAE,KAAK;MACXG,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;KACX;EAWE;EAEHmB,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1B,aAAa,CAACC,IAAI,GAAG,IAAI;IAChC,CAAC,EAAE,GAAG,CAAC;IAEPyB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1B,aAAa,CAACI,YAAY,GAAG,IAAI;IACxC,CAAC,EAAE,GAAG,CAAC;IAEPsB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1B,aAAa,CAACK,QAAQ,GAAG,IAAI;IACpC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAoB,mBAAmBA,CAAA;IACjB,MAAMvE,EAAE,GAAG,IAAI,CAACyD,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAAC3E,EAAE,EAAE;MACP,IAAI,CAACiE,OAAO,GAAG,KAAK;MACpB,IAAI,CAACD,YAAY,CAACxF,KAAK,CACrB,sBAAsB,EACtB,2BAA2B,CAC5B;MACD;;IAGF,IAAI,CAACmF,eAAe,CAACiB,eAAe,CAAC5E,EAAE,CAAC,CAAC6E,SAAS,CAAC;MACjDC,IAAI,EAAGpG,QAAa,IAAI;QACtB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ;QACjC,IAAI,CAACwF,SAAS,GAAGxF,QAAQ,CAACA,QAAQ,CAACqG,QAAQ,CAACC,GAAG,KAAK,IAAI,CAACnB,WAAW,CAACoB,gBAAgB,EAAE;QACvF,IAAI,CAAChB,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,CAACX,MAAM,GAAG,IAAI,CAAC5E,QAAQ,CAACyE,QAAQ,CAAC+B,GAAG,CAAC,CAACC,OAAY,EAAEC,KAAa,KAAI;UACvE,MAAMC,QAAQ,GAAG,GAAGF,OAAO,CAACG,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIJ,OAAO,CAACK,UAAU,KAAK;UAC5E,MAAMC,MAAM,GAAG,GAAGN,OAAO,CAACG,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIJ,OAAO,CAACO,QAAQ,KAAK;UAExE;UACA,MAAMC,GAAG,GAAIP,KAAK,GAAG,GAAG,GAAI,GAAG,CAAC,CAAC;UAEjC,OAAO;YACLvE,KAAK,EAAE,IAAIwD,IAAI,CAACgB,QAAQ,CAAC;YACzBvE,GAAG,EAAE,IAAIuD,IAAI,CAACoB,MAAM,CAAC;YACrBhF,KAAK,EAAE0E,OAAO,CAACtC,KAAK;YACpB+C,MAAM,EAAE,KAAK;YACbC,KAAK,EAAE;cACLC,OAAO,EAAE,OAAOH,GAAG,aAAa;cAChCI,SAAS,EAAE,OAAOJ,GAAG;aACtB;YACDzG,IAAI,EAAE;cACJC,WAAW,EAAEgG,OAAO,CAAChG,WAAW,IAAI,EAAE;cACtCa,EAAE,EAAEmF,OAAO,CAACH;;WAEf;QACH,CAAC,CAAC;QAEF,IAAI,CAAClB,GAAG,CAACkC,aAAa,EAAE;MAC1B,CAAC;MACDxH,KAAK,EAAGyH,GAAQ,IAAI;QAClB,IAAI,CAAChC,OAAO,GAAG,KAAK;QACpBiC,OAAO,CAAC1H,KAAK,CAAC,SAAS,EAAEyH,GAAG,CAAC;QAE7B,IAAIA,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAACnC,YAAY,CAACoC,YAAY,CAAC,uBAAuB,EAAEH,GAAG,CAACE,MAAM,CAAC;SACpE,MAAM,IAAIF,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAACnC,YAAY,CAACxF,KAAK,CACrB,sBAAsB,EACtB,qDAAqD,CACtD;SACF,MAAM;UACL,MAAM6H,YAAY,GAAGJ,GAAG,CAACzH,KAAK,EAAE8H,OAAO,IAAI,uCAAuC;UAClF,IAAI,CAACtC,YAAY,CAACxF,KAAK,CACrB,sBAAsB,EACtB6H,YAAY,CACb;;MAEL;KACD,CAAC;EACJ;EAEA7D,cAAcA,CAACC,GAAyB;IACtC,IAAI,CAACvB,YAAY,GAAGuB,GAAG,CAAC6C,IAAI;IAC5B,IAAI,CAACnE,iBAAiB,GAAGsB,GAAG,CAACa,MAAM;IAEnC;IACA,IAAIb,GAAG,CAACa,MAAM,CAACF,MAAM,GAAG,CAAC,EAAE;MACzB;MACAoB,UAAU,CAAC,MAAK;QACd,MAAM+B,gBAAgB,GAAGC,QAAQ,CAACC,aAAa,CAAC,aAAa,CAAC;QAC9D,IAAIF,gBAAgB,EAAE;UACpBA,gBAAgB,CAACG,cAAc,CAAC;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAS,CAAE,CAAC;;MAE7E,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEA;EACArF,gBAAgBA,CAAA;IACd,IAAI,CAACqB,SAAS,GAAG,SAAS;EAC5B;EAEAlB,gBAAgBA,CAAA;IACd,IAAI,CAACkB,SAAS,GAAG,SAAS;EAC5B;EAGAV,YAAYA,CAAA;IACV,IAAI,IAAI,CAACxD,QAAQ,EAAE;MACjB,IAAI,CAACgF,MAAM,CAACmD,QAAQ,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAACnI,QAAQ,CAACsG,GAAG,CAAC,CAAC;;EAEhE;EAEA3C,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC3D,QAAQ,IAAIoI,OAAO,CAAC,wCAAwC,CAAC,EAAE;MACtE,IAAI,CAACnD,eAAe,CAACtB,cAAc,CAAC,IAAI,CAAC3D,QAAQ,CAACsG,GAAG,CAAC,CAACH,SAAS,CAAC;QAC/DC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACd,YAAY,CAAC+C,OAAO,CACvB,mBAAmB,EACnB,wCAAwC,CACzC;UACD,IAAI,CAACrD,MAAM,CAACmD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDrI,KAAK,EAAGyH,GAAG,IAAI;UACbC,OAAO,CAAC1H,KAAK,CAAC,4CAA4C,EAAEyH,GAAG,CAAC;UAEhE,IAAIA,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;YACtB,IAAI,CAACnC,YAAY,CAACoC,YAAY,CAAC,uBAAuB,EAAEH,GAAG,CAACE,MAAM,CAAC;WACpE,MAAM,IAAIF,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;YAC7B,IAAI,CAACnC,YAAY,CAACxF,KAAK,CACrB,cAAc,EACd,qDAAqD,CACtD;WACF,MAAM;YACL,MAAM6H,YAAY,GAAGJ,GAAG,CAACzH,KAAK,EAAE8H,OAAO,IAAI,2CAA2C;YACtF,IAAI,CAACtC,YAAY,CAACxF,KAAK,CACrB,uBAAuB,EACvB6H,YAAY,EACZ,IAAI,CACL;;QAEL;OACD,CAAC;;EAEN;EAEAtE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACrD,QAAQ,EAAE;MACjB;MACA,IAAI,CAACgF,MAAM,CAACmD,QAAQ,CAAC,CAAC,2BAA2B,CAAC,EAAE;QAClDG,WAAW,EAAE;UAAEC,UAAU,EAAE,IAAI,CAACvI,QAAQ,CAACsG;QAAG;OAC7C,CAAC;;EAEN;EAEA;;;;EAIAjF,WAAWA,CAACmH,SAAiB;IAC3B,IAAIA,SAAS,EAAE;MACb,IAAI,CAACxD,MAAM,CAACmD,QAAQ,CAAC,CAAC,oBAAoB,EAAEK,SAAS,CAAC,CAAC;;EAE3D;EAEA;;;;EAIA9G,aAAaA,CAAC8G,SAAiB;IAC7B,IAAIJ,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACjE,IAAI,CAAClD,cAAc,CAACxD,aAAa,CAAC8G,SAAS,CAAC,CAACrC,SAAS,CAAC;QACrDC,IAAI,EAAGqC,QAAQ,IAAI;UACjBjB,OAAO,CAACkB,GAAG,CAAC,gCAAgC,EAAED,QAAQ,CAAC;UAEvD,IAAI,CAACnD,YAAY,CAAC+C,OAAO,CACvB,mBAAmB,EACnB,wCAAwC,CACzC;UAED;UACA,IAAI,CAACxC,mBAAmB,EAAE;UAE1B;UACA,IAAI,CAACpD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACkG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACpI,IAAI,EAAEc,EAAE,KAAKkH,SAAS,CAAC;QAC/F,CAAC;QACD1I,KAAK,EAAGA,KAAK,IAAI;UACf0H,OAAO,CAAC1H,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAEtD,IAAIA,KAAK,CAAC2H,MAAM,KAAK,GAAG,EAAE;YACxB,IAAI,CAACnC,YAAY,CAACoC,YAAY,CAAC,yBAAyB,EAAE5H,KAAK,CAAC2H,MAAM,CAAC;WACxE,MAAM,IAAI3H,KAAK,CAAC2H,MAAM,KAAK,GAAG,EAAE;YAC/B,IAAI,CAACnC,YAAY,CAACxF,KAAK,CACrB,cAAc,EACd,qDAAqD,CACtD;WACF,MAAM;YACL,MAAM6H,YAAY,GAAG7H,KAAK,CAACA,KAAK,EAAE8H,OAAO,IAAI,6CAA6C;YAC1F,IAAI,CAACtC,YAAY,CAACxF,KAAK,CACrB,uBAAuB,EACvB6H,YAAY,EACZ,IAAI,CACL;;QAEL;OACD,CAAC;;EAEN;EAEAkB,iBAAiBA,CAACpI,WAAmB;IACnC;IACA,MAAMqI,aAAa,GAAGrI,WAAW,CAACsI,OAAO,CACvC,4BAA4B,EAC5B,wEAAwE,CACzE;IAED;IACA,OAAO,IAAI,CAAC1D,SAAS,CAAC2D,uBAAuB,CAACF,aAAa,CAAC;EAC9D;;;uBA5PWjE,uBAAuB,EAAAzF,+DAAA,CAAA8J,2DAAA,GAAA9J,+DAAA,CAAA8J,mDAAA,GAAA9J,+DAAA,CAAAiK,2EAAA,GAAAjK,+DAAA,CAAAmK,yEAAA,GAAAnK,+DAAA,CAAAqK,2EAAA,GAAArK,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAwK,mEAAA,GAAAxK,+DAAA,CAAA0K,qEAAA;IAAA;EAAA;;;YAAvBjF,uBAAuB;MAAAmF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9CpClL,4DAAA,aAAyC;UAE/BA,wDAAA,mBAAAoL,yDAAA;YAAA,OAASD,GAAA,CAAAvF,MAAA,CAAAmD,QAAA,EAAiB,YAAY,EAAE;UAAA,EAAC;UAE/C/I,4DAAA,EAAgG;UAAhGA,4DAAA,aAAgG;UAC9FA,uDAAA,cAA0L;UAC5LA,0DAAA,EAAM;UACNA,oDAAA,6BACF;UAAAA,0DAAA,EAAS;UAGTA,wDAAA,IAAAqL,sCAAA,iBAGM;UAGNrL,wDAAA,IAAAsL,sCAAA,iBAOM;UAGNtL,wDAAA,IAAAuL,sCAAA,mBAmMM;UACRvL,0DAAA,EAAM;;;UApNEA,uDAAA,GAAa;UAAbA,wDAAA,SAAAmL,GAAA,CAAAhF,OAAA,CAAa;UAMbnG,uDAAA,GAAW;UAAXA,wDAAA,SAAAmL,GAAA,CAAAzK,KAAA,CAAW;UAUXV,uDAAA,GAA0B;UAA1BA,wDAAA,UAAAmL,GAAA,CAAAhF,OAAA,IAAAgF,GAAA,CAAAvK,QAAA,CAA0B;;;;;;mBDTpB;QACV;QACAjB,6DAAO,CAAC,UAAU,EAAE,CAClBI,gEAAU,CAAC,QAAQ,EAAE,CACnBF,2DAAK,CAAC;UAAE2L,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAkB,CAAE,CAAC,EACpD3L,6DAAO,CAAC,eAAe,EAAED,2DAAK,CAAC;UAAE2L,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC5E,CAAC,CACH,CAAC;QAEF;QACA9L,6DAAO,CAAC,WAAW,EAAE,CACnBC,2DAAK,CAAC,SAAS,EAAEC,2DAAK,CAAC;UACrB4L,SAAS,EAAE,UAAU;UACrBC,SAAS,EAAE;SACZ,CAAC,CAAC,EACH9L,2DAAK,CAAC,SAAS,EAAEC,2DAAK,CAAC;UACrB4L,SAAS,EAAE,aAAa;UACxBC,SAAS,EAAE;SACZ,CAAC,CAAC,EACH3L,gEAAU,CAAC,oBAAoB,EAAE,CAC/BD,6DAAO,CAAC,kBAAkB,CAAC,CAC5B,CAAC,EACFC,gEAAU,CAAC,oBAAoB,EAAE,CAC/BD,6DAAO,CAAC,kBAAkB,CAAC,CAC5B,CAAC,CACH,CAAC;MACH;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;AE3CiE;;;;;;;;;;ICWhEE,4DAAA,cAAuG;IACrGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA4L,MAAA,CAAArD,YAAA,MACF;;;;;IAyBUvI,4DAAA,WAA8D;IAAAA,oDAAA,+BAAwB;IAAAA,0DAAA,EAAO;;;;;IAC7FA,4DAAA,WAA+D;IAAAA,oDAAA,wCAA4B;IAAAA,0DAAA,EAAO;;;;;IAHpGA,4DAAA,cAA0I;IACxIA,uDAAA,YAA8C;IAC9CA,wDAAA,IAAA6L,4CAAA,mBAA6F;IAC7F7L,wDAAA,IAAA8L,4CAAA,mBAAkG;IACpG9L,0DAAA,EAAM;;;;;;IAFGA,uDAAA,GAAqD;IAArDA,wDAAA,UAAA+L,OAAA,GAAAtL,MAAA,CAAAuL,YAAA,CAAAnF,GAAA,4BAAAkF,OAAA,CAAAE,MAAA,kBAAAF,OAAA,CAAAE,MAAA,aAAqD;IACrDjM,uDAAA,GAAsD;IAAtDA,wDAAA,UAAAkM,OAAA,GAAAzL,MAAA,CAAAuL,YAAA,CAAAnF,GAAA,4BAAAqF,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IAsEjEjM,4DAAA,iBAA4E;IAC1EA,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IAFmCA,wDAAA,UAAAmM,OAAA,CAAAjF,GAAA,CAAkB;IAC5DlH,uDAAA,GACF;IADEA,gEAAA,MAAAmM,OAAA,CAAAlL,QAAA,MACF;;;;;IAEFjB,4DAAA,cAAwJ;IACtJA,uDAAA,YAA8C;IAC9CA,oDAAA,2DACF;IAAAA,0DAAA,EAAM;;;;;IAyCNA,uDAAA,YAAmD;;;;;IACnDA,uDAAA,YAA6D;;;ADhJ/D,MAAOoM,qBAAqB;EAMhC1G,YACU2G,EAAe,EACfC,WAAwB,EACxBzG,eAAgC,EAChCD,MAAc,EACdM,YAA0B;IAJ1B,KAAAmG,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAzG,eAAe,GAAfA,eAAe;IACf,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAM,YAAY,GAAZA,YAAY;IATtB,KAAAqG,SAAS,GAAG,KAAK;IACjB,KAAAhE,YAAY,GAAkB,IAAI;IAClC,KAAAiE,MAAM,GAAuB,IAAI,CAACF,WAAW,CAACG,WAAW,EAAE;EAQxD;EAEHjG,QAAQA,CAAA;IACN,IAAI,CAACwF,YAAY,GAAG,IAAI,CAACK,EAAE,CAACK,KAAK,CAAC;MAChC3H,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC4G,sDAAU,CAACgB,QAAQ,EAAEhB,sDAAU,CAACiB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DvL,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBR,IAAI,EAAE,CAAC,EAAE,CAAC;MACVqE,SAAS,EAAE,CAAC,EAAE,EAAEyG,sDAAU,CAACgB,QAAQ,CAAC;MACpCxH,OAAO,EAAE,CAAC,EAAE,EAAEwG,sDAAU,CAACgB,QAAQ,CAAC;MAClCvH,YAAY,EAAE,CAAC,EAAE,EAAEuG,sDAAU,CAACgB,QAAQ;KACvC,CAAC;EACJ;EAEAE,MAAMA,CAAA;IACJzE,OAAO,CAACkB,GAAG,CAAC,sBAAsB,CAAC;IACnClB,OAAO,CAACkB,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC0C,YAAY,CAACc,KAAK,CAAC;IACnD1E,OAAO,CAACkB,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC0C,YAAY,CAACe,KAAK,CAAC;IAEpD,IAAI,IAAI,CAACf,YAAY,CAACc,KAAK,EAAE;MAC3B,IAAI,CAACP,SAAS,GAAG,IAAI;MACrB,IAAI,CAAChE,YAAY,GAAG,IAAI;MAExB;MACA,MAAMyE,UAAU,GAAG,IAAI,CAAChB,YAAY,CAACe,KAAK;MAE1C;MACA,MAAME,YAAY,GAAG;QACnBlI,KAAK,EAAEiI,UAAU,CAACjI,KAAK;QACvB1D,WAAW,EAAE2L,UAAU,CAAC3L,WAAW,IAAI,EAAE;QACzC6D,SAAS,EAAE8H,UAAU,CAAC9H,SAAS;QAC/BC,OAAO,EAAE6H,UAAU,CAAC7H,OAAO;QAC3BtE,IAAI,EAAEmM,UAAU,CAACnM,IAAI,IAAI,EAAE;QAC3BuE,YAAY,EAAE4H,UAAU,CAAC5H,YAAY,IAAI;OAC1C;MAEDgD,OAAO,CAACkB,GAAG,CAAC,0BAA0B,EAAE2D,YAAY,CAAC;MAErD;MACA,IAAI,CAACpH,eAAe,CAACqH,cAAc,CAACD,YAAmB,CAAC,CAAClG,SAAS,CAAC;QACjEC,IAAI,EAAGmG,WAAgB,IAAI;UACzB/E,OAAO,CAACkB,GAAG,CAAC,gCAAgC,EAAE6D,WAAW,CAAC;UAC1D,IAAI,CAACZ,SAAS,GAAG,KAAK;UAEtB;UACA,IAAI,CAACrG,YAAY,CAACkH,WAAW,CAAC,oCAAoC,CAAC;UAEnE;UACA,IAAI,CAACxH,MAAM,CAACmD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACDrI,KAAK,EAAGA,KAAU,IAAI;UACpB0H,OAAO,CAAC1H,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD0H,OAAO,CAAC1H,KAAK,CACX,gBAAgB,EAChBA,KAAK,CAACA,KAAK,IAAIA,KAAK,CAAC8H,OAAO,IAAI9H,KAAK,CACtC;UACD,IAAI,CAAC6L,SAAS,GAAG,KAAK;UAEtB;UACA,IAAI7L,KAAK,CAAC2H,MAAM,KAAK,GAAG,EAAE;YACxB,IAAI,CAACnC,YAAY,CAACmH,SAAS,CACzB,kEAAkE,CACnE;WACF,MAAM,IAAI3M,KAAK,CAAC2H,MAAM,KAAK,GAAG,EAAE;YAC/B,IAAI,CAACnC,YAAY,CAACmH,SAAS,CACzB,iDAAiD,CAClD;WACF,MAAM;YACL;YACA,MAAM9E,YAAY,GAChB7H,KAAK,CAACA,KAAK,EAAE8H,OAAO,IACpB,yDAAyD;YAC3D,IAAI,CAACtC,YAAY,CAACmH,SAAS,CAAC9E,YAAY,EAAE,IAAI,CAAC;;QAEnD;OACD,CAAC;KACH,MAAM;MACLH,OAAO,CAACkB,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACgE,uBAAuB,EAAE,CAAC;MAEtE;MACA,IAAI,CAACC,oBAAoB,EAAE;MAE3B,IAAI,CAACrH,YAAY,CAACsH,WAAW,CAC3B,gEAAgE,CACjE;;EAEL;EAEA;EACAF,uBAAuBA,CAAA;IACrB,MAAMrB,MAAM,GAAQ,EAAE;IACtBwB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1B,YAAY,CAAC2B,QAAQ,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAI;MACtD,MAAMC,OAAO,GAAG,IAAI,CAAC9B,YAAY,CAACnF,GAAG,CAACgH,GAAG,CAAC;MAC1C,IAAIC,OAAO,IAAIA,OAAO,CAAC7B,MAAM,EAAE;QAC7BA,MAAM,CAAC4B,GAAG,CAAC,GAAGC,OAAO,CAAC7B,MAAM;;IAEhC,CAAC,CAAC;IACF,OAAOA,MAAM;EACf;EAEA;EACAsB,oBAAoBA,CAAA;IAClBE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1B,YAAY,CAAC2B,QAAQ,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAI;MACtD,MAAMC,OAAO,GAAG,IAAI,CAAC9B,YAAY,CAACnF,GAAG,CAACgH,GAAG,CAAC;MAC1C,IAAIC,OAAO,EAAE;QACXA,OAAO,CAACC,aAAa,EAAE;;IAE3B,CAAC,CAAC;EACJ;;;uBAvHW3B,qBAAqB,EAAApM,+DAAA,CAAA8J,uDAAA,GAAA9J,+DAAA,CAAAiK,mEAAA,GAAAjK,+DAAA,CAAAmK,2EAAA,GAAAnK,+DAAA,CAAAqK,mDAAA,GAAArK,+DAAA,CAAAwK,qEAAA;IAAA;EAAA;;;YAArB4B,qBAAqB;MAAAxB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkD,+BAAAhD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCflClL,4DAAA,aAAmD;UAI7CA,uDAAA,WAAyD;UACzDA,oDAAA,yBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,WAAgC;UAAAA,oDAAA,6EAAuD;UAAAA,0DAAA,EAAI;UAG7FA,4DAAA,cAAyH;UAAxFA,wDAAA,sBAAAmO,wDAAA;YAAA,OAAYhD,GAAA,CAAA0B,MAAA,EAAQ;UAAA,EAAC;UAEpD7M,wDAAA,IAAAoO,oCAAA,iBAEM;UAENpO,4DAAA,aAAoC;UAI9BA,uDAAA,aAAuD;UACvDA,oDAAA,0CACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAmD;UAI7CA,uDAAA,aAA+C;UAC/CA,oDAAA,iBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAME;UACFA,wDAAA,KAAAqO,qCAAA,kBAIM;UACRrO,0DAAA,EAAM;UAGNA,4DAAA,WAAK;UAEDA,uDAAA,aAA0D;UAC1DA,oDAAA,sBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAKE;UACJA,0DAAA,EAAM;UAKVA,4DAAA,eAA4F;UAExFA,uDAAA,aAAuD;UACvDA,oDAAA,kCACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAmD;UAI7CA,uDAAA,aAAuD;UACvDA,oDAAA,8BACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAIE;UACJA,0DAAA,EAAM;UAGNA,4DAAA,WAAK;UAEDA,uDAAA,aAAuD;UACvDA,oDAAA,uBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAIE;UACJA,0DAAA,EAAM;UAKVA,4DAAA,eAAkG;UAE9FA,uDAAA,aAAkD;UAClDA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,iBAA+D;UAC7DA,uDAAA,aAAyD;UACzDA,oDAAA,8CACF;UAAAA,0DAAA,EAAQ;UACRA,4DAAA,kBAIC;UACCA,wDAAA,KAAAsO,wCAAA,qBAES;;UACXtO,0DAAA,EAAS;UACTA,wDAAA,KAAAuO,qCAAA,kBAGM;UACNvO,4DAAA,aAAyC;UACvCA,uDAAA,aAAuC;UACvCA,oDAAA,+EACF;UAAAA,0DAAA,EAAI;UAINA,4DAAA,eAAkG;UAE9FA,uDAAA,aAAsD;UACtDA,oDAAA,qBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,iBAA8D;UAC5DA,uDAAA,aAAgD;UAChDA,oDAAA,sCACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,oBAKY;UACdA,0DAAA,EAAM;UAIRA,4DAAA,eAAgG;UAK5FA,uDAAA,aAAiC;UACjCA,oDAAA,iBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAKC;UAHCA,wDAAA,mBAAAwO,wDAAA;YAAA,OAASrD,GAAA,CAAA0B,MAAA,EAAQ;UAAA,EAAC;UAIlB7M,wDAAA,KAAAyO,mCAAA,gBAAmD;UACnDzO,wDAAA,KAAA0O,mCAAA,gBAA6D;UAC7D1O,oDAAA,IACF;UAAAA,0DAAA,EAAS;;;;;;UAvJPA,uDAAA,GAA0B;UAA1BA,wDAAA,cAAAmL,GAAA,CAAAa,YAAA,CAA0B;UAExBhM,uDAAA,GAAkB;UAAlBA,wDAAA,SAAAmL,GAAA,CAAA5C,YAAA,CAAkB;UAsBdvI,uDAAA,IAAiG;UAAjGA,yDAAA,qBAAA4O,OAAA,GAAAzD,GAAA,CAAAa,YAAA,CAAAnF,GAAA,4BAAA+H,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAzD,GAAA,CAAAa,YAAA,CAAAnF,GAAA,4BAAA+H,OAAA,CAAAE,OAAA,EAAiG;UAG7F9O,uDAAA,GAA8E;UAA9EA,wDAAA,WAAA+O,OAAA,GAAA5D,GAAA,CAAAa,YAAA,CAAAnF,GAAA,4BAAAkI,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA5D,GAAA,CAAAa,YAAA,CAAAnF,GAAA,4BAAAkI,OAAA,CAAAD,OAAA,EAA8E;UAyE7D9O,uDAAA,IAAiB;UAAjBA,wDAAA,YAAAA,yDAAA,SAAAmL,GAAA,CAAAqB,MAAA,EAAiB;UAItCxM,uDAAA,GAA4F;UAA5FA,wDAAA,WAAAgP,OAAA,GAAA7D,GAAA,CAAAa,YAAA,CAAAnF,GAAA,mCAAAmI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAA7D,GAAA,CAAAa,YAAA,CAAAnF,GAAA,mCAAAmI,OAAA,CAAAF,OAAA,EAA4F;UAyClG9O,uDAAA,IAA8C;UAA9CA,wDAAA,aAAAmL,GAAA,CAAAoB,SAAA,IAAApB,GAAA,CAAAa,YAAA,CAAA6C,OAAA,CAA8C;UAGjB7O,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAmL,GAAA,CAAAoB,SAAA,CAAgB;UACLvM,uDAAA,GAAe;UAAfA,wDAAA,SAAAmL,GAAA,CAAAoB,SAAA,CAAe;UACvDvM,uDAAA,GACF;UADEA,gEAAA,MAAAmL,GAAA,CAAAoB,SAAA,uDACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7JkE;AAW3C;;;;;;;;;;;ICIzBvM,6DAAA,EAA+C;IAA/CA,4DAAA,cAA+C;IAEvCA,uDAAA,cAA+F;IAE/FA,4DAAA,cAAiH;IAC7GA,4DAAA,EAA8G;IAA9GA,4DAAA,cAA8G;IAC1GA,uDAAA,eAAmK;IACvKA,0DAAA,EAAM;IAGdA,6DAAA,EAA4C;IAA5CA,4DAAA,YAA4C;IAAAA,oDAAA,kCAA2B;IAAAA,0DAAA,EAAI;;;;;;IAM/EA,6DAAA,EAAwG;IAAxGA,4DAAA,cAAwG;IACpGA,4DAAA,EAAqG;IAArGA,4DAAA,cAAqG;IACjGA,uDAAA,eAA4M;IAChNA,0DAAA,EAAM;IACNA,6DAAA,EAAmD;IAAnDA,4DAAA,aAAmD;IAAAA,oDAAA,gCAAyB;IAAAA,0DAAA,EAAK;IACjFA,4DAAA,YAA8B;IAAAA,oDAAA,2FAAqE;IAAAA,0DAAA,EAAI;IACvGA,4DAAA,YACiK;IAC7JA,4DAAA,EAAmH;IAAnHA,4DAAA,cAAmH;IAC/GA,uDAAA,cAAuG;IAC3GA,0DAAA,EAAM;IACNA,oDAAA,gCACJ;IAAAA,0DAAA,EAAI;;;;;;;;;;;;IASJA,4DAAA,cAI6G;IAFxGA,wDAAA,wBAAAqP,sEAAA;MAAA,MAAA3N,WAAA,GAAA1B,2DAAA,CAAAsP,GAAA;MAAA,MAAAC,IAAA,GAAA7N,WAAA,CAAA4F,KAAA;MAAA,MAAAkI,MAAA,GAAAxP,2DAAA;MAAA,OAAcA,yDAAA,CAAAwP,MAAA,CAAAC,YAAA,CAAAF,IAAA,CAAe;IAAA,EAAC,wBAAAG,sEAAA;MAAA1P,2DAAA,CAAAsP,GAAA;MAAA,MAAAK,MAAA,GAAA3P,2DAAA;MAAA,OAChBA,yDAAA,CAAA2P,MAAA,CAAAC,YAAA,EAAc;IAAA,EADE;IAG/B5P,4DAAA,cAA8C;IAI9BA,oDAAA,GACJ;IAAAA,0DAAA,EAAI;IAERA,uDAAA,YAA6G;;IACjHA,0DAAA,EAAM;IACNA,4DAAA,iBAC+E;IADvEA,wDAAA,mBAAA6P,oEAAAzN,MAAA;MAAA,MAAAV,WAAA,GAAA1B,2DAAA,CAAAsP,GAAA;MAAA,MAAAQ,WAAA,GAAApO,WAAA,CAAAG,SAAA;MAAA,MAAAkO,MAAA,GAAA/P,2DAAA;MAAS+P,MAAA,CAAAxL,cAAA,CAAAuL,WAAA,CAAA5I,GAAA,CAA4B;MAAA,OAAElH,yDAAA,CAAAoC,MAAA,CAAAG,eAAA,EAAwB;IAAA,EAAE;IAErEvC,4DAAA,EAA8G;IAA9GA,4DAAA,cAA8G;IAC1GA,uDAAA,gBAAyM;IAC7MA,0DAAA,EAAM;IAIdA,6DAAA,EAAwE;IAAxEA,4DAAA,eAAwE;IACpEA,4DAAA,EAAgG;IAAhGA,4DAAA,eAAgG;IAC5FA,uDAAA,gBAAmK;IACvKA,0DAAA,EAAM;IACNA,oDAAA,IACJ;;;IAAAA,0DAAA,EAAM;IAENA,6DAAA,EAAkF;IAAlFA,4DAAA,eAAkF;IAKtEA,4DAAA,EACkI;IADlIA,4DAAA,eACkI;IAC9HA,uDAAA,gBAAmK;IAGvKA,0DAAA,EAAM;IAENA,6DAAA,EAAQ;IAARA,4DAAA,cAAQ;IAAAA,oDAAA,IAAoC;IAAAA,0DAAA,EAAS;IAAAA,oDAAA,8BACzD;IAAAA,0DAAA,EAAO;IAEXA,4DAAA,aAEsC;IAFnCA,wDAAA,mBAAAgQ,gEAAA;MAAA,MAAAtO,WAAA,GAAA1B,2DAAA,CAAAsP,GAAA;MAAA,MAAAQ,WAAA,GAAApO,WAAA,CAAAG,SAAA;MAAA,MAAAoO,OAAA,GAAAjQ,2DAAA;MAAA,OAASA,yDAAA,CAAAiQ,OAAA,CAAAC,UAAA,CAAAJ,WAAA,CAAA5I,GAAA,CAAwB;IAAA,EAAC;IAGjClH,oDAAA,kCACJ;IAAAA,0DAAA,EAAI;;;;;;IA/CPA,wDAAA,eAAAW,MAAA,CAAAwP,YAAA,CAAAZ,IAAA,EAA8B;IAQfvP,uDAAA,GACJ;IADIA,gEAAA,MAAA8P,WAAA,CAAA/K,KAAA,MACJ;IAEoB/E,uDAAA,GAAgF;IAAhFA,wDAAA,cAAAA,yDAAA,OAAA8P,WAAA,CAAAzO,WAAA,2BAAArB,4DAAA,CAAgF;IAc5GA,uDAAA,GACJ;IADIA,gEAAA,MAAAA,yDAAA,SAAA8P,WAAA,CAAA5K,SAAA,wBAAAlF,yDAAA,SAAA8P,WAAA,CAAA3K,OAAA,qBACJ;IAIUnF,uDAAA,GAA4H;IAA5HA,wDAAA,YAAAA,6DAAA,KAAAqQ,GAAA,IAAAP,WAAA,CAAAzK,QAAA,kBAAAyK,WAAA,CAAAzK,QAAA,CAAAC,MAAA,eAAAwK,WAAA,CAAAzK,QAAA,kBAAAyK,WAAA,CAAAzK,QAAA,CAAAC,MAAA,eAA4H;IAIrHtF,uDAAA,GAA4H;IAA5HA,wDAAA,YAAAA,6DAAA,KAAAqQ,GAAA,IAAAP,WAAA,CAAAzK,QAAA,kBAAAyK,WAAA,CAAAzK,QAAA,CAAAC,MAAA,eAAAwK,WAAA,CAAAzK,QAAA,kBAAAyK,WAAA,CAAAzK,QAAA,CAAAC,MAAA,eAA4H;IAMzHtF,uDAAA,GAAoC;IAApCA,+DAAA,EAAA8P,WAAA,CAAAzK,QAAA,kBAAAyK,WAAA,CAAAzK,QAAA,CAAAC,MAAA,OAAoC;;;;;;IA5ChEtF,6DAAA,EAE4C;IAF5CA,4DAAA,cAE4C;IACxCA,wDAAA,IAAAsQ,2CAAA,oBAkDM;IACVtQ,0DAAA,EAAM;;;;IApDDA,wDAAA,sBAAA6E,MAAA,CAAA0L,SAAA,CAAAjL,MAAA,CAAsC;IACbtF,uDAAA,GAAc;IAAdA,wDAAA,YAAA6E,MAAA,CAAA0L,SAAA,CAAc,iBAAA1L,MAAA,CAAA2L,SAAA;;;ADuC1C,MAAOC,qBAAqB;EAMhC/K,YACUG,eAAgC,EACjCE,WAA4B,EAC3BH,MAAc,EACdD,KAAqB,EACrBO,YAA0B;IAJ1B,KAAAL,eAAe,GAAfA,eAAe;IAChB,KAAAE,WAAW,GAAXA,WAAW;IACV,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAO,YAAY,GAAZA,YAAY;IAVtB,KAAAqK,SAAS,GAAe,EAAE;IAC1B,KAAApK,OAAO,GAAG,IAAI;IACd,KAAAzF,KAAK,GAAkB,IAAI;IAC3B,KAAAgQ,YAAY,GAAkB,IAAI;EAQ/B;EAEHlK,QAAQA,CAAA;IACN4B,OAAO,CAACkB,GAAG,CAAC,mCAAmC,CAAC;IAEhD;IACA,IAAI,CAAC1D,MAAM,CAACJ,MAAM,CAACuB,SAAS,CAAEyC,KAAK,IAAI;MACrC;MACA,IAAIA,KAAK,YAAYyF,0DAAa,EAAE;QAClC7G,OAAO,CAACkB,GAAG,CAAC,iDAAiD,CAAC;QAC9D,IAAI,CAACqH,aAAa,EAAE;;IAExB,CAAC,CAAC;IAEF;IACA,IAAI,CAACA,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACxK,OAAO,GAAG,IAAI;IACnBiC,OAAO,CAACkB,GAAG,CAAC,sBAAsB,CAAC;IAEnC;IACA,IAAI,CAACzD,eAAe,CAAC+K,eAAe,EAAE,CAAC7J,SAAS,CAAC;MAC/CC,IAAI,EAAGqC,QAAa,IAAI;QACtBjB,OAAO,CAACkB,GAAG,CAAC,oBAAoB,EAAED,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACJ,OAAO,EAAE;UACpB;UACA,IAAIsH,SAAS,GAAGlH,QAAQ,CAACkH,SAAS;UAElC;UACAA,SAAS,CAACM,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;YAChC,MAAMC,SAAS,GAAGF,CAAC,CAACzL,QAAQ,EAAEC,MAAM,IAAI,CAAC;YACzC,MAAM2L,SAAS,GAAGF,CAAC,CAAC1L,QAAQ,EAAEC,MAAM,IAAI,CAAC;YACzC,OAAO2L,SAAS,GAAGD,SAAS,CAAC,CAAC;UAChC,CAAC,CAAC;;UAEF,IAAI,CAACT,SAAS,GAAGA,SAAS;UAC1BnI,OAAO,CAACkB,GAAG,CACT,+CAA+C,EAC/C,IAAI,CAACiH,SAAS,CAACjL,MAAM,CACtB;UAED,IAAI,IAAI,CAACiL,SAAS,CAACjL,MAAM,GAAG,CAAC,EAAE;YAC7B8C,OAAO,CAACkB,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACiH,SAAS,CAAC,CAAC,CAAC,CAAC;YACjDnI,OAAO,CAACkB,GAAG,CACT,iBAAiB,EACjB,IAAI,CAACiH,SAAS,CAACnJ,GAAG,CAAE8J,CAAC,KAAM;cACzBnM,KAAK,EAAEmM,CAAC,CAACnM,KAAK;cACdM,QAAQ,EAAE6L,CAAC,CAAC7L,QAAQ,EAAEC,MAAM,IAAI;aACjC,CAAC,CAAC,CACJ;;SAEJ,MAAM;UACL8C,OAAO,CAAC1H,KAAK,CAAC,oBAAoB,EAAE2I,QAAQ,CAAC;UAC7C,IAAI,CAACnD,YAAY,CAACmH,SAAS,CACzB,yCAAyC,CAC1C;;QAGH,IAAI,CAAClH,OAAO,GAAG,KAAK;MACtB,CAAC;MACDzF,KAAK,EAAGyH,GAAG,IAAI;QACbC,OAAO,CAAC1H,KAAK,CAAC,0BAA0B,EAAEyH,GAAG,CAAC;QAC9C,IAAI,CAAChC,OAAO,GAAG,KAAK;QAEpB,MAAMoC,YAAY,GAAGJ,GAAG,CAACK,OAAO,IAAIL,GAAG,CAACgJ,UAAU,IAAI,iBAAiB;QACvE,IAAI,CAACjL,YAAY,CAACmH,SAAS,CACzB,4CAA4C9E,YAAY,EAAE,CAC3D;MACH;KACD,CAAC;EACJ;EAEAhE,cAAcA,CAACrC,EAAU;IACvB,IAAI8G,OAAO,CAAC,yBAAyB,CAAC,EAAE;MACtC,IAAI,CAACnD,eAAe,CAACtB,cAAc,CAACrC,EAAE,CAAC,CAAC6E,SAAS,CAAC;QAChDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACuJ,SAAS,GAAG,IAAI,CAACA,SAAS,CAAChH,MAAM,CAAE2H,CAAC,IAAKA,CAAC,CAAChK,GAAG,KAAKhF,EAAE,CAAC;UAC3D,IAAI,CAACgE,YAAY,CAACkH,WAAW,CAC3B,wCAAwC,CACzC;QACH,CAAC;QACD1M,KAAK,EAAGyH,GAAG,IAAI;UACbC,OAAO,CAAC1H,KAAK,CAAC,4CAA4C,EAAEyH,GAAG,CAAC;UAEhE;UACA,IAAIA,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;YACtB,IAAI,CAACnC,YAAY,CAACmH,SAAS,CACzB,sEAAsE,CACvE;WACF,MAAM,IAAIlF,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;YAC7B,IAAI,CAACnC,YAAY,CAACmH,SAAS,CACzB,qDAAqD,CACtD;WACF,MAAM;YACL,MAAM9E,YAAY,GAChBJ,GAAG,CAACzH,KAAK,EAAE8H,OAAO,IAAI,2CAA2C;YACnE,IAAI,CAACtC,YAAY,CAACmH,SAAS,CAAC9E,YAAY,EAAE,IAAI,CAAC;;QAEnD;OACD,CAAC;;EAEN;EAEA2H,UAAUA,CAAChO,EAAsB;IAC/B,IAAIA,EAAE,EAAE;MACN,IAAI,CAAC0D,MAAM,CAACmD,QAAQ,CAAC,CAAC7G,EAAE,CAAC,EAAE;QAAEkP,UAAU,EAAE,IAAI,CAACzL;MAAK,CAAE,CAAC;;EAE1D;EAEA;EACA8J,YAAYA,CAACnI,KAAa;IACxB,IAAI,CAACoJ,YAAY,GAAGpJ,KAAK;EAC3B;EAEAsI,YAAYA,CAAA;IACV,IAAI,CAACc,YAAY,GAAG,IAAI;EAC1B;EAEAP,YAAYA,CAAC7I,KAAa;IACxB,OAAO,IAAI,CAACoJ,YAAY,KAAKpJ,KAAK,GAAG,SAAS,GAAG,SAAS;EAC5D;EAEA;EACAkJ,SAASA,CAAClJ,KAAa,EAAE1G,QAAa;IACpC,OAAOA,QAAQ,CAACsG,GAAG,IAAII,KAAK,CAAC+J,QAAQ,EAAE;EACzC;;;uBA5IWZ,qBAAqB,EAAAzQ,+DAAA,CAAA8J,8EAAA,GAAA9J,+DAAA,CAAAiK,8EAAA,GAAAjK,+DAAA,CAAAmK,mDAAA,GAAAnK,+DAAA,CAAAmK,2DAAA,GAAAnK,+DAAA,CAAAqK,wEAAA;IAAA;EAAA;;;YAArBoG,qBAAqB;MAAA7F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAsG,+BAAApG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/FlClL,4DAAA,aAAyC;UAI4DA,oDAAA,oBAAa;UAAAA,0DAAA,EAAO;UAC7GA,uDAAA,cAAyC;UAC7CA,0DAAA,EAAK;UACLA,4DAAA,WACiN;UAEzMA,4DAAA,EAAmH;UAAnHA,4DAAA,aAAmH;UAC/GA,uDAAA,cAAuG;UAC3GA,0DAAA,EAAM;UACNA,oDAAA,0BACJ;UAAAA,0DAAA,EAAO;UAKfA,wDAAA,KAAAuR,qCAAA,iBAWM;UAKNvR,wDAAA,KAAAwR,qCAAA,mBAaM;UAKNxR,wDAAA,KAAAyR,qCAAA,kBAsDM;UAGVzR,0DAAA,EAAM;;;UA5GkDA,uDAAA,GAAa;UAAbA,wDAAA,gBAAAkD,SAAA,CAAa;UAiB3DlD,uDAAA,IAAa;UAAbA,wDAAA,SAAAmL,GAAA,CAAAhF,OAAA,CAAa;UAgBbnG,uDAAA,GAAwC;UAAxCA,wDAAA,UAAAmL,GAAA,CAAAhF,OAAA,IAAAgF,GAAA,CAAAoF,SAAA,CAAAjL,MAAA,OAAwC;UAkBxCtF,uDAAA,GAAsC;UAAtCA,wDAAA,UAAAmL,GAAA,CAAAhF,OAAA,IAAAgF,GAAA,CAAAoF,SAAA,CAAAjL,MAAA,KAAsC;;;;;;mBDhClC;QACV;QACA3F,4DAAO,CAAC,kBAAkB,EAAE,CAC1BI,+DAAU,CAAC,QAAQ,EAAE,CACnBmP,0DAAK,CACH,QAAQ,EACR,CACErP,0DAAK,CAAC;UAAE2L,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAA8B,CAAE,CAAC,EAChE0D,4DAAO,CAAC,OAAO,EAAE,CACfrP,4DAAO,CACL,uCAAuC,EACvCsP,8DAAS,CAAC,CACRvP,0DAAK,CAAC;UACJ2L,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,8BAA8B;UACzCiG,MAAM,EAAE;SACT,CAAC,EACF7R,0DAAK,CAAC;UACJ2L,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,8BAA8B;UACzCiG,MAAM,EAAE;SACT,CAAC,EACF7R,0DAAK,CAAC;UACJ2L,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,wBAAwB;UACnCiG,MAAM,EAAE;SACT,CAAC,CACH,CAAC,CACH,CACF,CAAC,CACH,EACD;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB,CACF,CAAC,CACH,CAAC;QAEF;QACAhS,4DAAO,CAAC,WAAW,EAAE,CACnBC,0DAAK,CACH,SAAS,EACTC,0DAAK,CAAC;UACJ4L,SAAS,EAAE,wBAAwB;UACnCC,SAAS,EACP;SACH,CAAC,CACH,EACD9L,0DAAK,CACH,SAAS,EACTC,0DAAK,CAAC;UACJ4L,SAAS,EAAE,8BAA8B;UACzCC,SAAS,EACP;SACH,CAAC,CACH,EACD3L,+DAAU,CAAC,oBAAoB,EAAE,CAC/BD,4DAAO,CAAC,uCAAuC,CAAC,CACjD,CAAC,EACFC,+DAAU,CAAC,oBAAoB,EAAE,CAC/BD,4DAAO,CAAC,uCAAuC,CAAC,CACjD,CAAC,CACH,CAAC;QAEF;QACAH,4DAAO,CAAC,YAAY,EAAE,CACpBI,+DAAU,CAAC,QAAQ,EAAE,CACnBF,0DAAK,CAAC;UAAE2L,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,EACrD3L,4DAAO,CACL,eAAe,EACfD,0DAAK,CAAC;UAAE2L,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAClD,CACF,CAAC,CACH,CAAC;MACH;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;AE5FoD;AACyB;AACM;AACN;AACuB;;;AAEvG,MAAMqG,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEvB,yFAAqBA;CAC3C,EACD;EACEsB,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAE5F,yFAAqBA;CAClD,EACD;EACE2F,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEH,mHAAqBA;CACnD,EACD;EACEE,IAAI,EAAE,KAAK;EAAEC,SAAS,EAAEvM,+FAAuB,CAAE;CAClD,CACF;;AASK,MAAOwM,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXK,sBAAsB;IAAAE,OAAA,GAAArI,yDAAA;IAAAsI,OAAA,GAFvBR,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AC1BuB;AAEqB;AACY;AACM;AACN;AACd;AACc;AACjB;AACU;AACf;;;AAqBpD,MAAOgB,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAXxBP,yDAAY,EACZJ,6EAAsB,EACtBK,uDAAW,EACXC,+DAAmB,EACnBC,4DAAc,CAACK,OAAO,CAAC;QACrBC,OAAO,EAAEL,yDAAW;QACpBM,UAAU,EAAEL,oFAAcA;OAC3B,CAAC,EACFC,4DAAW;IAAA;EAAA;;;sHAGFC,eAAe;IAAAI,YAAA,GAjBxBvC,yFAAqB,EACrBhL,+FAAuB,EACvB2G,yFAAqB,EACrByF,yFAAqB;IAAAM,OAAA,GAGrBE,yDAAY,EACZJ,6EAAsB,EACtBK,uDAAW,EACXC,+DAAmB,EAAAzI,4DAAA,EAKnB6I,4DAAW;EAAA;AAAA", "sources": ["./src/app/views/front/plannings/planning-detail/planning-detail.component.ts", "./src/app/views/front/plannings/planning-detail/planning-detail.component.html", "./src/app/views/front/plannings/planning-form/planning-form.component.ts", "./src/app/views/front/plannings/planning-form/planning-form.component.html", "./src/app/views/front/plannings/planning-list/planning-list.component.ts", "./src/app/views/front/plannings/planning-list/planning-list.component.html", "./src/app/views/front/plannings/plannings-routing.module.ts", "./src/app/views/front/plannings/plannings.module.ts"], "sourcesContent": ["import {ChangeDetectorRef, Component, OnInit, HostListener} from '@angular/core';\n\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from '@app/services/authuser.service';\nimport { PlanningService } from '@app/services/planning.service';\nimport { ReunionService } from '@app/services/reunion.service';\nimport {\n  CalendarEvent, CalendarMonthViewDay,\n  CalendarView,\n} from 'angular-calendar';\nimport { <PERSON><PERSON>anitizer, SafeHtml } from '@angular/platform-browser';\nimport { trigger, state, style, animate, transition } from '@angular/animations';\nimport { ToastService } from '@app/services/toast.service';\n\n@Component({\n  selector: 'app-planning-detail',\n  templateUrl: './planning-detail.component.html',\n  styleUrls: ['./planning-detail.component.css'],\n  animations: [\n    // Animation pour l'entrée des sections\n    trigger('fadeInUp', [\n      transition(':enter', [\n        style({ opacity: 0, transform: 'translateY(20px)' }),\n        animate('0.5s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\n      ])\n    ]),\n\n    // Animation pour le survol des cartes\n    trigger('cardHover', [\n      state('default', style({\n        transform: 'scale(1)',\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n      })),\n      state('hovered', style({\n        transform: 'scale(1.02)',\n        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'\n      })),\n      transition('default => hovered', [\n        animate('0.2s ease-in-out')\n      ]),\n      transition('hovered => default', [\n        animate('0.2s ease-in-out')\n      ])\n    ])\n  ]\n})\nexport class PlanningDetailComponent implements OnInit {\n\n  planning: any | null = null;\n  loading = true;\n  error: string | null = null;\n  isCreator = false;\n  selectedDayEvents: CalendarEvent[] = [];\n  selectedDate: Date | null = null;\n  cardState = 'default';\n\n  // Calendar setup\n  view: CalendarView = CalendarView.Month;\n  viewDate: Date = new Date();\n  events: CalendarEvent[] = [];\n\n  // Pour les animations\n  sectionStates = {\n    info: false,\n    participants: false,\n    reunions: false\n  };\n\n  constructor(\n    public route: ActivatedRoute,\n    public router: Router,\n    private planningService: PlanningService,\n    private reunionService: ReunionService,\n    public authService: AuthuserService,\n    private cdr: ChangeDetectorRef,\n    private sanitizer: DomSanitizer,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadPlanningDetails();\n\n    // Activer les animations des sections avec un délai\n    setTimeout(() => {\n      this.sectionStates.info = true;\n    }, 300);\n\n    setTimeout(() => {\n      this.sectionStates.participants = true;\n    }, 600);\n\n    setTimeout(() => {\n      this.sectionStates.reunions = true;\n    }, 900);\n  }\n\n  loadPlanningDetails(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.loading = false;\n      this.toastService.error(\n        'Erreur de navigation',\n        'ID de planning non fourni'\n      );\n      return;\n    }\n\n    this.planningService.getPlanningById(id).subscribe({\n      next: (planning: any) => {\n        this.planning = planning.planning;\n        this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\n        this.loading = false;\n\n        // Créer les événements pour le calendrier avec des couleurs personnalisées\n        this.events = this.planning.reunions.map((reunion: any, index: number) => {\n          const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\n          const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\n\n          // Générer une couleur basée sur l'index pour différencier les événements\n          const hue = (index * 137) % 360; // Formule pour distribuer les couleurs\n\n          return {\n            start: new Date(startStr),\n            end: new Date(endStr),\n            title: reunion.titre,\n            allDay: false,\n            color: {\n              primary: `hsl(${hue}, 70%, 50%)`,\n              secondary: `hsl(${hue}, 70%, 90%)`\n            },\n            meta: {\n              description: reunion.description || '',\n              id: reunion._id\n            }\n          };\n        });\n\n        this.cdr.detectChanges();\n      },\n      error: (err: any) => {\n        this.loading = false;\n        console.error('Erreur:', err);\n\n        if (err.status === 403) {\n          this.toastService.accessDenied('accéder à ce planning', err.status);\n        } else if (err.status === 404) {\n          this.toastService.error(\n            'Planning introuvable',\n            'Le planning demandé n\\'existe pas ou a été supprimé'\n          );\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors du chargement du planning';\n          this.toastService.error(\n            'Erreur de chargement',\n            errorMessage\n          );\n        }\n      }\n    });\n  }\n\n  handleDayClick(day: CalendarMonthViewDay): void {\n    this.selectedDate = day.date;\n    this.selectedDayEvents = day.events;\n\n    // Animation pour l'affichage des événements\n    if (day.events.length > 0) {\n      // Effet de scroll doux vers les détails des événements\n      setTimeout(() => {\n        const dayEventsElement = document.querySelector('.day-events');\n        if (dayEventsElement) {\n          dayEventsElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });\n        }\n      }, 100);\n    }\n  }\n\n  // Méthodes pour les animations\n  onCardMouseEnter(): void {\n    this.cardState = 'hovered';\n  }\n\n  onCardMouseLeave(): void {\n    this.cardState = 'default';\n  }\n\n\n  editPlanning(): void {\n    if (this.planning) {\n      this.router.navigate(['/plannings/edit', this.planning._id]);\n    }\n  }\n\n  deletePlanning(): void {\n    if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\n      this.planningService.deletePlanning(this.planning._id).subscribe({\n        next: () => {\n          this.toastService.success(\n            'Planning supprimé',\n            'Le planning a été supprimé avec succès'\n          );\n          this.router.navigate(['/plannings']);\n        },\n        error: (err) => {\n          console.error('Erreur lors de la suppression du planning:', err);\n\n          if (err.status === 403) {\n            this.toastService.accessDenied('supprimer ce planning', err.status);\n          } else if (err.status === 401) {\n            this.toastService.error(\n              'Non autorisé',\n              'Vous devez être connecté pour supprimer un planning'\n            );\n          } else {\n            const errorMessage = err.error?.message || 'Erreur lors de la suppression du planning';\n            this.toastService.error(\n              'Erreur de suppression',\n              errorMessage,\n              8000\n            );\n          }\n        }\n      });\n    }\n  }\n\n  nouvelleReunion(): void {\n    if (this.planning) {\n      // Rediriger vers le formulaire de création de réunion avec l'ID du planning préselectionné\n      this.router.navigate(['/reunions/nouvelleReunion'], {\n        queryParams: { planningId: this.planning._id }\n      });\n    }\n  }\n\n  /**\n   * Modifie une réunion\n   * @param reunionId ID de la réunion à modifier\n   */\n  editReunion(reunionId: string): void {\n    if (reunionId) {\n      this.router.navigate(['/reunions/modifier', reunionId]);\n    }\n  }\n\n  /**\n   * Supprime une réunion après confirmation\n   * @param reunionId ID de la réunion à supprimer\n   */\n  deleteReunion(reunionId: string): void {\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ?')) {\n      this.reunionService.deleteReunion(reunionId).subscribe({\n        next: (response) => {\n          console.log('Réunion supprimée avec succès:', response);\n\n          this.toastService.success(\n            'Réunion supprimée',\n            'La réunion a été supprimée avec succès'\n          );\n\n          // Recharger les détails du planning pour mettre à jour le calendrier\n          this.loadPlanningDetails();\n\n          // Vider les événements du jour sélectionné si la réunion supprimée était affichée\n          this.selectedDayEvents = this.selectedDayEvents.filter(event => event.meta?.id !== reunionId);\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression:', error);\n\n          if (error.status === 403) {\n            this.toastService.accessDenied('supprimer cette réunion', error.status);\n          } else if (error.status === 401) {\n            this.toastService.error(\n              'Non autorisé',\n              'Vous devez être connecté pour supprimer une réunion'\n            );\n          } else {\n            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n            this.toastService.error(\n              'Erreur de suppression',\n              errorMessage,\n              8000\n            );\n          }\n        }\n      });\n    }\n  }\n\n  formatDescription(description: string): SafeHtml {\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = description.replace(\n      /\\(presence obligatoire\\)/gi,\n      '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>'\n    );\n\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n}", "<div class=\"container mx-auto px-4 py-6\">\n  <!-- Bouton retour avec animation -->\n  <button (click)=\"router.navigate(['/plannings'])\"\n          class=\"back-button mb-4 flex items-center\">\n    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n      <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n    </svg>\n    Retour aux plannings\n  </button>\n\n  <!-- Chargement avec animation améliorée -->\n  <div *ngIf=\"loading\" class=\"text-center py-8\">\n    <div class=\"loading-spinner\"></div>\n    <p class=\"text-purple-600 mt-3 font-medium\">Chargement des détails...</p>\n  </div>\n\n  <!-- Erreur avec animation -->\n  <div *ngIf=\"error\" class=\"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-lg shadow-md mb-6 animate__animated animate__fadeIn\">\n    <div class=\"flex items-center\">\n      <svg class=\"h-6 w-6 text-red-500 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\n      </svg>\n      <span>{{ error }}</span>\n    </div>\n  </div>\n\n  <!-- Détails du planning avec design moderne -->\n  <div *ngIf=\"!loading && planning\" class=\"planning-card\" [@cardHover]=\"cardState\"\n       (mouseenter)=\"onCardMouseEnter()\" (mouseleave)=\"onCardMouseLeave()\">\n    <!-- En-tête du planning -->\n    <div class=\"planning-header\" [@fadeInUp]>\n      <h1 class=\"mb-2\">{{ planning.titre }}</h1>\n      <p class=\"text-base\" [innerHTML]=\"planning.description | highlightPresence\"></p>\n    </div>\n\n    <!-- Informations -->\n    <div class=\"planning-section\" [@fadeInUp]=\"sectionStates.info\">\n      <h2>\n        <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n        Informations\n      </h2>\n      <div class=\"info-item\">\n        <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n        <span>\n          Du <strong>{{ planning.dateDebut | date:'mediumDate' }}</strong>\n          au <strong>{{ planning.dateFin | date:'mediumDate' }}</strong>\n        </span>\n      </div>\n\n      <div *ngIf=\"planning.lieu\" class=\"info-item\">\n        <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n        <span>{{ planning.lieu }}</span>\n      </div>\n    </div>\n\n    <!-- Participants -->\n    <div class=\"planning-section\" [@fadeInUp]=\"sectionStates.participants\">\n      <h2>\n        <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\" />\n        </svg>\n        Participants\n      </h2>\n      <div class=\"participants-list\">\n        <div *ngFor=\"let participant of planning.participants; let i = index\"\n             class=\"participant-badge\"\n             [style.animation-delay]=\"i * 0.1 + 's'\">\n          <span>{{ participant.username }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Réunions associées -->\n    <div class=\"planning-section\" [@fadeInUp]=\"sectionStates.reunions\">\n      <div class=\"flex justify-between items-center mb-4\">\n        <h2>\n          <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n          </svg>\n          Réunions associées\n        </h2>\n        <button (click)=\"nouvelleReunion()\" class=\"btn btn-primary\">\n          <span class=\"flex items-center\">\n            <svg class=\"h-5 w-5 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n            Nouvelle Réunion\n          </span>\n        </button>\n      </div>\n\n      <!-- Boutons Modifier et Supprimer -->\n      <div class=\"flex justify-end space-x-3 mb-4\">\n        <button (click)=\"editPlanning()\" class=\"btn btn-secondary\">\n          <span class=\"flex items-center\">\n            <svg class=\"h-5 w-5 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n            </svg>\n            Modifier Planning\n          </span>\n        </button>\n        <button (click)=\"deletePlanning()\" class=\"btn btn-danger\">\n          <span class=\"flex items-center\">\n            <svg class=\"h-5 w-5 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n            </svg>\n            Supprimer Planning\n          </span>\n        </button>\n      </div>\n\n      <!-- Statistiques des réunions -->\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n        <div class=\"bg-gradient-to-br from-purple-50 to-indigo-50 p-4 rounded-lg shadow-sm\">\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <p class=\"text-sm text-gray-500\">Total Réunions</p>\n              <p class=\"text-2xl font-bold text-gray-800\">{{ planning.reunions?.length || 0 }}</p>\n            </div>\n            <div class=\"bg-purple-100 p-3 rounded-full\">\n              <svg class=\"h-6 w-6 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-gradient-to-br from-blue-50 to-cyan-50 p-4 rounded-lg shadow-sm\">\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <p class=\"text-sm text-gray-500\">Période</p>\n              <p class=\"text-lg font-bold text-gray-800\">\n                {{ planning.dateDebut | date:'shortDate' }} - {{ planning.dateFin | date:'shortDate' }}\n              </p>\n            </div>\n            <div class=\"bg-blue-100 p-3 rounded-full\">\n              <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-lg shadow-sm\">\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <p class=\"text-sm text-gray-500\">Participants</p>\n              <p class=\"text-2xl font-bold text-gray-800\">{{ planning.participants?.length || 0 }}</p>\n            </div>\n            <div class=\"bg-green-100 p-3 rounded-full\">\n              <svg class=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Calendrier avec style amélioré -->\n      <div class=\"calendar-container\">\n        <mwl-calendar-month-view\n          [viewDate]=\"viewDate\"\n          [events]=\"events\"\n          (dayClicked)=\"handleDayClick($event.day)\">\n        </mwl-calendar-month-view>\n      </div>\n\n      <!-- Détails des réunions du jour sélectionné -->\n      <div class=\"day-events\" *ngIf=\"selectedDayEvents.length > 0\" [@fadeInUp]>\n        <h3>\n          <span class=\"flex items-center\">\n            <svg class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n            Détails pour le {{ selectedDate | date: 'fullDate' }}\n          </span>\n        </h3>\n        <ul class=\"space-y-3\">\n          <li *ngFor=\"let event of selectedDayEvents; let i = index\"\n              class=\"event-item bg-white p-4 rounded-lg shadow-sm border border-gray-100\"\n              [style.animation-delay]=\"i * 0.1 + 's'\">\n            <div class=\"flex justify-between items-start\">\n              <div class=\"flex-1\">\n                <strong [innerHTML]=\"event.title | highlightPresence\"></strong>\n                <div class=\"flex items-center text-gray-600 mt-1\">\n                  <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  {{ event.start | date: 'shortTime' }} - {{ event.end | date: 'shortTime' }}\n                </div>\n                <div *ngIf=\"event.meta?.description\" class=\"mt-2 text-sm text-gray-500\">\n                  {{ event.meta.description }}\n                </div>\n              </div>\n              <div class=\"flex space-x-2 ml-4\">\n                <button (click)=\"editReunion(event.meta.id)\"\n                        class=\"text-blue-500 hover:text-blue-700 transition-colors duration-300 p-1 rounded-full hover:bg-blue-50\"\n                        title=\"Modifier la réunion\">\n                  <svg class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                  </svg>\n                </button>\n                <button (click)=\"deleteReunion(event.meta.id); $event.stopPropagation();\"\n                        class=\"text-red-500 hover:text-red-700 transition-colors duration-300 p-1 rounded-full hover:bg-red-50\"\n                        title=\"Supprimer la réunion\">\n                  <svg class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </li>\n        </ul>\n      </div>\n    </div>\n  </div>\n</div>", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Observable } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { DataService } from '@app/services/data.service';\n\nimport { PlanningService } from '@app/services/planning.service';\nimport { Router } from '@angular/router';\nimport { ToastService } from '@app/services/toast.service';\n\n@Component({\n  selector: 'app-planning-form',\n  templateUrl: './planning-form.component.html',\n  styleUrls: ['./planning-form.component.css'],\n})\nexport class PlanningFormComponent implements OnInit {\n  planningForm!: FormGroup;\n  isLoading = false;\n  errorMessage: string | null = null;\n  users$: Observable<User[]> = this.userService.getAllUsers();\n\n  constructor(\n    private fb: FormBuilder,\n    private userService: DataService,\n    private planningService: PlanningService,\n    private router: Router,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.planningForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: [''],\n      lieu: [''],\n      dateDebut: ['', Validators.required],\n      dateFin: ['', Validators.required],\n      participants: [[], Validators.required],\n    });\n  }\n\n  submit(): void {\n    console.log('Submit method called');\n    console.log('Form valid:', this.planningForm.valid);\n    console.log('Form values:', this.planningForm.value);\n\n    if (this.planningForm.valid) {\n      this.isLoading = true;\n      this.errorMessage = null;\n\n      // Extract form values\n      const formValues = this.planningForm.value;\n\n      // Create a simplified planning object with just the fields the API expects\n      const planningData = {\n        titre: formValues.titre,\n        description: formValues.description || '',\n        dateDebut: formValues.dateDebut,\n        dateFin: formValues.dateFin,\n        lieu: formValues.lieu || '',\n        participants: formValues.participants || [],\n      };\n\n      console.log('Planning data to submit:', planningData);\n\n      // Call the createPlanning method to add the new planning\n      this.planningService.createPlanning(planningData as any).subscribe({\n        next: (newPlanning: any) => {\n          console.log('Planning created successfully:', newPlanning);\n          this.isLoading = false;\n\n          // Afficher un toast de succès\n          this.toastService.showSuccess('Le planning a été créé avec succès');\n\n          // Navigate to plannings list page after successful creation\n          this.router.navigate(['/plannings']);\n        },\n        error: (error: any) => {\n          console.error('Error creating planning:', error);\n          console.error(\n            'Error details:',\n            error.error || error.message || error\n          );\n          this.isLoading = false;\n\n          // Gestion spécifique des erreurs d'autorisation\n          if (error.status === 403) {\n            this.toastService.showError(\n              \"Accès refusé : vous n'avez pas les droits pour créer un planning\"\n            );\n          } else if (error.status === 401) {\n            this.toastService.showError(\n              'Vous devez être connecté pour créer un planning'\n            );\n          } else {\n            // Autres erreurs\n            const errorMessage =\n              error.error?.message ||\n              'Une erreur est survenue lors de la création du planning';\n            this.toastService.showError(errorMessage, 8000);\n          }\n        },\n      });\n    } else {\n      console.log('Form validation errors:', this.getFormValidationErrors());\n\n      // Marquer tous les champs comme \"touched\" pour afficher les erreurs\n      this.markFormGroupTouched();\n\n      this.toastService.showWarning(\n        'Veuillez corriger les erreurs avant de soumettre le formulaire'\n      );\n    }\n  }\n\n  // Helper method to get form validation errors\n  getFormValidationErrors() {\n    const errors: any = {};\n    Object.keys(this.planningForm.controls).forEach((key) => {\n      const control = this.planningForm.get(key);\n      if (control && control.errors) {\n        errors[key] = control.errors;\n      }\n    });\n    return errors;\n  }\n\n  // Marquer tous les champs comme \"touched\" pour déclencher l'affichage des erreurs\n  markFormGroupTouched() {\n    Object.keys(this.planningForm.controls).forEach((key) => {\n      const control = this.planningForm.get(key);\n      if (control) {\n        control.markAsTouched();\n      }\n    });\n  }\n}\n", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\n  <!-- En-tête avec gradient coloré -->\n  <div class=\"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-6 text-white mb-0\">\n    <h1 class=\"text-2xl font-bold flex items-center\">\n      <i class=\"fas fa-calendar-plus mr-3 text-purple-200\"></i>\n      Nouveau Planning\n    </h1>\n    <p class=\"text-purple-100 mt-2\">Créez un nouveau planning pour organiser vos événements</p>\n  </div>\n\n  <form [formGroup]=\"planningForm\" (ngSubmit)=\"submit()\" novalidate class=\"bg-white rounded-b-lg shadow-lg p-6 border-t-0\">\n    <!-- Error message -->\n    <div *ngIf=\"errorMessage\" class=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n      {{ errorMessage }}\n    </div>\n\n    <div class=\"grid grid-cols-1 gap-6\">\n      <!-- Section Informations générales -->\n      <div class=\"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200\">\n        <h3 class=\"text-lg font-semibold text-purple-800 mb-4 flex items-center\">\n          <i class=\"fas fa-info-circle mr-2 text-purple-600\"></i>\n          Informations générales\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <!-- Titre -->\n          <div>\n            <label class=\"block text-sm font-medium text-purple-700 mb-2\">\n              <i class=\"fas fa-tag mr-2 text-purple-500\"></i>\n              Titre *\n            </label>\n            <input\n              type=\"text\"\n              formControlName=\"titre\"\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-purple-200 rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 focus:ring-2 transition-all duration-200\"\n              [class.border-red-300]=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\"\n              placeholder=\"Nom de votre planning...\"\n            />\n            <div *ngIf=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\" class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              <span *ngIf=\"planningForm.get('titre')?.errors?.['required']\">Le titre est obligatoire</span>\n              <span *ngIf=\"planningForm.get('titre')?.errors?.['minlength']\">Au moins 3 caractères requis</span>\n            </div>\n          </div>\n\n          <!-- Lieu -->\n          <div>\n            <label class=\"block text-sm font-medium text-orange-700 mb-2\">\n              <i class=\"fas fa-map-marker-alt mr-2 text-orange-500\"></i>\n              Lieu / Salle\n            </label>\n            <input\n              type=\"text\"\n              formControlName=\"lieu\"\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-orange-200 rounded-lg shadow-sm focus:ring-orange-500 focus:border-orange-500 focus:ring-2 transition-all duration-200\"\n              placeholder=\"Salle, bureau, lieu de l'événement...\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Section Période -->\n      <div class=\"bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-200\">\n        <h3 class=\"text-lg font-semibold text-blue-800 mb-4 flex items-center\">\n          <i class=\"fas fa-calendar-week mr-2 text-blue-600\"></i>\n          Période du planning\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <!-- Date début -->\n          <div>\n            <label class=\"block text-sm font-medium text-green-700 mb-2\">\n              <i class=\"fas fa-calendar-day mr-2 text-green-500\"></i>\n              Date de début *\n            </label>\n            <input\n              type=\"date\"\n              formControlName=\"dateDebut\"\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-green-200 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 focus:ring-2 transition-all duration-200\"\n            />\n          </div>\n\n          <!-- Date fin -->\n          <div>\n            <label class=\"block text-sm font-medium text-red-700 mb-2\">\n              <i class=\"fas fa-calendar-check mr-2 text-red-500\"></i>\n              Date de fin *\n            </label>\n            <input\n              type=\"date\"\n              formControlName=\"dateFin\"\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-red-200 rounded-lg shadow-sm focus:ring-red-500 focus:border-red-500 focus:ring-2 transition-all duration-200\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Section Participants -->\n      <div class=\"bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200\">\n        <h3 class=\"text-lg font-semibold text-emerald-800 mb-4 flex items-center\">\n          <i class=\"fas fa-users mr-2 text-emerald-600\"></i>\n          Participants\n        </h3>\n        <label class=\"block text-sm font-medium text-emerald-700 mb-2\">\n          <i class=\"fas fa-user-friends mr-2 text-emerald-500\"></i>\n          Sélectionnez les participants *\n        </label>\n        <select\n          formControlName=\"participants\"\n          multiple\n          class=\"mt-1 block w-full px-4 py-3 border-2 border-emerald-200 rounded-lg shadow-sm focus:ring-emerald-500 focus:border-emerald-500 focus:ring-2 transition-all duration-200 text-sm min-h-[120px]\"\n        >\n          <option *ngFor=\"let user of users$ | async\" [value]=\"user._id\" class=\"py-2\">\n            {{ user.username }}\n          </option>\n        </select>\n        <div *ngIf=\"planningForm.get('participants')?.invalid && planningForm.get('participants')?.touched\" class=\"text-red-500 text-sm mt-2 flex items-center\">\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\n          Veuillez sélectionner au moins un participant\n        </div>\n        <p class=\"text-xs text-emerald-600 mt-2\">\n          <i class=\"fas fa-info-circle mr-1\"></i>\n          Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs participants\n        </p>\n      </div>\n\n      <!-- Section Description -->\n      <div class=\"bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg border border-indigo-200\">\n        <h3 class=\"text-lg font-semibold text-indigo-800 mb-4 flex items-center\">\n          <i class=\"fas fa-align-left mr-2 text-indigo-600\"></i>\n          Description\n        </h3>\n        <label class=\"block text-sm font-medium text-indigo-700 mb-2\">\n          <i class=\"fas fa-edit mr-2 text-indigo-500\"></i>\n          Décrivez votre planning\n        </label>\n        <textarea\n          formControlName=\"description\"\n          class=\"mt-1 block w-full px-4 py-3 border-2 border-indigo-200 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 focus:ring-2 transition-all duration-200\"\n          rows=\"4\"\n          placeholder=\"Décrivez les objectifs, le contexte ou les détails de ce planning...\"\n        ></textarea>\n      </div>\n    </div>\n\n    <!-- Boutons d'action avec design amélioré -->\n    <div class=\"mt-8 flex justify-end space-x-4 bg-gray-50 p-4 rounded-lg border-t border-gray-200\">\n      <button\n        type=\"button\"\n        routerLink=\"/plannings\"\n        class=\"px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 flex items-center\">\n        <i class=\"fas fa-times mr-2\"></i>\n        Annuler\n      </button>\n      <button\n        type=\"button\"\n        (click)=\"submit()\"\n        [disabled]=\"isLoading || planningForm.invalid\"\n        class=\"px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-lg\"\n      >\n        <i class=\"fas fa-save mr-2\" *ngIf=\"!isLoading\"></i>\n        <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"isLoading\"></i>\n        {{ isLoading ? 'Enregistrement...' : 'Créer le planning' }}\n      </button>\n    </div>\n  </form>\n</div>", "import { Component, OnInit } from '@angular/core';\nimport { PlanningService } from 'src/app/services/planning.service';\nimport { Planning } from 'src/app/models/planning.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { ActivatedRoute, Router, NavigationEnd } from '@angular/router';\nimport { ToastService } from 'src/app/services/toast.service';\nimport {\n  trigger,\n  style,\n  animate,\n  transition,\n  query,\n  stagger,\n  keyframes,\n  state,\n} from '@angular/animations';\n\n@Component({\n  selector: 'app-planning-list',\n  templateUrl: './planning-list.component.html',\n  styleUrls: ['./planning-list.component.css'],\n  animations: [\n    // Animation pour l'entrée des cartes de planning (plus fluide)\n    trigger('staggerAnimation', [\n      transition('* => *', [\n        query(\n          ':enter',\n          [\n            style({ opacity: 0, transform: 'translateY(20px) scale(0.95)' }),\n            stagger('100ms', [\n              animate(\n                '0.6s cubic-bezier(0.25, 0.8, 0.25, 1)',\n                keyframes([\n                  style({\n                    opacity: 0,\n                    transform: 'translateY(20px) scale(0.95)',\n                    offset: 0,\n                  }),\n                  style({\n                    opacity: 0.6,\n                    transform: 'translateY(10px) scale(0.98)',\n                    offset: 0.4,\n                  }),\n                  style({\n                    opacity: 1,\n                    transform: 'translateY(0) scale(1)',\n                    offset: 1.0,\n                  }),\n                ])\n              ),\n            ]),\n          ],\n          { optional: true }\n        ),\n      ]),\n    ]),\n\n    // Animation pour le survol des cartes (plus douce)\n    trigger('cardHover', [\n      state(\n        'default',\n        style({\n          transform: 'scale(1) translateY(0)',\n          boxShadow:\n            '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n        })\n      ),\n      state(\n        'hovered',\n        style({\n          transform: 'scale(1.02) translateY(-3px)',\n          boxShadow:\n            '0 15px 20px -5px rgba(0, 0, 0, 0.08), 0 8px 8px -5px rgba(0, 0, 0, 0.03)',\n        })\n      ),\n      transition('default => hovered', [\n        animate('0.4s cubic-bezier(0.25, 0.8, 0.25, 1)'),\n      ]),\n      transition('hovered => default', [\n        animate('0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'),\n      ]),\n    ]),\n\n    // Animation pour l'en-tête\n    trigger('fadeInDown', [\n      transition(':enter', [\n        style({ opacity: 0, transform: 'translateY(-20px)' }),\n        animate(\n          '0.5s ease-out',\n          style({ opacity: 1, transform: 'translateY(0)' })\n        ),\n      ]),\n    ]),\n  ],\n})\nexport class PlanningListComponent implements OnInit {\n  plannings: Planning[] = [];\n  loading = true;\n  error: string | null = null;\n  hoveredIndex: number | null = null;\n\n  constructor(\n    private planningService: PlanningService,\n    public authService: AuthuserService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('PlanningListComponent initialized');\n\n    // S'abonner aux événements de navigation pour recharger les plannings\n    this.router.events.subscribe((event) => {\n      // NavigationEnd est émis lorsque la navigation est terminée\n      if (event instanceof NavigationEnd) {\n        console.log('Navigation terminée, rechargement des plannings');\n        this.loadPlannings();\n      }\n    });\n\n    // Chargement initial des plannings\n    this.loadPlannings();\n  }\n\n  loadPlannings(): void {\n    this.loading = true;\n    console.log('Loading plannings...');\n\n    // Utiliser getAllPlannings au lieu de getPlanningsByUser pour afficher tous les plannings\n    this.planningService.getAllPlannings().subscribe({\n      next: (response: any) => {\n        console.log('Response received:', response);\n\n        if (response.success) {\n          // Récupérer les plannings\n          let plannings = response.plannings;\n\n          // Trier les plannings par nombre de réunions (ordre décroissant)\n          plannings.sort((a: any, b: any) => {\n            const reunionsA = a.reunions?.length || 0;\n            const reunionsB = b.reunions?.length || 0;\n            return reunionsB - reunionsA; // Ordre décroissant\n          });\n\n          this.plannings = plannings;\n          console.log(\n            'Plannings loaded and sorted by reunion count:',\n            this.plannings.length\n          );\n\n          if (this.plannings.length > 0) {\n            console.log('First planning:', this.plannings[0]);\n            console.log(\n              'Reunion counts:',\n              this.plannings.map((p) => ({\n                titre: p.titre,\n                reunions: p.reunions?.length || 0,\n              }))\n            );\n          }\n        } else {\n          console.error('Error in response:', response);\n          this.toastService.showError(\n            'Erreur lors du chargement des plannings'\n          );\n        }\n\n        this.loading = false;\n      },\n      error: (err) => {\n        console.error('Error loading plannings:', err);\n        this.loading = false;\n\n        const errorMessage = err.message || err.statusText || 'Erreur inconnue';\n        this.toastService.showError(\n          `Erreur lors du chargement des plannings: ${errorMessage}`\n        );\n      },\n    });\n  }\n\n  deletePlanning(id: string): void {\n    if (confirm('Supprimer ce planning ?')) {\n      this.planningService.deletePlanning(id).subscribe({\n        next: () => {\n          this.plannings = this.plannings.filter((p) => p._id !== id);\n          this.toastService.showSuccess(\n            'Le planning a été supprimé avec succès'\n          );\n        },\n        error: (err) => {\n          console.error('Erreur lors de la suppression du planning:', err);\n\n          // Gestion spécifique des erreurs d'autorisation\n          if (err.status === 403) {\n            this.toastService.showError(\n              \"Accès refusé : vous n'avez pas les droits pour supprimer ce planning\"\n            );\n          } else if (err.status === 401) {\n            this.toastService.showError(\n              'Vous devez être connecté pour supprimer un planning'\n            );\n          } else {\n            const errorMessage =\n              err.error?.message || 'Erreur lors de la suppression du planning';\n            this.toastService.showError(errorMessage, 8000);\n          }\n        },\n      });\n    }\n  }\n\n  GotoDetail(id: string | undefined) {\n    if (id) {\n      this.router.navigate([id], { relativeTo: this.route });\n    }\n  }\n\n  // Méthodes pour les animations de survol\n  onMouseEnter(index: number): void {\n    this.hoveredIndex = index;\n  }\n\n  onMouseLeave(): void {\n    this.hoveredIndex = null;\n  }\n\n  getCardState(index: number): string {\n    return this.hoveredIndex === index ? 'hovered' : 'default';\n  }\n\n  // Méthode pour le suivi des éléments dans ngFor\n  trackByFn(index: number, planning: any): string {\n    return planning._id || index.toString();\n  }\n}\n", "<div class=\"container mx-auto px-4 py-6\">\n    <!-- En-tête avec animation -->\n    <div class=\"flex justify-between items-center mb-6\" [@fadeInDown]>\n        <h1 class=\"text-2xl font-bold text-gray-800 relative planning-header\">\n            <span class=\"bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-500\">Mes Plannings</span>\n            <span class=\"underline-animation\"></span>\n        </h1>\n        <a routerLink=\"/plannings/nouveau\"\n           class=\"px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-md hover:from-purple-700 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 hover:shadow-lg add-button\">\n            <span class=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                </svg>\n                Nouveau Planning\n            </span>\n        </a>\n    </div>\n\n    <!-- Chargement -->\n    <div *ngIf=\"loading\" class=\"text-center py-12\">\n        <div class=\"relative mx-auto w-20 h-20\">\n            <div class=\"absolute top-0 left-0 w-full h-full border-4 border-purple-200 rounded-full\"></div>\n            <div class=\"absolute top-0 left-0 w-full h-full border-4 border-transparent border-t-purple-600 rounded-full animate-spin\"></div>\n            <div class=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-purple-600 font-semibold\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-8 w-8\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n            </div>\n        </div>\n        <p class=\"mt-4 text-gray-600 animate-pulse\">Chargement des plannings...</p>\n    </div>\n\n\n\n    <!-- Liste vide -->\n    <div *ngIf=\"!loading && plannings.length === 0\" class=\"text-center py-12 bg-white rounded-lg shadow-md\">\n        <svg class=\"mx-auto h-16 w-16 text-purple-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n        </svg>\n        <h3 class=\"mt-4 text-xl font-medium text-gray-900\">Aucun planning disponible</h3>\n        <p class=\"mt-2 text-gray-600\">Créez votre premier planning pour commencer à organiser vos réunions.</p>\n        <a routerLink=\"/plannings/nouveau\"\n           class=\"mt-6 inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-all duration-300 transform hover:scale-105\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n            Créer un planning\n        </a>\n    </div>\n\n\n\n    <!-- Liste des plannings avec animation professionnelle -->\n    <div *ngIf=\"!loading && plannings.length > 0\"\n         class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\"\n         [@staggerAnimation]=\"plannings.length\">\n        <div *ngFor=\"let planning of plannings; let i = index; trackBy: trackByFn\"\n             [@cardHover]=\"getCardState(i)\"\n             (mouseenter)=\"onMouseEnter(i)\"\n             (mouseleave)=\"onMouseLeave()\"\n             class=\"bg-white rounded-lg shadow-md p-4 cursor-pointer transform transition-all duration-300 relative\">\n            <div class=\"flex justify-between items-start\">\n                <div>\n                    <h3 class=\"text-lg font-semibold text-gray-800\">\n                        <a class=\"hover:text-purple-600 planning-title\">\n                            {{ planning.titre }}\n                        </a>\n                    </h3>\n                    <p class=\"text-sm mt-1\" [innerHTML]=\"(planning.description || 'Aucune description') | highlightPresence\"></p>\n                </div>\n                <button (click)=\"deletePlanning(planning._id); $event.stopPropagation();\"\n                        class=\"text-red-500 hover:text-red-700 transition-colors duration-300\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                    </svg>\n                </button>\n            </div>\n\n            <div class=\"mt-3 flex items-center text-sm text-purple-700 font-medium\">\n                <svg class=\"h-4 w-4 mr-1 text-purple-700\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                {{ planning.dateDebut | date:'mediumDate' }} - {{ planning.dateFin | date:'mediumDate' }}\n            </div>\n\n            <div class=\"mt-4 pt-3 border-t border-gray-100 flex justify-between items-center\">\n                <span class=\"text-sm font-medium reunion-count\"\n                      [ngClass]=\"{'text-gray-800': (planning.reunions?.length || 0) > 0, 'text-gray-400': (planning.reunions?.length || 0) === 0}\">\n                    <span class=\"flex items-center\">\n                        <!-- Icône de réunion (calendrier avec horloge) -->\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\"\n                             [ngClass]=\"{'text-gray-800': (planning.reunions?.length || 0) > 0, 'text-gray-400': (planning.reunions?.length || 0) === 0}\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                            <circle cx=\"12\" cy=\"14\" r=\"3\" stroke-width=\"1.5\" />\n                            <path stroke-linecap=\"round\" stroke-width=\"1.5\" d=\"M12 12v2h2\" />\n                        </svg>\n\n                        <strong>{{ planning.reunions?.length || 0 }}</strong>&nbsp;réunion(s)\n                    </span>\n                </span>\n                <a (click)=\"GotoDetail(planning._id)\"\n                   class=\"text-sm hover:text-purple-900 font-medium details-link\"\n                   style=\"color: #6b46c1 !important;\">\n                    Voir détails →\n                </a>\n            </div>\n        </div>\n    </div>\n\n\n</div>", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport {PlanningEditComponent} from \"@app/views/front/plannings/planning-edit/planning-edit.component\";\n\nconst routes: Routes = [\n  {\n    path: '', component: PlanningListComponent\n  },\n  {\n    path: 'nouveau', component: PlanningFormComponent\n  },\n  {\n    path: 'edit/:id', component: PlanningEditComponent\n  },\n  {\n    path: ':id', component: PlanningDetailComponent  // <-- put this last\n  }\n];\n\n\n\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class PlanningsRoutingModule { }", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { PipesModule } from '../../../pipes/pipes.module';\n\n@NgModule({\n  declarations: [\n    PlanningListComponent,\n    PlanningDetailComponent,\n    PlanningFormComponent,\n    PlanningEditComponent,\n  ],\n  imports: [\n    CommonModule,\n    PlanningsRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    CalendarModule.forRoot({\n      provide: DateAdapter,\n      useFactory: adapterFactory,\n    }),\n    PipesModule,\n  ],\n})\nexport class PlanningsModule {}\n"], "names": ["CalendarView", "trigger", "state", "style", "animate", "transition", "i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ctx_r3", "planning", "lieu", "ɵɵstyleProp", "i_r7", "participant_r6", "username", "ɵɵtextInterpolate1", "event_r9", "meta", "description", "ɵɵtemplate", "PlanningDetailComponent_div_7_div_88_li_8_div_11_Template", "ɵɵlistener", "PlanningDetailComponent_div_7_div_88_li_8_Template_button_click_13_listener", "restoredCtx", "ɵɵrestoreView", "_r14", "$implicit", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "editReunion", "id", "PlanningDetailComponent_div_7_div_88_li_8_Template_button_click_16_listener", "$event", "ctx_r15", "deleteReunion", "stopPropagation", "i_r10", "ɵɵproperty", "ɵɵpipeBind1", "title", "ɵɵsanitizeHtml", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "start", "end", "PlanningDetailComponent_div_7_div_88_li_8_Template", "undefined", "ctx_r5", "selectedDate", "selectedDayEvents", "PlanningDetailComponent_div_7_Template_div_mouseenter_0_listener", "_r17", "ctx_r16", "onCardMouseEnter", "PlanningDetailComponent_div_7_Template_div_mouseleave_0_listener", "ctx_r18", "onCardMouseLeave", "PlanningDetailComponent_div_7_div_23_Template", "PlanningDetailComponent_div_7_div_30_Template", "PlanningDetailComponent_div_7_Template_button_click_37_listener", "ctx_r19", "nouvelleReunion", "PlanningDetailComponent_div_7_Template_button_click_43_listener", "ctx_r20", "editPlanning", "PlanningDetailComponent_div_7_Template_button_click_48_listener", "ctx_r21", "deletePlanning", "PlanningDetailComponent_div_7_Template_mwl_calendar_month_view_dayClicked_87_listener", "ctx_r22", "handleDayClick", "day", "PlanningDetailComponent_div_7_div_88_Template", "ctx_r2", "cardState", "titre", "sectionStates", "info", "dateDebut", "dateFin", "participants", "reunions", "length", "viewDate", "events", "PlanningDetailComponent", "constructor", "route", "router", "planningService", "reunionService", "authService", "cdr", "sanitizer", "toastService", "loading", "isCreator", "view", "Month", "Date", "ngOnInit", "loadPlanningDetails", "setTimeout", "snapshot", "paramMap", "get", "getPlanningById", "subscribe", "next", "<PERSON>ur", "_id", "getCurrentUserId", "map", "reunion", "index", "startStr", "date", "substring", "heureDebut", "endStr", "heure<PERSON>in", "hue", "allDay", "color", "primary", "secondary", "detectChanges", "err", "console", "status", "accessDenied", "errorMessage", "message", "dayEventsElement", "document", "querySelector", "scrollIntoView", "behavior", "block", "navigate", "confirm", "success", "queryParams", "planningId", "reunionId", "response", "log", "filter", "event", "formatDescription", "formattedText", "replace", "bypassSecurityTrustHtml", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "PlanningService", "i3", "ReunionService", "i4", "AuthuserService", "ChangeDetectorRef", "i5", "Dom<PERSON><PERSON><PERSON>zer", "i6", "ToastService", "selectors", "decls", "vars", "consts", "template", "PlanningDetailComponent_Template", "rf", "ctx", "PlanningDetailComponent_Template_button_click_1_listener", "PlanningDetailComponent_div_5_Template", "PlanningDetailComponent_div_6_Template", "PlanningDetailComponent_div_7_Template", "opacity", "transform", "boxShadow", "Validators", "ctx_r0", "PlanningFormComponent_div_20_span_2_Template", "PlanningFormComponent_div_20_span_3_Template", "tmp_0_0", "planningForm", "errors", "tmp_1_0", "user_r8", "PlanningFormComponent", "fb", "userService", "isLoading", "users$", "getAllUsers", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "submit", "valid", "value", "formValues", "planningData", "createPlanning", "newPlanning", "showSuccess", "showError", "getFormValidationErrors", "markFormGroupTouched", "showWarning", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "FormBuilder", "DataService", "PlanningFormComponent_Template", "PlanningFormComponent_Template_form_ngSubmit_7_listener", "PlanningFormComponent_div_8_Template", "PlanningFormComponent_div_20_Template", "PlanningFormComponent_option_49_Template", "PlanningFormComponent_div_51_Template", "PlanningFormComponent_Template_button_click_67_listener", "PlanningFormComponent_i_68_Template", "PlanningFormComponent_i_69_Template", "ɵɵclassProp", "tmp_2_0", "invalid", "touched", "tmp_3_0", "tmp_5_0", "NavigationEnd", "query", "stagger", "keyframes", "PlanningListComponent_div_13_div_1_Template_div_mouseenter_0_listener", "_r7", "i_r5", "ctx_r6", "onMouseEnter", "PlanningListComponent_div_13_div_1_Template_div_mouseleave_0_listener", "ctx_r8", "onMouseLeave", "PlanningListComponent_div_13_div_1_Template_button_click_8_listener", "planning_r4", "ctx_r9", "PlanningListComponent_div_13_div_1_Template_a_click_27_listener", "ctx_r10", "GotoDetail", "getCardState", "ɵɵpureFunction2", "_c0", "PlanningListComponent_div_13_div_1_Template", "plannings", "trackByFn", "PlanningListComponent", "hoveredIndex", "loadPlannings", "getAllPlannings", "sort", "a", "b", "reunionsA", "reunionsB", "p", "statusText", "relativeTo", "toString", "PlanningListComponent_Template", "PlanningListComponent_div_11_Template", "PlanningListComponent_div_12_Template", "PlanningListComponent_div_13_Template", "offset", "optional", "RouterModule", "PlanningEditComponent", "routes", "path", "component", "PlanningsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "FormsModule", "ReactiveFormsModule", "CalendarModule", "DateAdapter", "adapterFactory", "PipesModule", "PlanningsModule", "forRoot", "provide", "useFactory", "declarations"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}