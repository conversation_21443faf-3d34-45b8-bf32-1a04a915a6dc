{"version": 3, "file": "src_app_views_front_projects_projects_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;IC2DIA,4DAAA,cAAyD;IAErDA,uDAAA,cAAwJ;IAG1JA,0DAAA,EAAM;;;;;IA2CAA,4DAAA,cAAwD;IAK9CA,4DAAA,EAA8G;IAA9GA,4DAAA,cAA8G;IAC5GA,uDAAA,eAA4L;IAC9LA,0DAAA,EAAM;IAERA,6DAAA,EAA4B;IAA5BA,4DAAA,cAA4B;IAExBA,oDAAA,GACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,aAAsD;IAAAA,oDAAA,0BAAkB;IAAAA,0DAAA,EAAI;IAGhFA,4DAAA,aAC+O;IAE3OA,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAAgJ;IAClJA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IAAAA,oDAAA,6BAAW;IAAAA,0DAAA,EAAO;;;;;IAXtBA,uDAAA,GACF;IADEA,gEAAA,MAAAS,MAAA,CAAAC,WAAA,CAAAC,OAAA,OACF;IAIDX,uDAAA,GAAyB;IAAzBA,wDAAA,SAAAS,MAAA,CAAAI,UAAA,CAAAF,OAAA,GAAAX,2DAAA,CAAyB;;;;;IAjBpCA,4DAAA,cAAwF;IACtFA,wDAAA,IAAAgB,mDAAA,mBA2BM;IACRhB,0DAAA,EAAM;;;;IA5BkBA,uDAAA,GAAkB;IAAlBA,wDAAA,YAAAiB,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IA8B1CnB,4DAAA,cAAwF;IAGlFA,4DAAA,EAA8G;IAA9GA,4DAAA,cAA8G;IAC5GA,uDAAA,eAAsM;IACxMA,0DAAA,EAAM;IAERA,6DAAA,EAAsD;IAAtDA,4DAAA,YAAsD;IAAAA,oDAAA,2CAA+B;IAAAA,0DAAA,EAAI;;;;;IAsF3FA,qEAAA,GAAmC;IACjCA,4DAAA,cAA8G;IAGxGA,4DAAA,EAA8G;IAA9GA,4DAAA,cAA8G;IAC5GA,uDAAA,eAA+H;IACjIA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,UAAK;IACiEA,oDAAA,oBAAa;IAAAA,0DAAA,EAAI;IACrFA,4DAAA,YAAsD;IAAAA,oDAAA,oEAAwC;IAAAA,0DAAA,EAAI;IAI1GA,mEAAA,EAAe;;;;;;;;IAEfA,qEAAA,GAAoC;IAClCA,4DAAA,YAC+N;IAE3NA,4DAAA,EAA2E;IAA3EA,4DAAA,cAA2E;IACzEA,uDAAA,eAAuK;IACzKA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAO;IAGvCA,mEAAA,EAAe;;;;IATVA,uDAAA,GAA6C;IAA7CA,wDAAA,eAAAA,6DAAA,IAAAuB,GAAA,EAAAC,MAAA,CAAAC,QAAA,EAA6C;;;;;IAnL1DzB,4DAAA,cAAsE;IAS5DA,4DAAA,EAA8G;IAA9GA,4DAAA,cAA8G;IAC5GA,uDAAA,eAAwG;IAC1GA,0DAAA,EAAM;IAERA,6DAAA,EAAqE;IAArEA,4DAAA,aAAqE;IAAAA,oDAAA,4BAAqB;IAAAA,0DAAA,EAAK;IAEjGA,4DAAA,cAA2D;IAEvDA,oDAAA,IACF;IAAAA,0DAAA,EAAI;IAKRA,4DAAA,eAA0I;IAGpIA,4DAAA,EAAgH;IAAhHA,4DAAA,eAAgH;IAC9GA,uDAAA,gBAAsM;IACxMA,0DAAA,EAAM;IAERA,6DAAA,EAAqE;IAArEA,4DAAA,cAAqE;IACnEA,oDAAA,8BACA;IAAAA,4DAAA,gBAA0E;IACxEA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAIXA,wDAAA,KAAA0B,6CAAA,kBA6BM;IAEN1B,wDAAA,KAAA2B,6CAAA,kBASM;IACR3B,0DAAA,EAAM;IAIRA,4DAAA,eAAuB;IAObA,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAA2I;IAC7IA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,WAAK;IAC+BA,oDAAA,oBAAY;IAAAA,0DAAA,EAAK;IACnDA,4DAAA,aAAiC;IAAAA,oDAAA,8BAAiB;IAAAA,0DAAA,EAAI;IAK5DA,4DAAA,eAA2B;IAKnBA,4DAAA,EAAgH;IAAhHA,4DAAA,eAAgH;IAC9GA,uDAAA,gBAA6H;IAC/HA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,WAAK;IACwFA,oDAAA,mBAAW;IAAAA,0DAAA,EAAI;IAC1GA,4DAAA,aAAoE;IAAAA,oDAAA,IAA+D;;IAAAA,0DAAA,EAAI;IAM7IA,4DAAA,eAAmG;IAG7FA,4DAAA,EAA4G;IAA5GA,4DAAA,eAA4G;IAC1GA,uDAAA,gBAAgH;IAClHA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,WAAK;IACwFA,oDAAA,qBAAa;IAAAA,0DAAA,EAAI;IAC5GA,4DAAA,aAAoE;IAAAA,oDAAA,IAA8B;IAAAA,0DAAA,EAAI;IAM5GA,4DAAA,eAAmG;IAG7FA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAAwV;IAC1VA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,WAAK;IACwFA,oDAAA,oBAAY;IAAAA,0DAAA,EAAI;IAC3GA,4DAAA,aAAoE;IAAAA,oDAAA,IAA0C;IAAAA,0DAAA,EAAI;IAQ5HA,4DAAA,eAA0I;IAGpIA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAAqjB;IAEvjBA,0DAAA,EAAM;IAERA,6DAAA,EAAqE;IAArEA,4DAAA,cAAqE;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAK;IAGnFA,4DAAA,eAAuB;IACrBA,wDAAA,KAAA4B,sDAAA,4BAce;IAEf5B,wDAAA,KAAA6B,sDAAA,2BAUe;IAGf7B,4DAAA,aACoN;IAEhNA,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAA6G;IAC/GA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IAAAA,oDAAA,0BAAkB;IAAAA,0DAAA,EAAO;;;;IApLjCA,uDAAA,IACF;IADEA,gEAAA,OAAA8B,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAa,WAAA,uDACF;IAeI/B,uDAAA,GACF;IADEA,gEAAA,QAAA8B,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,kBAAAW,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAc,MAAA,sBAAAH,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,kBAAAW,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAc,MAAA,6BACF;IAIEjC,uDAAA,GAAkC;IAAlCA,wDAAA,UAAA8B,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,kBAAAW,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAc,MAAA,MAAkC;IA+BlCjC,uDAAA,GAAuD;IAAvDA,wDAAA,WAAA8B,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,KAAAW,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAc,MAAA,OAAuD;IA2CejC,uDAAA,IAA+D;IAA/DA,+DAAA,CAAAA,yDAAA,SAAA8B,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAkB,UAAA,qBAA+D;IAe/DpC,uDAAA,IAA8B;IAA9BA,gEAAA,KAAA8B,MAAA,CAAAO,gBAAA,aAA8B;IAe9BrC,uDAAA,IAA0C;IAA1CA,+DAAA,EAAA8B,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAoB,MAAA,wBAA0C;IAoBrGtC,uDAAA,IAAkB;IAAlBA,wDAAA,SAAA8B,MAAA,CAAAS,YAAA,CAAkB;IAgBlBvC,uDAAA,GAAmB;IAAnBA,wDAAA,UAAA8B,MAAA,CAAAS,YAAA,CAAmB;;;ADhP9C;AAMM,MAAOC,sBAAsB;EAOjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAA4B;IAJ5B,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAXrB,KAAArB,QAAQ,GAAW,EAAE;IAGrB,KAAAsB,SAAS,GAAG,IAAI;IAChB,KAAAR,YAAY,GAAG,KAAK;EAQjB;EAEHS,QAAQA,CAAA;IACN,IAAI,CAACvB,QAAQ,GAAG,IAAI,CAACiB,KAAK,CAACO,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC5D,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,iBAAiBA,CAAA;IACf,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,aAAa,CAACU,aAAa,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAAC8B,SAAS,CAAC;MACxDC,IAAI,EAAGtC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAAC6B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDU,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEC,GAAG,CAAC;QACzD,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC;KACD,CAAC;EACJ;EAEAP,gBAAgBA,CAAA;IACd,MAAMQ,UAAU,GAAG,IAAI,CAACf,WAAW,CAACgB,gBAAgB,EAAE;IACtD,IAAID,UAAU,EAAE;MACd,IAAI,CAAChB,aAAa,CAACkB,gBAAgB,CAAC,IAAI,CAACtC,QAAQ,EAAEoC,UAAU,CAAC,CAACN,SAAS,CAAC;QACvEC,IAAI,EAAGQ,MAAe,IAAI;UACxBL,OAAO,CAACM,GAAG,CAACD,MAAM,CAAC;UACnB,IAAI,CAACzB,YAAY,GAAGyB,MAAM;QAC5B,CAAC;QACDP,KAAK,EAAGC,GAAQ,IAAI;UAClBC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;QAC/D;OACD,CAAC;;EAEN;EAEA7C,UAAUA,CAACqD,QAAgB;IACzB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,8CAA8CkC,QAAQ,EAAE;EACjE;EAEAzD,WAAWA,CAACwD,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOiC,QAAQ;EACjB;EAEAK,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAChC,YAAY,EAAE,OAAO,WAAW;IAEzC,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAEkB,UAAU,EAAE,OAAO,QAAQ;IAE7C,MAAMoC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAAC,IAAI,CAACvD,MAAM,CAACkB,UAAU,CAAC;IAEjD,IAAIsC,QAAQ,GAAGF,GAAG,EAAE,OAAO,SAAS;IAEpC,MAAMG,cAAc,GAAG,IAAIF,IAAI,EAAE;IACjCE,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAEpD,IAAIH,QAAQ,IAAIC,cAAc,EAAE,OAAO,QAAQ;IAE/C,OAAO,QAAQ;EACjB;EAEAG,cAAcA,CAAA;IACZ,MAAMC,MAAM,GAAG,IAAI,CAACR,gBAAgB,EAAE;IAEtC,QAAQQ,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,uHAAuH;MAChI,KAAK,QAAQ;QACX,OAAO,6HAA6H;MACtI,KAAK,SAAS;QACZ,OAAO,2GAA2G;MACpH;QACE,OAAO,iHAAiH;;EAE9H;EAEAC,aAAaA,CAAA;IACX,MAAMD,MAAM,GAAG,IAAI,CAACR,gBAAgB,EAAE;IAEtC,QAAQQ,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,eAAe;MACxB,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,SAAS;QACZ,OAAO,QAAQ;MACjB;QACE,OAAO,OAAO;;EAEpB;EAEA1C,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAEkB,UAAU,EAAE,OAAO,CAAC;IAEtC,MAAMoC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAAC,IAAI,CAACvD,MAAM,CAACkB,UAAU,CAAC;IACjD,MAAM6C,QAAQ,GAAGP,QAAQ,CAACQ,OAAO,EAAE,GAAGV,GAAG,CAACU,OAAO,EAAE;IACnD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,OAAOG,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;EAC9B;;;uBAtIW3C,sBAAsB,EAAAxC,+DAAA,CAAAwF,2DAAA,GAAAxF,+DAAA,CAAAwF,mDAAA,GAAAxF,+DAAA,CAAA2F,wEAAA,GAAA3F,+DAAA,CAAA6F,0EAAA,GAAA7F,+DAAA,CAAA+F,8EAAA;IAAA;EAAA;;;YAAtBvD,sBAAsB;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZnCvG,4DAAA,aAA0K;UAGtKA,uDAAA,aAA6K;UAE/KA,0DAAA,EAAM;UAENA,4DAAA,aAAuD;UAKkDA,oDAAA,kBAAW;UAAAA,0DAAA,EAAI;UAClHA,4DAAA,EAA2E;UAA3EA,4DAAA,aAA2E;UACzEA,uDAAA,eAA8F;UAChGA,0DAAA,EAAM;UACNA,6DAAA,EAA6D;UAA7DA,4DAAA,gBAA6D;UAAAA,oDAAA,IAA0C;UAAAA,0DAAA,EAAO;UAGhHA,4DAAA,eAA0I;UAIlIA,4DAAA,EAAsF;UAAtFA,4DAAA,eAAsF;UACpFA,uDAAA,gBAA4J;UAC9JA,0DAAA,EAAM;UAERA,6DAAA,EAAK;UAALA,4DAAA,WAAK;UAEDA,oDAAA,IACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAA8C;UAE1CA,4DAAA,EAA8G;UAA9GA,4DAAA,eAA8G;UAC5GA,uDAAA,gBAAwV;UAC1VA,0DAAA,EAAM;UACNA,6DAAA,EAAqE;UAArEA,4DAAA,gBAAqE;UAAAA,oDAAA,IAA0C;UAAAA,0DAAA,EAAO;UAExHA,4DAAA,eAAyC;UACvCA,4DAAA,EAA2F;UAA3FA,4DAAA,eAA2F;UACzFA,uDAAA,gBAA6H;UAC/HA,0DAAA,EAAM;UACNA,6DAAA,EAAyD;UAAzDA,4DAAA,gBAAyD;UAAAA,oDAAA,IAAsE;;UAAAA,0DAAA,EAAO;UAO9IA,4DAAA,eAAyC;UAErCA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAOfA,wDAAA,KAAAyG,sCAAA,kBAMM;UAGNzG,wDAAA,KAAA0G,sCAAA,oBA2MM;UACR1G,0DAAA,EAAM;;;UAhQ6DA,uDAAA,IAA0C;UAA1CA,+DAAA,EAAAwG,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAyF,KAAA,8BAA0C;UAa/F3G,uDAAA,GACF;UADEA,gEAAA,OAAAwG,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAyF,KAAA,0BACF;UAMyE3G,uDAAA,GAA0C;UAA1CA,+DAAA,EAAAwG,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAoB,MAAA,wBAA0C;UAMtDtC,uDAAA,GAAsE;UAAtEA,+DAAA,CAAAA,yDAAA,QAAAwG,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAkB,UAAA,qBAAsE;UAQ/HpC,uDAAA,GAA4B;UAA5BA,wDAAA,YAAAwG,GAAA,CAAA1B,cAAA,GAA4B;UAChC9E,uDAAA,GACF;UADEA,gEAAA,MAAAwG,GAAA,CAAAxB,aAAA,QACF;UAOFhF,uDAAA,GAAe;UAAfA,wDAAA,SAAAwG,GAAA,CAAAzD,SAAA,CAAe;UASf/C,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAwG,GAAA,CAAAzD,SAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9DiC;;;;;;;;;;;IC8DrD/C,6DAAA,EAA2E;IAA3EA,4DAAA,cAA2E;IAKwBA,oDAAA,YAAK;IAAAA,0DAAA,EAAI;IACpGA,4DAAA,YAAiE;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAI;IAC7FA,4DAAA,YAA2D;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAI;IAExEA,4DAAA,eAA4G;IAC1GA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAA4J;IAC9JA,0DAAA,EAAM;IAMZA,6DAAA,EAA2L;IAA3LA,4DAAA,eAA2L;IAG1FA,oDAAA,cAAM;IAAAA,0DAAA,EAAI;IACrGA,4DAAA,aAAiE;IAAAA,oDAAA,IAAsB;IAAAA,0DAAA,EAAI;IAC3FA,4DAAA,aAA2D;IAAAA,oDAAA,2BAAS;IAAAA,0DAAA,EAAI;IAE1EA,4DAAA,eAAyG;IACvGA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAA+H;IACjIA,0DAAA,EAAM;IAMZA,6DAAA,EAA2L;IAA3LA,4DAAA,eAA2L;IAGxFA,oDAAA,kBAAU;IAAAA,0DAAA,EAAI;IAC3GA,4DAAA,aAAmE;IAAAA,oDAAA,IAAuB;IAAAA,0DAAA,EAAI;IAC9FA,4DAAA,aAA2D;IAAAA,oDAAA,qBAAQ;IAAAA,0DAAA,EAAI;IAEzEA,4DAAA,eAA2G;IACzGA,4DAAA,EAAgH;IAAhHA,4DAAA,eAAgH;IAC9GA,uDAAA,gBAA6H;IAC/HA,0DAAA,EAAM;IAMZA,6DAAA,EAA2L;IAA3LA,4DAAA,eAA2L;IAG1FA,oDAAA,YAAI;IAAAA,0DAAA,EAAI;IACnGA,4DAAA,aAAiE;IAAAA,oDAAA,IAAuB;IAAAA,0DAAA,EAAI;IAC5FA,4DAAA,aAA2D;IAAAA,oDAAA,qBAAQ;IAAAA,0DAAA,EAAI;IAEzEA,4DAAA,eAA4G;IAC1GA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAAgH;IAClHA,0DAAA,EAAM;;;;IAtD2DA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAA6G,MAAA,CAAAC,gBAAA,GAAwB;IAgBxB9G,uDAAA,IAAsB;IAAtBA,+DAAA,CAAA6G,MAAA,CAAAE,cAAA,GAAsB;IAgBpB/G,uDAAA,IAAuB;IAAvBA,+DAAA,CAAA6G,MAAA,CAAAG,eAAA,GAAuB;IAgBzBhH,uDAAA,IAAuB;IAAvBA,gEAAA,KAAA6G,MAAA,CAAAI,cAAA,QAAuB;;;;;;IAahGjH,6DAAA,EAAuL;IAAvLA,4DAAA,cAAuL;IAE9GA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAK;IAC7FA,4DAAA,eAAqE;IAAAA,oDAAA,GAAgC;IAAAA,0DAAA,EAAO;IAE9GA,4DAAA,cAAoE;IAClEA,uDAAA,cAC8C;IAChDA,0DAAA,EAAM;IACNA,4DAAA,cAAkF;IAC1EA,oDAAA,IAAqC;IAAAA,0DAAA,EAAO;IAClDA,4DAAA,YAAM;IAAAA,oDAAA,IAAkC;IAAAA,0DAAA,EAAO;;;;IARsBA,uDAAA,GAAgC;IAAhCA,gEAAA,KAAA8B,MAAA,CAAAmF,cAAA,2BAAgC;IAIhGjH,uDAAA,GAAkC;IAAlCA,yDAAA,UAAA8B,MAAA,CAAAmF,cAAA,QAAkC;IAGjCjH,uDAAA,GAAqC;IAArCA,gEAAA,KAAA8B,MAAA,CAAAiF,cAAA,sBAAqC;IACrC/G,uDAAA,GAAkC;IAAlCA,gEAAA,KAAA8B,MAAA,CAAAkF,eAAA,kBAAkC;;;;;;IAM9ChH,6DAAA,EAAyD;IAAzDA,4DAAA,cAAyD;IAErDA,uDAAA,cAEO;IAKTA,0DAAA,EAAM;;;;;;IAIRA,6DAAA,EAGC;IAHDA,4DAAA,cAGC;IAIGA,4DAAA,EAKC;IALDA,4DAAA,cAKC;IACCA,uDAAA,eAKQ;IACVA,0DAAA,EAAM;IAENA,6DAAA,EAEC;IAFDA,uDAAA,cAEO;IACTA,0DAAA,EAAM;IACNA,4DAAA,aAEC;IACCA,oDAAA,gCACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAmD;IACjDA,oDAAA,2CACF;IAAAA,0DAAA,EAAI;;;;;IA8EIA,4DAAA,cAGC;IAGKA,4DAAA,EAKC;IALDA,4DAAA,cAKC;IACCA,uDAAA,eAKQ;IACVA,0DAAA,EAAM;IAENA,6DAAA,EAEC;IAFDA,uDAAA,cAEO;IACTA,0DAAA,EAAM;IACNA,4DAAA,eAEG;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EACV;IAEHA,4DAAA,YAKC;IACCA,uDAAA,cAEO;IAIPA,4DAAA,gBAEC;IACCA,4DAAA,EAKC;IALDA,4DAAA,eAKC;IACCA,uDAAA,iBAKQ;IACVA,0DAAA,EAAM;IACNA,oDAAA,+BACF;IAAAA,0DAAA,EAAO;;;;;IA5BPA,uDAAA,GAAyB;IAAzBA,wDAAA,SAAAmH,OAAA,CAAAtG,UAAA,CAAAuG,QAAA,GAAApH,2DAAA,CAAyB;IAEzBA,yDAAA,aAAAmH,OAAA,CAAAzG,WAAA,CAAA0G,QAAA,EAAmC;;;;;IA1C3CpH,4DAAA,cAGC;IAIGA,oDAAA,iBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAAuB;IACrBA,wDAAA,IAAAsH,uDAAA,mBA4DM;IACRtH,0DAAA,EAAM;;;;IA5DeA,uDAAA,GAAkB;IAAlBA,wDAAA,YAAAuH,SAAA,CAAApG,QAAA,CAAkB;;;;;IA6FvCnB,qEAAA,GAA0C;IACxCA,4DAAA,gBAEC;IAEGA,4DAAA,EAIC;IAJDA,4DAAA,eAIC;IACCA,uDAAA,gBAIQ;IACVA,0DAAA,EAAM;IAENA,6DAAA,EAEC;IAFDA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IACNA,4DAAA,WAAM;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAO;IAEtBA,mEAAA,EAAe;;;;;;;;IAEfA,qEAAA,GAA2C;IACzCA,4DAAA,aAGC;IACCA,uDAAA,eAEO;IAIPA,4DAAA,gBAEC;IACCA,oDAAA,eACF;IAAAA,0DAAA,EAAO;IAEXA,mEAAA,EAAe;;;;IAfXA,uDAAA,GAA+C;IAA/CA,wDAAA,eAAAA,6DAAA,IAAAuB,GAAA,EAAAgG,SAAA,CAAAC,GAAA,EAA+C;;;;;;;;IA/LzDxH,4DAAA,cAGC;IAIGA,uDAAA,cAEO;IAOPA,4DAAA,cAAqD;IAI/CA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IAGTA,4DAAA,aAEC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAAsD;IAIlDA,4DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAC9EA,uDAAA,gBAAwV;IAC1VA,0DAAA,EAAM;IACNA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IACPA,6DAAA,EAAiD;IAAjDA,4DAAA,gBAAiD;IAAAA,oDAAA,cAAC;IAAAA,0DAAA,EAAO;IACzDA,4DAAA,gBAEC;IACCA,4DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAC9EA,uDAAA,gBAAwK;IAC1KA,0DAAA,EAAM;IACNA,oDAAA,IACF;;IAAAA,0DAAA,EAAO;IAMbA,6DAAA,EAAiB;IAAjBA,4DAAA,eAAiB;IAIbA,oDAAA,IACF;IAAAA,0DAAA,EAAI;IAGJA,wDAAA,KAAAyH,iDAAA,kBAwEM;IAGNzH,4DAAA,eAEC;IAMKA,4DAAA,EAKC;IALDA,4DAAA,eAKC;IACCA,uDAAA,gBAKQ;IACVA,0DAAA,EAAM;IAENA,6DAAA,EAEC;IAFDA,uDAAA,eAEO;IACTA,0DAAA,EAAM;IACNA,4DAAA,YAAM;IAAAA,oDAAA,oBAAO;IAAAA,0DAAA,EAAO;IAGtBA,wDAAA,KAAA0H,0DAAA,2BAuBe;IAEf1H,wDAAA,KAAA2H,0DAAA,2BAiBe;IACjB3H,0DAAA,EAAM;;;;;IA5LIA,uDAAA,GAAkC;IAAlCA,wDAAA,YAAAwB,MAAA,CAAAsD,cAAA,CAAAyC,SAAA,EAAkC;IACtCvH,uDAAA,GACF;IADEA,gEAAA,MAAAwB,MAAA,CAAAwD,aAAA,CAAAuC,SAAA,OACF;IAMAvH,uDAAA,GACF;IADEA,gEAAA,MAAAuH,SAAA,CAAAZ,KAAA,MACF;IAQI3G,uDAAA,GACF;IADEA,gEAAA,MAAAuH,SAAA,CAAAjF,MAAA,gBACF;IAQEtC,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,SAAAuH,SAAA,CAAAnF,UAAA,qBACF;IAUFpC,uDAAA,GACF;IADEA,gEAAA,MAAAuH,SAAA,CAAAxF,WAAA,8BACF;IAIG/B,uDAAA,GAAmD;IAAnDA,wDAAA,SAAAuH,SAAA,CAAApG,QAAA,IAAAoG,SAAA,CAAApG,QAAA,CAAAc,MAAA,KAAmD;IA8ElDjC,uDAAA,GAA+C;IAA/CA,wDAAA,eAAAA,6DAAA,KAAA4H,GAAA,EAAAL,SAAA,CAAAC,GAAA,EAA+C;IAyBlCxH,uDAAA,GAAyB;IAAzBA,wDAAA,SAAAwB,MAAA,CAAAqG,OAAA,CAAAN,SAAA,CAAAC,GAAA,EAAyB;IAyBzBxH,uDAAA,GAA0B;IAA1BA,wDAAA,UAAAwB,MAAA,CAAAqG,OAAA,CAAAN,SAAA,CAAAC,GAAA,EAA0B;;;;;;IAjMjDxH,6DAAA,EAGC;IAHDA,4DAAA,cAGC;IACCA,wDAAA,IAAA8H,0CAAA,oBAiNM;IACR9H,0DAAA,EAAM;;;;IAjNiBA,uDAAA,GAAU;IAAVA,wDAAA,YAAA+H,MAAA,CAAAC,OAAA,CAAU;;;ADtMrC;AAMM,MAAOC,oBAAoB;EAM/BxF,YACUG,aAA4B,EAC5BE,WAA4B,EAC5BD,aAA4B,EAC5BqF,WAAwB;IAHxB,KAAAtF,aAAa,GAAbA,aAAa;IACb,KAAAE,WAAW,GAAXA,WAAW;IACX,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAqF,WAAW,GAAXA,WAAW;IATrB,KAAAF,OAAO,GAAa,EAAE;IACtB,KAAAG,SAAS,GAAyB,IAAIC,GAAG,EAAE;IAC3C,KAAArF,SAAS,GAAG,IAAI;IAChB,KAAAsF,SAAS,GAAW,EAAE;EAOnB;EAEHrF,QAAQA,CAAA;IACN;IACA,IAAI,CAACqF,SAAS,GAAG,IAAI,CAACvF,WAAW,CAACwF,cAAc,EAAE,EAAEhG,MAAM,IAAI,EAAE;IAChE,IAAI,CAACiG,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACxF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,aAAa,CAAC4F,UAAU,EAAE,CAACjF,SAAS,CAAC;MACxCC,IAAI,EAAGwE,OAAiB,IAAI;QAC1B;QACA,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACjF,SAAS,GAAG,KAAK;QAEtB;QACA,IAAI,CAACiF,OAAO,CAACS,OAAO,CAAEvH,MAAM,IAAI;UAC9B,IAAIA,MAAM,CAACsG,GAAG,EAAE;YACd,IAAI,CAACnE,gBAAgB,CAACnC,MAAM,CAACsG,GAAG,CAAC;;QAErC,CAAC,CAAC;MACJ,CAAC;MACD/D,KAAK,EAAGA,KAAU,IAAI;QACpBE,OAAO,CAACF,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,IAAI,CAACV,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAM,gBAAgBA,CAAC5B,QAAgB;IAC/B,MAAMoC,UAAU,GAAG,IAAI,CAACf,WAAW,CAACgB,gBAAgB,EAAE;IACtD,IAAI,CAACD,UAAU,EAAE;IAEjB,IAAI,CAAChB,aAAa,CAACkB,gBAAgB,CAACtC,QAAQ,EAAEoC,UAAU,CAAC,CAACN,SAAS,CAAC;MAClEC,IAAI,EAAGQ,MAAe,IAAI;QACxB,IAAI,CAACmE,SAAS,CAACO,GAAG,CAACjH,QAAQ,EAAEuC,MAAM,CAAC;MACtC,CAAC;MACDP,KAAK,EAAGA,KAAU,IAAI;QACpBE,OAAO,CAACF,KAAK,CACX,0DAA0DhC,QAAQ,EAAE,EACpEgC,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEA5C,UAAUA,CAACqD,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAG2E,qEAAW,CAAC+B,UAAU,uBAAuBxE,QAAQ,EAAE;EACnE;EAEAzD,WAAWA,CAACwD,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOiC,QAAQ;EACjB;EAEA;EACA2D,OAAOA,CAACpG,QAA4B;IAClC,OAAOA,QAAQ,GAAG,IAAI,CAAC0G,SAAS,CAAChF,GAAG,CAAC1B,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK;EACjE;EAEA;EACAqF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACkB,OAAO,CAAC/F,MAAM;EAC5B;EAEA8E,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACiB,OAAO,CAACY,MAAM,CAAC1H,MAAM,IAC/BA,MAAM,CAACsG,GAAG,IAAI,IAAI,CAACK,OAAO,CAAC3G,MAAM,CAACsG,GAAG,CAAC,CACvC,CAACvF,MAAM;EACV;EAEA+E,eAAeA,CAAA;IACb,OAAO,IAAI,CAACgB,OAAO,CAACY,MAAM,CAAC1H,MAAM,IAC/BA,MAAM,CAACsG,GAAG,IAAI,CAAC,IAAI,CAACK,OAAO,CAAC3G,MAAM,CAACsG,GAAG,CAAC,CACxC,CAACvF,MAAM;EACV;EAEAgF,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACe,OAAO,CAAC/F,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IACvC,OAAOmD,IAAI,CAACyD,KAAK,CAAE,IAAI,CAAC9B,cAAc,EAAE,GAAG,IAAI,CAACiB,OAAO,CAAC/F,MAAM,GAAI,GAAG,CAAC;EACxE;EAEA;EACA6G,iBAAiBA,CAAA;IACf,MAAMtE,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAME,cAAc,GAAG,IAAIF,IAAI,EAAE;IACjCE,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAEpD,OAAO,IAAI,CAACmD,OAAO,CAACY,MAAM,CAAC1H,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAACkB,UAAU,IAAI,IAAI,CAACyF,OAAO,CAAC3G,MAAM,CAACsG,GAAG,CAAC,EAAE,OAAO,KAAK;MAChE,MAAM9C,QAAQ,GAAG,IAAID,IAAI,CAACvD,MAAM,CAACkB,UAAU,CAAC;MAC5C,OAAOsC,QAAQ,IAAIF,GAAG,IAAIE,QAAQ,IAAIC,cAAc;IACtD,CAAC,CAAC;EACJ;EAEA;EACAoE,kBAAkBA,CAAA;IAChB,MAAMvE,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,OAAO,IAAI,CAACuD,OAAO,CAACY,MAAM,CAAC1H,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAACkB,UAAU,IAAI,IAAI,CAACyF,OAAO,CAAC3G,MAAM,CAACsG,GAAG,CAAC,EAAE,OAAO,KAAK;MAChE,MAAM9C,QAAQ,GAAG,IAAID,IAAI,CAACvD,MAAM,CAACkB,UAAU,CAAC;MAC5C,OAAOsC,QAAQ,GAAGF,GAAG;IACvB,CAAC,CAAC;EACJ;EAEA;EACAD,gBAAgBA,CAACrD,MAAc;IAC7B,IAAI,IAAI,CAAC2G,OAAO,CAAC3G,MAAM,CAACsG,GAAG,CAAC,EAAE,OAAO,WAAW;IAEhD,IAAI,CAACtG,MAAM,CAACkB,UAAU,EAAE,OAAO,QAAQ;IAEvC,MAAMoC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAACvD,MAAM,CAACkB,UAAU,CAAC;IAE5C,IAAIsC,QAAQ,GAAGF,GAAG,EAAE,OAAO,SAAS;IAEpC,MAAMG,cAAc,GAAG,IAAIF,IAAI,EAAE;IACjCE,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAEpD,IAAIH,QAAQ,IAAIC,cAAc,EAAE,OAAO,QAAQ;IAE/C,OAAO,QAAQ;EACjB;EAEA;EACAG,cAAcA,CAAC5D,MAAc;IAC3B,MAAM6D,MAAM,GAAG,IAAI,CAACR,gBAAgB,CAACrD,MAAM,CAAC;IAE5C,QAAQ6D,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,sEAAsE;MAC/E,KAAK,QAAQ;QACX,OAAO,0EAA0E;MACnF,KAAK,SAAS;QACZ,OAAO,8DAA8D;MACvE;QACE,OAAO,kEAAkE;;EAE/E;EAEA;EACAC,aAAaA,CAAC9D,MAAc;IAC1B,MAAM6D,MAAM,GAAG,IAAI,CAACR,gBAAgB,CAACrD,MAAM,CAAC;IAE5C,QAAQ6D,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,OAAO;MAChB,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,SAAS;QACZ,OAAO,QAAQ;MACjB;QACE,OAAO,OAAO;;EAEpB;;;uBAzLWkD,oBAAoB,EAAAjI,+DAAA,CAAAwF,wEAAA,GAAAxF,+DAAA,CAAA2F,8EAAA,GAAA3F,+DAAA,CAAA6F,0EAAA,GAAA7F,+DAAA,CAAA+F,sEAAA;IAAA;EAAA;;;YAApBkC,oBAAoB;MAAAhC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4C,8BAAA1C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdjCvG,4DAAA,aAA6E;UAGzEA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAIVA,4DAAA,cAA6C;UAQnCA,oDAAA,qBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAAwE;UACtEA,oDAAA,uEACF;UAAAA,0DAAA,EAAI;UAENA,4DAAA,eAEC;UAECA,uDAAA,eAEO;UAEPA,4DAAA,EAMC;UANDA,4DAAA,eAMC;UACCA,uDAAA,gBAKE;UACJA,0DAAA,EAAM;UAKVA,wDAAA,KAAAkJ,oCAAA,mBAgEM;UAGNlJ,wDAAA,KAAAmJ,oCAAA,mBAaM;UACRnJ,0DAAA,EAAM;UAGNA,wDAAA,KAAAoJ,oCAAA,kBAUM;UAGNpJ,wDAAA,KAAAqJ,oCAAA,kBAiCM;UAGNrJ,wDAAA,KAAAsJ,oCAAA,kBAsNM;UACRtJ,0DAAA,EAAM;;;UA5VuDA,uDAAA,IAAgB;UAAhBA,wDAAA,UAAAwG,GAAA,CAAAzD,SAAA,CAAgB;UAmEsE/C,uDAAA,GAAsC;UAAtCA,wDAAA,UAAAwG,GAAA,CAAAzD,SAAA,IAAAyD,GAAA,CAAAwB,OAAA,CAAA/F,MAAA,KAAsC;UAiBjLjC,uDAAA,GAAe;UAAfA,wDAAA,SAAAwG,GAAA,CAAAzD,SAAA,CAAe;UAclB/C,uDAAA,GAAwC;UAAxCA,wDAAA,UAAAwG,GAAA,CAAAzD,SAAA,IAAAyD,GAAA,CAAAwB,OAAA,CAAA/F,MAAA,OAAwC;UAoCxCjC,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAwG,GAAA,CAAAzD,SAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzM6C;;;;;;;;;;IC0ChE/C,4DAAA,cAAyD;IAErDA,uDAAA,cAAwJ;IAG1JA,0DAAA,EAAM;;;;;IA0CEA,4DAAA,eAC8D;IAC5DA,4DAAA,EAA2E;IAA3EA,4DAAA,aAA2E;IACzEA,uDAAA,gBAAmI;IACrIA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,+EAAmE;IAAAA,0DAAA,EAAO;;;;;IAsClFA,4DAAA,eAA2H;IACzHA,4DAAA,EAA2E;IAA3EA,4DAAA,aAA2E;IACzEA,uDAAA,gBAAmI;IACrIA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,sDAA0C;IAAAA,0DAAA,EAAO;;;;;;IAUrDA,4DAAA,eAAkM;IAG5LA,4DAAA,EAA8G;IAA9GA,4DAAA,cAA8G;IAC5GA,uDAAA,eAAsM;IACxMA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,UAAK;IAC+DA,oDAAA,GAAe;IAAAA,0DAAA,EAAI;IACrFA,4DAAA,aAAsD;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAI;IAG1FA,4DAAA,mBAAwG;IAAlFA,wDAAA,mBAAAyJ,iFAAA;MAAA,MAAAC,WAAA,GAAA1J,2DAAA,CAAA4J,IAAA;MAAA,MAAAC,KAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,OAAA,GAAA/J,2DAAA;MAAA,OAASA,yDAAA,CAAA+J,OAAA,CAAAG,UAAA,CAAAL,KAAA,CAAa;IAAA,EAAC;IAC3C7J,4DAAA,EAA2E;IAA3EA,4DAAA,cAA2E;IACzEA,uDAAA,iBAAsG;IACxGA,0DAAA,EAAM;;;;;IAP8DA,uDAAA,GAAe;IAAfA,+DAAA,CAAAmK,QAAA,CAAAC,IAAA,CAAe;IAC3BpK,uDAAA,GAA4B;IAA5BA,+DAAA,CAAAqK,OAAA,CAAAC,WAAA,CAAAH,QAAA,CAAAI,IAAA,EAA4B;;;;;IAf5FvK,4DAAA,cAAwD;IAEgBA,oDAAA,wCAAuB;IAAAA,0DAAA,EAAI;IAC/FA,4DAAA,eAAyD;IAAAA,oDAAA,GAA2E;IAAAA,0DAAA,EAAO;IAE7IA,4DAAA,eAAoC;IAClCA,wDAAA,IAAAwK,uDAAA,oBAiBM;IACRxK,0DAAA,EAAM;;;;IArBqDA,uDAAA,GAA2E;IAA3EA,gEAAA,KAAA+H,MAAA,CAAA0C,aAAA,CAAAxI,MAAA,cAAA8F,MAAA,CAAA0C,aAAA,CAAAxI,MAAA,oBAA2E;IAG9GjC,uDAAA,GAAkB;IAAlBA,wDAAA,YAAA+H,MAAA,CAAA0C,aAAA,CAAkB;;;;;IA0DxCzK,4DAAA,EAAiG;IAAjGA,4DAAA,cAAiG;IAC/FA,uDAAA,eAAuK;IACzKA,0DAAA,EAAM;;;;;IACNA,uDAAA,eAAkH;;;;;IAClHA,4DAAA,WAA4B;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAO;;;;;IACtDA,4DAAA,WAA2B;IAAAA,oDAAA,6BAAsB;IAAAA,0DAAA,EAAO;;;;;IAO5DA,4DAAA,eAAkK;IAChKA,4DAAA,EAA2E;IAA3EA,4DAAA,aAA2E;IACzEA,uDAAA,gBAA2N;IAC7NA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,kDAAsC;IAAAA,0DAAA,EAAO;;;;;IAErDA,4DAAA,eAA4J;IAC1JA,4DAAA,EAA2E;IAA3EA,4DAAA,aAA2E;IACzEA,uDAAA,gBAA+H;IACjIA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,iCAAgB;IAAAA,0DAAA,EAAO;;;;;;;;;IA/KzCA,4DAAA,cAAgF;IAStEA,4DAAA,EAA4G;IAA5GA,4DAAA,cAA4G;IAC1GA,uDAAA,eAAwG;IAC1GA,0DAAA,EAAM;IAERA,6DAAA,EAAqE;IAArEA,4DAAA,aAAqE;IAAAA,oDAAA,mCAA4B;IAAAA,0DAAA,EAAK;IAGxGA,4DAAA,eAAiG;IAA1CA,wDAAA,sBAAA0K,oEAAA;MAAA1K,2DAAA,CAAA2K,IAAA;MAAA,MAAAC,OAAA,GAAA5K,2DAAA;MAAA,OAAYA,yDAAA,CAAA4K,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAC5E7K,4DAAA,eAAuB;IAEnBA,4DAAA,EAA4G;IAA5GA,4DAAA,eAA4G;IAC1GA,uDAAA,gBAAsM;IACxMA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IAAAA,oDAAA,yBAAiB;IAAAA,0DAAA,EAAO;IAC9BA,4DAAA,gBAA2B;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAO;IAErCA,4DAAA,eAAsB;IACpBA,uDAAA,oBAMY;IACdA,0DAAA,EAAM;IACNA,4DAAA,eAA6E;IACrEA,oDAAA,yCAA4B;IAAAA,0DAAA,EAAO;IACzCA,4DAAA,YAAM;IAAAA,oDAAA,IAAsE;IAAAA,0DAAA,EAAO;IAErFA,wDAAA,KAAA8K,iDAAA,kBAMM;IACR9K,0DAAA,EAAM;IAGNA,4DAAA,eAAuB;IAEnBA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAAuK;IACzKA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IAAAA,oDAAA,0BAAkB;IAAAA,0DAAA,EAAO;IAC/BA,4DAAA,gBAA2B;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAO;IAErCA,4DAAA,eAAsB;IAKlBA,wDAAA,oBAAA+K,oEAAAC,MAAA;MAAAhL,2DAAA,CAAA2K,IAAA;MAAA,MAAAM,OAAA,GAAAjL,2DAAA;MAAA,OAAUA,yDAAA,CAAAiL,OAAA,CAAAC,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAJjChL,0DAAA,EAME;IACFA,4DAAA,eAA6O;IAGvOA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAAuK;IACzKA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,WAAK;IACuDA,oDAAA,gCAAwB;IAAAA,0DAAA,EAAI;IACtFA,4DAAA,aAAsD;IAAAA,oDAAA,iCAAyB;IAAAA,0DAAA,EAAI;IAErFA,4DAAA,aAAsD;IAAAA,oDAAA,4CAA+B;IAAAA,0DAAA,EAAI;IAM/FA,wDAAA,KAAAmL,iDAAA,kBAKM;IAGNnL,wDAAA,KAAAoL,iDAAA,kBAyBM;IACRpL,0DAAA,EAAM;IAKVA,4DAAA,eAA0I;IAGpIA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAAqjB;IAEvjBA,0DAAA,EAAM;IAERA,6DAAA,EAAqE;IAArEA,4DAAA,cAAqE;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAK;IAGnFA,4DAAA,eAAuB;IAOfA,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAA6G;IAC/GA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IAAAA,oDAAA,+BAAkB;IAAAA,0DAAA,EAAO;IAKnCA,4DAAA,kBAKC;IAEGA,wDAAA,KAAAqL,sDAAA,kBAEM;IACNrL,wDAAA,KAAAsL,iDAAA,kBAAkH;IAClHtL,wDAAA,KAAAuL,kDAAA,mBAAsD;IACtDvL,wDAAA,KAAAwL,kDAAA,mBAAwD;IAC1DxL,0DAAA,EAAM;IAKVA,4DAAA,eAAyB;IACvBA,wDAAA,KAAAyL,iDAAA,kBAKM;IACNzL,wDAAA,KAAA0L,iDAAA,kBAKM;IACR1L,0DAAA,EAAM;IAOZA,4DAAA,eAAuB;IAObA,4DAAA,EAA2E;IAA3EA,4DAAA,eAA2E;IACzEA,uDAAA,gBAA2I;IAC7IA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,WAAK;IAC+BA,oDAAA,oBAAY;IAAAA,0DAAA,EAAK;IACnDA,4DAAA,aAAiC;IAAAA,oDAAA,8BAAiB;IAAAA,0DAAA,EAAI;IAK5DA,4DAAA,eAA2B;IAKnBA,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAAsN;IACxNA,0DAAA,EAAM;IAERA,6DAAA,EAAoB;IAApBA,4DAAA,eAAoB;IACyEA,oDAAA,aAAK;IAAAA,0DAAA,EAAI;IACpGA,4DAAA,aAAkF;IAAAA,oDAAA,IAAkB;IAAAA,0DAAA,EAAI;IAM9GA,4DAAA,eAAiE;IAG3DA,4DAAA,EAA4G;IAA5GA,4DAAA,gBAA4G;IAC1GA,uDAAA,iBAAwG;IAC1GA,0DAAA,EAAM;IAERA,6DAAA,EAAoB;IAApBA,4DAAA,gBAAoB;IACyEA,oDAAA,oBAAW;IAAAA,0DAAA,EAAI;IAC1GA,4DAAA,cAAiF;IAAAA,oDAAA,KAAgD;IAAAA,0DAAA,EAAI;IAM3IA,4DAAA,gBAAiE;IAIzDA,4DAAA,EAAgH;IAAhHA,4DAAA,gBAAgH;IAC9GA,uDAAA,iBAA6H;IAC/HA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,YAAK;IACwFA,oDAAA,oBAAW;IAAAA,0DAAA,EAAI;IAC1GA,4DAAA,cAAoE;IAAAA,oDAAA,KAA6C;;IAAAA,0DAAA,EAAI;IAGzHA,4DAAA,gBAAwB;IACgCA,oDAAA,KAA8B;IAAAA,0DAAA,EAAI;IACxFA,4DAAA,cAAwD;IAAAA,oDAAA,iBAAQ;IAAAA,0DAAA,EAAI;IAM1EA,4DAAA,gBAAiE;IAG3DA,4DAAA,EAAgH;IAAhHA,4DAAA,gBAAgH;IAC9GA,uDAAA,iBAAwV;IAC1VA,0DAAA,EAAM;IAERA,6DAAA,EAAK;IAALA,4DAAA,YAAK;IACwFA,oDAAA,qBAAY;IAAAA,0DAAA,EAAI;IAC3GA,4DAAA,cAAoE;IAAAA,oDAAA,KAAyC;IAAAA,0DAAA,EAAI;IAQ3HA,4DAAA,gBAA0I;IAGpIA,4DAAA,EAA4G;IAA5GA,4DAAA,gBAA4G;IAC1GA,uDAAA,iBAA2I;IAC7IA,0DAAA,EAAM;IAERA,6DAAA,EAAqE;IAArEA,4DAAA,eAAqE;IAAAA,oDAAA,iBAAQ;IAAAA,0DAAA,EAAK;IAGpFA,4DAAA,gBAAkE;IAE9DA,4DAAA,EAAmI;IAAnIA,4DAAA,iBAAmI;IACjIA,uDAAA,kBAA+H;IACjIA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,aAAM;IAAAA,oDAAA,kFAA+D;IAAAA,0DAAA,EAAO;IAE9EA,4DAAA,iBAAwC;IACtCA,4DAAA,EAAmI;IAAnIA,4DAAA,iBAAmI;IACjIA,uDAAA,kBAA+H;IACjIA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,aAAM;IAAAA,oDAAA,8DAAqD;IAAAA,0DAAA,EAAO;IAEpEA,4DAAA,iBAAwC;IACtCA,4DAAA,EAAmI;IAAnIA,4DAAA,iBAAmI;IACjIA,uDAAA,kBAA+H;IACjIA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,aAAM;IAAAA,oDAAA,qFAA6D;IAAAA,0DAAA,EAAO;IAE5EA,4DAAA,iBAAwC;IACtCA,4DAAA,EAAqI;IAArIA,4DAAA,iBAAqI;IACnIA,uDAAA,kBAA2N;IAC7NA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,aAAM;IAAAA,oDAAA,6EAAqD;IAAAA,0DAAA,EAAO;;;;;;IAjS5CA,uDAAA,GAA4B;IAA5BA,wDAAA,cAAA8B,MAAA,CAAA6J,cAAA,CAA4B;IAoB1C3L,uDAAA,IAAsE;IAAtEA,gEAAA,OAAA4L,OAAA,GAAA9J,MAAA,CAAA6J,cAAA,CAAAxI,GAAA,kCAAAyI,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAA5J,MAAA,2BAAsE;IAExEjC,uDAAA,GAA8F;IAA9FA,wDAAA,WAAA8L,OAAA,GAAAhK,MAAA,CAAA6J,cAAA,CAAAxI,GAAA,kCAAA2I,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAhK,MAAA,CAAA6J,cAAA,CAAAxI,GAAA,kCAAA2I,OAAA,CAAAE,OAAA,EAA8F;IA2C9FhM,uDAAA,IAA0D;IAA1DA,wDAAA,SAAA8B,MAAA,CAAA2I,aAAA,CAAAxI,MAAA,UAAAH,MAAA,CAAA6J,cAAA,CAAAK,OAAA,CAA0D;IAQ1DhM,uDAAA,GAA8B;IAA9BA,wDAAA,SAAA8B,MAAA,CAAA2I,aAAA,CAAAxI,MAAA,KAA8B;IA8CjCjC,uDAAA,IAA6C;IAA7CA,wDAAA,eAAAA,6DAAA,KAAAuB,GAAA,EAAAO,MAAA,CAAAL,QAAA,EAA6C;IAc9CzB,uDAAA,GAAiF;IAAjFA,wDAAA,aAAA8B,MAAA,CAAA6J,cAAA,CAAAI,OAAA,IAAAjK,MAAA,CAAA2I,aAAA,CAAAxI,MAAA,UAAAH,MAAA,CAAAmK,YAAA,CAAiF;IAIzEjM,uDAAA,GAAmB;IAAnBA,wDAAA,UAAA8B,MAAA,CAAAmK,YAAA,CAAmB;IAGnBjM,uDAAA,GAAkB;IAAlBA,wDAAA,SAAA8B,MAAA,CAAAmK,YAAA,CAAkB;IACjBjM,uDAAA,GAAmB;IAAnBA,wDAAA,UAAA8B,MAAA,CAAAmK,YAAA,CAAmB;IACnBjM,uDAAA,GAAkB;IAAlBA,wDAAA,SAAA8B,MAAA,CAAAmK,YAAA,CAAkB;IAOvBjM,uDAAA,GAA0D;IAA1DA,wDAAA,SAAA8B,MAAA,CAAA6J,cAAA,CAAAI,OAAA,IAAAjK,MAAA,CAAA2I,aAAA,CAAAxI,MAAA,OAA0D;IAM1DjC,uDAAA,GAAsD;IAAtDA,wDAAA,SAAA8B,MAAA,CAAA6J,cAAA,CAAAO,KAAA,IAAApK,MAAA,CAAA2I,aAAA,CAAAxI,MAAA,KAAsD;IA0C0BjC,uDAAA,IAAkB;IAAlBA,+DAAA,CAAA8B,MAAA,CAAAZ,MAAA,CAAAyF,KAAA,CAAkB;IAenB3G,uDAAA,IAAgD;IAAhDA,+DAAA,CAAA8B,MAAA,CAAAZ,MAAA,CAAAa,WAAA,yBAAgD;IAgB3D/B,uDAAA,IAA6C;IAA7CA,+DAAA,CAAAA,yDAAA,UAAA8B,MAAA,CAAAZ,MAAA,CAAAkB,UAAA,gBAA6C;IAI7DpC,uDAAA,GAA8B;IAA9BA,gEAAA,KAAA8B,MAAA,CAAAO,gBAAA,aAA8B;IAgBhBrC,uDAAA,IAAyC;IAAzCA,+DAAA,CAAA8B,MAAA,CAAAZ,MAAA,CAAAoB,MAAA,uBAAyC;;;ADrT/H;AAMM,MAAO6J,0BAA0B;EAQrC1J,YACU2J,EAAe,EACf1J,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAA4B;IAL5B,KAAAsJ,EAAE,GAAFA,EAAE;IACF,KAAA1J,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAbrB,KAAArB,QAAQ,GAAW,EAAE;IAGrB,KAAAgJ,aAAa,GAAW,EAAE;IAC1B,KAAA1H,SAAS,GAAG,IAAI;IAChB,KAAAkJ,YAAY,GAAG,KAAK;IAUlB,IAAI,CAACN,cAAc,GAAG,IAAI,CAACS,EAAE,CAACC,KAAK,CAAC;MAClCtK,WAAW,EAAE,CAAC,EAAE,EAAE,CAACwH,sDAAU,CAAC+C,QAAQ,EAAE/C,sDAAU,CAACgD,SAAS,CAAC,EAAE,CAAC,CAAC;KAClE,CAAC;EACJ;EAEAvJ,QAAQA,CAAA;IACN,IAAI,CAACvB,QAAQ,GAAG,IAAI,CAACiB,KAAK,CAACO,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC5D,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,aAAa,CAACU,aAAa,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAAC8B,SAAS,CAAC;MACxDC,IAAI,EAAGtC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAAC6B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDU,KAAK,EAAGC,GAAU,IAAI;QACpBC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEC,GAAG,CAAC;QACzD,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC;KACD,CAAC;EACJ;EAEAsH,YAAYA,CAACsB,KAAY;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAE;MACf,IAAI,CAAClC,aAAa,GAAGmC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAACE,KAAK,CAAC;;EAEhD;EAEA9B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACc,cAAc,CAACI,OAAO,IAAI,IAAI,CAACtB,aAAa,CAACxI,MAAM,KAAK,CAAC,EAAE;MAClE;;IAGF,IAAI,CAACgK,YAAY,GAAG,IAAI;IACxB,MAAMa,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACvL,QAAQ,CAAC;IACxCqL,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAAClK,WAAW,CAACgB,gBAAgB,EAAE,IAAI,EAAE,CAAC;IACtEgJ,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,IAAI,CAACrB,cAAc,CAACE,KAAK,CAAC9J,WAAW,CAAC;IAErE,IAAI,CAAC0I,aAAa,CAAChC,OAAO,CAAEwE,IAAI,IAAI;MAClCH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEC,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAACpK,aAAa,CAACqK,WAAW,CAACJ,QAAQ,CAAC,CAACvJ,SAAS,CAAC;MACjDC,IAAI,EAAG2J,QAAa,IAAI;QACtBC,KAAK,CAAC,uCAAuC,CAAC;QAC9C,IAAI,CAACzK,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACDH,KAAK,EAAGC,GAAU,IAAI;QACpBC,OAAO,CAACF,KAAK,CAAC,wCAAwC,EAAEC,GAAG,CAAC;QAC5D0J,KAAK,CAAC,yDAAyD,CAAC;QAChE,IAAI,CAACnB,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEA;EACA/B,UAAUA,CAACJ,KAAa;IACtB,IAAI,CAACW,aAAa,CAAC4C,MAAM,CAACvD,KAAK,EAAE,CAAC,CAAC;EACrC;EAEA;EACAQ,WAAWA,CAACgD,KAAa;IACvB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAE7B,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,MAAMC,CAAC,GAAGrI,IAAI,CAACsI,KAAK,CAACtI,IAAI,CAACnB,GAAG,CAACqJ,KAAK,CAAC,GAAGlI,IAAI,CAACnB,GAAG,CAACsJ,CAAC,CAAC,CAAC;IAEnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGlI,IAAI,CAACwI,GAAG,CAACL,CAAC,EAAEE,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGL,KAAK,CAACC,CAAC,CAAC;EACzE;EAEA;EACApL,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAEkB,UAAU,EAAE,OAAO,CAAC;IAEtC,MAAMoC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAAC,IAAI,CAACvD,MAAM,CAACkB,UAAU,CAAC;IACjD,MAAM6C,QAAQ,GAAGP,QAAQ,CAACQ,OAAO,EAAE,GAAGV,GAAG,CAACU,OAAO,EAAE;IACnD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,OAAOG,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;EAC9B;;;uBAtGWgH,0BAA0B,EAAAnM,+DAAA,CAAAwF,uDAAA,GAAAxF,+DAAA,CAAA2F,2DAAA,GAAA3F,+DAAA,CAAA2F,mDAAA,GAAA3F,+DAAA,CAAA6F,wEAAA,GAAA7F,+DAAA,CAAA+F,0EAAA,GAAA/F,+DAAA,CAAA+N,8EAAA;IAAA;EAAA;;;YAA1B5B,0BAA0B;MAAAlG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2H,oCAAAzH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbvCvG,4DAAA,aAA0K;UAGtKA,uDAAA,aAA6K;UAE/KA,0DAAA,EAAM;UAENA,4DAAA,aAAuD;UAKkDA,oDAAA,kBAAW;UAAAA,0DAAA,EAAI;UAClHA,4DAAA,EAA2E;UAA3EA,4DAAA,aAA2E;UACzEA,uDAAA,eAA8F;UAChGA,0DAAA,EAAM;UACNA,6DAAA,EAA0H;UAA1HA,4DAAA,aAA0H;UAAAA,oDAAA,IAA+B;UAAAA,0DAAA,EAAI;UAC7JA,4DAAA,EAA2E;UAA3EA,4DAAA,cAA2E;UACzEA,uDAAA,eAA8F;UAChGA,0DAAA,EAAM;UACNA,6DAAA,EAA6D;UAA7DA,4DAAA,gBAA6D;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAO;UAG/EA,4DAAA,eAA0I;UAGpIA,4DAAA,EAAsF;UAAtFA,4DAAA,eAAsF;UACpFA,uDAAA,gBAAuK;UACzKA,0DAAA,EAAM;UAERA,6DAAA,EAAK;UAALA,4DAAA,WAAK;UAEDA,oDAAA,8BACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAA8C;UAC5CA,oDAAA,2EACF;UAAAA,0DAAA,EAAI;UAOZA,wDAAA,KAAAiO,0CAAA,kBAMM;UAGNjO,wDAAA,KAAAkO,0CAAA,qBAsTM;UACRlO,0DAAA,EAAM;;;UA3VGA,uDAAA,IAA6C;UAA7CA,wDAAA,eAAAA,6DAAA,IAAAuB,GAAA,EAAAiF,GAAA,CAAA/E,QAAA,EAA6C;UAA0EzB,uDAAA,GAA+B;UAA/BA,+DAAA,EAAAwG,GAAA,CAAAtF,MAAA,kBAAAsF,GAAA,CAAAtF,MAAA,CAAAyF,KAAA,cAA+B;UA2BvJ3G,uDAAA,IAAe;UAAfA,wDAAA,SAAAwG,GAAA,CAAAzD,SAAA,CAAe;UASf/C,uDAAA,GAA0B;UAA1BA,wDAAA,SAAAwG,GAAA,CAAAtF,MAAA,KAAAsF,GAAA,CAAAzD,SAAA,CAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;ACnDmB;AACsB;AACM;AACY;;;AAE/F,MAAMqL,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAErG,sFAAoBA;AAAA,CAAE,EAC7C;EAAEoG,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAE9L,4FAAsBA;AAAA,CAAE,EACzD;EAAE6L,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEnC,wGAA0BA;AAAA,CAAE,CAC9D;AAMK,MAAOoC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBJ,yDAAY,CAACK,QAAQ,CAACJ,MAAM,CAAC,EAC7BD,yDAAY;IAAA;EAAA;;;sHAEXI,qBAAqB;IAAAE,OAAA,GAAAjJ,yDAAA;IAAAkJ,OAAA,GAFtBP,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACmB;AACW;AACM;AACY;AAC7B;AACnB;;AAgBzC,MAAOW,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAPvBH,yDAAY,EACZJ,2EAAqB,EACrBK,uDAAW,EACXC,+DAAmB,EACnBV,yDAAY;IAAA;EAAA;;;sHAGHW,cAAc;IAAAC,YAAA,GAZvB9G,sFAAoB,EACpBzF,4FAAsB,EACtB2J,wGAA0B;IAAAsC,OAAA,GAG1BE,yDAAY,EACZJ,2EAAqB,EACrBK,uDAAW,EACXC,+DAAmB,EACnBV,yDAAY;EAAA;AAAA", "sources": ["./src/app/views/front/projects/project-detail/project-detail.component.ts", "./src/app/views/front/projects/project-detail/project-detail.component.html", "./src/app/views/front/projects/project-list/project-list.component.ts", "./src/app/views/front/projects/project-list/project-list.component.html", "./src/app/views/front/projects/project-submission/project-submission.component.ts", "./src/app/views/front/projects/project-submission/project-submission.component.html", "./src/app/views/front/projects/projects-routing.module.ts", "./src/app/views/front/projects/projects.module.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProjetService } from '@app/services/projets.service';\nimport { RendusService } from 'src/app/services/rendus.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\n\n// Composant pour afficher les détails d'un projet\n@Component({\n  selector: 'app-project-detail',\n  templateUrl: './project-detail.component.html',\n  styleUrls: ['./project-detail.component.css']\n})\nexport class ProjectDetailComponent implements OnInit {\n  projetId: string = '';\n  projet: any;\n  rendu: any;\n  isLoading = true;\n  hasSubmitted = false;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private projetService: ProjetService,\n    private rendusService: RendusService,\n    private authService: AuthuserService\n  ) {}\n\n  ngOnInit(): void {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n    this.checkRenduStatus();\n  }\n\n  loadProjetDetails(): void {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: (projet: any) => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      },\n    });\n  }\n\n  checkRenduStatus(): void {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (etudiantId) {\n      this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\n        next: (exists: boolean) => {\n          console.log(exists)\n          this.hasSubmitted = exists;\n        },\n        error: (err: any) => {\n          console.error('Erreur lors de la vérification du rendu', err);\n        },\n      });\n    }\n  }\n\n  getFileUrl(filePath: string): string {\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n\n    // Utiliser l'endpoint API spécifique pour le téléchargement\n    return `http://localhost:3000/api/projets/download/${fileName}`;\n  }\n\n  getFileName(filePath: string): string {\n    if (!filePath) return 'Fichier';\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n\n    return filePath;\n  }\n\n  getProjectStatus(): 'completed' | 'urgent' | 'expired' | 'active' {\n    if (this.hasSubmitted) return 'completed';\n\n    if (!this.projet?.dateLimite) return 'active';\n\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n\n    if (deadline < now) return 'expired';\n\n    const oneWeekFromNow = new Date();\n    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n\n    if (deadline <= oneWeekFromNow) return 'urgent';\n\n    return 'active';\n  }\n\n  getStatusClass(): string {\n    const status = this.getProjectStatus();\n\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-800/30';\n      case 'urgent':\n        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400 border border-orange-200 dark:border-orange-800/30';\n      case 'expired':\n        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-800/30';\n      default:\n        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30';\n    }\n  }\n\n  getStatusText(): string {\n    const status = this.getProjectStatus();\n\n    switch (status) {\n      case 'completed':\n        return 'Projet soumis';\n      case 'urgent':\n        return 'Urgent';\n      case 'expired':\n        return 'Expiré';\n      default:\n        return 'Actif';\n    }\n  }\n\n  getRemainingDays(): number {\n    if (!this.projet?.dateLimite) return 0;\n\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    const diffTime = deadline.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    return Math.max(0, diffDays);\n  }\n}\n", "<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary relative\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\n    <div class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\n  </div>\n\n  <div class=\"container mx-auto px-4 py-8 relative z-10\">\n\n    <!-- Header moderne avec breadcrumb -->\n    <div class=\"mb-8\">\n      <nav class=\"flex items-center space-x-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4\">\n        <a routerLink=\"/projects\" class=\"hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\">Mes Projets</a>\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n        </svg>\n        <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ projet?.titre || 'Détails du projet' }}</span>\n      </nav>\n\n      <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n          <div class=\"flex items-center space-x-4 mb-6 lg:mb-0\">\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] flex items-center justify-center shadow-lg\">\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\n              </svg>\n            </div>\n            <div>\n              <h1 class=\"text-3xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\">\n                {{ projet?.titre || 'Chargement...' }}\n              </h1>\n              <div class=\"flex items-center space-x-4 mt-2\">\n                <div class=\"flex items-center space-x-1\">\n                  <svg class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                  </svg>\n                  <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">{{ projet?.groupe || 'Tous les groupes' }}</span>\n                </div>\n                <div class=\"flex items-center space-x-1\">\n                  <svg class=\"w-4 h-4 text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                  <span class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ projet?.dateLimite | date : \"dd/MM/yyyy\" || 'Pas de date limite' }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Badge de statut -->\n          <div class=\"flex items-center space-x-3\">\n            <span [ngClass]=\"getStatusClass()\" class=\"px-4 py-2 rounded-xl text-sm font-medium\">\n              {{ getStatusText() }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\n      <div class=\"relative\">\n        <div class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"></div>\n        <!-- Glow effect -->\n        <div class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"></div>\n      </div>\n    </div>\n\n    <!-- Contenu principal -->\n    <div *ngIf=\"!isLoading\" class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n\n      <!-- Carte principale du projet -->\n      <div class=\"lg:col-span-2 space-y-6\">\n\n        <!-- Description du projet -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Description du projet</h3>\n          </div>\n          <div class=\"prose prose-gray dark:prose-invert max-w-none\">\n            <p class=\"text-[#6d6870] dark:text-[#a0a0a0] leading-relaxed\">\n              {{ projet?.description || 'Aucune description fournie pour ce projet.' }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Fichiers du projet -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-orange-600 dark:text-orange-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">\n              Ressources du projet\n              <span class=\"text-sm font-normal text-[#6d6870] dark:text-[#a0a0a0] ml-2\">\n                ({{ projet?.fichiers?.length || 0 }} fichier{{ (projet?.fichiers?.length || 0) > 1 ? 's' : '' }})\n              </span>\n            </h3>\n          </div>\n\n          <div *ngIf=\"projet?.fichiers?.length > 0\" class=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            <div *ngFor=\"let file of projet.fichiers\" class=\"group\">\n              <div class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-xl p-4 border border-[#edf1f4] dark:border-[#2a2a2a] hover:border-[#4f5fad] dark:hover:border-[#6d78c9] transition-all duration-200 hover:shadow-md\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"flex items-center space-x-3 flex-1 min-w-0\">\n                    <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg group-hover:scale-110 transition-transform\">\n                      <svg class=\"w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"></path>\n                      </svg>\n                    </div>\n                    <div class=\"flex-1 min-w-0\">\n                      <p class=\"text-sm font-medium text-[#3d4a85] dark:text-[#6d78c9] truncate\">\n                        {{ getFileName(file) }}\n                      </p>\n                      <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">Document de projet</p>\n                    </div>\n                  </div>\n                  <a [href]=\"getFileUrl(file)\" download\n                     class=\"ml-3 px-3 py-2 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] rounded-lg transition-all duration-200 text-xs font-medium group-hover:scale-105\">\n                    <div class=\"flex items-center space-x-1\">\n                      <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\n                      </svg>\n                      <span>Télécharger</span>\n                    </div>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div *ngIf=\"!projet?.fichiers || projet.fichiers.length === 0\" class=\"text-center py-8\">\n            <div class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-xl p-6\">\n              <div class=\"bg-[#edf1f4] dark:bg-[#2a2a2a] p-3 rounded-lg inline-flex items-center justify-center mb-3\">\n                <svg class=\"w-6 h-6 text-[#6d6870] dark:text-[#a0a0a0]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                </svg>\n              </div>\n              <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">Aucun fichier joint à ce projet</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Sidebar avec informations et actions -->\n      <div class=\"space-y-6\">\n\n        <!-- Informations du projet -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\">\n          <div class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] p-6 text-white\">\n            <div class=\"flex items-center space-x-3\">\n              <div class=\"bg-white/20 p-2 rounded-lg\">\n                <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n              </div>\n              <div>\n                <h3 class=\"text-lg font-semibold\">Informations</h3>\n                <p class=\"text-sm text-white/80\">Détails du projet</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"p-6 space-y-4\">\n            <!-- Date limite -->\n            <div class=\"flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-orange-600 dark:text-orange-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Date limite</p>\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ projet?.dateLimite | date : \"dd/MM/yyyy\" || 'Non définie' }}</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- Temps restant -->\n            <div class=\"flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Temps restant</p>\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ getRemainingDays() }} jours</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- Groupe -->\n            <div class=\"flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Groupe cible</p>\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ projet?.groupe || 'Tous les groupes' }}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Actions -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Actions</h3>\n          </div>\n\n          <div class=\"space-y-3\">\n            <ng-container *ngIf=\"hasSubmitted\">\n              <div class=\"p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-xl\">\n                <div class=\"flex items-center space-x-3\">\n                  <div class=\"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg\">\n                    <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <p class=\"text-sm font-semibold text-green-800 dark:text-green-400\">Projet soumis</p>\n                    <p class=\"text-xs text-green-600 dark:text-green-500\">Votre rendu a été enregistré avec succès</p>\n                  </div>\n                </div>\n              </div>\n            </ng-container>\n\n            <ng-container *ngIf=\"!hasSubmitted\">\n              <a [routerLink]=\"['/projects/submit', projetId]\"\n                 class=\"block w-full px-6 py-3 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium text-center\">\n                <div class=\"flex items-center justify-center space-x-2\">\n                  <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                  </svg>\n                  <span>Soumettre mon projet</span>\n                </div>\n              </a>\n            </ng-container>\n\n            <!-- Bouton retour -->\n            <a routerLink=\"/projects\"\n               class=\"block w-full px-6 py-3 bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#3d4a85] dark:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-xl transition-all duration-200 font-medium text-center\">\n              <div class=\"flex items-center justify-center space-x-2\">\n                <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\n                </svg>\n                <span>Retour aux projets</span>\n              </div>\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { ProjetService } from '@app/services/projets.service';\nimport { Projet } from 'src/app/models/projet.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { RendusService } from 'src/app/services/rendus.service';\nimport { FileService } from 'src/app/services/file.service';\nimport { environment } from 'src/environments/environment';\n\n// Composant pour afficher la liste des projets\n@Component({\n  selector: 'app-project-list',\n  templateUrl: './project-list.component.html',\n  styleUrls: ['./project-list.component.css'],\n})\nexport class ProjectListComponent implements OnInit {\n  projets: Projet[] = [];\n  rendusMap: Map<string, boolean> = new Map();\n  isLoading = true;\n  userGroup: string = '';\n\n  constructor(\n    private projetService: ProjetService,\n    private authService: AuthuserService,\n    private rendusService: RendusService,\n    private fileService: FileService\n  ) {}\n\n  ngOnInit(): void {\n    // On garde cette ligne pour une utilisation future\n    this.userGroup = this.authService.getCurrentUser()?.groupe || '';\n    this.loadProjets();\n  }\n\n  loadProjets(): void {\n    this.isLoading = true;\n    this.projetService.getProjets().subscribe({\n      next: (projets: Projet[]) => {\n        // Afficher tous les projets sans filtrage\n        this.projets = projets;\n        this.isLoading = false;\n\n        // Vérifier quels projets ont déjà été rendus par l'étudiant\n        this.projets.forEach((projet) => {\n          if (projet._id) {\n            this.checkRenduStatus(projet._id);\n          }\n        });\n      },\n      error: (error: any) => {\n        console.error('Erreur lors du chargement des projets', error);\n        this.isLoading = false;\n      },\n    });\n  }\n\n  checkRenduStatus(projetId: string): void {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (!etudiantId) return;\n\n    this.rendusService.checkRenduExists(projetId, etudiantId).subscribe({\n      next: (exists: boolean) => {\n        this.rendusMap.set(projetId, exists);\n      },\n      error: (error: any) => {\n        console.error(\n          `Erreur lors de la vérification du rendu pour le projet ${projetId}`,\n          error\n        );\n      },\n    });\n  }\n\n  getFileUrl(filePath: string): string {\n    if (!filePath) return '';\n\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n\n    // Utiliser la route qui pointe vers le bon emplacement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n\n  getFileName(filePath: string): string {\n    if (!filePath) return 'fichier';\n\n    // Extraire uniquement le nom du fichier\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n\n    return filePath;\n  }\n\n  // Méthode pour vérifier si un projet a été rendu\n  isRendu(projetId: string | undefined): boolean {\n    return projetId ? this.rendusMap.get(projetId) === true : false;\n  }\n\n  // Méthodes pour les statistiques\n  getTotalProjects(): number {\n    return this.projets.length;\n  }\n\n  getRendusCount(): number {\n    return this.projets.filter(projet =>\n      projet._id && this.isRendu(projet._id)\n    ).length;\n  }\n\n  getPendingCount(): number {\n    return this.projets.filter(projet =>\n      projet._id && !this.isRendu(projet._id)\n    ).length;\n  }\n\n  getSuccessRate(): number {\n    if (this.projets.length === 0) return 0;\n    return Math.round((this.getRendusCount() / this.projets.length) * 100);\n  }\n\n  // Méthode pour obtenir les projets urgents (date limite dans moins de 7 jours)\n  getUrgentProjects(): Projet[] {\n    const now = new Date();\n    const oneWeekFromNow = new Date();\n    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n\n    return this.projets.filter(projet => {\n      if (!projet.dateLimite || this.isRendu(projet._id)) return false;\n      const deadline = new Date(projet.dateLimite);\n      return deadline >= now && deadline <= oneWeekFromNow;\n    });\n  }\n\n  // Méthode pour obtenir les projets expirés\n  getExpiredProjects(): Projet[] {\n    const now = new Date();\n    return this.projets.filter(projet => {\n      if (!projet.dateLimite || this.isRendu(projet._id)) return false;\n      const deadline = new Date(projet.dateLimite);\n      return deadline < now;\n    });\n  }\n\n  // Méthode pour obtenir le statut d'un projet\n  getProjectStatus(projet: Projet): 'completed' | 'urgent' | 'expired' | 'active' {\n    if (this.isRendu(projet._id)) return 'completed';\n\n    if (!projet.dateLimite) return 'active';\n\n    const now = new Date();\n    const deadline = new Date(projet.dateLimite);\n\n    if (deadline < now) return 'expired';\n\n    const oneWeekFromNow = new Date();\n    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n\n    if (deadline <= oneWeekFromNow) return 'urgent';\n\n    return 'active';\n  }\n\n  // Méthode pour obtenir la classe CSS du statut\n  getStatusClass(projet: Projet): string {\n    const status = this.getProjectStatus(projet);\n\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400';\n      case 'urgent':\n        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400';\n      case 'expired':\n        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400';\n      default:\n        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400';\n    }\n  }\n\n  // Méthode pour obtenir le texte du statut\n  getStatusText(projet: Projet): string {\n    const status = this.getProjectStatus(projet);\n\n    switch (status) {\n      case 'completed':\n        return 'Rendu';\n      case 'urgent':\n        return 'Urgent';\n      case 'expired':\n        return 'Expiré';\n      default:\n        return 'Actif';\n    }\n  }\n}\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-[#121212] p-4 md:p-6 relative\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-6xl mx-auto relative z-10\">\n    <!-- Header moderne avec statistiques -->\n    <div class=\"mb-8\">\n      <div class=\"flex flex-col lg:flex-row lg:justify-between lg:items-center mb-6\">\n        <div>\n          <h1\n            class=\"text-3xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n          >\n            Mes Projets\n          </h1>\n          <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm md:text-base mt-1\">\n            Gérez vos missions académiques et suivez vos rendus\n          </p>\n        </div>\n        <div\n          class=\"h-12 w-12 rounded-full bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] flex items-center justify-center text-white shadow-lg relative group overflow-hidden mt-4 lg:mt-0\"\n        >\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-full opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\n          ></div>\n\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            class=\"h-6 w-6 relative z-10 group-hover:scale-110 transition-transform duration-300\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            stroke=\"currentColor\"\n          >\n            <path\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              stroke-width=\"2\"\n              d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            />\n          </svg>\n        </div>\n      </div>\n\n      <!-- Statistiques -->\n      <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\" *ngIf=\"!isLoading\">\n        <!-- Total projets -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-4 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md hover:shadow-lg transition-all duration-300 group\">\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <p class=\"text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider\">Total</p>\n              <p class=\"text-2xl font-bold text-[#3d4a85] dark:text-[#6d78c9]\">{{ getTotalProjects() }}</p>\n              <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">Projets</p>\n            </div>\n            <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-3 rounded-xl group-hover:scale-110 transition-transform\">\n              <svg class=\"w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <!-- Projets rendus -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-4 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md hover:shadow-lg transition-all duration-300 group\">\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <p class=\"text-xs font-medium text-green-600 dark:text-green-400 uppercase tracking-wider\">Rendus</p>\n              <p class=\"text-2xl font-bold text-green-700 dark:text-green-400\">{{ getRendusCount() }}</p>\n              <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">Complétés</p>\n            </div>\n            <div class=\"bg-green-100 dark:bg-green-900/30 p-3 rounded-xl group-hover:scale-110 transition-transform\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <!-- Projets en attente -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-4 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md hover:shadow-lg transition-all duration-300 group\">\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <p class=\"text-xs font-medium text-orange-600 dark:text-orange-400 uppercase tracking-wider\">En attente</p>\n              <p class=\"text-2xl font-bold text-orange-700 dark:text-orange-400\">{{ getPendingCount() }}</p>\n              <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">À rendre</p>\n            </div>\n            <div class=\"bg-orange-100 dark:bg-orange-900/30 p-3 rounded-xl group-hover:scale-110 transition-transform\">\n              <svg class=\"w-5 h-5 text-orange-600 dark:text-orange-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <!-- Taux de réussite -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-4 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md hover:shadow-lg transition-all duration-300 group\">\n          <div class=\"flex items-center justify-between\">\n            <div>\n              <p class=\"text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider\">Taux</p>\n              <p class=\"text-2xl font-bold text-[#3d4a85] dark:text-[#6d78c9]\">{{ getSuccessRate() }}%</p>\n              <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">Réussite</p>\n            </div>\n            <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-3 rounded-xl group-hover:scale-110 transition-transform\">\n              <svg class=\"w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"></path>\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Barre de progression -->\n      <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-6 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md mb-8\" *ngIf=\"!isLoading && projets.length > 0\">\n        <div class=\"flex items-center justify-between mb-3\">\n          <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Progression globale</h3>\n          <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">{{ getSuccessRate() }}% complété</span>\n        </div>\n        <div class=\"w-full bg-[#edf1f4] dark:bg-[#2a2a2a] rounded-full h-3\">\n          <div class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] h-3 rounded-full transition-all duration-500\"\n               [style.width.%]=\"getSuccessRate()\"></div>\n        </div>\n        <div class=\"flex justify-between text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\n          <span>{{ getRendusCount() }} projets rendus</span>\n          <span>{{ getPendingCount() }} en attente</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\n      <div class=\"relative\">\n        <div\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\n        ></div>\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- No Projects -->\n    <div\n      *ngIf=\"!isLoading && projets.length === 0\"\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n    >\n      <div\n        class=\"w-24 h-24 mx-auto mb-6 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 rounded-full flex items-center justify-center relative\"\n      >\n        <svg\n          class=\"w-12 h-12 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path\n            stroke-linecap=\"round\"\n            stroke-linejoin=\"round\"\n            stroke-width=\"1.5\"\n            d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n          ></path>\n        </svg>\n        <!-- Glow effect -->\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n      <h3\n        class=\"text-xl font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\"\n      >\n        Aucun projet disponible\n      </h3>\n      <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\n        Vos missions apparaîtront ici\n      </p>\n    </div>\n\n    <!-- Projects Grid -->\n    <div\n      *ngIf=\"!isLoading\"\n      class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n    >\n      <div\n        *ngFor=\"let projet of projets\"\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group\"\n      >\n        <!-- Header -->\n        <div class=\"relative overflow-hidden\">\n          <!-- Decorative gradient top border -->\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n          ></div>\n\n          <!-- Glow effect on hover -->\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n          ></div>\n\n          <div class=\"p-5 bg-white dark:bg-[#1e1e1e] relative\">\n            <!-- Badge de statut en haut à droite -->\n            <div class=\"absolute top-3 right-3\">\n              <span [ngClass]=\"getStatusClass(projet)\" class=\"text-xs px-2 py-1 rounded-full font-medium backdrop-blur-sm\">\n                {{ getStatusText(projet) }}\n              </span>\n            </div>\n\n            <h3\n              class=\"text-lg font-bold pr-16 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent group-hover:scale-[1.01] transition-transform duration-300 origin-left\"\n            >\n              {{ projet.titre }}\n            </h3>\n            <div class=\"flex items-center mt-2 text-xs space-x-2\">\n              <span\n                class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] px-2 py-0.5 rounded-full backdrop-blur-sm flex items-center\"\n              >\n                <svg class=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                </svg>\n                {{ projet.groupe || \"Tous\" }}\n              </span>\n              <span class=\"text-[#6d6870] dark:text-[#a0a0a0]\">•</span>\n              <span\n                class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] px-2 py-0.5 rounded-full backdrop-blur-sm flex items-center\"\n              >\n                <svg class=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n                </svg>\n                {{ projet.dateLimite | date : \"dd/MM/yyyy\" }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Content -->\n        <div class=\"p-5\">\n          <p\n            class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4 line-clamp-3\"\n          >\n            {{ projet.description || \"Aucune description\" }}\n          </p>\n\n          <!-- Files -->\n          <div\n            *ngIf=\"projet.fichiers && projet.fichiers.length > 0\"\n            class=\"mb-4\"\n          >\n            <h4\n              class=\"text-xs font-semibold text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider mb-2\"\n            >\n              Fichiers\n            </h4>\n            <div class=\"space-y-2\">\n              <div\n                *ngFor=\"let file of projet.fichiers\"\n                class=\"flex items-center justify-between bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-lg p-2.5 backdrop-blur-sm group/file hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\n              >\n                <div class=\"flex items-center truncate\">\n                  <div class=\"relative mr-2\">\n                    <svg\n                      class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9] relative z-10 group-hover/file:scale-110 transition-transform\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        stroke-width=\"2\"\n                        d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                      ></path>\n                    </svg>\n                    <!-- Glow effect -->\n                    <div\n                      class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/file:opacity-100 transition-opacity blur-md rounded-full\"\n                    ></div>\n                  </div>\n                  <span\n                    class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] truncate\"\n                    >Document</span\n                  >\n                </div>\n                <a\n                  [href]=\"getFileUrl(file)\"\n                  download\n                  [attr.download]=\"getFileName(file)\"\n                  class=\"relative overflow-hidden group/download\"\n                >\n                  <div\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover/download:scale-105\"\n                  ></div>\n                  <div\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover/download:opacity-100 blur-md transition-opacity duration-300\"\n                  ></div>\n                  <span\n                    class=\"relative flex items-center text-white text-xs px-3 py-1 rounded-lg transition-all z-10\"\n                  >\n                    <svg\n                      class=\"w-3 h-3 mr-1 group-hover/download:scale-110 transition-transform\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        stroke-width=\"2\"\n                        d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\n                      ></path>\n                    </svg>\n                    Télécharger\n                  </span>\n                </a>\n              </div>\n            </div>\n          </div>\n\n          <!-- Actions -->\n          <div\n            class=\"flex justify-between items-center pt-3 border-t border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            <a\n              [routerLink]=\"['/projects/detail', projet._id]\"\n              class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] text-sm font-medium flex items-center transition-colors relative group/details\"\n            >\n              <div class=\"relative mr-1\">\n                <svg\n                  class=\"w-4 h-4 relative z-10 group-hover/details:scale-110 transition-transform\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    stroke-width=\"2\"\n                    d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  ></path>\n                </svg>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/details:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span>Détails</span>\n            </a>\n\n            <ng-container *ngIf=\"isRendu(projet._id)\">\n              <span\n                class=\"bg-gradient-to-r from-green-100 to-green-50 dark:from-green-900/30 dark:to-green-800/30 text-green-800 dark:text-green-400 text-xs px-3 py-1.5 rounded-full flex items-center shadow-sm backdrop-blur-sm\"\n              >\n                <div class=\"relative mr-1\">\n                  <svg\n                    class=\"w-3 h-3 relative z-10\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fill-rule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clip-rule=\"evenodd\"\n                    ></path>\n                  </svg>\n                  <!-- Glow effect -->\n                  <div\n                    class=\"absolute inset-0 bg-green-500/20 blur-md rounded-full transform scale-150 -z-10\"\n                  ></div>\n                </div>\n                <span>Rendu</span>\n              </span>\n            </ng-container>\n\n            <ng-container *ngIf=\"!isRendu(projet._id)\">\n              <a\n                [routerLink]=\"['/projects/submit', projet._id]\"\n                class=\"relative overflow-hidden group/submit\"\n              >\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover/submit:scale-105\"\n                ></div>\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover/submit:opacity-100 blur-md transition-opacity duration-300\"\n                ></div>\n                <span\n                  class=\"relative flex items-center text-white text-sm font-medium px-3 py-1.5 rounded-lg transition-all z-10\"\n                >\n                  Rendre\n                </span>\n              </a>\n            </ng-container>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProjetService } from '@app/services/projets.service';\nimport { RendusService } from 'src/app/services/rendus.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\n\n// Composant pour soumettre un projet\n@Component({\n  selector: 'app-project-submission',\n  templateUrl: './project-submission.component.html',\n  styleUrls: ['./project-submission.component.css'],\n})\nexport class ProjectSubmissionComponent implements OnInit {\n  projetId: string = '';\n  projet: any;\n  submissionForm: FormGroup;\n  selectedFiles: File[] = [];\n  isLoading = true;\n  isSubmitting = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private projetService: ProjetService,\n    private rendusService: RendusService,\n    private authService: AuthuserService\n  ) {\n    this.submissionForm = this.fb.group({\n      description: ['', [Validators.required, Validators.minLength(10)]],\n    });\n  }\n\n  ngOnInit(): void {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n  }\n\n  loadProjetDetails(): void {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: (projet: any) => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: (err: Error) => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      },\n    });\n  }\n\n  onFileChange(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    if (input.files) {\n      this.selectedFiles = Array.from(input.files);\n    }\n  }\n\n  onSubmit(): void {\n    if (this.submissionForm.invalid || this.selectedFiles.length === 0) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    const formData = new FormData();\n    formData.append('projet', this.projetId);\n    formData.append('etudiant', this.authService.getCurrentUserId() || '');\n    formData.append('description', this.submissionForm.value.description);\n\n    this.selectedFiles.forEach((file) => {\n      formData.append('fichiers', file);\n    });\n\n    this.rendusService.submitRendu(formData).subscribe({\n      next: (response: any) => {\n        alert('Votre projet a été soumis avec succès');\n        this.router.navigate(['/projects']);\n      },\n      error: (err: Error) => {\n        console.error('Erreur lors de la soumission du projet', err);\n        alert('Une erreur est survenue lors de la soumission du projet');\n        this.isSubmitting = false;\n      },\n    });\n  }\n\n  // Méthode pour supprimer un fichier de la sélection\n  removeFile(index: number): void {\n    this.selectedFiles.splice(index, 1);\n  }\n\n  // Méthode pour formater la taille des fichiers\n  getFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B';\n\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // Méthode pour calculer les jours restants\n  getRemainingDays(): number {\n    if (!this.projet?.dateLimite) return 0;\n\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    const diffTime = deadline.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    return Math.max(0, diffDays);\n  }\n}\n", "<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary relative\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\n    <div class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\n  </div>\n\n  <div class=\"container mx-auto px-4 py-8 relative z-10\">\n\n    <!-- Header moderne avec breadcrumb -->\n    <div class=\"mb-8\">\n      <nav class=\"flex items-center space-x-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4\">\n        <a routerLink=\"/projects\" class=\"hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\">Mes Projets</a>\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n        </svg>\n        <a [routerLink]=\"['/projects/detail', projetId]\" class=\"hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\">{{ projet?.titre || 'Projet' }}</a>\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n        </svg>\n        <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">Soumettre</span>\n      </nav>\n\n      <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 flex items-center justify-center shadow-lg\">\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n            </svg>\n          </div>\n          <div>\n            <h1 class=\"text-3xl font-bold bg-gradient-to-r from-green-600 to-green-700 dark:from-green-400 dark:to-green-500 bg-clip-text text-transparent\">\n              Soumettre mon projet\n            </h1>\n            <p class=\"text-[#6d6870] dark:text-[#a0a0a0]\">\n              Téléchargez vos fichiers et décrivez votre travail\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\n      <div class=\"relative\">\n        <div class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"></div>\n        <!-- Glow effect -->\n        <div class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"></div>\n      </div>\n    </div>\n\n    <!-- Contenu principal -->\n    <div *ngIf=\"projet && !isLoading\" class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n\n      <!-- Formulaire de soumission -->\n      <div class=\"lg:col-span-2 space-y-6\">\n\n        <!-- Description du travail -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Description de votre travail</h3>\n          </div>\n\n          <form id=\"submissionForm\" [formGroup]=\"submissionForm\" (ngSubmit)=\"onSubmit()\" class=\"space-y-6\">\n            <div class=\"space-y-2\">\n              <label for=\"description\" class=\"flex items-center space-x-2 text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">\n                <svg class=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                </svg>\n                <span>Rapport de projet</span>\n                <span class=\"text-red-500\">*</span>\n              </label>\n              <div class=\"relative\">\n                <textarea\n                  id=\"description\"\n                  formControlName=\"description\"\n                  rows=\"6\"\n                  placeholder=\"Décrivez votre travail : fonctionnalités implémentées, technologies utilisées, difficultés rencontrées, solutions apportées...\"\n                  class=\"w-full px-4 py-3 bg-white dark:bg-[#2a2a2a] border-2 border-[#edf1f4] dark:border-[#2a2a2a] rounded-xl focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-4 focus:ring-[#4f5fad]/10 dark:focus:ring-[#6d78c9]/20 transition-all duration-200 text-[#3d4a85] dark:text-[#6d78c9] placeholder-[#6d6870] dark:placeholder-[#a0a0a0] resize-none\"\n                ></textarea>\n              </div>\n              <div class=\"flex justify-between text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n                <span>Minimum 10 caractères requis</span>\n                <span>{{ submissionForm.get('description')?.value?.length || 0 }} caractères</span>\n              </div>\n              <div *ngIf=\"submissionForm.get('description')?.invalid && submissionForm.get('description')?.touched\"\n                   class=\"flex items-center space-x-2 text-red-500 text-sm\">\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n                <span>La description est requise et doit contenir au moins 10 caractères.</span>\n              </div>\n            </div>\n\n            <!-- Upload de fichiers -->\n            <div class=\"space-y-2\">\n              <label for=\"fichiers\" class=\"flex items-center space-x-2 text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">\n                <svg class=\"w-4 h-4 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                </svg>\n                <span>Fichiers du projet</span>\n                <span class=\"text-red-500\">*</span>\n              </label>\n              <div class=\"relative\">\n                <input\n                  type=\"file\"\n                  id=\"fichiers\"\n                  multiple\n                  (change)=\"onFileChange($event)\"\n                  class=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10\"\n                />\n                <div class=\"w-full px-6 py-8 bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 border-2 border-dashed border-[#4f5fad]/30 dark:border-[#6d78c9]/30 rounded-xl hover:border-[#4f5fad] dark:hover:border-[#6d78c9] transition-all duration-200 text-center\">\n                  <div class=\"space-y-3\">\n                    <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-3 rounded-xl w-fit mx-auto\">\n                      <svg class=\"w-8 h-8 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                      </svg>\n                    </div>\n                    <div>\n                      <p class=\"text-[#3d4a85] dark:text-[#6d78c9] font-medium\">Glissez vos fichiers ici</p>\n                      <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">ou cliquez pour parcourir</p>\n                    </div>\n                    <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">Tous types de fichiers acceptés</p>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Validation des fichiers -->\n              <div *ngIf=\"selectedFiles.length === 0 && submissionForm.touched\" class=\"flex items-center space-x-2 text-red-500 text-sm\">\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n                <span>Veuillez sélectionner au moins un fichier.</span>\n              </div>\n\n              <!-- Liste des fichiers sélectionnés -->\n              <div *ngIf=\"selectedFiles.length > 0\" class=\"space-y-3\">\n                <div class=\"flex items-center justify-between\">\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Fichiers sélectionnés :</p>\n                  <span class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">{{ selectedFiles.length }} fichier{{ selectedFiles.length > 1 ? 's' : '' }}</span>\n                </div>\n                <div class=\"grid grid-cols-1 gap-2\">\n                  <div *ngFor=\"let file of selectedFiles; let i = index\" class=\"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-xl\">\n                    <div class=\"flex items-center space-x-3\">\n                      <div class=\"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg\">\n                        <svg class=\"w-4 h-4 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n                        </svg>\n                      </div>\n                      <div>\n                        <p class=\"text-sm font-medium text-green-800 dark:text-green-400\">{{ file.name }}</p>\n                        <p class=\"text-xs text-green-600 dark:text-green-500\">{{ getFileSize(file.size) }}</p>\n                      </div>\n                    </div>\n                    <button type=\"button\" (click)=\"removeFile(i)\" class=\"text-red-500 hover:text-red-700 transition-colors\">\n                      <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </form>\n        </div>\n\n        <!-- Boutons d'action horizontaux -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Actions</h3>\n          </div>\n\n          <div class=\"space-y-4\">\n            <!-- Boutons d'action horizontaux -->\n            <div class=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n              <!-- Bouton retour -->\n              <a [routerLink]=\"['/projects/detail', projetId]\"\n                 class=\"flex items-center justify-center px-6 py-3 bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#3d4a85] dark:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-xl transition-all duration-200 font-medium order-2 sm:order-1\">\n                <div class=\"flex items-center space-x-2\">\n                  <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\n                  </svg>\n                  <span>Retour aux détails</span>\n                </div>\n              </a>\n\n              <!-- Bouton de soumission -->\n              <button\n                type=\"submit\"\n                form=\"submissionForm\"\n                [disabled]=\"submissionForm.invalid || selectedFiles.length === 0 || isSubmitting\"\n                class=\"flex items-center justify-center px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none order-1 sm:order-2\"\n              >\n                <div class=\"flex items-center space-x-2\">\n                  <svg *ngIf=\"!isSubmitting\" class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n                  </svg>\n                  <div *ngIf=\"isSubmitting\" class=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                  <span *ngIf=\"!isSubmitting\">Soumettre le projet</span>\n                  <span *ngIf=\"isSubmitting\">Soumission en cours...</span>\n                </div>\n              </button>\n            </div>\n\n            <!-- Indicateur de validation -->\n            <div class=\"text-center\">\n              <div *ngIf=\"submissionForm.invalid || selectedFiles.length === 0\" class=\"flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400 text-sm\">\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n                </svg>\n                <span>Complétez le formulaire pour soumettre</span>\n              </div>\n              <div *ngIf=\"submissionForm.valid && selectedFiles.length > 0\" class=\"flex items-center justify-center space-x-2 text-green-600 dark:text-green-400 text-sm\">\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n                <span>Prêt à soumettre</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n      </div>\n\n      <!-- Sidebar avec informations du projet -->\n      <div class=\"space-y-6\">\n\n        <!-- Informations du projet -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\">\n          <div class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] p-6 text-white\">\n            <div class=\"flex items-center space-x-3\">\n              <div class=\"bg-white/20 p-2 rounded-lg\">\n                <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                </svg>\n              </div>\n              <div>\n                <h3 class=\"text-lg font-semibold\">Informations</h3>\n                <p class=\"text-sm text-white/80\">Détails du projet</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"p-6 space-y-4\">\n            <!-- Titre -->\n            <div class=\"p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-start space-x-3\">\n                <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"></path>\n                  </svg>\n                </div>\n                <div class=\"flex-1\">\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Titre</p>\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9] leading-tight\">{{ projet.titre }}</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- Description -->\n            <div class=\"p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-start space-x-3\">\n                <div class=\"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\n                  </svg>\n                </div>\n                <div class=\"flex-1\">\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Description</p>\n                  <p class=\"text-sm text-[#3d4a85] dark:text-[#6d78c9] leading-tight line-clamp-3\">{{ projet.description || 'Aucune description' }}</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- Date limite -->\n            <div class=\"p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-center justify-between\">\n                <div class=\"flex items-center space-x-3\">\n                  <div class=\"bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg\">\n                    <svg class=\"w-4 h-4 text-orange-600 dark:text-orange-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Date limite</p>\n                    <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ projet.dateLimite | date : \"dd/MM/yyyy\" }}</p>\n                  </div>\n                </div>\n                <div class=\"text-right\">\n                  <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">{{ getRemainingDays() }} jours</p>\n                  <p class=\"text-xs text-orange-600 dark:text-orange-400\">restants</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- Groupe -->\n            <div class=\"p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"bg-purple-100 dark:bg-purple-900/30 p-2 rounded-lg\">\n                  <svg class=\"w-4 h-4 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Groupe cible</p>\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ projet.groupe || 'Tous les groupes' }}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Conseils de soumission -->\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\n          <div class=\"flex items-center space-x-3 mb-4\">\n            <div class=\"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg\">\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Conseils</h3>\n          </div>\n\n          <div class=\"space-y-3 text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n            <div class=\"flex items-start space-x-2\">\n              <svg class=\"w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n              <span>Décrivez clairement votre travail et les technologies utilisées</span>\n            </div>\n            <div class=\"flex items-start space-x-2\">\n              <svg class=\"w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n              <span>Incluez tous les fichiers sources et la documentation</span>\n            </div>\n            <div class=\"flex items-start space-x-2\">\n              <svg class=\"w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n              <span>Mentionnez les difficultés rencontrées et solutions apportées</span>\n            </div>\n            <div class=\"flex items-start space-x-2\">\n              <svg class=\"w-4 h-4 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n              </svg>\n              <span>Vérifiez que tous vos fichiers sont bien sélectionnés</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ProjectListComponent } from './project-list/project-list.component';\nimport { ProjectDetailComponent } from './project-detail/project-detail.component';\nimport { ProjectSubmissionComponent } from './project-submission/project-submission.component';\n\nconst routes: Routes = [\n  { path: '', component: ProjectListComponent },\n  { path: 'detail/:id', component: ProjectDetailComponent },\n  { path: 'submit/:id', component: ProjectSubmissionComponent }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class ProjectsRoutingModule { }\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ProjectsRoutingModule } from './projects-routing.module';\nimport { ProjectListComponent } from './project-list/project-list.component';\nimport { ProjectDetailComponent } from './project-detail/project-detail.component';\nimport { ProjectSubmissionComponent } from './project-submission/project-submission.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n@NgModule({\n  declarations: [\n    ProjectListComponent,\n    ProjectDetailComponent,\n    ProjectSubmissionComponent\n  ],\n  imports: [\n    CommonModule,\n    ProjectsRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    RouterModule\n  ]\n})\nexport class ProjectsModule { }\n"], "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r6", "getFileName", "file_r7", "ɵɵproperty", "getFileUrl", "ɵɵsanitizeUrl", "ɵɵtemplate", "ProjectDetailComponent_div_38_div_21_div_1_Template", "ctx_r2", "projet", "fichiers", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵpureFunction1", "_c0", "ctx_r5", "projetId", "ProjectDetailComponent_div_38_div_21_Template", "ProjectDetailComponent_div_38_div_22_Template", "ProjectDetailComponent_div_38_ng_container_76_Template", "ProjectDetailComponent_div_38_ng_container_77_Template", "ctx_r1", "description", "ɵɵtextInterpolate2", "length", "ɵɵtextInterpolate", "ɵɵpipeBind2", "dateLimite", "getRemainingDays", "groupe", "hasSubmitted", "ProjectDetailComponent", "constructor", "route", "router", "projetService", "rendusService", "authService", "isLoading", "ngOnInit", "snapshot", "paramMap", "get", "loadProjetDetails", "checkRenduStatus", "getProjetById", "subscribe", "next", "error", "err", "console", "navigate", "etudiantId", "getCurrentUserId", "checkRenduExists", "exists", "log", "filePath", "fileName", "includes", "parts", "split", "getProjectStatus", "now", "Date", "deadline", "oneWeekFromNow", "setDate", "getDate", "getStatusClass", "status", "getStatusText", "diffTime", "getTime", "diffDays", "Math", "ceil", "max", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "RendusService", "i4", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ProjectDetailComponent_Template", "rf", "ctx", "ProjectDetailComponent_div_37_Template", "ProjectDetailComponent_div_38_Template", "titre", "environment", "ctx_r0", "getTotalProjects", "getRendusCount", "getPendingCount", "getSuccessRate", "ɵɵstyleProp", "ctx_r10", "file_r11", "ɵɵattribute", "ProjectListComponent_div_33_div_1_div_25_div_4_Template", "projet_r6", "_id", "ProjectListComponent_div_33_div_1_div_25_Template", "ProjectListComponent_div_33_div_1_ng_container_34_Template", "ProjectListComponent_div_33_div_1_ng_container_35_Template", "_c1", "isRendu", "ProjectListComponent_div_33_div_1_Template", "ctx_r4", "projets", "ProjectListComponent", "fileService", "rendusMap", "Map", "userGroup", "getCurrentUser", "loadProjets", "getProjets", "for<PERSON>ach", "set", "urlBackend", "filter", "round", "getUrgentProjects", "getExpiredProjects", "FileService", "ProjectListComponent_Template", "ProjectListComponent_div_29_Template", "ProjectListComponent_div_30_Template", "ProjectListComponent_div_31_Template", "ProjectListComponent_div_32_Template", "ProjectListComponent_div_33_Template", "Validators", "ɵɵlistener", "ProjectSubmissionComponent_div_28_div_49_div_7_Template_button_click_10_listener", "restoredCtx", "ɵɵrestoreView", "_r15", "i_r13", "index", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "removeFile", "file_r12", "name", "ctx_r11", "getFileSize", "size", "ProjectSubmissionComponent_div_28_div_49_div_7_Template", "selectedFiles", "ProjectSubmissionComponent_div_28_Template_form_ngSubmit_9_listener", "_r17", "ctx_r16", "onSubmit", "ProjectSubmissionComponent_div_28_div_25_Template", "ProjectSubmissionComponent_div_28_Template_input_change_35_listener", "$event", "ctx_r18", "onFileChange", "ProjectSubmissionComponent_div_28_div_48_Template", "ProjectSubmissionComponent_div_28_div_49_Template", "ProjectSubmissionComponent_div_28__svg_svg_68_Template", "ProjectSubmissionComponent_div_28_div_69_Template", "ProjectSubmissionComponent_div_28_span_70_Template", "ProjectSubmissionComponent_div_28_span_71_Template", "ProjectSubmissionComponent_div_28_div_73_Template", "ProjectSubmissionComponent_div_28_div_74_Template", "submissionForm", "tmp_1_0", "value", "tmp_2_0", "invalid", "touched", "isSubmitting", "valid", "ProjectSubmissionComponent", "fb", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "event", "input", "target", "files", "Array", "from", "formData", "FormData", "append", "file", "submitRendu", "response", "alert", "splice", "bytes", "k", "sizes", "i", "floor", "parseFloat", "pow", "toFixed", "FormBuilder", "i5", "ProjectSubmissionComponent_Template", "ProjectSubmissionComponent_div_27_Template", "ProjectSubmissionComponent_div_28_Template", "RouterModule", "routes", "path", "component", "ProjectsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "FormsModule", "ReactiveFormsModule", "ProjectsModule", "declarations"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}