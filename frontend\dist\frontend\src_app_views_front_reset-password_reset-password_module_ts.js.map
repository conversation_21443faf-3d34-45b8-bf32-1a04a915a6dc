{"version": 3, "file": "src_app_views_front_reset-password_reset-password_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AACuD;AACa;;;AAEpE,MAAME,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH,6EAAsBA;AAAA,CAAE,CAAC;AAMlE,MAAOI,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAH3BL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXK,0BAA0B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAF3BT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;ACP4C;;;;;;;;ICqI1DW,4DAAA,cAGC;IAKKA,uDAAA,YAA2C;IAK7CA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,MAAAO,MAAA,CAAAC,KAAA,MACF;;;;;IAMNR,4DAAA,cAGC;IAKKA,uDAAA,YAAmC;IAKrCA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,MAAAS,MAAA,CAAAC,OAAA,MACF;;;ADpKV,MAAOpB,sBAAsB;EAKjCqB,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IAPf,KAAAL,OAAO,GAAG,EAAE;IACZ,KAAAF,KAAK,GAAG,EAAE;IAQR,IAAI,CAACQ,SAAS,GAAG,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnB,sDAAU,CAACoB,QAAQ,EAAEpB,sDAAU,CAACmB,KAAK,CAAC,CAAC;MACpDE,IAAI,EAAE,CAAC,EAAE,EAAE,CAACrB,sDAAU,CAACoB,QAAQ,EAAEpB,sDAAU,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACvB,sDAAU,CAACoB,QAAQ,EAAEpB,sDAAU,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC;KACjE,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,KAAK,CAACS,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,OAAO,CAAC,EAAE;QACnB,IAAI,CAACV,SAAS,CAACW,UAAU,CAAC;UACxBT,KAAK,EAAEQ,MAAM,CAAC,OAAO;SACtB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACZ,SAAS,CAACa,OAAO,EAAE;IAE5B,IAAI,CAAChB,WAAW,CAACiB,aAAa,CAAC,IAAI,CAACd,SAAS,CAACe,KAAK,CAAC,CAACN,SAAS,CAAC;MAC7DO,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACvB,OAAO,GAAGuB,GAAG,CAACvB,OAAO,GAAG,0BAA0B;QACvD,IAAI,CAACF,KAAK,GAAG,EAAE;QACf0B,UAAU,CAAC,MAAM,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;MAC1D,CAAC;MACD3B,KAAK,EAAG4B,GAAG,IAAI;QACb,IAAI,CAAC5B,KAAK,GAAG4B,GAAG,CAAC5B,KAAK,CAACE,OAAO,IAAI,cAAc;QAChD,IAAI,CAACA,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACJ;;;uBA3CWpB,sBAAsB,EAAAU,+DAAA,CAAAH,uDAAA,GAAAG,+DAAA,CAAAuC,+DAAA,GAAAvC,+DAAA,CAAAyC,mDAAA,GAAAzC,+DAAA,CAAAyC,2DAAA;IAAA;EAAA;;;YAAtBnD,sBAAsB;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVnClD,4DAAA,aAEC;UAGGA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAIVA,4DAAA,cAA2C;UAKvCA,uDAAA,cAEO;UAMPA,4DAAA,eAA6B;UAIzBA,oDAAA,wBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAA2D;UACzDA,oDAAA,iCACF;UAAAA,0DAAA,EAAI;UAINA,4DAAA,eAAiB;UACeA,wDAAA,sBAAAqD,0DAAA;YAAA,OAAYF,GAAA,CAAAvB,QAAA,EAAU;UAAA,EAAC;UAEnD5B,4DAAA,eAAmB;UAIfA,uDAAA,aAA8C;UAC9CA,oDAAA,eACF;UAAAA,0DAAA,EAAQ;UACRA,4DAAA,eAAsB;UACpBA,uDAAA,iBAKE;UACFA,4DAAA,eAEC;UACCA,uDAAA,eAEO;UACTA,0DAAA,EAAM;UAKVA,4DAAA,eAAmB;UAIfA,uDAAA,aAAyC;UACzCA,oDAAA,oBACF;UAAAA,0DAAA,EAAQ;UACRA,4DAAA,eAAsB;UACpBA,uDAAA,iBAME;UACFA,4DAAA,eAEC;UACCA,uDAAA,eAEO;UACTA,0DAAA,EAAM;UAKVA,4DAAA,eAAmB;UAIfA,uDAAA,aAA0C;UAC1CA,oDAAA,sBACF;UAAAA,0DAAA,EAAQ;UACRA,4DAAA,eAAsB;UACpBA,uDAAA,iBAKE;UACFA,4DAAA,eAEC;UACCA,uDAAA,eAEO;UACTA,0DAAA,EAAM;UAKVA,wDAAA,KAAAuD,sCAAA,kBAoBM;UAGNvD,wDAAA,KAAAwD,sCAAA,kBAoBM;UAGNxD,4DAAA,kBAIC;UACCA,uDAAA,eAEO;UAIPA,4DAAA,gBAEC;UACCA,uDAAA,aAA+B;UAC/BA,oDAAA,wBACF;UAAAA,0DAAA,EAAO;UAITA,4DAAA,eAEC;UAMKA,uDAAA,aAAgD;UAChDA,oDAAA,uBACF;UAAAA,0DAAA,EAAI;;;UA1JJA,uDAAA,IAAuB;UAAvBA,wDAAA,cAAAmD,GAAA,CAAAnC,SAAA,CAAuB;UA+ExBhB,uDAAA,IAAW;UAAXA,wDAAA,SAAAmD,GAAA,CAAA3C,KAAA,CAAW;UAuBXR,uDAAA,GAAa;UAAbA,wDAAA,SAAAmD,GAAA,CAAAzC,OAAA,CAAa;UAyBdV,uDAAA,GAA8B;UAA9BA,wDAAA,aAAAmD,GAAA,CAAAnC,SAAA,CAAAa,OAAA,CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;ACtLK;AACmB;AAEW;AACT;;AAW9D,MAAOgC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAN5BH,yDAAY,EACZC,uDAAW,EACXC,+DAAmB,EACnBlE,sFAA0B;IAAA;EAAA;;;sHAGjBmE,mBAAmB;IAAAC,YAAA,GARfxE,6EAAsB;IAAAM,OAAA,GAEnC8D,yDAAY,EACZC,uDAAW,EACXC,+DAAmB,EACnBlE,sFAA0B;EAAA;AAAA", "sources": ["./src/app/views/front/reset-password/reset-password-routing.module.ts", "./src/app/views/front/reset-password/reset-password.component.ts", "./src/app/views/front/reset-password/reset-password.component.html", "./src/app/views/front/reset-password/reset-password.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ResetPasswordComponent } from './reset-password.component';\n\nconst routes: Routes = [{ path: '', component: ResetPasswordComponent }];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class ResetPasswordRoutingModule {}\n", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { AuthService } from '../../../services/auth.service';\nimport { Router, ActivatedRoute } from '@angular/router';\n\n@Component({\n  selector: 'app-reset-password',\n  templateUrl: './reset-password.component.html',\n  styleUrls: ['./reset-password.component.css'],\n})\nexport class ResetPasswordComponent implements OnInit {\n  resetForm: FormGroup;\n  message = '';\n  error = '';\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute\n  ) {\n    this.resetForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      code: ['', [Validators.required, Validators.minLength(6)]],\n      newPassword: ['', [Validators.required, Validators.minLength(6)]],\n    });\n  }\n\n  ngOnInit() {\n    // Check if email is provided in query parameters\n    this.route.queryParams.subscribe(params => {\n      if (params['email']) {\n        this.resetForm.patchValue({\n          email: params['email']\n        });\n      }\n    });\n  }\n\n  onSubmit() {\n    if (this.resetForm.invalid) return;\n\n    this.authService.resetPassword(this.resetForm.value).subscribe({\n      next: (res: any) => {\n        this.message = res.message + ' Redirecting to login...';\n        this.error = '';\n        setTimeout(() => this.router.navigate(['/login']), 1500);\n      },\n      error: (err) => {\n        this.error = err.error.message || 'Reset failed';\n        this.message = '';\n      },\n    });\n  }\n}\n", "<div\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen flex items-center justify-center relative\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"w-full max-w-md relative z-10\">\n    <div\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\n    >\n      <!-- Decorative top border with gradient and glow -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\n      ></div>\n\n      <!-- Header -->\n      <div class=\"p-6 text-center\">\n        <h1\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n        >\n          Reset Password\n        </h1>\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\n          Enter your new password\n        </p>\n      </div>\n\n      <!-- Form Section -->\n      <div class=\"p-6\">\n        <form [formGroup]=\"resetForm\" (ngSubmit)=\"onSubmit()\" class=\"space-y-5\">\n          <!-- Email -->\n          <div class=\"group\">\n            <label\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\n            >\n              <i class=\"fas fa-envelope mr-1.5 text-xs\"></i>\n              Email\n            </label>\n            <div class=\"relative\">\n              <input\n                type=\"email\"\n                formControlName=\"email\"\n                placeholder=\"<EMAIL>\"\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n              />\n              <div\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n              >\n                <div\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n                ></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Reset Code -->\n          <div class=\"group\">\n            <label\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\n            >\n              <i class=\"fas fa-key mr-1.5 text-xs\"></i>\n              Reset Code\n            </label>\n            <div class=\"relative\">\n              <input\n                type=\"text\"\n                formControlName=\"code\"\n                placeholder=\"123456\"\n                maxlength=\"6\"\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n              />\n              <div\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n              >\n                <div\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n                ></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- New Password -->\n          <div class=\"group\">\n            <label\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\n            >\n              <i class=\"fas fa-lock mr-1.5 text-xs\"></i>\n              New Password\n            </label>\n            <div class=\"relative\">\n              <input\n                type=\"password\"\n                formControlName=\"newPassword\"\n                placeholder=\"••••••••\"\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n              />\n              <div\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n              >\n                <div\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n                ></div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Error Message -->\n          <div\n            *ngIf=\"error\"\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\"\n          >\n            <div class=\"flex items-start\">\n              <div\n                class=\"text-[#ff6b69] dark:text-[#ff8785] mr-2 text-base relative\"\n              >\n                <i class=\"fas fa-exclamation-triangle\"></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\n                ></div>\n              </div>\n              <div class=\"flex-1\">\n                <p class=\"text-xs text-[#ff6b69] dark:text-[#ff8785]\">\n                  {{ error }}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Success Message -->\n          <div\n            *ngIf=\"message\"\n            class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\"\n          >\n            <div class=\"flex items-start\">\n              <div\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-2 text-base relative\"\n              >\n                <i class=\"fas fa-check-circle\"></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n                ></div>\n              </div>\n              <div class=\"flex-1\">\n                <p class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\n                  {{ message }}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Submit Button -->\n          <button\n            type=\"submit\"\n            class=\"w-full relative overflow-hidden group mt-6\"\n            [disabled]=\"resetForm.invalid\"\n          >\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105 disabled:opacity-50\"\n            ></div>\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300 disabled:opacity-0\"\n            ></div>\n            <span\n              class=\"relative flex items-center justify-center text-white font-medium py-2.5 px-4 rounded-lg transition-all z-10\"\n            >\n              <i class=\"fas fa-key mr-2\"></i>\n              Reset Password\n            </span>\n          </button>\n\n          <!-- Back Link -->\n          <div\n            class=\"text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] space-y-2 pt-4\"\n          >\n            <div>\n              <a\n                routerLink=\"/login\"\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium flex items-center justify-center\"\n              >\n                <i class=\"fas fa-arrow-left mr-1.5 text-xs\"></i>\n                Back to Login\n              </a>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\nimport { ResetPasswordRoutingModule } from './reset-password-routing.module';\nimport { ResetPasswordComponent } from './reset-password.component';\n\n@NgModule({\n  declarations: [ResetPasswordComponent],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ResetPasswordRoutingModule,\n  ],\n})\nexport class ResetPasswordModule {}\n"], "names": ["RouterModule", "ResetPasswordComponent", "routes", "path", "component", "ResetPasswordRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "ctx_r1", "message", "constructor", "fb", "authService", "router", "route", "resetForm", "group", "email", "required", "code", "<PERSON><PERSON><PERSON><PERSON>", "newPassword", "ngOnInit", "queryParams", "subscribe", "params", "patchValue", "onSubmit", "invalid", "resetPassword", "value", "next", "res", "setTimeout", "navigate", "err", "ɵɵdirectiveInject", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "ResetPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ResetPasswordComponent_Template_form_ngSubmit_27_listener", "ɵɵtemplate", "ResetPasswordComponent_div_52_Template", "ResetPasswordComponent_div_53_Template", "ɵɵproperty", "CommonModule", "FormsModule", "ReactiveFormsModule", "ResetPasswordModule", "declarations"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}