import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActiveCallComponent } from '../active-call/active-call.component';
import { IncomingCallComponent } from '../incoming-call/incoming-call.component';

@NgModule({
  declarations: [ActiveCallComponent, IncomingCallComponent],
  imports: [CommonModule, FormsModule],
  exports: [ActiveCallComponent, IncomingCallComponent],
})
export class CallModule {}
