import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, Observable, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import {
  Call,
  CallType,
  CallStatus,
  IncomingCall,
  CallSuccess,
  CallSignal,
} from '../models/message.model';
import {
  INITIATE_CALL_MUTATION,
  ACCEPT_CALL_MUTATION,
  REJECT_CALL_MUTATION,
  END_CALL_MUTATION,
  INCOMING_CALL_SUBSCRIPTION,
  CALL_STATUS_CHANGED_SUBSCRIPTION,
  CALL_SIGNAL_SUBSCRIPTION,
  SEND_CALL_SIGNAL_MUTATION,
} from '../graphql/message.graphql';
import { LoggerService } from './logger.service';

/**
 * Service unifié pour la gestion des appels vidéo/audio
 * Gère l'état des appels, WebRTC, et la synchronisation
 */
@Injectable({
  providedIn: 'root',
})
export class CallService implements OnD<PERSON>roy {
  // ===== ÉTAT PRINCIPAL =====
  private activeCall = new BehaviorSubject<Call | null>(null);
  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);
  private callSignals = new BehaviorSubject<CallSignal | null>(null);

  // Observables publics
  public activeCall$ = this.activeCall.asObservable();
  public incomingCall$ = this.incomingCall.asObservable();
  public callSignals$ = this.callSignals.asObservable();

  // ===== ÉTAT DES APPELS =====
  private currentCallId: string | null = null;
  private callState:
    | 'idle'
    | 'initiating'
    | 'ringing'
    | 'connecting'
    | 'connected'
    | 'ending' = 'idle';

  // ===== GESTION AUDIO =====
  private sounds: { [key: string]: HTMLAudioElement } = {};
  private isPlaying: { [key: string]: boolean } = {};

  // ===== WEBRTC =====
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private localVideoElement: HTMLVideoElement | null = null;
  private remoteVideoElement: HTMLVideoElement | null = null;
  private isAudioEnabled = true;
  private isVideoEnabled = true;

  // Configuration WebRTC
  private readonly rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
    ],
  };

  constructor(private apollo: Apollo, private logger: LoggerService) {
    this.logger.info('CallService', '🚀 Initializing unified CallService...');
    this.initializeSounds();
    this.initializeSubscriptions();
    this.initializeWebRTC();
    this.logger.info('CallService', '✅ CallService initialized successfully');
  }

  ngOnDestroy(): void {
    this.logger.info('CallService', '🔄 Destroying CallService...');
    this.cleanup();
  }

  // ===== MÉTHODES PUBLIQUES PRINCIPALES =====

  /**
   * Initie un appel
   */
  initiateCall(
    recipientId: string,
    callType: CallType,
    conversationId?: string
  ): Observable<Call> {
    this.logger.info('CallService', '📞 Initiating call:', {
      recipientId,
      callType,
    });

    if (this.callState !== 'idle') {
      return throwError(() => new Error('Another call is already in progress'));
    }

    this.setCallState('initiating');
    const callId = this.generateCallId();

    return this.apollo
      .mutate<{ initiateCall: Call }>({
        mutation: INITIATE_CALL_MUTATION,
        variables: { recipientId, callType, callId, conversationId },
      })
      .pipe(
        map((result) => {
          const call = result.data?.initiateCall;
          if (!call) throw new Error('Failed to initiate call');

          this.handleCallInitiated(call);
          return call;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error initiating call:', error);
          this.setCallState('idle');
          return throwError(() => error);
        })
      );
  }

  /**
   * Accepte un appel entrant
   */
  acceptCall(call: IncomingCall): Observable<Call> {
    this.logger.info('CallService', '✅ Accepting call:', call.id);

    if (!call) {
      return throwError(() => new Error('No call to accept'));
    }

    this.setCallState('connecting');

    return this.apollo
      .mutate<{ acceptCall: Call }>({
        mutation: ACCEPT_CALL_MUTATION,
        variables: { callId: call.id },
      })
      .pipe(
        map((result) => {
          const acceptedCall = result.data?.acceptCall;
          if (!acceptedCall) throw new Error('Failed to accept call');

          this.handleCallAccepted(acceptedCall);
          return acceptedCall;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error accepting call:', error);
          this.setCallState('idle');
          return throwError(() => error);
        })
      );
  }

  /**
   * Rejette un appel
   */
  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {
    this.logger.info('CallService', '❌ Rejecting call:', callId);

    this.setCallState('ending');

    return this.apollo
      .mutate<{ rejectCall: CallSuccess }>({
        mutation: REJECT_CALL_MUTATION,
        variables: { callId, reason: reason || 'User rejected' },
      })
      .pipe(
        map((result) => {
          const success = result.data?.rejectCall;
          if (!success) throw new Error('Failed to reject call');

          this.handleCallEnded();
          return success;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error rejecting call:', error);
          this.handleCallEnded(); // Nettoyer même en cas d'erreur
          return throwError(() => error);
        })
      );
  }

  /**
   * Termine un appel
   */
  endCall(callId: string): Observable<CallSuccess> {
    this.logger.info('CallService', '🔚 Ending call:', callId);

    this.setCallState('ending');

    return this.apollo
      .mutate<{ endCall: CallSuccess }>({
        mutation: END_CALL_MUTATION,
        variables: { callId },
      })
      .pipe(
        map((result) => {
          const success = result.data?.endCall;
          if (!success) throw new Error('Failed to end call');

          this.handleCallEnded();
          return success;
        }),
        catchError((error) => {
          this.logger.error('CallService', 'Error ending call:', error);
          this.handleCallEnded(); // Nettoyer même en cas d'erreur
          return throwError(() => error);
        })
      );
  }

  // ===== GETTERS PUBLICS =====

  get currentCall(): Call | null {
    return this.activeCall.value;
  }

  get currentIncomingCall(): IncomingCall | null {
    return this.incomingCall.value;
  }

  get isCallActive(): boolean {
    return this.callState === 'connected';
  }

  get isCallInProgress(): boolean {
    return this.callState !== 'idle';
  }

  // ===== MÉTHODES PRIVÉES =====

  private generateCallId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setCallState(state: typeof this.callState): void {
    this.logger.debug(
      'CallService',
      `Call state: ${this.callState} → ${state}`
    );
    this.callState = state;
  }

  private handleCallInitiated(call: Call): void {
    this.logger.info('CallService', 'Call initiated successfully:', call.id);
    this.currentCallId = call.id;
    this.activeCall.next(call);
    this.setCallState('ringing');
    this.play('ringtone', true);
    this.startOutgoingCallMedia(call.type);
  }

  private handleCallAccepted(call: Call): void {
    this.logger.info('CallService', 'Call accepted successfully:', call.id);
    this.activeCall.next(call);
    this.incomingCall.next(null);
    this.setCallState('connected');
    this.stop('ringtone');
    this.play('call-connected');
  }

  private handleCallEnded(): void {
    this.logger.info('CallService', 'Call ended, cleaning up');
    this.setCallState('idle');
    this.currentCallId = null;
    this.activeCall.next(null);
    this.incomingCall.next(null);
    this.stopAllSounds();
    this.play('call-end');
    this.cleanupWebRTC();
  }

  private handleIncomingCall(call: IncomingCall): void {
    this.logger.info('CallService', 'Incoming call received:', call.id);
    this.currentCallId = call.id;
    this.incomingCall.next(call);
    this.setCallState('ringing');
    this.play('ringtone', true);
    this.prepareForIncomingCall(call);
  }

  private handleCallStatusChange(call: Call): void {
    this.logger.info('CallService', 'Call status changed:', call.status);

    if (call.id === this.currentCallId) {
      this.activeCall.next(call);

      switch (call.status) {
        case CallStatus.CONNECTED:
          this.setCallState('connected');
          this.stop('ringtone');
          this.play('call-connected');
          break;
        case CallStatus.ENDED:
        case CallStatus.REJECTED:
          this.handleCallEnded();
          break;
      }
    }
  }

  private handleCallSignal(signal: CallSignal): void {
    this.logger.debug('CallService', 'Call signal received:', signal.type);
    this.callSignals.next(signal);
    // Traitement WebRTC des signaux sera ajouté ici
  }

  // ===== INITIALISATION =====

  private initializeSounds(): void {
    this.logger.debug('CallService', 'Initializing sounds...');
    this.createSyntheticSounds();
  }

  private createSyntheticSounds(): void {
    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);
    this.createSyntheticSound(
      'call-connected',
      [523.25, 659.25, 783.99],
      0.8,
      false
    );
    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);
  }

  private createSyntheticSound(
    name: string,
    frequencies: number[],
    duration: number,
    loop: boolean
  ): void {
    try {
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      const sampleRate = audioContext.sampleRate;
      const frameCount = sampleRate * duration;
      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);
      const channelData = buffer.getChannelData(0);

      for (let i = 0; i < frameCount; i++) {
        let sample = 0;
        frequencies.forEach((freq) => {
          const amplitude = 0.3 / frequencies.length;
          const phase = (i / sampleRate) * freq * 2 * Math.PI;
          sample += Math.sin(phase) * amplitude;
        });
        const envelope = Math.sin((i / frameCount) * Math.PI);
        channelData[i] = sample * envelope;
      }

      const audio = new Audio();
      audio.loop = loop;
      (audio as any).customPlay = () => {
        const source = audioContext.createBufferSource();
        source.buffer = buffer;
        source.loop = loop;
        source.connect(audioContext.destination);
        source.start();
        if (!loop) {
          setTimeout(() => {
            this.isPlaying[name] = false;
          }, duration * 1000);
        }
        return source;
      };

      this.sounds[name] = audio;
      this.isPlaying[name] = false;
    } catch (error) {
      this.logger.error(
        'CallService',
        `Error creating sound '${name}':`,
        error
      );
    }
  }

  private initializeSubscriptions(): void {
    this.logger.debug('CallService', 'Initializing subscriptions...');
    this.subscribeToIncomingCalls();
    this.subscribeToCallStatusChanges();
    this.subscribeToCallSignals();
  }

  private subscribeToIncomingCalls(): void {
    this.apollo
      .subscribe<{ incomingCall: IncomingCall }>({
        query: INCOMING_CALL_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.incomingCall) {
            this.handleIncomingCall(data.incomingCall);
          }
          if (errors) {
            this.logger.error(
              'CallService',
              'Incoming call subscription errors:',
              errors
            );
          }
        },
        error: (error) => {
          this.logger.error(
            'CallService',
            'Error in incoming call subscription:',
            error
          );
          setTimeout(() => this.subscribeToIncomingCalls(), 5000);
        },
      });
  }

  private subscribeToCallStatusChanges(): void {
    this.apollo
      .subscribe<{ callStatusChanged: Call }>({
        query: CALL_STATUS_CHANGED_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.callStatusChanged) {
            this.handleCallStatusChange(data.callStatusChanged);
          }
          if (errors) {
            this.logger.error(
              'CallService',
              'Call status subscription errors:',
              errors
            );
          }
        },
        error: (error) => {
          this.logger.error(
            'CallService',
            'Error in call status subscription:',
            error
          );
          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);
        },
      });
  }

  private subscribeToCallSignals(): void {
    this.apollo
      .subscribe<{ callSignal: CallSignal }>({
        query: CALL_SIGNAL_SUBSCRIPTION,
        errorPolicy: 'all',
      })
      .subscribe({
        next: ({ data, errors }) => {
          if (data?.callSignal) {
            this.handleCallSignal(data.callSignal);
          }
          if (errors) {
            this.logger.error(
              'CallService',
              'Call signal subscription errors:',
              errors
            );
          }
        },
        error: (error) => {
          this.logger.error(
            'CallService',
            'Error in call signal subscription:',
            error
          );
          setTimeout(() => this.subscribeToCallSignals(), 5000);
        },
      });
  }

  private initializeWebRTC(): void {
    this.logger.debug('CallService', 'Initializing WebRTC...');
    this.createPeerConnection();
  }

  // ===== GESTION AUDIO =====

  private play(name: string, loop: boolean = false): void {
    try {
      const sound = this.sounds[name];
      if (!sound || this.isPlaying[name]) return;

      if ((sound as any).customPlay) {
        (sound as any).currentSource = (sound as any).customPlay();
        this.isPlaying[name] = true;
      }
    } catch (error) {
      this.logger.error('CallService', `Error playing sound '${name}':`, error);
    }
  }

  private stop(name: string): void {
    try {
      const sound = this.sounds[name];
      if (!sound || !this.isPlaying[name]) return;

      if ((sound as any).currentSource) {
        (sound as any).currentSource.stop();
        (sound as any).currentSource = null;
      }
      this.isPlaying[name] = false;
    } catch (error) {
      this.logger.error(
        'CallService',
        `Error stopping sound '${name}':`,
        error
      );
    }
  }

  private stopAllSounds(): void {
    Object.keys(this.sounds).forEach((name) => this.stop(name));
  }

  // ===== WEBRTC =====

  private createPeerConnection(): void {
    try {
      this.peerConnection = new RTCPeerConnection(this.rtcConfig);
      this.logger.debug('CallService', 'PeerConnection created successfully');

      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate && this.currentCallId) {
          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));
        }
      };

      this.peerConnection.ontrack = (event) => {
        this.logger.info(
          'CallService',
          'Remote track received:',
          event.track.kind
        );
        this.remoteStream = event.streams[0];
        this.attachRemoteStream();
      };

      this.peerConnection.onconnectionstatechange = () => {
        const state = this.peerConnection?.connectionState;
        this.logger.debug('CallService', 'Connection state changed:', state);

        if (state === 'connected') {
          this.logger.info('CallService', '✅ WebRTC connection established');
          this.setCallState('connected');
        } else if (state === 'failed') {
          this.logger.error('CallService', '❌ WebRTC connection failed');
          this.handleCallEnded();
        }
      };
    } catch (error) {
      this.logger.error('CallService', 'Error creating PeerConnection:', error);
    }
  }

  private async startOutgoingCallMedia(callType: CallType): Promise<void> {
    try {
      this.logger.info('CallService', '🎥 Starting outgoing call media');
      const stream = await this.getUserMedia(callType);
      this.addLocalStreamToPeerConnection(stream);
      this.attachLocalStream();
    } catch (error) {
      this.logger.error(
        'CallService',
        'Error starting outgoing call media:',
        error
      );
    }
  }

  private async prepareForIncomingCall(call: IncomingCall): Promise<void> {
    try {
      this.logger.debug('CallService', 'Preparing WebRTC for incoming call');
      if (!this.peerConnection) {
        this.createPeerConnection();
      }
      const stream = await this.getUserMedia(call.type);
      this.addLocalStreamToPeerConnection(stream);
    } catch (error) {
      this.logger.error(
        'CallService',
        'Error preparing for incoming call:',
        error
      );
    }
  }

  private async getUserMedia(callType: CallType): Promise<MediaStream> {
    const constraints: MediaStreamConstraints = {
      audio: true,
      video: callType === CallType.VIDEO,
    };

    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      this.localStream = stream;
      return stream;
    } catch (error) {
      this.logger.error('CallService', 'Error getting user media:', error);
      throw error;
    }
  }

  private addLocalStreamToPeerConnection(stream: MediaStream): void {
    if (!this.peerConnection) return;

    stream.getTracks().forEach((track) => {
      this.peerConnection!.addTrack(track, stream);
    });
  }

  private attachLocalStream(): void {
    if (this.localVideoElement && this.localStream) {
      this.localVideoElement.srcObject = this.localStream;
    }
  }

  private attachRemoteStream(): void {
    if (this.remoteVideoElement && this.remoteStream) {
      this.remoteVideoElement.srcObject = this.remoteStream;
    }
  }

  private sendSignal(signalType: string, signalData: string): void {
    if (!this.currentCallId) return;

    this.apollo
      .mutate({
        mutation: SEND_CALL_SIGNAL_MUTATION,
        variables: {
          callId: this.currentCallId,
          signalType,
          signalData,
        },
      })
      .subscribe({
        next: () =>
          this.logger.debug('CallService', 'Signal sent:', signalType),
        error: (error) =>
          this.logger.error('CallService', 'Error sending signal:', error),
      });
  }

  // ===== NETTOYAGE =====

  private cleanupWebRTC(): void {
    this.logger.debug('CallService', 'Cleaning up WebRTC resources');

    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => track.stop());
      this.localStream = null;
    }

    if (this.remoteStream) {
      this.remoteStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.localVideoElement) {
      this.localVideoElement.srcObject = null;
    }

    if (this.remoteVideoElement) {
      this.remoteVideoElement.srcObject = null;
    }

    // Recréer une nouvelle PeerConnection pour les futurs appels
    this.createPeerConnection();
  }

  private cleanup(): void {
    this.stopAllSounds();
    this.cleanupWebRTC();
    this.activeCall.complete();
    this.incomingCall.complete();
    this.callSignals.complete();
  }

  // ===== MÉTHODES PUBLIQUES UTILITAIRES =====

  /**
   * Attache les éléments vidéo pour l'affichage
   */
  attachVideoElements(
    localVideo: HTMLVideoElement,
    remoteVideo: HTMLVideoElement
  ): void {
    this.localVideoElement = localVideo;
    this.remoteVideoElement = remoteVideo;

    if (this.localStream) {
      this.attachLocalStream();
    }
    if (this.remoteStream) {
      this.attachRemoteStream();
    }
  }

  /**
   * Active/désactive l'audio
   */
  toggleAudio(): boolean {
    this.isAudioEnabled = !this.isAudioEnabled;
    if (this.localStream) {
      this.localStream.getAudioTracks().forEach((track) => {
        track.enabled = this.isAudioEnabled;
      });
    }
    return this.isAudioEnabled;
  }

  /**
   * Active/désactive la vidéo
   */
  toggleVideo(): boolean {
    this.isVideoEnabled = !this.isVideoEnabled;
    if (this.localStream) {
      this.localStream.getVideoTracks().forEach((track) => {
        track.enabled = this.isVideoEnabled;
      });
    }
    return this.isVideoEnabled;
  }

  /**
   * Méthode de compatibilité pour setVideoElements
   */
  setVideoElements(
    localVideo: HTMLVideoElement,
    remoteVideo: HTMLVideoElement
  ): void {
    this.attachVideoElements(localVideo, remoteVideo);
  }

  /**
   * Obtient l'état audio actuel
   */
  get audioEnabled(): boolean {
    return this.isAudioEnabled;
  }

  /**
   * Obtient l'état vidéo actuel
   */
  get videoEnabled(): boolean {
    return this.isVideoEnabled;
  }

  /**
   * Obtient le stream local
   */
  get localMediaStream(): MediaStream | null {
    return this.localStream;
  }

  /**
   * Obtient le stream distant
   */
  get remoteMediaStream(): MediaStream | null {
    return this.remoteStream;
  }

  /**
   * Active les sons (méthode de compatibilité)
   */
  enableSounds(): void {
    this.logger.debug(
      'CallService',
      'Sounds are always enabled in unified service'
    );
  }

  /**
   * Désactive les sons (méthode de compatibilité)
   */
  disableSounds(): void {
    this.logger.debug('CallService', 'Disabling sounds');
    this.stopAllSounds();
  }
}
