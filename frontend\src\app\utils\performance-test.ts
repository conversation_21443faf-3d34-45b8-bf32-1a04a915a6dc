/**
 * Performance Testing Utilities for Message System
 * Use these functions to measure and validate performance improvements
 */

export class PerformanceTest {
  private static measurements: Map<string, number[]> = new Map();

  /**
   * Start measuring performance for a specific operation
   */
  static startMeasurement(operationName: string): string {
    const measurementId = `${operationName}_${Date.now()}_${Math.random()}`;
    performance.mark(`${measurementId}_start`);
    return measurementId;
  }

  /**
   * End measurement and record the result
   */
  static endMeasurement(measurementId: string): number {
    const endMark = `${measurementId}_end`;
    const startMark = `${measurementId}_start`;
    
    performance.mark(endMark);
    performance.measure(measurementId, startMark, endMark);
    
    const measure = performance.getEntriesByName(measurementId)[0];
    const duration = measure.duration;
    
    // Store measurement
    const operationName = measurementId.split('_')[0];
    if (!this.measurements.has(operationName)) {
      this.measurements.set(operationName, []);
    }
    this.measurements.get(operationName)!.push(duration);
    
    // Clean up
    performance.clearMarks(startMark);
    performance.clearMarks(endMark);
    performance.clearMeasures(measurementId);
    
    return duration;
  }

  /**
   * Get performance statistics for an operation
   */
  static getStats(operationName: string) {
    const measurements = this.measurements.get(operationName) || [];
    if (measurements.length === 0) {
      return null;
    }

    const sorted = [...measurements].sort((a, b) => a - b);
    const avg = measurements.reduce((a, b) => a + b, 0) / measurements.length;
    const min = sorted[0];
    const max = sorted[sorted.length - 1];
    const median = sorted[Math.floor(sorted.length / 2)];
    const p95 = sorted[Math.floor(sorted.length * 0.95)];

    return {
      count: measurements.length,
      average: Math.round(avg * 100) / 100,
      min: Math.round(min * 100) / 100,
      max: Math.round(max * 100) / 100,
      median: Math.round(median * 100) / 100,
      p95: Math.round(p95 * 100) / 100,
    };
  }

  /**
   * Test message loading performance
   */
  static async testMessageLoading(messageService: any, conversationId: string) {
    console.log('🧪 Testing message loading performance...');
    
    const measurementId = this.startMeasurement('messageLoading');
    
    try {
      await messageService.getMessages('sender', 'receiver', conversationId, 1, 25).toPromise();
      const duration = this.endMeasurement(measurementId);
      
      console.log(`✅ Message loading took: ${duration.toFixed(2)}ms`);
      return duration;
    } catch (error) {
      console.error('❌ Message loading test failed:', error);
      return null;
    }
  }

  /**
   * Test subscription setup performance
   */
  static testSubscriptionSetup(messageService: any, conversationId: string) {
    console.log('🧪 Testing subscription setup performance...');
    
    const measurementId = this.startMeasurement('subscriptionSetup');
    
    const subscription = messageService.subscribeToNewMessages(conversationId);
    const duration = this.endMeasurement(measurementId);
    
    console.log(`✅ Subscription setup took: ${duration.toFixed(2)}ms`);
    
    // Clean up
    setTimeout(() => subscription.unsubscribe(), 100);
    
    return duration;
  }

  /**
   * Test memory usage
   */
  static testMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100, // MB
        totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100, // MB
        jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024 * 100) / 100, // MB
      };
    }
    return null;
  }

  /**
   * Run comprehensive performance test suite
   */
  static async runTestSuite(messageService: any, conversationId: string) {
    console.log('🚀 Starting comprehensive performance test suite...');
    
    const results = {
      messageLoading: [],
      subscriptionSetup: [],
      memoryBefore: this.testMemoryUsage(),
      memoryAfter: null,
    };

    // Test message loading multiple times
    for (let i = 0; i < 5; i++) {
      const duration = await this.testMessageLoading(messageService, conversationId);
      if (duration) results.messageLoading.push(duration);
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
    }

    // Test subscription setup multiple times
    for (let i = 0; i < 5; i++) {
      const duration = this.testSubscriptionSetup(messageService, conversationId);
      results.subscriptionSetup.push(duration);
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
    }

    results.memoryAfter = this.testMemoryUsage();

    // Generate report
    console.log('📊 Performance Test Results:');
    console.log('Message Loading Stats:', this.getStats('messageLoading'));
    console.log('Subscription Setup Stats:', this.getStats('subscriptionSetup'));
    console.log('Memory Usage:', {
      before: results.memoryBefore,
      after: results.memoryAfter,
    });

    return results;
  }

  /**
   * Clear all measurements
   */
  static clearMeasurements() {
    this.measurements.clear();
    performance.clearMarks();
    performance.clearMeasures();
  }
}

/**
 * Decorator for measuring method performance
 */
export function measurePerformance(operationName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const measurementId = PerformanceTest.startMeasurement(operationName);
      
      try {
        const result = method.apply(this, args);
        
        // Handle async methods
        if (result && typeof result.then === 'function') {
          return result.finally(() => {
            PerformanceTest.endMeasurement(measurementId);
          });
        } else {
          PerformanceTest.endMeasurement(measurementId);
          return result;
        }
      } catch (error) {
        PerformanceTest.endMeasurement(measurementId);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Performance monitoring for development
 */
export class PerformanceMonitor {
  private static isEnabled = !environment.production;

  static log(message: string, data?: any) {
    if (this.isEnabled) {
      console.log(`🔍 [Performance] ${message}`, data || '');
    }
  }

  static warn(message: string, data?: any) {
    if (this.isEnabled) {
      console.warn(`⚠️ [Performance] ${message}`, data || '');
    }
  }

  static time(label: string) {
    if (this.isEnabled) {
      console.time(`⏱️ [Performance] ${label}`);
    }
  }

  static timeEnd(label: string) {
    if (this.isEnabled) {
      console.timeEnd(`⏱️ [Performance] ${label}`);
    }
  }
}

// Export environment for the performance monitor
declare const environment: any;
